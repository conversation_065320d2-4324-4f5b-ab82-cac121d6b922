<?php
/**
 * Test custom tables functionality
 */

// Include the necessary files
require_once 'system/classes/startup_sequence.class.php';
require_once 'system/functions/functions.php';

// Initialize the system
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . '/system/config/path_schema.php';
$path['fs_system'] = $path['fs_app_root'] . '/' . $schema['system']['root'];
startup_sequence::start($path, $schema);

// Include the API functions
require_once 'system/api/data_sources.api.php';

echo "<h1>Test Custom Tables Functionality</h1>";

try {
    // Test the build_multi_table_query function with custom tables
    $tables = ['autodesk_subscriptions'];
    $joins = [];
    $selected_columns = [];
    $filters = [];
    $table_aliases = ['autodesk_subscriptions' => 'subs'];
    $column_aliases = [];
    $custom_columns = [];
    $sorting = [];
    $grouping = [];
    $limits = [];
    
    // Define a custom table similar to your example
    $custom_tables = [
        [
            'alias' => 'lastquote',
            'join_type' => 'LEFT JOIN',
            'sql' => 'SELECT
                        qi.subscription_id,
                        q.id AS quote_id,
                        q.quote_status,
                        q.quote_number,
                        q.quoted_date,
                        qi.id AS qitem_id
                    FROM
                        autodesk_quote_line_items qi
                    JOIN autodesk_quotes q ON q.id = qi.quote_id
                    WHERE
                        qi.subscription_id IS NOT NULL
                        AND q.quote_status NOT IN (\'Expired\', \'Cancelled\')
                    ORDER BY
                        qi.subscription_id, q.quoted_date DESC, qi.id DESC
                    LIMIT 1',
            'join_condition' => 'subs.subscriptionId = lastquote.subscription_id',
            'columns' => 'subscription_id, quote_id, quote_status, quote_number, quoted_date, qitem_id',
            'description' => 'Latest quote for each subscription'
        ]
    ];
    
    echo "<h2>Test Configuration:</h2>";
    echo "<h3>Tables:</h3>";
    echo "<pre>" . json_encode($tables, JSON_PRETTY_PRINT) . "</pre>";
    
    echo "<h3>Table Aliases:</h3>";
    echo "<pre>" . json_encode($table_aliases, JSON_PRETTY_PRINT) . "</pre>";
    
    echo "<h3>Custom Tables:</h3>";
    echo "<pre>" . json_encode($custom_tables, JSON_PRETTY_PRINT) . "</pre>";
    
    // Generate the query
    $query = \api\data_sources\build_multi_table_query(
        $tables,
        $joins,
        $selected_columns,
        $filters,
        $table_aliases,
        $column_aliases,
        $custom_columns,
        $sorting,
        $grouping,
        $limits,
        $custom_tables
    );
    
    echo "<h2>Generated Query:</h2>";
    echo "<pre style='background: #f5f5f5; padding: 15px; border: 1px solid #ddd; overflow-x: auto;'>";
    echo htmlspecialchars($query);
    echo "</pre>";
    
    // Test the API endpoints
    echo "<h2>Testing API Endpoints:</h2>";
    
    // Test add_custom_table
    echo "<h3>Testing add_custom_table:</h3>";
    $add_result = \api\data_sources\add_custom_table(['custom_tables' => []]);
    if (strpos($add_result, 'data-source-custom-table-row') !== false) {
        echo "<p style='color: green;'>✓ add_custom_table returned HTML component</p>";
    } else {
        echo "<p style='color: red;'>✗ add_custom_table failed</p>";
        echo "<pre>" . htmlspecialchars(substr($add_result, 0, 200)) . "</pre>";
    }
    
    // Test query_preview_fragment with custom tables
    echo "<h3>Testing query_preview_fragment with custom tables:</h3>";
    $preview_params = [
        'selected_tables' => json_encode($tables),
        'table_aliases' => $table_aliases,
        'custom_tables' => $custom_tables
    ];
    
    $preview_result = \api\data_sources\query_preview_fragment($preview_params);
    if (strpos($preview_result, 'lastquote') !== false) {
        echo "<p style='color: green;'>✓ query_preview_fragment includes custom table alias</p>";
    } else {
        echo "<p style='color: red;'>✗ query_preview_fragment doesn't include custom table</p>";
    }
    
    echo "<h4>Preview Result:</h4>";
    echo "<div style='border: 1px solid #ddd; padding: 10px; background: #f9f9f9;'>";
    echo $preview_result;
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='data_sources'>← Back to Data Sources</a></p>";
?>
