/* HTMX Indicator Styles */

.htmx-indicator {
    opacity: 0;
    transition: opacity 200ms ease-in-out;
}

.htmx-request .htmx-indicator {
    opacity: 1;
}

.htmx-request.htmx-indicator {
    opacity: 1;
}

/* Progress bar animation */
@keyframes progress {
    0% { width: 0; }
    10% { width: 30%; }
    20% { width: 50%; }
    30% { width: 70%; }
    40% { width: 80%; }
    50% { width: 85%; }
    60% { width: 90%; }
    70% { width: 93%; }
    80% { width: 95%; }
    90% { width: 97%; }
    100% { width: 99%; }
}

.htmx-request .progress-bar {
    animation: progress 2s ease-in-out forwards;
}

/* Enhanced Progress Bar Styles */
.progress {
    background-color: #e5e7eb;
    border-radius: 9999px;
    height: 0.5rem;
    overflow: hidden;
    position: relative;
}

.progress-bar {
    background-color: #4f46e5;
    height: 100%;
    border-radius: 9999px;
    transition: width 300ms ease-out;
    position: relative;
}

.progress-bar.bg-green-500 {
    background-color: #10b981;
}

.progress-bar.bg-indigo-600 {
    background-color: #4f46e5;
}

/* Smooth progress bar animation */
.progress-bar {
    transition: width 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Status text styling */
.status-text {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 1rem;
}

.status-text .status-icon {
    font-size: 1rem;
}

/* Button styling for restart button */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
    font-weight: 600;
    font-size: 0.875rem;
    line-height: 1.25rem;
    padding: 0.5rem 1rem;
    transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    cursor: pointer;
}

.btn-primary {
    background-color: #4f46e5;
    color: white;
}

.btn-primary:hover {
    background-color: #4338ca;
}

/* Fade-in animation for restart button */
.show {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 600ms ease-out, transform 600ms ease-out;
}

.show:not(.show) {
    opacity: 0;
    transform: translateY(10px);
}
