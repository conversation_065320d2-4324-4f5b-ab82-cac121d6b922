<?php
namespace api\products;
use autodesk_api\autodesk_api;
use system\data_importer;

print_rr($input_params);
function search($p) {
    return generate_product_table(criteria: ["search" => $p['search_terms'], "limit" => 50],just_body: true);
}

function import_csv_into_database() {
    autodesk_api::import_csv_into_database(Autodeskproducts::get_product_column_mapping(), DIR_FS_CATALOG . DIRECTORY_SEPARATOR . "feeds/products.csv");
}

function data_table_filter($p){
    $criteria = data_table::api_process_criteria($p);
    return generate_subscription_table(criteria: $criteria);
}


function view($p) {
    $autodesk = new autodesk_api();
    $product = $autodesk->product->get($p['product_number']);
    print_rr($product);

    $out = '<!-- Modal -->';
    $out .= generateCard('product', generateSection($product['body']['products'][0], 'product Details'));
    echo $out;
}

    // Function to generate a text input
    function generateTextInput($label, $name, $value) {

        //  <label for="name" class="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">Name</label>
        //  <input type="text" name="name" id="name" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" placeholder="Jane Smith">

        return "
    <div class='relative '>
        <label class='absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900' for='{$name}'>{$label}</label>
        <input type='text' id='{$name}' name='{$name}' value='{$value}' readonly
               class='block w-full rounded-md border-0 pl-1.5 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6'>
    </div>";
    }

// Function to generate a card with collapsible sections for arrays
function generateCard($title, $content) {
    return "
    <div x-data='{ open: false }' class='bg-white shadow-md rounded-lg col-span-4 '>
        <button @click='open = !open' class='w-full px-4 py-2 text-left text-lg font-semibold text-gray-700 hover:bg-gray-100 focus:outline-none'>
            {$title}
            <span x-show='!open' class='ml-2'>+</span>
            <span x-show='open' class='ml-2'>-</span>
        </button>
        <div x-show='open' class='px-4 py-2 gap-4 border-t border-gray-200 grid grid-cols-4'>
            {$content}
        </div>
    </div>";
}



// Function to generate a form section for associative arrays
function generateSection($data, $sectionTitle) {
    $content = '';
    $hasContent = false;
    print_rr($data, "generate section");
    foreach ($data as $key => $value) {
        if (is_array($value)) {
            $temp_content = generateCard(ucwords(str_replace('_', ' ', $key)), generateSection($value, $key));
            if (!$temp_content) continue;
            $content .= $temp_content;
            $hasContent = true;
        } else {
            if (empty($value)) continue;
            $content .= generateTextInput(ucwords(str_replace('_', ' ', $key)), $key, $value);
            $hasContent = true;
        }
    }
    if ($hasContent) return $content;
    else return false;
}
