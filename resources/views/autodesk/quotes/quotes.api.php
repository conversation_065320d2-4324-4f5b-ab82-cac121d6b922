<?php
namespace api\autodesk\quotes;

use autodesk_api\autodesk_api;
use autodesk_api\autodesk_quotes;
use Edge\Edge;
use data_table\data_table;
use system\data_importer;
use DateTime;

//print_rr($input_params);


function import_csv_into_database(){
    autodesk_api::import_csv_into_database(autodesk_quotes::get_quote_column_mapping(), DIR_FS_CATALOG . DIRECTORY_SEPARATOR . "feeds/quotes.csv");
}

function search($p){
    return generate_quote_table(criteria: ["search" => $p['search_terms'], "limit" => 50], just_body: false);
}


function data_table_filter($p){
    $criteria = data_table::api_process_criteria($p);
    return generate_quote_table(criteria: $criteria);
}

function create_quote($p){
    $autodesk = new autodesk_api();
    $quote = $autodesk->quote->create_renewal($p['subs_quoteId'], $p['user_id']);
    print_rr($quote,'quoty1');
    $quote_assoc = json_decode($quote, true);
    print_rr($quote_assoc,'quoty2');
    return edge::render('quote-v3-ui-form', ['quote_data' => $quote_assoc]);
}

function send_quote($p){
    $autodesk = new autodesk_api();
    $quotedata = $p['quote'];
    $quote = $autodesk->quote->send_to_autodesk($quotedata);
    print_rr(i:['quote' => $quote,'p' => $p],co:false,full:true);
    return edge::render('layout-card', ['content' =>  $quote]);
}

function email_send_reminder(){
    $autodesk = new autodesk_api();
    $subs = $autodesk->quotes->get_renewable();
    $file_path = FS_APP_ROOT . DIRECTORY_SEPARATOR . 'resources/views/quotes/email_history/reminder_email.view.php';
    $email_template = file_get_contents($file_path);
    if ($email_template === false) {
        throw new Exception("Failed to load email template from {$file_path}");
    }

    // Split the content by lines
    $lines = explode("\n", $email_template);

    $email_rules = explode(',', autodesk_api::database_get_storage('quote_renew_email_send_rules'));
    $settings_days = autodesk_api::database_get_storage('quote_renew_email_send_days');
    $settings_time = autodesk_api::database_get_storage('quote_renew_email_send_time');


    // Check if it's time to send an email.
    foreach ($subs as $sub) {
        if ($subs['tcs_unsubscribe'] = 1) continue;
        $now = new DateTime();
        $end_date = new DateTime($sub['endDate']);
        $date_last_sent = $sub['last_email_sent_date'] ?? $sub['startDate'];
        $last_sent = new DateTime($date_last_sent);
        $days_remaining = $now->diff($end_date)->days;
        $days_since_last_sent = $now->diff($last_sent)->days;
        echo "Processing quote " . $sub['quoteReferenceNumber'] . " id " . $sub['id'] . " for customer: " . $sub['endCustomer_name'] . " End date is " . $sub['endDate'] . " with d/r: {$days_remaining} and last sent: " . $sub['last_email_sent_date'] . ": ";
        foreach ($email_rules as $key => $rule) {
            if ($days_remaining <= $rule) {
                echo " is below rule $rule.<br> " . PHP_EOL;
                if ((($days_remaining + $days_since_last_sent) > $rule)) {
                    echo "sending email <br>" . PHP_EOL;
                    $autodesk->quotes->send_reminder_email($sub['id']);
                    //print_rr($autodesk->quotes->send_reminder_email($sub['id'], $rule));
                }
                break;
            }
        }
    }
}

function view($p) {
    $autodesk = new autodesk_api();

    // Define the quote display layout structure based on actual database schema
    $quote_display = [
        'quote_details' => [
            'label' => 'Quote Details',
            'collapsed' => 'false',
            'type' => 'layout-card',
            'class_suffix' => 'col-span-2',
            'content' => [
                'basic_info' => [
                    'label' => 'Quote Information',
                    'type' => 'layout-card',
                    'fields' => [
                        'quotes_quote_number' => 'Quote Number',
                        'quotes_quote_status' => 'Status',
                        'quotes_quote_created_time' => 'Created',
                        'quotes_quote_expiration_date' => 'Expires',
                        'quotes_quoted_date' => 'Quoted Date',
                        'quotes_quote_opportunity_number' => 'Opportunity Number',
                        'quotes_quote_language' => 'Language',
                        'quotes_modified_at' => 'Last Modified'
                    ]
                ]
            ]
        ],
        'customer_info' => [
            'label' => 'Customer Information',
            'collapsed' => 'false',
            'type' => 'layout-card',
            'class_suffix' => 'col-span-1',
            'content' => [
                'customer_details' => [
                    'label' => 'Customer Details',
                    'type' => 'layout-card',
                    'fields' => [
                        'endcust_name' => 'Customer Name',
                        'endcust_primary_admin_email' => 'Email',
                        'quotecontact_first_name' => 'Contact First Name',
                        'quotecontact_last_name' => 'Contact Last Name',
                        'quotecontact_email' => 'Contact Email'
                    ]
                ]
            ]
        ],
        'pricing_info' => [
            'label' => 'Pricing Information',
            'collapsed' => 'false',
            'type' => 'layout-card',
            'class_suffix' => 'col-span-2',
            'content' => [
                'pricing_details' => [
                    'label' => 'Pricing Details',
                    'type' => 'layout-card',
                    'fields' => [
                        'quotes_quote_currency' => 'Currency',
                        'quotes_total_list_amount' => 'List Amount',
                        'quotes_total_net_amount' => 'Net Amount',
                        'quotes_total_amount' => 'Total Amount',
                        'quotes_total_discount' => 'Discount',
                        'quotes_estimated_tax' => 'Estimated Tax',
                        'quotes_payment_terms_code' => 'Payment Terms',
                        'quotes_payment_terms_description' => 'Payment Description'
                    ]
                ]
            ]
        ],
        'line_items' => [
            'label' => 'Line Items',
            'collapsed' => 'false',
            'type' => 'layout-card',
            'class_suffix' => 'col-span-3',
            'content' => [
                'items' => [
                    'label' => 'Products & Services',
                    'type' => 'function',
                    'content' => function() use ($p) {
                        // Include the quotes function file if not already included
                        if (!function_exists('generate_quote_line_items_table')) {
                            include_once(FS_FUNCTIONS . 'quotes.fn.php');
                        }
                        return generate_quote_line_items_table($p);
                    }
                ]
            ]
        ],
        'timeline' => [
            'label' => 'Activity History',
            'collapsed' => 'false',
            'type' => 'layout-card',
            'class_suffix' => 'row-span-4',
            'content' => [
                'timeline' => [
                    'label' => '',
                    'type' => 'function',
                    'content' => function() use (&$activity) {
                        if (empty($activity)) {
                            return '<div class="p-4 text-gray-500">No activity history available</div>';
                        }

                        $html = '<div class="flow-root p-2">';
                        $html .= '<ul role="list" class="-mb-8">';

                        foreach ($activity as $index => $item) {
                            $is_last = $index === count($activity) - 1;
                            $date_formatted = is_string($item['date']) ? date('M j, Y', strtotime($item['date'])) : date('M j, Y');

                            // Determine icon based on activity type
                            $icon_class = 'bg-gray-400';
                            $icon_svg = '<svg class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zm0 14a6 6 0 100-12 6 6 0 000 12z" clip-rule="evenodd" />
                            </svg>';

                            if ($item['type'] === 'e-mail' || $item['type'] === 'email') {
                                $icon_class = 'bg-blue-500';
                                $icon_svg = '<svg class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                                </svg>';
                            } else if ($item['type'] === 'phone') {
                                $icon_class = 'bg-green-500';
                                $icon_svg = '<svg class="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                                </svg>';
                            }

                            $html .= '<li>';
                            $html .= '<div class="relative pb-8">';

                            // Line connecting timeline items
                            if (!$is_last) {
                                $html .= '<span class="absolute top-5 left-5 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>';
                            }

                            $html .= '<div class="relative flex items-start space-x-3">';

                            // Icon
                            $html .= '<div>';
                            $html .= '<div class="relative px-1">';
                            $html .= '<div class="h-10 w-10 rounded-full flex items-center justify-center ring-8 ring-white ' . $icon_class . '">';
                            $html .= $icon_svg;
                            $html .= '</div>';
                            $html .= '</div>';
                            $html .= '</div>';

                            // Content
                            $html .= '<div class="min-w-0 flex-1 py-1.5">';
                            $html .= '<div class="text-sm text-gray-500">';
                            $html .= '<span class="font-medium text-gray-900">' . htmlspecialchars($item['user']) . '</span>';
                            $html .= ' - <span class="text-gray-700">' . htmlspecialchars($date_formatted) . '</span>';
                            $html .= '</div>';
                            $html .= '<div class="mt-2 text-sm text-gray-700">';
                            $html .= '<p>' . htmlspecialchars($item['message']) . '</p>';
                            $html .= '</div>';

                            // Additional details if available
                            if (!empty($item['email_address']) || !empty($item['result'])) {
                                $html .= '<div class="mt-1 text-xs text-gray-500">';
                                if (!empty($item['email_address'])) {
                                    $html .= '<div>Email: ' . htmlspecialchars($item['email_address']) . '</div>';
                                }
                                if (!empty($item['result'])) {
                                    $html .= '<div>Result: ' . htmlspecialchars($item['result']) . '</div>';
                                }
                                $html .= '</div>';
                            }

                            $html .= '</div>';
                            $html .= '</div>';
                            $html .= '</div>';
                            $html .= '</li>';
                        }

                        $html .= '</ul>';
                        $html .= '</div>';

                        return $html;
                    }
                ]
            ]
        ]
    ];

    // Debug: Log the parameters received
    print_rr($p, 'quote_view_parameters');

    // Get quote data using the existing method
    $criteria = [];

    // Check for valid quote_number (not null, not empty, not string "null")
    if (isset($p['quote_number']) && $p['quote_number'] !== null && $p['quote_number'] !== '' && $p['quote_number'] !== 'null') {
        $criteria['where'] = ['quotes.quote_number' => ['=', $p['quote_number']]];
    }
    // Check for valid id (not null, not empty, not string "null")
    elseif (isset($p['id']) && $p['id'] !== null && $p['id'] !== '' && $p['id'] !== 'null') {
        $criteria['where'] = ['quotes.id' => ['=', $p['id']]];
    }
    else {
        echo Edge::render('layout-card', [
            'content' => '<div class="p-4">
                <h3 class="text-lg font-medium text-red-600 mb-2">Invalid Quote Identifier</h3>
                <p class="text-sm text-gray-600 mb-2">No valid quote identifier provided.</p>
                <div class="bg-gray-100 p-2 rounded text-xs">
                    <strong>Parameters received:</strong><br>
                    ' . htmlspecialchars(print_r($p, true)) . '
                </div>
            </div>'
        ]);
        return;
    }

    // Define the columns we need for the quote display
    $columns = [
        'table_id' => 'quotes',
        'db_table' => 'quotes',
        'columns' => [
            // Quote basic info
            'quotes_quote_number',
            'quotes_quote_status',
            'quotes_quote_created_time',
            'quotes_quote_expiration_date',
            'quotes_quoted_date',
            'quotes_quote_opportunity_number',
            'quotes_quote_language',

            // Customer info
            'endcust_name',
            'endcust_primary_admin_email',
            'quotecontact_name',
            'quotecontact_email',

            // Pricing info
            'quotes_quote_currency',
            'quotes_total_list_amount',
            'quotes_total_net_amount',
            'quotes_total_amount',
            'quotes_total_discount',
            'quotes_estimated_tax',
            'quotes_payment_terms_code',
            'quotes_payment_terms_description',

            // Basic fields
            'quotes_id',
           // 'id'
        ]
    ];

    $quotes = $autodesk->quotes->get_all($columns['columns'], $criteria);

    if (!$quotes || count($quotes) == 0) {
        echo Edge::render('layout-card', ['content' => 'Quote not found']);
        return;
    }

    $quote = $quotes[0]; // Get the first (and should be only) result

    // Convert the quote data into the format expected by layout-modal_data_display
    $processed_layout = [];
    foreach ($quote_display as $section_key => $section) {
        $processed_section = $section;

        if (isset($section['content'])) {
            foreach ($section['content'] as $content_key => $content) {
                if (isset($content['fields'])) {
                    // Convert fields to the expected format
                    $field_content = [];
                    foreach ($content['fields'] as $field_key => $field_label) {
                        $value = $quote[$field_key] ?? '-';

                        // Format currency fields
                        if (strpos($field_key, 'amount') !== false || strpos($field_key, 'discount') !== false || strpos($field_key, 'tax') !== false) {
                            if ($value !== '-' && is_numeric($value)) {
                                $currency = $quote['quotes_quote_currency'] ?? 'GBP';
                                $value = $currency . ' ' . number_format($value, 2);
                            }
                        }

                        // Format date fields
                        if (strpos($field_key, 'date') !== false || strpos($field_key, 'time') !== false) {
                            if ($value !== '-' && !empty($value)) {
                                $value = date('M j, Y', strtotime($value));
                            }
                        }

                        $field_content[] = [
                            'label' => $field_label,
                            'value' => $value
                        ];
                    }
                    $processed_section['content'][$content_key]['content'] = $field_content;
                    unset($processed_section['content'][$content_key]['fields']);
                }
            }
        }

        $processed_layout[$section_key] = $processed_section;
    }

    // Get activity/timeline data (if available)
    $activity = [];

    // Try to get quote history from the database using correct schema
    if (isset($quote['quotes_quote_number'])) {
        $quote_number = $quote['quotes_quote_number'];

        // Query for history data using the correct table structure
        $history_query = "SELECT h.*, u.name as user_name
                         FROM autodesk_history h
                         LEFT JOIN autobooks_users u ON h.user_id = u.id
                         WHERE h.target = 'quote' AND h.target_reference = :quote_number
                         ORDER BY h.date DESC, h.id DESC";

        $history_params = [':quote_number' => $quote_number];

        try {
            $history_data = tcs_db_query($history_query, $history_params);

            if ($history_data && count($history_data) > 0) {
                foreach ($history_data as $history_item) {
                    $activity[] = [
                        'date' => $history_item['date'] ?? date('Y-m-d'),
                        'type' => strtolower($history_item['media'] ?? 'system'),
                        'message' => $history_item['message'] ?? 'No message',
                        'user' => $history_item['user_name'] ?? 'System',
                        'email_address' => $history_item['email_address'] ?? null,
                        'result' => $history_item['result'] ?? null
                    ];
                }
            }
        } catch (Exception $e) {
            // If history query fails, just continue without history
            print_rr("History query failed: " . $e->getMessage(), 'quote_history_error');
        }

        // Add quote creation as an activity if no history exists
        if (empty($activity) && isset($quote['quotes_quote_created_time'])) {
            $activity[] = [
                'date' => $quote['quotes_quote_created_time'],
                'type' => 'system',
                'message' => 'Quote created',
                'user' => 'System'
            ];
        }

        // Add quote status changes as activities
        if (isset($quote['quotes_quote_status']) && $quote['quotes_quote_status'] !== 'Draft') {
            $activity[] = [
                'date' => $quote['quotes_quoted_date'] ?? $quote['quotes_quote_created_time'],
                'type' => 'system',
                'message' => 'Quote status: ' . $quote['quotes_quote_status'],
                'user' => 'System'
            ];
        }
    }

    // We're using a function to render the timeline directly, so we don't need to pass activity data
    $out = Edge::render('layout-modal_data_display', [
        'layout' => $processed_layout,
        'content' => []
    ]);

    echo $out;
}

function flag_modal($p):string {
    return Edge::render('component-add-flag', $p);
}

function add_flag($p):void {
    $data = [
        'id' => $p['sub_id'],
        'quoteReferenceNumber' => $p['sub_num'],
        'media' => $p['label'],
        'user_id' => $p['user_id'],
        'message' => $p['message']
    ];
    $autodesk = new autodesk_api();
    $history = $autodesk->insert_history_item($data);
}

