<?php
namespace api\subscriptions\email_history;
use autodesk_api\autodesk_api;
use data_importer;
//print_rr($input_params);


function import_csv_into_database() {
    autodesk_api::import_csv_into_database(AutodeskSubscriptions::get_subscription_column_mapping(), DIR_FS_CATALOG . DIRECTORY_SEPARATOR . "feeds/subscriptions.csv");
}

function search($p) {
    return generate_subscription_table(criteria: [
        "column" => [
            "hist_date_sent","not_null"
        ],
        "order_by" => "hist_date_sent DESC",
        "search" => $p['search_terms'],
        "limit" => 50
    ],just_body: false);
}

function data_table_filter($p) {
   print_rr($p, 'subs_api_data_table_filter');
    $cols = [];
    $criteria = [
        "column" => [
            "hist_date_sent","not_null"
        ],
        "order_by" => "hist_date_sent DESC"
    ];
    if (isset($p['column'])) {
        foreach ($p['column'] as $column => $value) {
            if (empty($value)) continue;
            print_rr($column, ' val: ' . $value);
            $col_parts = explode("_", $column,2 );
            $table = $col_parts[0];$column_name = $col_parts[1];
            $cols["{$table}.{$column_name}"] = ['=', $value];
        }
        if (count($cols) > 0) $criteria["where"] = $cols;
    }
    if (isset($p['search_terms']) && $p['search_terms'] != '')  $criteria["search"] = $p['search_terms'];
   return generate_subscription_table($criteria, just_body: false);
}

function emails_send_reminders() {
    $autodesk = new autodesk_api();
    $subs = $autodesk->subscriptions->get_renewable();
    $file_path = FS_APP_ROOT . DIRECTORY_SEPARATOR . 'resources/views/subscriptions/email_history/reminder_email.view.php';
    $email_template = file_get_contents($file_path);
    if ($email_template === false) {
        throw new Exception("Failed to load email template from {$file_path}");
    }

    // Split the content by lines
    $lines = explode("\n", $email_template);

    $email_rules = explode(',', autodesk_api::database_get_storage('subscription_renew_email_send_rules'));
    $settings_days = autodesk_api::database_get_storage('subscription_renew_email_send_days');
    $settings_time = autodesk_api::database_get_storage('subscription_renew_email_send_time');


    // Check if it's time to send an email.
    foreach ($subs as $sub) {
        if ($subs['tcs_unsubscribe'] = 1) continue;
        $now = new DateTime();
        $end_date = new DateTime($sub['endDate']);
        $date_last_sent = $sub['last_email_sent_date'] ?? $sub['startDate'];
        $last_sent = new DateTime($date_last_sent);
        $days_remaining = $now->diff($end_date)->days;
        $days_since_last_sent = $now->diff($last_sent)->days;
        echo "Processing subscription " . $sub['subscriptionReferenceNumber'] . " id " . $sub['id'] . " for customer: " . $sub['endCustomer_name'] . " End date is " . $sub['endDate'] . " with d/r: {$days_remaining} and last sent: " . $sub['last_email_sent_date'] . ": ";
        foreach ($email_rules as $key => $rule) {
            if ($days_remaining <= $rule) {
                echo " is below rule $rule.<br> " . PHP_EOL;
                if ((($days_remaining + $days_since_last_sent) > $rule)) {
                    echo "sending email <br>" . PHP_EOL;
                    $autodesk->subscriptions->send_reminder_email($sub['id']);
                    //print_rr($autodesk->subscriptions->send_reminder_email($sub['id'], $rule));
                }
                break;
            }
        }
    }
}

function view($p) {
    $autodesk = new autodesk_api();
    $subscription = $autodesk->subscription->get($p['subscription_number']);
   // print_rr($subscription);

    $out = '<!-- Modal -->';
    $out .= generateCard('Subscription', generateSection($subscription['body']['results'][0], 'Subscription Details'),true);
    echo $out;
}

    // Function to generate a text input
    function generateTextInput($label, $name, $value) {

        //  <label for="name" class="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">Name</label>
        //  <input type="text" name="name" id="name" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" placeholder="Jane Smith">

        return "
    <div class='relative '>
        <label class='absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900' for='{$name}'>{$label}</label>
        <input type='text' id='{$name}' name='{$name}' value='{$value}' readonly
               class='block w-full rounded-md border-0 pl-1.5 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6'>
    </div>";
    }

// Function to generate a card with collapsible sections for arrays
function generateCard($title, $content, $open = false) {
    return "
    <div x-data='{ open: false}' class='bg-white shadow-md rounded-lg col-span-4 '>
        <button @click='open = !open' class='w-full px-4 py-2 text-left text-lg font-semibold text-gray-700 hover:bg-gray-100 focus:outline-none'>
            {$title}
            <span x-show='!open' class='ml-2'>+</span>
            <span x-show='open' class='ml-2'>-</span>
        </button>
        <div x-show='open' class='px-4 py-2 gap-4 border-t border-gray-200 grid grid-cols-4'>
            {$content}
        </div>
    </div>";
}



// Function to generate a form section for associative arrays
function generateSection($data, $sectionTitle) {
    $content = '';
    $hasContent = false;
  // print_rr($data, "generate section");
    foreach ($data as $key => $value) {
        if (is_array($value)) {
            $temp_content = generateCard(ucwords(str_replace('_', ' ', $key)), generateSection($value, $key));
            if (!$temp_content) continue;
            $content .= $temp_content;
            $hasContent = true;
        } else {
            if (empty($value)) continue;
            $content .= generateTextInput(ucwords(str_replace('_', ' ', $key)), $key, $value);
            $hasContent = true;
        }
    }
    if ($hasContent) return $content;
    else return false;
}
