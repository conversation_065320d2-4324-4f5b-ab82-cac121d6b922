<?php
use autodesk_api\autodesk_api;
use edge\Edge;

    $query = tcs_db_query("select * from autodesk_storage where autodesk_storage_key = 'subscription_table_config'");
    $config = json_decode( $query[0]['autodesk_storage_data'], true);

    // Get database fields from the subscriptions table
   $db_fields = get_db_fields();
   echo Edge::render('subscription-table-config', [
       'config' => $config,
       'db_fields' => $db_fields
   ]);
   echo Edge::render('subscriptions-email-settings');
?>

