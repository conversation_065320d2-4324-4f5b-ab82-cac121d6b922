<?php
namespace api\subscriptions\config;

function add_replacement_row() {
    return Edge::render('subscription-table-config-replacement-row', [
        'search' => '',
        'replace' => '',
        'is_new' => true
    ]);
}

function add_column_row() {
    return Edge::render('subscription-table-config-column', [
        'index' => request()->get('index'),
        'column' => [
            'label' => '',
            'field' => '',
            'auto_filter' => false,
            'filters' => []
        ],
        'db_fields' => get_db_fields(),
        'is_new' => true
    ]);
}

function add_filter_row() {
    return Edge::render('subscription-table-config-filter-row', [
        'columnIndex' => request()->get('columnIndex'),
        'filterKey' => '',
        'filterValue' => '',
        'is_new' => true
    ]);
}