<?php
namespace api\orders;
use autodesk_api\autodesk_api;
use edge\Edge;

function update($p) {
    $quotes = tcs_db_query("
            Select * from autodesk_quotes
            LEFT Join orders on orders.orders_id = autodesk_quotes.orders_id
            LEFT join customers on customers.customers_id = orders.customers_id
            where autodesk_quotes.modifiedAt > '{$p['last_update']}'"
    );
   // print_rr($quotes);
   $input = Edge::render('forms-input', [
           'type' => 'hidden',
           'id' => 'last_update',
           'name' => 'last_update',
           'value' => date('Y-m-d H:i:s'),
           'hx-post' => 'api/update',
           'hx-swap' => 'outerHTML',
           'hx-trigger' => 'every 10s'
       ]
   );

    $rows = '';
    //print_rr($quotes, 'quotes',);
    foreach ($quotes as $quote) {
        $rows .= PHP_EOL . '<template>' . generate_quote_table(criteria: ['where'=> ['quotes_quote_id' => ['=',$quote['quote_id'] ] ] ],just_rows: true, htmx_oob: true) . '</template>';
   }

    return $input . $rows;
}

function autodesk_send_quote($p) {
    if (is_numeric($p['orders_id'])) {
        $autodesk = new autodesk_api();
        $quote_result = $autodesk->order->send($p['orders_id']);
        $response = ["status" => $quote_result['response'], "data" => $quote_result, "debug" => $autodesk->debugLog];
        print_rr($response);
        return 'Sent';
    } else {
        return 'Invalid order id';
    }
}

function  autodesk_finalize_quote($p) {
    if (isset($p['quote_number'])) {
        $autodesk = new autodesk_api();
        $quote_result = $autodesk->quote->finalize(quote_number: $p['quote_number']);
        $response = ["status" => $quote_result['response'], "data" => $quote_result, "debug" => $autodesk->debugLog];
        print_rr($response);
        return 'Sent';
    } else {
        return 'Invalid order id';
    }
}