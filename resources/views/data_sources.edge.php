<?php
/**
 * Fallback Data Sources Router
 * This file redirects to the system views for data sources
 */

// Include the main system router
$system_router = __DIR__ . '/../system/views/data_sources.edge.php';

if (file_exists($system_router)) {
    include $system_router;
} else {
    // If system router doesn't exist, create a basic error message
    echo '<div class="p-4 bg-red-100 border border-red-400 text-red-700 rounded">';
    echo '<h3 class="font-bold">Error</h3>';
    echo '<p>Data sources system files not found. Please ensure the system views are properly installed.</p>';
    echo '<p>Looking for: ' . $system_router . '</p>';
    echo '</div>';
}
?>
