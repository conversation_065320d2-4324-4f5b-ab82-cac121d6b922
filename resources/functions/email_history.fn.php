<?php
use autodesk_api\autodesk_api;
use edge\Edge;


function generate_customer_history_feed($csn){
// In your controller

    $autodesk = new autodesk_api();
    $activity = [];
    $hist_columns = [
        'hist_id',
        'hist_customer_csn',
        'hist_subscription_id',
        'hist_subscription_reference_number',
        'hist_date',
        'hist_media',
        'hist_message',
        'users_name'
    ];
    $where_hist = ['hist.customer_csn' => ['=', $csn]];
    $activity = $autodesk->customers->get_history($csn,$hist_columns, ['where' => $where_hist]);

return Edge::render('component-activity-feed', ['activity' => $activity]);
}