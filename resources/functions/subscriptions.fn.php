<?php


use autodesk_api\autodesk_api;
use Edge\Edge;
use data_table\data_table;

function generate_subscription_table($criteria = [],$just_table = false, $just_body = false,$just_rows = false, $cols_hidden = []): string {
    $timing_start = microtime(true);
    $timing_points = [];

    print_rr($criteria, 'critty');

    $replacements = [
        'subs_offeringName' => [
            ' - including specialized toolsets' => '',
            'Architecture Engineering & Construction' => 'AEC',
            'Architecture Engineering Construction' => 'AEC',
            'Product Design & Manufacturing' => 'PDM',
            'Product Design Manufacturing' => 'PDM',
            'Product Design & MFG' => 'PDM'
        ]
    ];
    $table_structure = json_decode(autodesk_api::database_get_storage('subscription_table_config'),true);
    //autodesk_api::database_get_storage('subscription_replacements');

    $timing_points['table_config_load'] = microtime(true) - $timing_start;

    print_rr($table_structure, 'table_struct');
    $table_structure['columns']['actions'] = ['label' => 'Actions', 'field' => 'subs_id', 'content' => function ($item) {
        $remind = '';
        if (($item['subs_enddatediff'] <= 90) && ($item['subs_enddatediff'] >= -30)) {
            $remind = Edge::render(
                'forms-button',
                [
                    'type' => 'button',
                    'label' => 'Send Reminder',
                    'icon' => 'envelope',
                    'icon_position' => 'replace',
                    'variant' => 'circular',
                    'hx-post' => APP_ROOT . '/api/autodesk/subscriptions/SendEmail',
                    'hx-swap' => 'innerHTML',
                    'hx-target' => '#modal_body',
                    'hx-vals' => json_encode([
                        "subscription_number" => $item['subs_subscriptionReferenceNumber'],
                        "orders_id" => $item['orders_id'],
                        "subscription_id" => $item['subs_subscriptionId'],
                        "user_id" => $_SESSION['user_id']

                    ]),
                    '@click' => 'showModal = true',
                    'data_target' => '#modal_body',
                    'data_subscription_number' => $item['subs_subscriptionReferenceNumber']
                ]
            ) . Edge::render(
                'forms-button',
                [
                    'type' => 'button',
                    'label' => 'quote_autodesk',
                    'icon' => 'autodesk',
                    'icon_position' => 'replace',
                    'variant' => 'circular',
                    'hx-post' => APP_ROOT . '/api/autodesk/subscriptions/create_quote',
                    'hx-swap' => 'innerHTML',
                    'hx-target' => '#modal_body',
                    'hx-vals' => json_encode([
                        "sub_num" => $item['subs_subscriptionReferenceNumber'],
                        "subs_subscriptionId" => $item['subs_subscriptionId'],
                        "sub_id" => $item['subs_id'],
                        "user_id" => $_SESSION['user_id']
                    ]),
                    '@click' => 'showModal = true',
                    'data_target' => '#modal_body',
                    'data_subscription_number' => $item['subs_subscriptionReferenceNumber']
                ]
            );
        }
        return Edge::render('forms-button', [
                'type' => 'button',
                'label' => 'View',
                'icon' => 'book-open',
                'icon_position' => 'replace',
                'variant' => 'circular',
                'hx-post' => APP_ROOT . '/api/autodesk/subscriptions/view',
                'hx-swap' => 'innerHTML',
                'hx-target' => '#modal_body',
                'hx-vals' => json_encode([
                    "subscription_number" => $item['subs_subscriptionReferenceNumber'],
                    "orders_id" => $item['orders_id'],
                    "subscription_id" => $item['subs_subscriptionId'],
                ]),
                '@click' => 'showModal = true',
                'data_target' => '#modal_body',
                'data_subscription_number' => $item['subs_subscriptionReferenceNumber']
            ]) . $remind . Edge::render('forms-button', [
                'type' => 'button',
                'label' => 'flag',
                'icon' => 'flag',
                'icon_position' => 'replace',
                'variant' => 'circular',
                'hx-post' => APP_ROOT . '/api/autodesk/subscriptions/flag_modal',
                'hx-swap' => 'innerHTML',
                'hx-target' => '#modal_body',
                'hx-vals' => json_encode([
                    "target" => 'subscription',
                    "target_reference" => $item['subs_subscriptionId'] ?? $item['subs_subscriptionReferenceNumber'],
                    "customer_csn" => (string)$item['endcust_account_csn'],
                    "endcust_name" => $item['endcust_name'],
                    "email_address" => $item['endcust_primary_admin_email'],
                    "user_id" => $_SESSION['user_id']
                ],),
                '@click' => 'showModal = true',
                'data_target' => '#modal_body',
                'data_subscription_number' => $item['subs_subscriptionReferenceNumber']
            ]);
        }
    ];

    $timing_points['actions_column_setup'] = microtime(true) - $timing_start;

    print_rr(json_encode($table_structure), 'table_structcha');
    $default_criteria = [
        "order_by" => "subs_enddatediff"
    ];
    if (in_array('search', array_keys($criteria))) {
        $default_criteria["search_columns"] = [
            "subs.subscriptionReferenceNumber",
            "subs.offeringName",
            "subs.subscriptionid",
            "soldto.account_csn",
            "soldto.name",
            "solpro.account_csn",
            "solpro.name",
            "solpro.address1",
            "solpro.address2",
            "solpro.address3",
            "solpro.city",
            "solpro.state_province",
            "solpro.postal_code",
            "solpro.country",
            "solpro.state_province_code",
            "solpro.country_code",
            "resell.account_csn",
            "resell.name",
            "endcust.account_csn",
            "endcust.name",
            "endcust.address1",
            "endcust.address2",
            "endcust.address3",
            "endcust.city",
            "endcust.state_province",
            "endcust.postal_code",
            "endcust.country",
            "endcust.primary_admin_first_name",
            "endcust.primary_admin_last_name",
            "endcust.primary_admin_email",
            "endcust.team_id",
            "endcust.team_name",
            "endcust.first_name",
            "endcust.last_name",
            "endcust.email"
        ];
    }
    $criteria = array_merge($default_criteria, $criteria);

    $timing_points['criteria_setup'] = microtime(true) - $timing_start;

    $columns = filter_db_schema($table_structure);
    print_rr($columns, 'colsy');

    $timing_points['columns_filter'] = microtime(true) - $timing_start;

    $autodesk = new autodesk_api();

    $timing_points['autodesk_api_init'] = microtime(true) - $timing_start;

    // Handle pagination mode - get total count first
    $total_count = null;
    if (isset($criteria['pagination_mode']) && $criteria['pagination_mode']) {
        // Get total count without pagination and ordering
        $count_criteria = $criteria;
        unset($count_criteria['limit'], $count_criteria['offset'], $count_criteria['pagination_mode'], $count_criteria['order_by'], $count_criteria['order_direction']);

        // Get just the count by selecting a single column
        $count_data = $autodesk->subscriptions->get_all(['subs_id'], $count_criteria);
        $total_count = count($count_data);

        print_rr($total_count, 'total_count_for_pagination');
    }

    $data = $autodesk->subscriptions->get_all($columns, $criteria);

    $timing_points['data_fetch'] = microtime(true) - $timing_start;

    $filter_criteria = ['limit' => 100];
    foreach ($table_structure['columns'] as $key => $col) {
        if(!isset($col['auto_filter'])) continue;
        $table_structure['columns'][$key]['filter_data'] = $autodesk->subscriptions->get_distinct([$col['field']], $filter_criteria);
    }

    $timing_points['filter_data_fetch'] = microtime(true) - $timing_start;

   $timing_points['before_process_data_table'] = microtime(true) - $timing_start;

   $result = data_table::process_data_table(
       table_structure: $table_structure,
       data: $data,
       callback: __FUNCTION__,
       replacements: $replacements,
       criteria: $criteria,
       cols_hidden: $cols_hidden,
       just_body: $just_body,
       just_rows: $just_rows,
       just_table: $just_table,
       htmx_oob: false,
       total_count: $total_count,
       table_name: 'autodesk_subscriptions',
       available_fields: get_db_fields()
   );

   $timing_points['after_process_data_table'] = microtime(true) - $timing_start;
   $timing_points['total_execution'] = microtime(true) - $timing_start;

   // Log timing information
   error_log("=== generate_subscription_table TIMING ANALYSIS ===");
   foreach ($timing_points as $point => $time) {
       error_log(sprintf("%s: %.4f seconds", $point, $time));
   }
   error_log("=== END TIMING ANALYSIS ===");

   return $result;
}

/**
 * Generate unified subscription table for data from all sources
 */
function generate_unified_subscription_table($unified_data, $params = []): string {
    $timing_start = microtime(true);

    // Define table structure for unified data
    $table_structure = [
        'columns' => [
            'source_indicator' => [
                'label' => 'Source',
                'field' => 'source_indicator',
                'filter' => false,
                'content' => function($item) {
                    return $item['source_indicator'] ?? '';
                }
            ],
            'subs_subscriptionReferenceNumber' => [
                'label' => 'Reference #',
                'field' => 'subs_subscriptionReferenceNumber',
                'filter' => false
            ],
            'subs_offeringName' => [
                'label' => 'Product',
                'field' => 'subs_offeringName',
                'filter' => false
            ],
            'endcust_name' => [
                'label' => 'Company',
                'field' => 'endcust_name',
                'filter' => false
            ],
            'endcust_primary_admin_email' => [
                'label' => 'Email',
                'field' => 'endcust_primary_admin_email',
                'filter' => false
            ],
            'subs_startDate' => [
                'label' => 'Start Date',
                'field' => 'subs_startDate',
                'filter' => false,
                'content' => function($item) {
                    $date = $item['subs_startDate'] ?? '';
                    return !empty($date) ? date('M j, Y', strtotime($date)) : '';
                }
            ],
            'subs_endDate' => [
                'label' => 'End Date',
                'field' => 'subs_endDate',
                'filter' => false,
                'content' => function($item) {
                    $date = $item['subs_endDate'] ?? '';
                    if (empty($date)) return '';

                    $end_date = strtotime($date);
                    $now = time();
                    $diff_days = ($end_date - $now) / (60 * 60 * 24);

                    $formatted_date = date('M j, Y', $end_date);

                    if ($diff_days < 0) {
                        return '<span class="text-red-600 font-medium">' . $formatted_date . ' (Expired)</span>';
                    } elseif ($diff_days <= 30) {
                        return '<span class="text-orange-600 font-medium">' . $formatted_date . ' (Expires in ' . round($diff_days) . ' days)</span>';
                    } elseif ($diff_days <= 90) {
                        return '<span class="text-yellow-600 font-medium">' . $formatted_date . ' (Expires in ' . round($diff_days) . ' days)</span>';
                    } else {
                        return '<span class="text-gray-900">' . $formatted_date . '</span>';
                    }
                }
            ],
            'subs_quantity' => [
                'label' => 'Quantity',
                'field' => 'subs_quantity',
                'filter' => false
            ],
            'subs_status' => [
                'label' => 'Status',
                'field' => 'subs_status',
                'filter' => false,
                'content' => function($item) {
                    $status = $item['subs_status'] ?? '';
                    $class = 'bg-gray-100 text-gray-800';

                    switch (strtolower($status)) {
                        case 'active':
                            $class = 'bg-green-100 text-green-800';
                            break;
                        case 'expired':
                            $class = 'bg-red-100 text-red-800';
                            break;
                        case 'manual':
                            $class = 'bg-blue-100 text-blue-800';
                            break;
                        case 'csv import':
                            $class = 'bg-purple-100 text-purple-800';
                            break;
                    }

                    return '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ' . $class . '">' . htmlspecialchars($status) . '</span>';
                }
            ],
            'actions' => [
                'label' => 'Actions',
                'field' => 'subs_id',
                'filter' => false,
                'content' => function($item) {
                    $actions = '';
                    $data_source = $item['data_source'] ?? '';

                    // Different actions based on data source
                    if ($data_source === 'autodesk') {
                        // Autodesk API actions
                        if (($item['subs_enddatediff'] ?? 0) <= 90 && ($item['subs_enddatediff'] ?? 0) >= -30) {
                            $actions .= Edge::render('forms-button', [
                                'type' => 'button',
                                'label' => 'Send Reminder',
                                'icon' => 'envelope',
                                'icon_position' => 'replace',
                                'variant' => 'circular',
                                'class' => 'mr-2',
                                'hx-post' => APP_ROOT . '/api/autodesk/subscriptions/SendEmail',
                                'hx-swap' => 'innerHTML',
                                'hx-target' => '#modal_body',
                                'hx-vals' => json_encode([
                                    "subscription_number" => $item['subs_subscriptionReferenceNumber'],
                                    "subscription_id" => $item['subs_subscriptionId'] ?? '',
                                    "user_id" => $_SESSION['user_id']
                                ])
                            ]);
                        }
                    } elseif ($data_source === 'manual') {
                        // Manual entry actions
                        $actions .= '<button type="button" class="text-indigo-600 hover:text-indigo-900 text-sm font-medium mr-2">Edit</button>';
                        $actions .= '<button type="button" class="text-red-600 hover:text-red-900 text-sm font-medium">Delete</button>';
                    } elseif ($data_source === 'csv') {
                        // CSV entry actions
                        if ($item['can_add_to_unity'] ?? false) {
                            $actions .= '<button type="button" class="text-green-600 hover:text-green-900 text-sm font-medium">Add to Unity</button>';
                        }
                    }

                    return $actions;
                }
            ]
        ]
    ];

    // Apply string replacements for product names
    $replacements = [
        'subs_offeringName' => [
            ' - including specialized toolsets' => '',
            'Architecture Engineering & Construction' => 'AEC',
            'Architecture Engineering Construction' => 'AEC',
            'Product Design & Manufacturing' => 'PDM',
            'Product Design Manufacturing' => 'PDM',
            'Product Design & MFG' => 'PDM'
        ]
    ];

    // Process the data table
    $result = data_table::process_data_table(
        table_structure: $table_structure,
        data: $unified_data,
        callback: 'unified_subscriptions_table_filter',
        replacements: $replacements,
        criteria: $params,
        cols_hidden: [],
        just_body: $params['just_body'] ?? false,
        htmx_oob: false,
        total_count: count($unified_data)
    );

    $timing_end = microtime(true);
    error_log("generate_unified_subscription_table execution time: " . ($timing_end - $timing_start) . " seconds");

    return $result;
}

/**
 * Global callback function for unified subscriptions table filtering
 * This function bridges the gap between the data table system and the namespaced API function
 */
function unified_subscriptions_table_filter($criteria) {
    return \api\unified\unified_subscriptions_table_filter($criteria);
}


function get_db_fields()
{

    // Get database fields from the subscriptions table
    return [
        "subs.accessModel",
        "subs.autoRenew",
        "subs.billingBehavior",
        "subs.billingFrequency",
        "subs.connectivity",
        "subs.connectivityInterval",
        "subs.endCustomer_id",
        "subs.endDate",
        "subs.id",
        "subs.intendedUsage",
        "subs.last_email_history_id",
        "subs.nurtureReseller_id",
        "subs.offeringCode",
        "subs.offeringId",
        "subs.offeringName",
        "subs.opportunityNumber",
        "subs.paymentMethod",
        "subs.quantity",
        "subs.recordType",
        "subs.renewalCounter",
        "subs.servicePlan",
        "subs.soldTo_id",
        "subs.solutionProvider_id",
        "subs.startDate",
        "subs.status",
        "subs.subscriptionId",
        "subs.subscriptionReferenceNumber",
        "subs.tcs_unsubscribe",
        "subs.term",
        "subs_enddatediff",
        "subs_created_at",
        "subs_last_modified",
        "endcust.account_csn",
        "endcust.account_type",
        "endcust.address1",
        "endcust.address2",
        "endcust.address3",
        "endcust.city",
        "endcust.country",
        "endcust.country_code",
        "endcust.do_not_call",
        "endcust.do_not_email",
        "endcust.do_not_mail",
        "endcust.email",
        "endcust.first_name",
        "endcust.id",
        "endcust.individual_flag",
        "endcust.last_name",
        "endcust.local_language_name",
        "endcust.lockdate",
        "endcust.name",
        "endcust.named_account_flag",
        "endcust.named_account_group",
        "endcust.nurture_discount_eligibility",
        "endcust.parent_industry_group",
        "endcust.parent_industry_segment",
        "endcust.phone",
        "endcust.portal_registration",
        "endcust.postal_code",
        "endcust.primary_admin_email",
        "endcust.primary_admin_first_name",
        "endcust.primary_admin_last_name",
        "endcust.state_province",
        "endcust.state_province_code",
        "endcust.status",
        "endcust.team_id",
        "endcust.team_name",
        "hist.customer_id",
        "hist.date_sent",
        "hist.email_address",
        "hist.id",
        "hist.result",
        "hist.subscription_id",
        "hist.triggered_by",
        "resell.account_csn",
        "resell.account_type",
        "resell.address1",
        "resell.address2",
        "resell.address3",
        "resell.city",
        "resell.country",
        "resell.country_code",
        "resell.do_not_call",
        "resell.do_not_email",
        "resell.do_not_mail",
        "resell.email",
        "resell.first_name",
        "resell.id",
        "resell.individual_flag",
        "resell.last_name",
        "resell.local_language_name",
        "resell.lockdate",
        "resell.name",
        "resell.named_account_flag",
        "resell.named_account_group",
        "resell.nurture_discount_eligibility",
        "resell.parent_industry_group",
        "resell.parent_industry_segment",
        "resell.phone",
        "resell.portal_registration",
        "resell.postal_code",
        "resell.primary_admin_email",
        "resell.primary_admin_first_name",
        "resell.primary_admin_last_name",
        "resell.state_province",
        "resell.state_province_code",
        "resell.status",
        "resell.team_id",
        "resell.team_name",
        "soldto.account_csn",
        "soldto.account_type",
        "soldto.address1",
        "soldto.address2",
        "soldto.address3",
        "soldto.city",
        "soldto.country",
        "soldto.country_code",
        "soldto.do_not_call",
        "soldto.do_not_email",
        "soldto.do_not_mail",
        "soldto.email",
        "soldto.first_name",
        "soldto.id",
        "soldto.individual_flag",
        "soldto.last_name",
        "soldto.local_language_name",
        "soldto.lockdate",
        "soldto.name",
        "soldto.named_account_flag",
        "soldto.named_account_group",
        "soldto.nurture_discount_eligibility",
        "soldto.parent_industry_group",
        "soldto.parent_industry_segment",
        "soldto.phone",
        "soldto.portal_registration",
        "soldto.postal_code",
        "soldto.primary_admin_email",
        "soldto.primary_admin_first_name",
        "soldto.primary_admin_last_name",
        "soldto.state_province",
        "soldto.state_province_code",
        "soldto.status",
        "soldto.team_id",
        "soldto.team_name",
        "solpro.account_csn",
        "solpro.account_type",
        "solpro.address1",
        "solpro.address2",
        "solpro.address3",
        "solpro.city",
        "solpro.country",
        "solpro.country_code",
        "solpro.do_not_call",
        "solpro.do_not_email",
        "solpro.do_not_mail",
        "solpro.email",
        "solpro.first_name",
        "solpro.id",
        "solpro.individual_flag",
        "solpro.last_name",
        "solpro.local_language_name",
        "solpro.lockdate",
        "solpro.name",
        "solpro.named_account_flag",
        "solpro.named_account_group",
        "solpro.nurture_discount_eligibility",
        "solpro.parent_industry_group",
        "solpro.parent_industry_segment",
        "solpro.phone",
        "solpro.portal_registration",
        "solpro.postal_code",
        "solpro.primary_admin_email",
        "solpro.primary_admin_first_name",
        "solpro.primary_admin_last_name",
        "solpro.state_province",
        "solpro.state_province_code",
        "solpro.status",
        "solpro.team_id",
        "solpro.team_name",
    ];
}