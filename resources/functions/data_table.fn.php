<?php

namespace defunct;

use autodesk_api\autodesk_api;
use edge\Edge;

class data_table {
    public static array $table_structure;
    public static array $data;
    public static array $replacements;
    public static array $criteria;
    public static array $cols_hidden;
    public static bool $just_body;
    public static bool $just_row;
    public static bool $htmx_oob;

    public static function process_data_table(
        array $table_structure,
        array $data,
        array $replacements,
        array $criteria = [],
        array $cols_hidden = [],
        bool $just_body = false,
        bool $just_row = false,
        $htmx_oob = false
    ): false|string {
        self::$table_structure = $table_structure;
        self::$data = $data;
        self::$replacements = $replacements;
        self::$criteria = $criteria;
        self::$cols_hidden = $cols_hidden;
        self::$just_body = $just_body;
        self::$just_row = $just_row;
        self::$htmx_oob = $htmx_oob;


        foreach ($cols_hidden as $col) {
            unset($row_content[$col]);
        }

        if ($htmx_oob) $row = true;

        // build_column_filters

        self::process_column_filters();
        self::process_string_replacements();

        $htmx_oob_out = "";
        if ($htmx_oob) {
            $htmx_oob_out = [
                "hx-swap-oob" => "true",
                "hx-ext" => "class-tools",
                "hx-classes" => "add htmx-settling, remove:htmx-settling:10s"
            ];
        }
        $data = [
            "title" => "subscriptions",
            "description" => "",
            "items" => $subscriptions,
            "columns" => self::build_column_filters(),
            "rows" => [
                'id' => 'subscription_id_' . $items['id'],
                'extra_parameters' => $htmx_oob_out,
            ],
            "just_body" => $just_body,
            "just_rows" => $just_row,
            "sort_column" => 'name',
            "sort_direction" => 'desc'
        ];

        if ($just_body) return Edge::render('data-table', $data);
        return $data;
    }

    public static function filter_db_schema(): array {
        $db = [];
        foreach (self::$table_structure['columns'] as $column) {
            if (is_array($column['field'])) {
                foreach ($column['field'] as $key => $sub_field) $db[] = $sub_field;
                continue;
            }
            $db[] = $column['field'];
        }
        return $db;
    }

    public static function build_column_filters(): array {
        $columns = self::process_auto_filters(self::$table_structure['columns']);
        $dsucpunt = 0;
        foreach ($columns as $key => $col) {
            $count = 1;
            $dsucpunt++;
            $col_field = str_replace('_', '.', $col['field'], $count);
            if (is_string($col_field)) $columns[$key]['selected'] = $criteria['where'][$col_field][1] ?? $col['label'];
            //$columns[$key]['selected'] = $criteria['where'][$col_field][1] ?? $col['label'];
        }
        print_rr($columns, 'build column filters end');
        return $columns;
    }

    public static function process_auto_filters(): array {
        $columns = self::$table_structure['columns'];
        foreach ($columns as $key => $col) {
            $filter_criteria = [
                "order_by" => $col['field'],
                'limit' => $col['filter_limit'] ?? 10
            ];
            if (isset($col['auto_filter']) && is_array($col['filter_data']) && count($col['filter_data']) > 0) {
                $filter_assoc = array_combine($col['filter_data'], $col['filter_data']);
                $columns[$key]['filters'] = $filter_assoc;
                unset($columns[$key]['filter_data']);
            }
        }
        return $columns;
    }


    public static function process_string_replacements(): void {
        foreach (self::$data as &$item) {
            foreach (self::$replacements as $field => $replacements) {
                if (is_string($item[$field])) {
                    $item[$field] = strtr($item[$field], $replacements);
                }
            }
            //$item[$field] = strtr($item[$field], $replacements);
        }
        unset($item);
    }

    public static function process_column_filters() {
        foreach (self::$table_structure['columns'] as $key => $col) {
            $filter_criteria = [
                "order_by" => $col['field'],
                'limit' => $col['filter_limit'] ?? 10
            ];

            if (isset($col['string_replacements'])) {
                self::process_string_replacements(self::$data, $col['string_replacements']);
            }

            $filter_criteria = array_merge($criteria, $filter_criteria);

            if (isset($col['auto_filter'])) {
                self::$table_structure['columns'][$key]['filter_data'] = $autodesk->subscriptions->get_distinct([$col['field']], $filter_criteria);
                if (isset($col['string_replacements'])) {
                    foreach (self::$table_structure['columns'][$key]['filter_data'] as &$item) {
                        if (is_string($item)) {
                            $item = strtr($item, $col['string_replacements']);
                        }
                    }
                }
            }
        }
    }
}
