<?php
/**
 * Get navigation items from the database or fallback to the config file
 * 
 * @return array Navigation items
 */
function get_navigation() {
    // Try to get navigation from the database
    $db_navigation = api\system\get_navigation_items();
    
    // If we have database navigation items, use those
    if (!empty($db_navigation)) {
        return $db_navigation;
    }
    
    // Otherwise, fall back to the config file
    return NAV_ITEMS;
}