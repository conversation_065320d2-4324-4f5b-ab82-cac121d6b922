// NewEndCustomer.json
[
  {
    "field": "endCustomer.accountCsn",
    "description": "The accountCsn alone or all account details are required. Only the accountCsn should be passed if a Renewal, Switch, Extension, or True-up line-item action is present on the quote.",
    "required": false,
    "type": "string"
  },
  {
    "field": "endCustomer.isIndividual",
    "description": "Determines whether the end customer is an individual (true) or a company (false).",
    "required": false,
    "type": "boolean"
  },
  {
    "field": "endCustomer.name",
    "description": "Required if isIndividual is false. Shouldn't be present when isIndividual is true.",
    "required": false,
    "type": "string"
  },
  {
    "field": "endCustomer.addressLine1",
    "description": "The street address of the account.",
    "required": true,
    "type": "string"
  },
  {
    "field": "endCustomer.addressLine2",
    "description": "The suite or secondary address information of the account.",
    "required": false,
    "type": "string"
  },
  {
    "field": "endCustomer.city",
    "description": "The city of the account.",
    "required": true,
    "type": "string"
  },
  {
    "field": "endCustomer.stateProvinceCode",
    "description": "The two-digit code for the state or province of the account. Required for certain countries. Please refer to Appendix A: End Customer Account State & Postal Code Requirements.",
    "required": false,
    "type": "string"
  },
  {
    "field": "endCustomer.postalCode",
    "description": "The postal code of the account. Required for certain countries. Please refer to Appendix A: End Customer Account State & Postal Code Requirements.",
    "required": false,
    "type": "string"
  },
  {
    "field": "endCustomer.countryCode",
    "description": "The standard country code for an account.",
    "required": true,
    "type": "string"
  }
]



