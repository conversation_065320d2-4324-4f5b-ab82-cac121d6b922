// ExtensionLineItem.json
[
  {
    "field": "lineItems[i].action",
    "description": "Action to perform. - Extension",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].agentLineReference",
    "description": "External line reference identifier, if applicable. Can be up to 37 characters and must start and end with a letter or digit. All characters must be a letter, digit, or hyphen.",
    "required": false,
    "type": "string"
  },
  {
    "field": "lineItems[i].subscriptionId",
    "description": "Subscription ID of subscription to be extended.",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].endDate",
    "description": "End date for the subscription extension. The subscription being extended will have its end date changed to this end date. Validations are performed based on the end customer’s time zone. Please refer to the Policy Guide for information about how far in the future a subscription can be extended.",
    "required": true,
    "type": "string"
  }
]
