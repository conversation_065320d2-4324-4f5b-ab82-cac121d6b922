// GeneralRequest.json
[
  {
    "field": "quoteNote",
    "description": "Optional free-text note field. Does not have any functionality currently. This field can be used as a workaround for sending the same quote payload.",
    "required": false,
    "type": "string"
  },
  {
    "field": "currency",
    "description": "Currency for the quote. Please refer to the policy guide for supported currencies. If quoting from an opportunity that contains value-based line items, the currency must match the currency specified on the opportunity.",
    "required": true,
    "type": "string"
  },
  {
    "field": "quoteLanguage",
    "description": "Language for the quote. Please refer to Appendix D: Quote Language Logic for more information.",
    "required": false,
    "type": "string"
  },
  {
    "field": "quoteNotes",
    "description": "Optional free text field. Quote notes that are specified on a quote will be visible to the customer on the quote PDF when quoted. Cannot be blank (“”). If you wish to not include quote notes on the quote, do not include the field in the quote request or provide a null value.",
    "required": false,
    "type": "string"
  },
  {
    "field": "opportunityNumber",
    "description": "Opportunity number to be renewed. Used for “As-Is Renewals” where no line items need to be specified, True-Ups, and DDAs.",
    "required": false,
    "type": "string"
  },
  {
    "field": "agentAccount",
    "description": "Agent creating the quote.",
    "required": true,
    "type": "object"
  },
  {
    "field": "agentContact",
    "description": "Agent’s contact information.",
    "required": true,
    "type": "object"
  },
  {
    "field": "endCustomer",
    "description": "Account information used to create a quote for the correct individual or organization. If line-item actions consist of Renewal, Switch, Extension, or True-up, this account must match the end customer account of the subscription being modified. If creating a quote from an opportunity (As Is), the end customer account must match the end customer account on the opportunity. If the subscriptions have different end customers, the line-item actions must be split into separate quotes.",
    "required": true,
    "type": "object"
  },
  {
    "field": "quoteContact",
    "description": "Quote contact for the current quote. Autodesk may be missing contact details for an existing quote contact. As a result, quote creation will fail, and the Partner will need to resend the quote request with all quote contact details on the quote.",
    "required": true,
    "type": "object"
  },
  {
    "field": "admin",
    "description": "Primary admin for products being purchased. Only applicable to new subscriptions. Renewals for existing subscriptions will not have primary admin modifications. The quote contact, by default, will be the primary admin for new subscriptions if the admin is not specified.",
    "required": false,
    "type": "object"
  },
  {
    "field": "additionalRecipients",
    "description": "Additional recipients that should receive the quote. If included, must contain at least one recipient, and cannot exceed four recipients.",
    "required": false,
    "type": "array"
  },
  {
    "field": "skipDDACheck",
    "description": "Indicate if end customer DDA check should be skipped. If not provided, the value will be “false” by default. Please refer to Deal Discount Approvals Validations for more details.",
    "required": false,
    "type": "boolean"
  },
  {
    "field": "agentQuoteReference",
    "description": "External quote reference identifier, if applicable. Can be up to 63 characters and must start and end with a letter or digit. All characters must be a letter, digit, or hyphen.",
    "required": false,
    "type": "string"
  },
  {
    "field": "skipM2SDiscountValidation",
    "description": "Indicate if the M2S discount eligibility check should be skipped when creating, updating, or finalizing the quote. This flag should be set to true if extending subscription(s) on the quote with M2S pricing past the M2S program end date. Will be false if not included.",
    "required": false,
    "type": "boolean"
  },
  {
    "field": "lineItems",
    "description": "Line items information. Only optional for As-Is Renewals or DDAs.",
    "required": true,
    "type": "array"
  }
]
