// CoTermLineItemDDAModification.json
[
  {
    "field": "lineItems[i].action",
    "description": "Action to perform. - Co-term",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].referenceSubscriptionId",
    "description": "Subscription ID of subscription to be co-termed with. The subscription can belong to the same account CSN specified as the end customer on the quote, or a different account CSN if the customer has multiple accounts.",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].quantity",
    "description": "New number of units. • Flex: Must be greater than or equal to 100 (>= 100). • SUS: Must be greater than 0 (> 0).",
    "required": true,
    "type": "integer"
  },
  {
    "field": "lineItems[i].startDate",
    "description": "Date when the subscription is intended to start. This date can include today. If startDate is populated: • If the quote is ordered before the startDate, the customer will be able to start using the subscription only from the specified start date. If startDate is not populated: • The quote must be ordered before the calculated quote expiration date. • If the quote is ordered before the quote expiration date, the customer can start using the subscription instantly without having to wait for a future start date. Cannot be more than 30 days in the future. Validations are performed based on the end customer’s time zone.",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].opportunityLineItemId",
    "description": "The unique opportunity line-item id to be referenced. This field is used when modifying line items on a DDA opportunity, which was provided in the quote request.",
    "required": true,
    "type": "string"
  }
]
