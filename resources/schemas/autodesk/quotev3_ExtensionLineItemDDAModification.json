// ExtensionLineItemDDAModification.json
[
  {
    "field": "lineItems[i].action",
    "description": "Action to perform. - Extension",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].subscriptionId",
    "description": "Subscription ID of subscription to be extended.",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].endDate",
    "description": "End date for the subscription extension. The subscription being extended will have its end date changed to this end date. Validations are performed based on the end customer’s time zone. Please refer to the Policy Guide for information about how far in the future a subscription can be extended.",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].opportunityLineItemId",
    "description": "The unique opportunity line-item id to be referenced. This field is used when modifying line items on a DDA opportunity, which was provided in the quote request.",
    "required": true,
    "type": "string"
  }
]
