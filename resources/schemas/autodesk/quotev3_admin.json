// Admin.json
[
  {
    "field": "admin.email",
    "description": "<PERSON><PERSON>’s email address. The email alone (for an existing admin) or all admin details (including email) are required. If the endCustomer account has “isIndividual” set to true, all admin details are required regardless of if the contact already exists.",
    "required": true,
    "type": "string"
  },
  {
    "field": "admin.firstName",
    "description": "<PERSON><PERSON>’s first name.",
    "required": true,
    "type": "string"
  },
  {
    "field": "admin.lastName",
    "description": "<PERSON><PERSON>’s last name.",
    "required": true,
    "type": "string"
  },
  {
    "field": "admin.phone",
    "description": "<PERSON><PERSON>’s phone number.",
    "required": true,
    "type": "string"
  },
  {
    "field": "admin.preferredLanguage",
    "description": "Admin’s preferred language. Please refer to Appendix B: Supported Language Codes.",
    "required": true,
    "type": "string"
  }
]
