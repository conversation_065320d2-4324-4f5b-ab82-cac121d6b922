// SwitchProductLineItem.json
[
  {
    "field": "lineItems[i].offeringId",
    "description": "Offering id. If you are performing a many-to-one (M:1) switch at renewal using the “lineItems[i].referenceSubscriptions” field, this ID must correspond to an ACS product.",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].offeringName",
    "description": "Offering name. If you are performing a many-to-one (M:1) switch at renewal using the “lineItems[i].referenceSubscriptions” field, this ID must correspond to an ACS product.",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].offeringCode",
    "description": "Offering code. If you are performing a many-to-one (M:1) switch at renewal using the “lineItems[i].referenceSubscriptions” field, this ID must correspond to an ACS product.",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].action",
    "description": "Action to perform. - Switch",
    "required": true,
    "type": "string"
  },
  {
    "field": "lineItems[i].agentLineReference",
    "description": "External line reference identifier, if applicable. Can be up to 37 characters and must start and end with a letter or digit. All characters must be a letter, digit, or hyphen.",
    "required": false,
    "type": "string"
  },
  {
    "field": "lineItems[i].quantity",
    "description": "Number of units. Must be greater than 0 (> 0).",
    "required": true,
    "type": "integer"
  },
  {
    "field": "lineItems[i].referenceSubscriptionId",
    "description": "Subscription ID of subscription to be switched. The subscription must be in the renewal window. You must include either referenceSubscriptionId or referenceSubscriptions in your request. Do not include both. Please refer to Appendix C: Same Subscription on Multiple Line Items for more information if multiple line items on the quote reference the same subscription. The subscription will be renewed with the specified product.",
    "required": false,
    "type": "string"
  },
  {
    "field": "lineItems[i].referenceSubscriptions",
    "description": "Array of subscriptions to be switched. All subscriptions must be in the renewal window. The same subscription should not be included more than once in the array. You must include either referenceSubscriptionId or referenceSubscriptions in your request. Do not include both. Please refer to Appendix C: Same Subscription on Multiple Line Items for more information if multiple line items on the quote reference the same subscription. The subscriptions will be renewed with the specified product.",
    "required": false,
    "type": "array",
    "items": {
      "type": "object",
      "properties": {
        "id": {
          "field": "lineItems[i].referenceSubscriptions[j].id",
          "description": "ID of the subscription to be switched. Must be an ACS subscription.",
          "required": true,
          "type": "string"
        },
        "quantity": {
          "field": "lineItems[i].referenceSubscriptions[j].quantity",
          "description": "Quantity of the subscription to be switched. Must match the quantity of the subscription.",
          "required": true,
          "type": "integer"
        }
      }
    }
  },
  {
    "field": "lineItems[i].promotionCode",
    "description": "Manually apply a promotion code to the line item. This promotion code overrides any automatically applied promotion codes. If the promotion code end date is earlier than the quote expiration date, the promotion code end date overrides the quote expiration date. The quote will expire when the promotion code expires.",
    "required": false,
    "type": "string"
  },
  {
    "field": "lineItems[i].offer",
    "description": "Offer information.",
    "required": true,
    "type": "object",
    "properties": {
      "term": {
        "field": "lineItems[i].offer.term",
        "description": "The term of the offer (e.g., Annual, 3 Year).",
        "required": true,
        "type": "object",
        "properties": {
          "code": {
            "field": "lineItems[i].offer.term.code",
            "description": "Term code. - A01 - A06",
            "required": true,
            "type": "string"
          },
          "description": {
            "field": "lineItems[i].offer.term.description",
            "description": "Term description. - Annual - 3 year",
            "required": true,
            "type": "string"
          }
        }
      },
      "accessModel": {
        "field": "lineItems[i].offer.accessModel",
        "description": "The access model of the offer. How access is provided to the subscription (e.g., Single User, Flex).",
        "required": true,
        "type": "object",
        "properties": {
          "code": {
            "field": "lineItems[i].offer.accessModel.code",
            "description": "Access model code. - S - F",
            "required": true,
            "type": "string"
          },
          "description": {
            "field": "lineItems[i].offer.accessModel.description",
            "description": "Access model description. - Single User - Flex",
            "required": true,
            "type": "string"
          }
        }
      },
      "intendedUsage": {
        "field": "lineItems[i].offer.intendedUsage",
        "description": "The usage type of the offer. Defines if the offer is for intended commercial or other use (e.g., COM, NFR).",
        "required": true,
        "type": "object",
        "properties": {
          "code": {
            "field": "lineItems[i].offer.intendedUsage.code",
            "description": "Usage type code. - COM - NFR",
            "required": true,
            "type": "string"
          },
          "description": {
            "field": "lineItems[i].offer.intendedUsage.description",
            "description": "Usage type description. - Commercial - Not for Resale",
            "required": true,
            "type": "string"
          }
        }
      },
      "connectivity": {
        "field": "lineItems[i].offer.connectivity",
        "description": "The connectivity of the offer. Whether the customer needs to be connected to the cloud to run the software (e.g., Online, Offline).",
        "required": true,
        "type": "object",
        "properties": {
          "code": {
            "field": "lineItems[i].offer.connectivity.code",
            "description": "Connectivity code. - C100",
            "required": true,
            "type": "string"
          },
          "description": {
            "field": "lineItems[i].offer.connectivity.description",
            "description": "Connectivity description. - Online",
            "required": true,
            "type": "string"
          }
        }
      },
      "servicePlan": {
        "field": "lineItems[i].offer.servicePlan",
        "description": "The service plan of the offer (e.g., Standard, Premium).",
        "required": true,
        "type": "object",
        "properties": {
          "code": {
            "field": "lineItems[i].offer.servicePlan.code",
            "description": "Service plan code. - STND - STNDNS - PREMSUB - PREMNS",
            "required": true,
            "type": "string"
          },
          "description": {
            "field": "lineItems[i].offer.servicePlan.description",
            "description": "Service plan description. - Standard - Standard No Support - Premium - Premium No Support",
            "required": true,
            "type": "string"
          }
        }
      }
    }
  }
]
`