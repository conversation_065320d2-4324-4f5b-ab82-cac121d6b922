// Listen for HTMX events
document.addEventListener('DOMContentLoaded', function() {
    // Listen for the htmx:responseError event
    document.body.addEventListener('htmx:responseError', function(evt) {
        // Create a notification for the error
        const detail = {
            type: 'error',
            message: 'Error: ' + (evt.detail.error || 'An unknown error occurred')
        };
        
        // Dispatch a custom event to show the notification
        window.dispatchEvent(new CustomEvent('show-notification', { detail }));
    });
    
    // Listen for the htmx:beforeSwap event to handle custom notifications
    document.body.addEventListener('htmx:beforeSwap', function(evt) {
        // Check if there's a notification trigger in the headers
        const notificationHeader = evt.detail.xhr.getResponseHeader('HX-Trigger');
        if (notificationHeader) {
            try {
                const triggers = JSON.parse(notificationHeader);
                if (triggers.showNotification) {
                    // Dispatch a custom event to show the notification
                    window.dispatchEvent(new CustomEvent('show-notification', {
                        detail: triggers.showNotification
                    }));
                }

                if (triggers.redirectAfterDelay) {
                    // <PERSON>le delayed redirect
                    setTimeout(() => {
                        window.location.href = triggers.redirectAfterDelay.url;
                    }, triggers.redirectAfterDelay.delay || 2000);
                }
            } catch (e) {
                console.error('Error parsing notification header:', e);
            }
        }
    });
});
