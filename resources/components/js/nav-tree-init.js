// Navigation tree initialization function
var APP_ROOT = APP_ROOT || '/';

function initNavTree() {
    return {
        currentRoute: localStorage.getItem('currentNavRoute') || window.location.pathname.replace(APP_ROOT, '').replace(/^\/+|\/+$/g, ''),
        init() {
            // Set initial route based on current URL
            if (!localStorage.getItem('currentNavRoute')) {
                localStorage.setItem('currentNavRoute', this.currentRoute);
            }

            // Listen for HTMX after-request events
            document.body.addEventListener('htmx:afterOnLoad', (event) => {
                // Don't update if this is a background request (not navigation)
                if (event.detail.target.id !== 'content_wrapper') return;

                // Extract the path from the URL
                const url = new URL(event.detail.xhr.responseURL);
                const path = url.pathname.replace(APP_ROOT, '');
                const cleanPath = path.replace(/^\/+|\/+$/g, '');

                // Update the current route
                this.currentRoute = cleanPath;
                localStorage.setItem('currentNavRoute', cleanPath);

                // Dispatch a custom event to synchronize all nav-tree instances
                window.dispatchEvent(new CustomEvent('nav-route-changed', { detail: { route: cleanPath } }));
            });

            // Listen for route change events from other nav-tree instances
            window.addEventListener('nav-route-changed', (event) => {
                this.currentRoute = event.detail.route;
            });
        }
    };
}
