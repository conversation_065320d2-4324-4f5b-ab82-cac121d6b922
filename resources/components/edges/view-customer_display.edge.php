@props([
    'title' => 'Customer Details',
    'description' => '',
    'customer' => [],
    'matched_data' => [],
    'histdata' => [],
    'subscriptions' => [],
    'class' => ''
])

@php
    $headerMetadata = [
        [
            'label' => 'CSN',
            'value' => $customer['endcust_account_csn'] ?? 'N/A'
        ],
        [
            'label' => 'Type',
            'value' => $customer['endcust_account_type'] ?? 'Unknown',
            'class' => 'px-2 py-1 text-xs font-medium rounded-full ' .
                      (($customer['endcust_account_type'] ?? '') === 'COMMERCIAL' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800')
        ]
    ];

    if (!empty($customer['endcust_team_name'])) {
        $headerMetadata[] = [
            'label' => 'Team',
            'value' => $customer['endcust_team_name']
        ];
    }
@endphp

<!-- Header Section -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <div class="bg-white shadow-sm rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div class="flex-1 min-w-0">
                    <h1 class="text-2xl font-bold text-gray-900 truncate">
                        {{ $customer['endcust_name'] ?? 'Customer Details' }}
                    </h1>
                    @if(!empty($headerMetadata))
                        <div class="mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:space-x-6">
                            @foreach($headerMetadata as $item)
                                <div class="mt-2 flex items-center text-sm text-gray-500">
                                    @if(isset($item['label']))
                                        <span class="font-medium">{{ $item['label'] }}:</span>
                                    @endif
                                    <span class="ml-1 {{ $item['class'] ?? '' }}">{{ $item['value'] ?? $item }}</span>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>


                <div class="mt-4 sm:mt-0 sm:ml-4 flex space-x-2">
                    <button type="button"
                            class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            hx-post="{{ APP_ROOT }}/api/customers/SendEmail"
                            hx-swap="innerHTML"
                            hx-target="#modal_body"
                            data-tab-title="Send Email"
                            hx-vals='{{ json_encode([
                                "customer_csn" => $customer["endcust_account_csn"] ?? "",
                                "customer_id" => $customer["endcust_id"] ?? "",
                                "user_id" => $_SESSION["user_id"] ?? ""
                            ]) }}'
                            @click="showModal = true">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        Send Email
                    </button>

                    <button type="button"
                            class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            hx-post="{{ APP_ROOT }}/api/customers/flag_modal"
                            hx-swap="innerHTML"
                            hx-target="#modal_body"
                            data-tab-title="Add Note"
                            hx-vals='{{ json_encode([
                                "target" => "customer",
                                "target_reference" => $customer["endcust_account_csn"] ?? "",
                                "customer_csn" => $customer["endcust_account_csn"] ?? "",
                                "endcust_name" => $customer["endcust_name"] ?? "",
                                "email_address" => $customer["endcust_primary_admin_email"] ?? "",
                                "user_id" => $_SESSION["user_id"] ?? ""
                            ]) }}'
                            @click="showModal = true">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21a2 2 0 012 2v11a2 2 0 01-2 2H3z"></path>
                        </svg>
                        Add Note
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Left Column - Primary Information -->
    <div class="lg:col-span-2 space-y-6">
        <!-- Customer Details Card -->
        @php
            $contactItems = [
                [
                    'label' => 'Company Name',
                    'value' => $customer['endcust_name'] ?? 'N/A'
                ],
                [
                    'label' => 'Primary Admin',
                    'value' => trim(($customer['endcust_primary_admin_first_name'] ?? '') . ' ' . ($customer['endcust_primary_admin_last_name'] ?? '')) ?: 'N/A'
                ],
                [
                    'label' => 'Email',
                    'value' => $customer['endcust_primary_admin_email'] ?? 'N/A',
                    'link' => !empty($customer['endcust_primary_admin_email']) ? 'mailto:' . $customer['endcust_primary_admin_email'] : null
                ],
                [
                    'label' => 'Account CSN',
                    'value' => $customer['endcust_account_csn'] ?? 'N/A',
                    'valueClass' => 'mt-1 text-sm text-gray-900 font-mono'
                ]
            ];

            $addressFields = [
                'address1' => 'endcust_address1',
                'address2' => 'endcust_address2',
                'address3' => 'endcust_address3',
                'city' => 'endcust_city',
                'state' => 'endcust_state_province',
                'postal' => 'endcust_postal_code',
                'country' => 'endcust_country'
            ];
        @endphp

        <x-component-card title="Customer Information">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Primary Contact Details -->
                <div>
                    <h3 class="text-sm font-medium text-gray-900 mb-3">Primary Contact</h3>
                    <x-component-data-list :items="$contactItems" />
                </div>

                <!-- Address Information -->
                <div>
                    <h3 class="text-sm font-medium text-gray-900 mb-3">Address</h3>
                    <x-component-address
                        :address="$customer"
                        :fields="$addressFields"
                    />
                </div>
            </div>
        </x-component-card>

        <!-- Subscriptions Card -->
        @php
            $autodesk_count = count(array_filter($subscriptions, fn($s) => ($s['data_source'] ?? 'autodesk') === 'autodesk'));
            $csv_count = count(array_filter($subscriptions, fn($s) => ($s['data_source'] ?? 'autodesk') === 'csv_table'));
            $total_count = count($subscriptions);

            $subscription_title = "Subscriptions ({$total_count})";
            if ($autodesk_count > 0 && $csv_count > 0) {
                $subscription_title .= " • {$autodesk_count} Autodesk, {$csv_count} CSV";
            } elseif ($csv_count > 0) {
                $subscription_title .= " • {$csv_count} from CSV data";
            } elseif ($autodesk_count > 0) {
                $subscription_title .= " • {$autodesk_count} from Autodesk";
            }
        @endphp

        <x-component-card :title="$subscription_title">
            @if(!empty($subscriptions))
                <x-component-subscription-list :subscriptions="$subscriptions" />
            @else
                <div class="text-center py-6">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No subscriptions found</h3>
                    <p class="mt-1 text-sm text-gray-500">No subscriptions found in Autodesk data or uploaded CSV files.</p>
                </div>
            @endif
        </x-component-card>

        <!-- Additional Details (Collapsible) -->
        @php
            $additionalDetails = [
                [
                    'label' => 'Account Type',
                    'value' => $customer['endcust_account_type'] ?? 'N/A'
                ],
                [
                    'label' => 'Individual Flag',
                    'value' => $customer['endcust_individual_flag'] ?? 'N/A'
                ],
                [
                    'label' => 'Named Account',
                    'value' => $customer['endcust_named_account_flag'] ?? 'N/A'
                ],
                [
                    'label' => 'Team ID',
                    'value' => $customer['endcust_team_id'] ?? 'N/A'
                ],
                [
                    'label' => 'Team Name',
                    'value' => $customer['endcust_team_name'] ?? 'N/A'
                ],
                [
                    'label' => 'Industry Group',
                    'value' => $customer['endcust_parent_industry_group'] ?? 'N/A'
                ]
            ];
        @endphp

        <div class="bg-white shadow-sm rounded-lg" x-data="{ expanded: false }">
            <div class="px-6 py-4 border-b border-gray-200">
                <button @click="expanded = !expanded"
                        class="flex items-center justify-between w-full text-left">
                    <h2 class="text-lg font-medium text-gray-900">Additional Details</h2>
                    <svg :class="{'rotate-180': expanded}"
                         class="w-5 h-5 text-gray-400 transform transition-transform duration-200"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
            </div>
            <div x-show="expanded" x-transition class="px-6 py-4">
                <x-component-data-list :items="$additionalDetails" :columns="3" />
            </div>
        </div>
    </div>

    <!-- Right Column - Actions and Timeline -->
    <div class="space-y-6">
        <!-- Quick Actions Card -->
        <x-component-card title="Quick Actions">
            <div class="space-y-3">
                <button type="button"
                        class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        hx-post="{{ APP_ROOT }}/api/quotes/create_quote_modal"
                        hx-swap="innerHTML"
                        hx-target="#modal_body"
                        data-tab-title="Create Quote"
                        hx-vals='{{ json_encode([
                            "customer_csn" => $customer["endcust_account_csn"] ?? "",
                            "customer_name" => $customer["endcust_name"] ?? "",
                            "customer_email" => $customer["endcust_primary_admin_email"] ?? "",
                            "user_id" => $_SESSION["user_id"] ?? ""
                        ]) }}'
                        @click="showModal = true">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Create Quote
                </button>

                <button type="button"
                        class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        onclick="window.print()">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                    </svg>
                    Print Details
                </button>

                <button type="button"
                        class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        onclick="navigator.clipboard.writeText('{{ $customer['endcust_account_csn'] ?? '' }}')">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                    Copy CSN
                </button>
            </div>
        </x-component-card>

        <!-- Activity Timeline Card -->
        <x-component-card title="Activity Timeline">
            <x-component-activity-feed
                :activity="$histdata"
                empty-title="No activity yet"
                empty-description="Customer activity and communications will appear here."
            />
        </x-component-card>

        <!-- Enhanced Data Card (if available) -->
        @if(!empty($matched_data))
            @php
                $enhancedDataItems = [];
                foreach($matched_data as $key => $value) {
                    if (!empty($value) && !in_array($key, ['data_source', 'source_table', 'can_add_to_unity', 'confidence', 'match_type'])) {
                        $enhancedDataItems[] = [
                            'label' => ucwords(str_replace('_', ' ', $key)),
                            'value' => $value,
                            'labelClass' => 'text-sm font-medium text-blue-700',
                            'valueClass' => 'mt-1 text-sm text-blue-900'
                        ];
                    }
                }
            @endphp

            <div class="bg-blue-50 shadow-sm rounded-lg border border-blue-200">
                <div class="px-6 py-4 border-b border-blue-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-lg font-medium text-gray-900">Enhanced Data</h2>
                            <p class="mt-1 text-sm text-gray-500">Additional information from manual entries</p>
                        </div>
                        @if(!empty($matched_data['source_table']))
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clip-rule="evenodd"></path>
                                </svg>
                                {{ ucwords(str_replace('_', ' ', $matched_data['source_table'])) }}
                            </span>
                        @endif
                    </div>
                </div>
                <div class="px-6 py-4">
                    <x-component-data-list :items="$enhancedDataItems" />
                    @if(!empty($matched_data['confidence']))
                        <div class="mt-4 pt-4 border-t border-blue-200">
                            <div class="flex items-center text-xs text-blue-600">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Match confidence: {{ round($matched_data['confidence'] * 100) }}%
                                @if(!empty($matched_data['match_type']))
                                    ({{ ucfirst($matched_data['match_type']) }} match)
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        @endif

        <!-- CSV Data Sources Info (if any CSV subscriptions exist) -->
        @php
            $csv_sources = array_unique(array_filter(array_map(function($sub) {
                return ($sub['data_source'] ?? '') === 'csv_table' ? $sub['source_table'] : null;
            }, $subscriptions)));
        @endphp

        @if(!empty($csv_sources))
            <div class="bg-green-50 shadow-sm rounded-lg border border-green-200">
                <div class="px-6 py-4 border-b border-green-200">
                    <h2 class="text-lg font-medium text-gray-900">CSV Data Sources</h2>
                    <p class="mt-1 text-sm text-gray-500">Subscription data from uploaded CSV files</p>
                </div>
                <div class="px-6 py-4">
                    <div class="space-y-2">
                        @foreach($csv_sources as $source)
                            <div class="flex items-center text-sm">
                                <svg class="w-4 h-4 mr-2 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-900">{{ ucwords(str_replace('_', ' ', $source)) }}</span>
                                <span class="ml-2 text-gray-500">
                                    ({{ count(array_filter($subscriptions, fn($s) => ($s['source_table'] ?? '') === $source)) }} entries)
                                </span>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endif

        <!-- System Information Card -->
        @php
            $systemInfoItems = [
                [
                    'label' => 'Customer ID',
                    'value' => $customer['endcust_id'] ?? 'N/A',
                    'valueClass' => 'mt-1 text-sm text-gray-700 font-mono'
                ],
                [
                    'label' => 'Account CSN',
                    'value' => $customer['endcust_account_csn'] ?? 'N/A',
                    'valueClass' => 'mt-1 text-sm text-gray-700 font-mono'
                ]
            ];

            if (!empty($customer['endcust_created_at'])) {
                $systemInfoItems[] = [
                    'label' => 'Created',
                    'value' => $customer['endcust_created_at'],
                    'valueClass' => 'mt-1 text-sm text-gray-700'
                ];
            }

            if (!empty($customer['endcust_last_modified'])) {
                $systemInfoItems[] = [
                    'label' => 'Last Modified',
                    'value' => $customer['endcust_last_modified'],
                    'valueClass' => 'mt-1 text-sm text-gray-700'
                ];
            }
        @endphp

        <div class="bg-gray-50 shadow-sm rounded-lg" x-data="{ expanded: false }">
            <div class="px-6 py-4 border-b border-gray-200">
                <button @click="expanded = !expanded"
                        class="flex items-center justify-between w-full text-left">
                    <h2 class="text-lg font-medium text-gray-700">System Information</h2>
                    <svg :class="{'rotate-180': expanded}"
                         class="w-5 h-5 text-gray-400 transform transition-transform duration-200"
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
            </div>
            <div x-show="expanded" x-transition class="px-6 py-4">
                <x-component-data-list :items="$systemInfoItems" />
            </div>
        </div>
    </div>
</div>

<!-- Print Styles -->
<style>
@media print {
    .no-print { display: none !important; }
    .print-break { page-break-before: always; }
    body { font-size: 12px; }
    .shadow-sm { box-shadow: none !important; }
    .bg-white { background: white !important; }
    .text-indigo-600 { color: #000 !important; }
    .bg-indigo-600 { background: #000 !important; }
    .border-gray-200 { border-color: #ccc !important; }
}
</style>
