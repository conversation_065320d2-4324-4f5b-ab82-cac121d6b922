@props([
    'title' => 'New Customer Form',
    'description' => 'Form for creating a new customer',
    'quote_data' => [],
    'errors' => []
])

<!-- New Customer Fields -->
<div class="sm:col-span-6 space-y-6">
    <!-- Is Individual -->
    <div>
        <x-forms-field-with-description
                label="Is Individual?"
                field_name="isIndividual"
                description_text="Determines if the end customer is an individual (true) or a company (false). Required when creating a new customer."
        >
            <x-forms-checkbox-toggle
                    name="endCustomer.isIndividual"
                    label="Is Individual?"
                    checked="{{ isset($quote_data['endCustomer']['isIndividual']) && $quote_data['endCustomer']['isIndividual'] ? 'true' : 'false' }}"
                    hx-get='{{ APP_ROOT }}/api/quote-v3/toggle-individual'
                    hx-target='#company-fields'
                    hx-trigger='change'
            />
        </x-forms-field-with-description>
        @if(isset($errors['endCustomer.isIndividual']))
            <p class="mt-2 text-sm text-red-600">{{ $errors['endCustomer.isIndividual'] }}</p>
        @endif
    </div>

    <!-- Company Name (only if not individual) -->
    <div id="company-fields">
        <x-forms-field-with-description
                label="Company Name"
                field_name="companyName"
                description_text="Company name. Required if creating a new company customer (isIndividual=false). Max 100 chars."
        >
            <x-forms-input
                    type="text"
                    name="endCustomer.name"
                    id="endCustomer.name"
                    value="{{ isset($quote_data['endCustomer']['name']) ? $quote_data['endCustomer']['name'] : '' }}"
            />
        </x-forms-field-with-description>
        @if(isset($errors['endCustomer.name']))
            <p class="mt-2 text-sm text-red-600">{{ $errors['endCustomer.name'] }}</p>
        @endif
    </div>

    <!-- Address Fields -->
    <div class="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
        <div class="sm:col-span-3">
            <x-forms-field-with-description
                    label="Address Line 1"
                    field_name="addressLine1"
                    description_text="Street address. Required when creating a new customer."
            >
                <x-forms-input
                        type="text"
                        name="endCustomer.addressLine1"
                        id="endCustomer.addressLine1"
                        value="{{ isset($quote_data['endCustomer']['addressLine1']) ? $quote_data['endCustomer']['addressLine1'] : '' }}"
                />
            </x-forms-field-with-description>
        </div>

        <div class="sm:col-span-3">
            <x-forms-field-with-description
                    label="Address Line 2"
                    field_name="addressLine2"
                    description_text="Suite or secondary address information."
            >
                <x-forms-input
                        type="text"
                        name="endCustomer.addressLine2"
                        id="endCustomer.addressLine2"
                        value="{{ isset($quote_data['endCustomer']['addressLine2']) ? $quote_data['endCustomer']['addressLine2'] : '' }}"
                />
            </x-forms-field-with-description>
        </div>
    </div>
</div>