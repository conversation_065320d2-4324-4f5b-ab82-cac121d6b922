@props([
    'title' => 'Order Details',
    'description' => '',
    'order' => [],
    'matched_data' => [],
    'histdata' => [],
    'class' => ''
])

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <!-- Header Section -->
    <div class="bg-white shadow-sm rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div class="flex-1 min-w-0">
                    <h1 class="text-2xl font-bold text-gray-900 truncate">
                        Order #{{ $order['orders_order_number'] ?? 'Unknown' }}
                    </h1>
                    <div class="mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:space-x-6">
                        <div class="mt-2 flex items-center text-sm text-gray-500">
                            <span class="font-medium">Customer:</span>
                            <span class="ml-1">{{ $order['endcust_name'] ?? 'N/A' }}</span>
                        </div>
                        <div class="mt-2 flex items-center text-sm text-gray-500">
                            <span class="font-medium">Status:</span>
                            <span class="ml-1 px-2 py-1 text-xs font-medium rounded-full 
                                {{ ($order['orders_order_status'] ?? '') === 'SUBMITTED' ? 'bg-green-100 text-green-800' : 
                                   (($order['orders_order_status'] ?? '') === 'DRAFT' ? 'bg-yellow-100 text-yellow-800' : 
                                   (($order['orders_order_status'] ?? '') === 'PROCESSING' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800')) }}">
                                {{ $order['orders_order_status'] ?? 'Unknown' }}
                            </span>
                        </div>
                        @if(!empty($order['orders_total_amount']))
                            <div class="mt-2 flex items-center text-sm text-gray-500">
                                <span class="font-medium">Total:</span>
                                <span class="ml-1 font-semibold text-gray-900">
                                    {{ $order['orders_order_currency'] ?? '$' }}{{ number_format($order['orders_total_amount'], 2) }}
                                </span>
                            </div>
                        @endif
                    </div>
                </div>
                <div class="mt-4 sm:mt-0 sm:ml-4 flex space-x-2">
                    @if(($order['orders_order_status'] ?? '') === 'DRAFT')
                        <button type="button" 
                                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                                hx-post="{{ APP_ROOT }}/api/orders/submit"
                                hx-swap="innerHTML"
                                hx-target="#modal_body"
                                hx-vals='{{ json_encode([
                                    "order_number" => $order['orders_order_number'] ?? '',
                                    "order_id" => $order['orders_id'] ?? '',
                                    "user_id" => $_SESSION['user_id'] ?? ''
                                ]) }}'
                                hx-confirm="Submit this order? This action cannot be undone.">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                            Submit Order
                        </button>
                    @endif
                    
                    @if(in_array($order['orders_order_status'] ?? '', ['SUBMITTED', 'PROCESSING']))
                        <button type="button" 
                                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                hx-post="{{ APP_ROOT }}/api/orders/track"
                                hx-swap="innerHTML"
                                hx-target="#modal_body"
                                hx-vals='{{ json_encode([
                                    "order_number" => $order['orders_order_number'] ?? '',
                                    "order_id" => $order['orders_id'] ?? '',
                                    "user_id" => $_SESSION['user_id'] ?? ''
                                ]) }}'>
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                            </svg>
                            Track Order
                        </button>
                    @endif
                    
                    <button type="button" 
                            class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            hx-post="{{ APP_ROOT }}/api/orders/flag_modal"
                            hx-swap="innerHTML"
                            hx-target="#modal_body"
                            hx-vals='{{ json_encode([
                                "target" => "order",
                                "target_reference" => $order['orders_order_number'] ?? '',
                                "customer_csn" => $order['endcust_account_csn'] ?? '',
                                "endcust_name" => $order['endcust_name'] ?? '',
                                "email_address" => $order['endcust_primary_admin_email'] ?? '',
                                "user_id" => $_SESSION['user_id'] ?? ''
                            ]) }}'
                            @click="showModal = true">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21a2 2 0 012 2v11a2 2 0 01-2 2H3z"></path>
                        </svg>
                        Add Note
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left Column - Primary Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Order Details Card -->
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Order Information</h2>
                </div>
                <div class="px-6 py-4">
                    <dl class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Order Number</dt>
                            <dd class="mt-1 text-sm text-gray-900 font-mono">{{ $order['orders_order_number'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Status</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $order['orders_order_status'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Created Date</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $order['orders_order_created_time'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Submitted Date</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $order['orders_order_submitted_time'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">PO Number</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $order['orders_po_number'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Language</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $order['orders_order_language'] ?? 'N/A' }}</dd>
                        </div>
                    </dl>
                </div>
            </div>

            <!-- Pricing Information Card -->
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Pricing Details</h2>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-sm font-medium text-gray-900 mb-3">Amounts</h3>
                            <dl class="space-y-3">
                                <div class="flex justify-between">
                                    <dt class="text-sm font-medium text-gray-500">List Amount</dt>
                                    <dd class="text-sm text-gray-900">
                                        {{ $order['orders_order_currency'] ?? '$' }}{{ number_format($order['orders_total_list_amount'] ?? 0, 2) }}
                                    </dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Net Amount</dt>
                                    <dd class="text-sm text-gray-900">
                                        {{ $order['orders_order_currency'] ?? '$' }}{{ number_format($order['orders_total_net_amount'] ?? 0, 2) }}
                                    </dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Discount</dt>
                                    <dd class="text-sm text-gray-900">
                                        {{ $order['orders_order_currency'] ?? '$' }}{{ number_format($order['orders_total_discount'] ?? 0, 2) }}
                                    </dd>
                                </div>
                                <div class="flex justify-between">
                                    <dt class="text-sm font-medium text-gray-500">Tax</dt>
                                    <dd class="text-sm text-gray-900">
                                        {{ $order['orders_order_currency'] ?? '$' }}{{ number_format($order['orders_total_tax'] ?? 0, 2) }}
                                    </dd>
                                </div>
                                <div class="flex justify-between pt-3 border-t border-gray-200">
                                    <dt class="text-base font-semibold text-gray-900">Total Amount</dt>
                                    <dd class="text-base font-semibold text-gray-900">
                                        {{ $order['orders_order_currency'] ?? '$' }}{{ number_format($order['orders_total_amount'] ?? 0, 2) }}
                                    </dd>
                                </div>
                            </dl>
                        </div>
                        
                        <div>
                            <h3 class="text-sm font-medium text-gray-900 mb-3">Payment & Shipping</h3>
                            <dl class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Payment Terms</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $order['orders_payment_terms_description'] ?? 'N/A' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Currency</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $order['orders_order_currency'] ?? 'N/A' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Shipping Method</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $order['orders_shipping_method'] ?? 'N/A' }}</dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Information Card -->
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Customer Information</h2>
                    @if(!empty($matched_data))
                        <p class="mt-1 text-sm text-gray-500">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Enhanced with additional data
                            </span>
                        </p>
                    @endif
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- End Customer -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-900 mb-3">End Customer</h3>
                            <dl class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Company</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $order['endcust_name'] ?? 'N/A' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        @if(!empty($order['endcust_primary_admin_email']))
                                            <a href="mailto:{{ $order['endcust_primary_admin_email'] }}" 
                                               class="text-indigo-600 hover:text-indigo-500">
                                                {{ $order['endcust_primary_admin_email'] }}
                                            </a>
                                        @else
                                            N/A
                                        @endif
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">CSN</dt>
                                    <dd class="mt-1 text-sm text-gray-900 font-mono">{{ $order['endcust_account_csn'] ?? 'N/A' }}</dd>
                                </div>
                            </dl>
                        </div>

                        <!-- Order Contact -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-900 mb-3">Order Contact</h3>
                            <dl class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Contact Name</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $order['ordercontact_name'] ?? 'N/A' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        @if(!empty($order['ordercontact_email']))
                                            <a href="mailto:{{ $order['ordercontact_email'] }}" 
                                               class="text-indigo-600 hover:text-indigo-500">
                                                {{ $order['ordercontact_email'] }}
                                            </a>
                                        @else
                                            N/A
                                        @endif
                                    </dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Details (Collapsible) -->
            <div x-data="{ expanded: false }" class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <button @click="expanded = !expanded" 
                            class="flex items-center justify-between w-full text-left">
                        <h2 class="text-lg font-medium text-gray-900">Additional Details</h2>
                        <svg :class="{'rotate-180': expanded}" 
                             class="w-5 h-5 text-gray-400 transform transition-transform duration-200" 
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                </div>
                <div x-show="expanded" x-transition class="px-6 py-4">
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Skip DDA Check</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $order['orders_skip_dda_check'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Modified At</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $order['orders_modified_at'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Quote Number</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $order['quotes_quote_number'] ?? 'N/A' }}</dd>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column - Actions and Timeline -->
        <div class="space-y-6">
            <!-- Quick Actions Card -->
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Quick Actions</h2>
                </div>
                <div class="px-6 py-4 space-y-3">
                    @if(($order['orders_order_status'] ?? '') === 'SUBMITTED')
                        <button type="button"
                                class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                hx-post="{{ APP_ROOT }}/api/orders/check_status"
                                hx-swap="innerHTML"
                                hx-target="#modal_body"
                                hx-vals='{{ json_encode([
                                    "order_number" => $order['orders_order_number'] ?? '',
                                    "order_id" => $order['orders_id'] ?? '',
                                    "user_id" => $_SESSION['user_id'] ?? ''
                                ]) }}'
                                @click="showModal = true">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Check Status
                        </button>
                    @endif

                    <button type="button"
                            class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            onclick="window.print()">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                        </svg>
                        Print Order
                    </button>

                    <button type="button"
                            class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            onclick="navigator.clipboard.writeText('{{ $order['orders_order_number'] ?? '' }}')">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        Copy Order Number
                    </button>
                </div>
            </div>

            <!-- Order Status Card -->
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Order Status</h2>
                </div>
                <div class="px-6 py-4">
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                @if(($order['orders_order_status'] ?? '') === 'SUBMITTED')
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                @elseif(($order['orders_order_status'] ?? '') === 'PROCESSING')
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                @elseif(($order['orders_order_status'] ?? '') === 'DRAFT')
                                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                @else
                                    <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                @endif
                            </div>
                            <div class="ml-4">
                                <h3 class="text-sm font-medium text-gray-900">{{ $order['orders_order_status'] ?? 'Unknown' }}</h3>
                                <p class="text-sm text-gray-500">
                                    @if(($order['orders_order_status'] ?? '') === 'SUBMITTED')
                                        Order has been submitted and is being processed
                                    @elseif(($order['orders_order_status'] ?? '') === 'PROCESSING')
                                        Order is currently being processed
                                    @elseif(($order['orders_order_status'] ?? '') === 'DRAFT')
                                        Order is in draft status
                                    @else
                                        Order status unknown
                                    @endif
                                </p>
                            </div>
                        </div>

                        @if(!empty($order['orders_order_submitted_time']))
                            <div class="pt-4 border-t border-gray-200">
                                <div class="text-sm">
                                    <span class="font-medium text-gray-500">Submitted:</span>
                                    <span class="ml-1 text-gray-900">{{ $order['orders_order_submitted_time'] }}</span>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Activity Timeline Card -->
            <div class="bg-white shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Activity Timeline</h2>
                </div>
                <div class="px-6 py-4">
                    @if(!empty($histdata))
                        <x-component-activity-feed :activity="$histdata" />
                    @else
                        <div class="text-center py-6">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No activity yet</h3>
                            <p class="mt-1 text-sm text-gray-500">Order activity and communications will appear here.</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- System Information Card -->
            <div x-data="{ expanded: false }" class="bg-gray-50 shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <button @click="expanded = !expanded"
                            class="flex items-center justify-between w-full text-left">
                        <h2 class="text-lg font-medium text-gray-700">System Information</h2>
                        <svg :class="{'rotate-180': expanded}"
                             class="w-5 h-5 text-gray-400 transform transition-transform duration-200"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                </div>
                <div x-show="expanded" x-transition class="px-6 py-4">
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Order ID</dt>
                            <dd class="mt-1 text-sm text-gray-700 font-mono">{{ $order['orders_id'] ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Quote ID</dt>
                            <dd class="mt-1 text-sm text-gray-700 font-mono">{{ $order['quotes_id'] ?? 'N/A' }}</dd>
                        </div>
                        @if(!empty($order['orders_created_at']))
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Created</dt>
                                <dd class="mt-1 text-sm text-gray-700">{{ $order['orders_created_at'] }}</dd>
                            </div>
                        @endif
                        @if(!empty($order['orders_modified_at']))
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Last Modified</dt>
                                <dd class="mt-1 text-sm text-gray-700">{{ $order['orders_modified_at'] }}</dd>
                            </div>
                        @endif
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Print Styles -->
<style>
@media print {
    .no-print { display: none !important; }
    .print-break { page-break-before: always; }
    body { font-size: 12px; }
    .shadow-sm { box-shadow: none !important; }
    .bg-white { background: white !important; }
    .text-indigo-600 { color: #000 !important; }
    .bg-indigo-600 { background: #000 !important; }
    .border-gray-200 { border-color: #ccc !important; }
}
</style>
