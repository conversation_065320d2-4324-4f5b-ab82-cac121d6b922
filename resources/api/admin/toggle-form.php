<?php
// API endpoint to toggle the admin form
// This will be called via HTMX when the user checks/unchecks the "Add Primary Admin" checkbox

// Check if the checkbox is checked
$show_admin = isset($_GET['show_admin']) && $_GET['show_admin'] === 'on';

// If checked, return the admin form fields
if ($show_admin) {
    ?>
    <!-- Admin Email -->
    <div class="sm:col-span-3">
        <x-forms-input
            name="admin.email"
            label="Admin Email"
            type="email"
            required
        />
    </div>

    <!-- Admin First Name -->
    <div class="sm:col-span-3">
        <x-forms-input
            name="admin.firstName"
            label="Admin First Name"
            type="text"
        />
    </div>

    <!-- Admin Last Name -->
    <div class="sm:col-span-3">
        <x-forms-input
            name="admin.lastName"
            label="Admin Last Name"
            type="text"
        />
    </div>

    <!-- Admin Phone -->
    <div class="sm:col-span-3">
        <x-forms-input
            name="admin.phone"
            label="Admin Phone"
            type="tel"
        />
    </div>

    <!-- Admin Preferred Language -->
    <div class="sm:col-span-3">
        <x-forms-select
            name="admin.preferredLanguage"
            label="Admin Preferred Language"
            :options="['en' => 'English']"
        />
    </div>
    <?php
} else {
    // Return empty content if unchecked
    echo '<!-- Admin form fields will appear here when checkbox is checked -->';
}
?>
