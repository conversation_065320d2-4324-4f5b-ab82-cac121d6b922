<?php
// API endpoint to remove a recipient
// This will be called via HTMX when the user clicks the "Remove" button

// Get the email from the query string
$email = isset($_GET['email']) ? $_GET['email'] : '';

// Get existing recipients from the session
session_start();
if (!isset($_SESSION['recipients'])) {
    $_SESSION['recipients'] = [];
}

// Remove the email from the list
$_SESSION['recipients'] = array_filter($_SESSION['recipients'], function($recipient) use ($email) {
    return $recipient !== $email;
});

// Return the updated list of recipients
$index = 0;
foreach ($_SESSION['recipients'] as $recipient) {
    ?>
    <div class="flex items-center justify-between py-2 border-b">
        <div>
            <span><?= htmlspecialchars($recipient) ?></span>
            <input type="hidden" name="additionalRecipients[<?= $index ?>].email" value="<?= htmlspecialchars($recipient) ?>">
        </div>
        <button type="button" 
                hx-delete="api/recipients/remove?email=<?= urlencode($recipient) ?>"
                hx-target="#recipients-list"
                hx-swap="innerHTML"
                class="text-red-600 hover:text-red-800">
            Remove
        </button>
    </div>
    <?php
    $index++;
}
?>
