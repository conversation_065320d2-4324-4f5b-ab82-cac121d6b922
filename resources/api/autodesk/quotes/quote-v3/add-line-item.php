<?php
// This endpoint adds a new line item to the form
// It's called via HTMX and returns the new line item HTML

// Include the Edge class to render the template
require_once 'resources/classes/edge.class.php';
use Edge\Edge;

// Get the current line items count
$index = isset($_POST['index']) ? intval($_POST['index']) : 0;

// Create a new empty line item
$item = [
    'action' => '',
    'quantity' => 1,
    'offeringId' => null,
    'offeringName' => null,
    'offeringCode' => null,
    'subscriptionId' => null,
    'referenceSubscriptionId' => null,
    'startDate' => null,
    'endDate' => null,
    'promotionCode' => null
];

// Render the line item template
echo Edge::render('quote-v3-line-item', [
    'index' => $index,
    'item' => $item
]);
