<?php
// This endpoint toggles the visibility of a field description
// It's called via HTMX and returns the updated content

// Get the target element ID from the request
$target_id = $_GET['target'] ?? '';

// Check if the description is currently hidden
// In a real implementation, you might want to store the state in a session or database
// For simplicity, we'll just toggle based on the presence of a class

// Return the updated content
echo '<div class="mt-1 text-sm text-gray-600 bg-gray-50 p-2 rounded">';
echo 'Field description content here. This would be populated from the schema.';
echo '</div>';
