<?php
// Set content type to JSON
header('Content-Type: application/json');

// Get the JSON data from the request
$json_data = file_get_contents('php://input');
$data = json_decode($json_data, true);

// Load the schema
$schema_path = '../../resources/schemas/QuoteV3.json';
if (file_exists($schema_path)) {
    $schema_content = file_get_contents($schema_path);
    $schema = json_decode($schema_content, true);
} else {
    // Return error if schema not found
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'errors' => [['field' => 'general', 'message' => 'Schema not found']]
    ]);
    exit;
}

// Validate the data against the schema
$errors = validateForm($data, $schema);

// Return the validation result
if (empty($errors)) {
    echo json_encode([
        'success' => true,
        'message' => 'Validation successful'
    ]);
} else {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'errors' => $errors
    ]);
}

/**
 * Validate the form data against the schema
 * 
 * @param array $data The form data
 * @param array $schema The schema
 * @return array Array of validation errors
 */
function validateForm($data, $schema) {
    $errors = [];
    
    // Validate fields
    if (isset($schema['fields']) && is_array($schema['fields'])) {
        foreach ($schema['fields'] as $field) {
            validateField($field, '', $data, $errors);
        }
    }
    
    return $errors;
}

/**
 * Validate a single field
 * 
 * @param array $field The field schema
 * @param string $parentPath The parent path
 * @param array $data The form data
 * @param array &$errors Array to store validation errors
 */
function validateField($field, $parentPath, $data, &$errors) {
    $fieldPath = $parentPath ? "{$parentPath}.{$field['name']}" : $field['name'];
    $value = getNestedValue($data, $fieldPath);
    
    // Check if required
    if (isset($field['required']) && $field['required'] && ($value === null || $value === '')) {
        $errors[] = [
            'field' => $fieldPath,
            'message' => "{$field['label']} is required"
        ];
    }
    
    // Skip further validation if value is empty and not required
    if ($value === null || $value === '') {
        return;
    }
    
    // Check pattern
    if (isset($field['pattern']) && !preg_match("/{$field['pattern']}/", $value)) {
        $errors[] = [
            'field' => $fieldPath,
            'message' => "{$field['label']} does not match the required format"
        ];
    }
    
    // Check maxLength
    if (isset($field['maxLength']) && strlen($value) > $field['maxLength']) {
        $errors[] = [
            'field' => $fieldPath,
            'message' => "{$field['label']} exceeds the maximum length of {$field['maxLength']}"
        ];
    }
    
    // Check minValue/maxValue for integer fields
    if ($field['type'] === 'integer') {
        $numValue = intval($value);
        if (isset($field['minValue']) && $numValue < $field['minValue']) {
            $errors[] = [
                'field' => $fieldPath,
                'message' => "{$field['label']} must be at least {$field['minValue']}"
            ];
        }
        if (isset($field['maxValue']) && $numValue > $field['maxValue']) {
            $errors[] = [
                'field' => $fieldPath,
                'message' => "{$field['label']} must be at most {$field['maxValue']}"
            ];
        }
    }
    
    // Validate email format
    if ($field['type'] === 'email' && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
        $errors[] = [
            'field' => $fieldPath,
            'message' => "{$field['label']} must be a valid email address"
        ];
    }
    
    // Validate nested fields
    if ($field['type'] === 'object' && isset($field['fields'])) {
        foreach ($field['fields'] as $subfield) {
            validateField($subfield, $fieldPath, $data, $errors);
        }
    }
    
    // Validate array items
    if ($field['type'] === 'array' && isset($field['itemSchema']) && isset($field['itemSchema']['fields'])) {
        $arrayValue = getNestedValue($data, $fieldPath);
        if (is_array($arrayValue)) {
            foreach ($arrayValue as $index => $item) {
                foreach ($field['itemSchema']['fields'] as $subfield) {
                    validateField($subfield, "{$fieldPath}[{$index}]", $data, $errors);
                }
            }
        }
    }
}

/**
 * Get a nested value from an array using dot notation
 * 
 * @param array $array The array
 * @param string $path The path in dot notation
 * @return mixed The value or null if not found
 */
function getNestedValue($array, $path) {
    // Handle array indices in the path
    if (preg_match('/(.+?)\[(\d+)\](.*)/', $path, $matches)) {
        $basePath = $matches[1];
        $index = $matches[2];
        $remainingPath = $matches[3];
        
        // Get the array at the base path
        $baseArray = getNestedValue($array, $basePath);
        
        // Check if it's an array and the index exists
        if (is_array($baseArray) && isset($baseArray[$index])) {
            if (empty($remainingPath)) {
                return $baseArray[$index];
            } else {
                // Remove the leading dot if present
                $remainingPath = ltrim($remainingPath, '.');
                return getNestedValue($baseArray[$index], $remainingPath);
            }
        }
        
        return null;
    }
    
    // Handle regular dot notation
    $keys = explode('.', $path);
    $current = $array;
    
    foreach ($keys as $key) {
        if (!is_array($current) || !isset($current[$key])) {
            return null;
        }
        $current = $current[$key];
    }
    
    return $current;
}
