<?php
// API endpoint to add a line item
// This will be called via HTMX when the user clicks the "Add Line Item" button

// Get the current count of line items from the session
session_start();
if (!isset($_SESSION['line_item_count'])) {
    $_SESSION['line_item_count'] = 0;
}

// Increment the count
$index = $_SESSION['line_item_count']++;

// Generate a unique ID for this line item
$id = uniqid();

// Return the HTML for a new line item
?>
<div id="line-item-<?= $id ?>" class="bg-gray-50 p-4 rounded-lg border border-gray-200">
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium">Line Item <?= $index + 1 ?></h3>
        <button type="button" 
                hx-delete="api/line-items/remove?id=<?= $id ?>"
                hx-target="#line-item-<?= $id ?>"
                hx-swap="outerHTML"
                class="text-red-600 hover:text-red-800">
            Remove
        </button>
    </div>

    <div class="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
        <!-- Action -->
        <div class="sm:col-span-3">
            <x-forms-select
                name="lineItems[<?= $index ?>].action"
                label="Action"
                :options="[
                    'New' => 'New',
                    'Renewal' => 'Renewal',
                    'Switch' => 'Switch (Product/Term)',
                    'Mid-term Switch' => 'Mid-term Switch (ACS Only)',
                    'Extension' => 'Extension',
                    'True-up' => 'True-up',
                    'Co-term' => 'Co-term'
                ]"
                required
            />
        </div>

        <!-- Quantity -->
        <div class="sm:col-span-3">
            <x-forms-input
                name="lineItems[<?= $index ?>].quantity"
                label="Quantity"
                type="number"
                min="1"
                value="1"
                required
            />
        </div>

        <!-- Agent Line Reference -->
        <div class="sm:col-span-3">
            <x-forms-input
                name="lineItems[<?= $index ?>].agentLineReference"
                label="Agent Line Reference"
                type="text"
                maxlength="37"
            />
        </div>

        <!-- Promotion Code -->
        <div class="sm:col-span-3">
            <x-forms-input
                name="lineItems[<?= $index ?>].promotionCode"
                label="Promotion Code"
                type="text"
            />
        </div>

        <!-- Offering ID -->
        <div class="sm:col-span-3">
            <x-forms-input
                name="lineItems[<?= $index ?>].offeringId"
                label="Offering ID"
                type="text"
                placeholder="OD-XXXXXX"
            />
        </div>

        <!-- Offering Name -->
        <div class="sm:col-span-3">
            <x-forms-input
                name="lineItems[<?= $index ?>].offeringName"
                label="Offering Name"
                type="text"
            />
        </div>

        <!-- Offering Code -->
        <div class="sm:col-span-3">
            <x-forms-input
                name="lineItems[<?= $index ?>].offeringCode"
                label="Offering Code"
                type="text"
            />
        </div>

        <!-- Start Date -->
        <div class="sm:col-span-3">
            <x-forms-input
                name="lineItems[<?= $index ?>].startDate"
                label="Start Date"
                type="date"
            />
        </div>

        <!-- End Date -->
        <div class="sm:col-span-3">
            <x-forms-input
                name="lineItems[<?= $index ?>].endDate"
                label="End Date"
                type="date"
            />
        </div>

        <!-- Subscription ID -->
        <div class="sm:col-span-3">
            <x-forms-input
                name="lineItems[<?= $index ?>].subscriptionId"
                label="Subscription ID"
                type="text"
            />
        </div>

        <!-- Reference Subscription ID -->
        <div class="sm:col-span-3">
            <x-forms-input
                name="lineItems[<?= $index ?>].referenceSubscriptionId"
                label="Reference Subscription ID"
                type="text"
            />
        </div>

        <!-- Offer Details Section -->
        <div class="col-span-full mt-4">
            <h4 class="font-medium text-gray-900">Offer Details</h4>

            <div class="mt-4 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                <!-- Term Code -->
                <div class="sm:col-span-3">
                    <x-forms-select
                        name="lineItems[<?= $index ?>].offer.term.code"
                        label="Term Code"
                        :options="['A01' => 'Annual', 'A06' => '3 Year']"
                    />
                </div>

                <!-- Term Description -->
                <div class="sm:col-span-3">
                    <x-forms-input
                        name="lineItems[<?= $index ?>].offer.term.description"
                        label="Term Description"
                        type="text"
                    />
                </div>

                <!-- Access Model Code -->
                <div class="sm:col-span-3">
                    <x-forms-select
                        name="lineItems[<?= $index ?>].offer.accessModel.code"
                        label="Access Model Code"
                        :options="['S' => 'Single User', 'F' => 'Flex']"
                    />
                </div>

                <!-- Access Model Description -->
                <div class="sm:col-span-3">
                    <x-forms-input
                        name="lineItems[<?= $index ?>].offer.accessModel.description"
                        label="Access Model Description"
                        type="text"
                    />
                </div>

                <!-- Usage Code -->
                <div class="sm:col-span-3">
                    <x-forms-select
                        name="lineItems[<?= $index ?>].offer.intendedUsage.code"
                        label="Usage Code"
                        :options="['COM' => 'Commercial', 'NFR' => 'Not for Resale']"
                    />
                </div>

                <!-- Usage Description -->
                <div class="sm:col-span-3">
                    <x-forms-input
                        name="lineItems[<?= $index ?>].offer.intendedUsage.description"
                        label="Usage Description"
                        type="text"
                    />
                </div>

                <!-- Connectivity Code -->
                <div class="sm:col-span-3">
                    <x-forms-select
                        name="lineItems[<?= $index ?>].offer.connectivity.code"
                        label="Connectivity Code"
                        :options="['C100' => 'Online']"
                    />
                </div>

                <!-- Connectivity Description -->
                <div class="sm:col-span-3">
                    <x-forms-input
                        name="lineItems[<?= $index ?>].offer.connectivity.description"
                        label="Connectivity Description"
                        type="text"
                    />
                </div>

                <!-- Service Plan Code -->
                <div class="sm:col-span-3">
                    <x-forms-select
                        name="lineItems[<?= $index ?>].offer.servicePlan.code"
                        label="Service Plan Code"
                        :options="[
                            'STND' => 'Standard',
                            'STNDNS' => 'Standard No Support',
                            'PREMSUB' => 'Premium',
                            'PREMNS' => 'Premium No Support'
                        ]"
                    />
                </div>

                <!-- Service Plan Description -->
                <div class="sm:col-span-3">
                    <x-forms-input
                        name="lineItems[<?= $index ?>].offer.servicePlan.description"
                        label="Service Plan Description"
                        type="text"
                    />
                </div>
            </div>
        </div>
    </div>
</div>
