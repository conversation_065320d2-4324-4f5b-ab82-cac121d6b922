<?php
namespace api\quote_v3;

use autodesk_api\autodesk_api;
use edge\Edge;

/**
 * Submit a quote to Autodesk
 * 
 * @param array $p Form data
 * @return string HTML response
 */
function submit($p) {
    // Validate the form data
    $errors = validate_quote_data($p);
    
    if (!empty($errors)) {
        // Return the form with validation errors
        return Edge::render('quote-v3-ui-form', [
            'quote_data' => $p,
            'errors' => $errors
        ]);
    }
    
    // Process the form data
    $autodesk = new autodesk_api();
    $result = $autodesk->quote->send_to_autodesk($p);
    
    // Check if the submission was successful
    if (isset($result['success']) && $result['success']) {
        // Return success message
        return Edge::render('quote-v3-success', [
            'message' => 'Quote submitted successfully!',
            'quote_id' => $result['quote_id'] ?? null
        ]);
    } else {
        // Return the form with API errors
        return Edge::render('quote-v3-ui-form', [
            'quote_data' => $p,
            'errors' => [
                'api' => $result['message'] ?? 'An error occurred while submitting the quote.'
            ]
        ]);
    }
}

/**
 * Add a new line item to the form
 * 
 * @param array $p Form data
 * @return string HTML response
 */
function add_line_item($p) {
    $index = isset($p['index']) ? intval($p['index']) : 0;
    
    // Create a new empty line item
    $item = [
        'action' => '',
        'quantity' => 1,
        'offeringId' => null,
        'offeringName' => null,
        'offeringCode' => null,
        'subscriptionId' => null,
        'referenceSubscriptionId' => null,
        'startDate' => null,
        'endDate' => null,
        'promotionCode' => null
    ];
    
    // Render the line item template
    return Edge::render('quote-v3-line-item', [
        'index' => $index,
        'item' => $item
    ]);
}

/**
 * Remove a line item from the form
 * 
 * @param array $p Form data
 * @return string Empty string to remove the element
 */
function remove_line_item($p) {
    // In a real implementation, you might want to update some state
    // For simplicity, we'll just return an empty string to remove the element
    return '';
}

/**
 * Search for customers
 * 
 * @param array $p Search parameters
 * @return string HTML response
 */
function search_customers($p) {
    $search_term = $p['search'] ?? '';
    
    // In a real implementation, you would search the database
    // For now, we'll return some dummy data
    $customers = [
        ['csn' => '1234567890', 'name' => 'Acme Corporation'],
        ['csn' => '0987654321', 'name' => 'Globex Industries'],
        ['csn' => '5555555555', 'name' => 'Initech LLC']
    ];
    
    // Filter customers based on search term
    if (!empty($search_term)) {
        $customers = array_filter($customers, function($customer) use ($search_term) {
            return stripos($customer['name'], $search_term) !== false || 
                   stripos($customer['csn'], $search_term) !== false;
        });
    }
    
    // Render the search results
    return Edge::render('quote-v3-customer-search-results', [
        'customers' => $customers
    ]);
}

/**
 * Search for offerings
 * 
 * @param array $p Search parameters
 * @return string HTML response
 */
function search_offerings($p) {
    $search_term = $p['search'] ?? '';
    
    // In a real implementation, you would search the database
    // For now, we'll return some dummy data
    $offerings = [
        ['id' => 'OD-000123', 'name' => 'AutoCAD LT', 'code' => 'ACDLT'],
        ['id' => 'OD-000456', 'name' => 'Revit', 'code' => 'REVIT'],
        ['id' => 'OD-000789', 'name' => '3ds Max', 'code' => '3DSMAX']
    ];
    
    // Filter offerings based on search term
    if (!empty($search_term)) {
        $offerings = array_filter($offerings, function($offering) use ($search_term) {
            return stripos($offering['name'], $search_term) !== false || 
                   stripos($offering['id'], $search_term) !== false ||
                   stripos($offering['code'], $search_term) !== false;
        });
    }
    
    // Render the search results
    return Edge::render('quote-v3-offering-search-results', [
        'offerings' => $offerings
    ]);
}

/**
 * Check if a contact exists
 * 
 * @param array $p Contact parameters
 * @return string HTML response
 */
function check_contact($p) {
    $email = $p['email'] ?? '';
    
    // In a real implementation, you would check the database
    // For now, we'll return a dummy response
    $contact_exists = !empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL);
    
    // Render the check result
    return Edge::render('quote-v3-contact-check-result', [
        'contact_exists' => $contact_exists,
        'email' => $email
    ]);
}

/**
 * Validate quote data
 * 
 * @param array $data Quote data
 * @return array Validation errors
 */
function validate_quote_data($data) {
    $errors = [];
    
    // Load the schema
    $schema_path = 'resources/schemas/QuoteV3.json';
    if (file_exists($schema_path)) {
        $schema_content = file_get_contents($schema_path);
        $schema = json_decode($schema_content, true);
        
        // Validate required fields
        foreach ($schema['fields'] as $field) {
            if (isset($field['required']) && $field['required']) {
                $field_name = $field['name'];
                if (!isset($data[$field_name]) || empty($data[$field_name])) {
                    $errors[$field_name] = $field['label'] . ' is required.';
                }
            }
            
            // Validate nested fields
            if (isset($field['fields']) && is_array($field['fields'])) {
                foreach ($field['fields'] as $nested_field) {
                    if (isset($nested_field['required']) && $nested_field['required']) {
                        $nested_field_name = $field['name'] . '.' . $nested_field['name'];
                        $nested_value = $data[$field['name']][$nested_field['name']] ?? null;
                        if (empty($nested_value)) {
                            $errors[$nested_field_name] = $nested_field['label'] . ' is required.';
                        }
                    }
                }
            }
        }
    }
    
    return $errors;
}
