<?php
// API endpoint to return the new customer form
// This will be called via HTMX when the user selects "New Customer"

// Return the HTML for the new customer form
?>
<!-- Customer Type -->
<div class="sm:col-span-3">
    <div class="flex items-center">
        <input id="is-individual" name="endCustomer.isIndividual" type="checkbox"
               class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600">
        <label for="is-individual" class="ml-3 block text-sm font-medium leading-6 text-gray-900">Is Individual?</label>
    </div>
</div>

<!-- Customer Name -->
<div class="sm:col-span-3">
    <x-forms-input
        name="endCustomer.name"
        label="Customer Name"
        type="text"
        required
        maxlength="100"
    />
</div>

<!-- Address Line 1 -->
<div class="sm:col-span-3">
    <x-forms-input
        name="endCustomer.addressLine1"
        label="Address Line 1"
        type="text"
        required
    />
</div>

<!-- Address Line 2 -->
<div class="sm:col-span-3">
    <x-forms-input
        name="endCustomer.addressLine2"
        label="Address Line 2"
        type="text"
    />
</div>

<!-- City -->
<div class="sm:col-span-3">
    <x-forms-input
        name="endCustomer.city"
        label="City"
        type="text"
        required
    />
</div>

<!-- State/Province -->
<div class="sm:col-span-3">
    <x-forms-input
        name="endCustomer.stateProvinceCode"
        label="State/Province Code"
        type="text"
        required
    />
</div>

<!-- Postal Code -->
<div class="sm:col-span-3">
    <x-forms-input
        name="endCustomer.postalCode"
        label="Postal Code"
        type="text"
        required
    />
</div>

<!-- Country Code -->
<div class="sm:col-span-3">
    <x-forms-select
        name="endCustomer.countryCode"
        label="Country Code"
        :options="['US' => 'US - United States', 'GB' => 'GB - United Kingdom', 'CA' => 'CA - Canada']"
        required
    />
</div>
