<?php

class subscription_matcher {
    
    private $db;
    private $confidence_threshold = 0.7;
    
    public function __construct() {
        // Database connection will be handled by existing TCS functions
    }
    
    /**
     * Match subscription data with manual entries based on email and company name
     * 
     * @param array $subscription_data The main subscription data from Autodesk API
     * @return array Matched data with confidence scores
     */
    public function match_subscription_data($subscription_data) {
        $matches = [];
        
        // Extract key matching fields
        $email = $subscription_data['endcust_primary_admin_email'] ?? '';
        $company_name = $subscription_data['endcust_name'] ?? '';
        $subscription_ref = $subscription_data['subs_subscriptionReferenceNumber'] ?? '';
        
        // Try exact email match first
        if (!empty($email)) {
            $email_matches = $this->find_by_email($email);
            foreach ($email_matches as $match) {
                $match['confidence'] = $this->calculate_confidence($subscription_data, $match, 'email');
                $match['match_type'] = 'email';
                $matches[] = $match;
            }
        }
        
        // Try company name match
        if (!empty($company_name)) {
            $company_matches = $this->find_by_company_name($company_name);
            foreach ($company_matches as $match) {
                // Avoid duplicates from email matching
                if (!$this->is_duplicate_match($matches, $match)) {
                    $match['confidence'] = $this->calculate_confidence($subscription_data, $match, 'company');
                    $match['match_type'] = 'company';
                    $matches[] = $match;
                }
            }
        }
        
        // Try subscription reference match
        if (!empty($subscription_ref)) {
            $ref_matches = $this->find_by_subscription_reference($subscription_ref);
            foreach ($ref_matches as $match) {
                if (!$this->is_duplicate_match($matches, $match)) {
                    $match['confidence'] = 1.0; // Exact reference match = highest confidence
                    $match['match_type'] = 'reference';
                    $matches[] = $match;
                }
            }
        }
        
        // Sort by confidence score (highest first)
        usort($matches, function($a, $b) {
            return $b['confidence'] <=> $a['confidence'];
        });
        
        // Return only matches above threshold
        return array_filter($matches, function($match) {
            return $match['confidence'] >= $this->confidence_threshold;
        });
    }
    
    /**
     * Find manual entries by email address
     */
    private function find_by_email($email) {
        try {
            $query = "
                SELECT * FROM manual_subscription_entries
                WHERE LOWER(email_address) = LOWER(?)
                OR LOWER(contact_email) = LOWER(?)
                OR LOWER(admin_email) = LOWER(?)
            ";

            // Using TCS database functions
            $results = tcs_db_query($query, [$email, $email, $email]);
            return $results ?: [];
        } catch (Exception $e) {
            // Table might not exist yet
            return [];
        }
    }
    
    /**
     * Find manual entries by company name using fuzzy matching
     */
    private function find_by_company_name($company_name) {
        try {
            // Clean company name for better matching
            $clean_name = $this->clean_company_name($company_name);

            $query = "
                SELECT *,
                       CASE
                           WHEN LOWER(company_name) = LOWER(?) THEN 1.0
                           WHEN LOWER(company_name) LIKE LOWER(?) THEN 0.9
                           WHEN LOWER(?) LIKE LOWER(CONCAT('%', company_name, '%')) THEN 0.8
                           WHEN LOWER(company_name) LIKE LOWER(CONCAT('%', ?, '%')) THEN 0.7
                           ELSE 0.0
                       END as name_similarity
                FROM manual_subscription_entries
                WHERE LOWER(company_name) LIKE LOWER(?)
                   OR LOWER(?) LIKE LOWER(CONCAT('%', company_name, '%'))
                   OR LOWER(company_name) LIKE LOWER(CONCAT('%', ?, '%'))
                HAVING name_similarity > 0.6
                ORDER BY name_similarity DESC
            ";

            $like_pattern = '%' . $clean_name . '%';
            $results = tcs_db_query($query, [
                $clean_name, $like_pattern, $clean_name, $clean_name,
                $like_pattern, $clean_name, $clean_name
            ]);

            return $results ?: [];
        } catch (Exception $e) {
            // Table might not exist yet
            return [];
        }
    }
    
    /**
     * Find manual entries by subscription reference number
     */
    private function find_by_subscription_reference($ref) {
        try {
            $query = "
                SELECT * FROM manual_subscription_entries
                WHERE subscription_reference = ?
                OR subscription_number = ?
                OR reference_number = ?
            ";

            $results = tcs_db_query($query, [$ref, $ref, $ref]);
            return $results ?: [];
        } catch (Exception $e) {
            // Table might not exist yet
            return [];
        }
    }
    
    /**
     * Calculate confidence score for a match
     */
    private function calculate_confidence($subscription_data, $match_data, $match_type) {
        $confidence = 0.0;
        
        switch ($match_type) {
            case 'email':
                $confidence = 0.9; // High confidence for email matches
                
                // Boost confidence if company names also match
                if (!empty($subscription_data['endcust_name']) && !empty($match_data['company_name'])) {
                    $name_similarity = $this->calculate_name_similarity(
                        $subscription_data['endcust_name'], 
                        $match_data['company_name']
                    );
                    $confidence = min(1.0, $confidence + ($name_similarity * 0.1));
                }
                break;
                
            case 'company':
                // Use the name similarity calculated in the query
                $confidence = $match_data['name_similarity'] ?? 0.7;
                
                // Boost if we have any email matches
                if (!empty($subscription_data['endcust_primary_admin_email']) && 
                    !empty($match_data['email_address'])) {
                    if (strtolower($subscription_data['endcust_primary_admin_email']) === 
                        strtolower($match_data['email_address'])) {
                        $confidence = min(1.0, $confidence + 0.2);
                    }
                }
                break;
                
            case 'reference':
                $confidence = 1.0; // Perfect match
                break;
        }
        
        return $confidence;
    }
    
    /**
     * Clean company name for better matching
     */
    private function clean_company_name($name) {
        // Remove common business suffixes and prefixes
        $suffixes = ['ltd', 'limited', 'inc', 'incorporated', 'corp', 'corporation', 
                    'llc', 'plc', 'co', 'company', '&', 'and'];
        
        $clean = strtolower(trim($name));
        
        // Remove punctuation
        $clean = preg_replace('/[^\w\s]/', ' ', $clean);
        
        // Remove common suffixes
        foreach ($suffixes as $suffix) {
            $clean = preg_replace('/\b' . preg_quote($suffix) . '\b/', '', $clean);
        }
        
        // Clean up extra spaces
        $clean = preg_replace('/\s+/', ' ', trim($clean));
        
        return $clean;
    }
    
    /**
     * Calculate similarity between two company names
     */
    private function calculate_name_similarity($name1, $name2) {
        $clean1 = $this->clean_company_name($name1);
        $clean2 = $this->clean_company_name($name2);
        
        // Use Levenshtein distance for similarity
        $max_len = max(strlen($clean1), strlen($clean2));
        if ($max_len === 0) return 0.0;
        
        $distance = levenshtein($clean1, $clean2);
        return 1.0 - ($distance / $max_len);
    }
    
    /**
     * Check if a match is already in the results array
     */
    private function is_duplicate_match($matches, $new_match) {
        foreach ($matches as $existing_match) {
            if (isset($existing_match['id']) && isset($new_match['id']) && 
                $existing_match['id'] === $new_match['id']) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Get all manual subscription entries for admin purposes
     * Includes both manual_subscription_entries table and CSV-based tables
     */
    public function get_all_manual_entries($criteria = []) {
        $all_entries = [];

        // Get entries from manual_subscription_entries table
        try {
            $where_conditions = [];
            $params = [];

            if (!empty($criteria['search'])) {
                $search = '%' . $criteria['search'] . '%';
                $where_conditions[] = "(company_name LIKE ? OR email_address LIKE ? OR subscription_reference LIKE ?)";
                $params = array_merge($params, [$search, $search, $search]);
            }

            $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
            $limit_clause = isset($criteria['limit']) ? 'LIMIT ' . intval($criteria['limit']) : 'LIMIT 100';

            $query = "SELECT *, 'manual_entries' as source_table FROM manual_subscription_entries {$where_clause} ORDER BY created_at DESC {$limit_clause}";

            $manual_entries = tcs_db_query($query, $params) ?: [];
            $all_entries = array_merge($all_entries, $manual_entries);
        } catch (Exception $e) {
            // Table might not exist yet, continue
        }

        // Get entries from CSV-based tables
//        $csv_entries = $this->get_csv_table_entries($criteria);
//        $all_entries = array_merge($all_entries, $csv_entries);

        // Sort by created date if available
        usort($all_entries, function($a, $b) {
            $a_date = $a['created_at'] ?? $a['date_created'] ?? '1970-01-01';
            $b_date = $b['created_at'] ?? $b['date_created'] ?? '1970-01-01';
            return strcmp($b_date, $a_date); // Newest first
        });

        // Apply limit if specified
        if (isset($criteria['limit'])) {
            $all_entries = array_slice($all_entries, 0, intval($criteria['limit']));
        }

        return $all_entries;
    }


    public function get_all_csv_entries($criteria = []) {
        return $this->get_csv_table_entries($criteria);
    }

    /**
     * Get entries from CSV-based tables that might contain subscription data
     */
    private function get_csv_table_entries($criteria = []) {
        $csv_entries = [];

        try {
            // First try to get list of CSV tables from table_configurations
            $csv_tables = [];

            // Check if table_configurations table exists
            $table_exists_query = "SHOW TABLES LIKE 'table_configurations'";
            $table_exists = tcs_db_query($table_exists_query);

            if (!empty($table_exists)) {
                // Table exists, get configured CSV tables
                $config_query = "SELECT table_name, column_mappings, description FROM table_configurations WHERE data_source = 'csv' AND is_active = 1";
                $csv_tables = tcs_db_query($config_query) ?: [];
            } else {
                // Fallback: Look for tables that might be CSV imports
                // Get all tables and filter for potential CSV tables
                $all_tables_query = "SHOW TABLES";
                $all_tables = tcs_db_query($all_tables_query) ?: [];

                foreach ($all_tables as $table_row) {
                    $table_name = array_values($table_row)[0]; // Get table name from result

                    // Skip system tables and known non-CSV tables
                    if ($this->is_potential_csv_table($table_name)) {
                        $csv_tables[] = [
                            'table_name' => $table_name,
                            'column_mappings' => '{}', // Empty mappings for fallback
                            'description' => "Potential CSV table: {$table_name}"
                        ];
                    }
                }
            }

            foreach ($csv_tables as $table_config) {
                $table_name = $table_config['table_name'];
                $column_mappings = json_decode($table_config['column_mappings'], true) ?: [];

                // Check if this table might contain subscription-related data
                if ($this->table_contains_subscription_data($table_name, $column_mappings)) {
                    $table_entries = $this->search_csv_table($table_name, $column_mappings, $criteria);

                    // Add metadata to each entry
                    foreach ($table_entries as &$entry) {
                        $entry['source_table'] = $table_name;
                        $entry['table_description'] = $table_config['description'] ?? '';
                        $entry['can_add_to_unity'] = true;
                    }

                    $csv_entries = array_merge($csv_entries, $table_entries);
                }
            }
        } catch (Exception $e) {
            // Continue without CSV entries if there's an error
        }

        return $csv_entries;
    }

    /**
     * Check if a table contains subscription-related data based on column names
     */
    private function table_contains_subscription_data($table_name, $column_mappings) {
        // Get actual column names from the table
        try {
            $columns_query = "SHOW COLUMNS FROM `{$table_name}`";
            $columns = tcs_db_query($columns_query) ?: [];
            $column_names = array_map(function($col) {
                return strtolower($col['Field']);
            }, $columns);

            // Look for subscription-related keywords in column names
            $subscription_keywords = [
                'subscription', 'company', 'email', 'product', 'license',
                'customer', 'client', 'account', 'reference', 'serial',
                'start_date', 'end_date', 'expiry', 'renewal', 'quantity'
            ];

            foreach ($subscription_keywords as $keyword) {
                foreach ($column_names as $column_name) {
                    if (strpos($column_name, $keyword) !== false) {
                        return true;
                    }
                }
            }

            return false;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Search a CSV table for subscription-related data
     */
    private function search_csv_table($table_name, $column_mappings, $criteria) {
        try {
            $where_conditions = [];
            $params = [];

            // Build search conditions if search term provided
            if (!empty($criteria['search'])) {
                $search = '%' . $criteria['search'] . '%';

                // Get searchable columns
                $columns_query = "SHOW COLUMNS FROM `{$table_name}`";
                $columns = tcs_db_query($columns_query) ?: [];

                $searchable_conditions = [];
                foreach ($columns as $column) {
                    $column_name = $column['Field'];
                    $column_type = strtolower($column['Type']);

                    // Only search text-based columns
                    if (strpos($column_type, 'varchar') !== false ||
                        strpos($column_type, 'text') !== false ||
                        strpos($column_type, 'char') !== false) {
                        $searchable_conditions[] = "`{$column_name}` LIKE ?";
                        $params[] = $search;
                    }
                }

                if (!empty($searchable_conditions)) {
                    $where_conditions[] = '(' . implode(' OR ', $searchable_conditions) . ')';
                }
            }

            $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
            $limit_clause = isset($criteria['limit']) ? 'LIMIT ' . intval($criteria['limit']) : 'LIMIT 50';

            $query = "SELECT * FROM `{$table_name}` {$where_clause} ORDER BY id DESC {$limit_clause}";

            $results = tcs_db_query($query, $params) ?: [];

            // Normalize the data to match subscription format
            return array_map(function($row) use ($table_name) {
                return $this->normalize_csv_entry($row, $table_name);
            }, $results);

        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * Normalize CSV table entry to match subscription format
     */
    private function normalize_csv_entry($entry, $table_name) {
        // Try to map common field names to subscription format
        $normalized = [
            'id' => $entry['id'] ?? uniqid(),
            'source_table' => $table_name,
            'data_source' => 'csv_table'
        ];

        // Map common field patterns
        foreach ($entry as $key => $value) {
            $lower_key = strtolower($key);

            // Company/Customer name mapping
            if (in_array($lower_key, ['company', 'company_name', 'customer', 'customer_name', 'client', 'client_name', 'account_name'])) {
                $normalized['company_name'] = $value;
                $normalized['endcust_name'] = $value;
            }

            // Email mapping
            if (in_array($lower_key, ['email', 'email_address', 'contact_email', 'admin_email', 'customer_email'])) {
                $normalized['email_address'] = $value;
                $normalized['endcust_primary_admin_email'] = $value;
            }

            // Product mapping
            if (in_array($lower_key, ['product', 'product_name', 'software', 'license', 'offering'])) {
                $normalized['product_name'] = $value;
                $normalized['subs_offeringName'] = $value;
            }

            // Reference/Serial mapping
            if (in_array($lower_key, ['reference', 'subscription_reference', 'serial', 'license_key', 'subscription_number'])) {
                $normalized['subscription_reference'] = $value;
                $normalized['subs_subscriptionReferenceNumber'] = $value;
            }

            // Quantity mapping
            if (in_array($lower_key, ['quantity', 'qty', 'licenses', 'seats'])) {
                $normalized['quantity'] = $value;
                $normalized['subs_quantity'] = $value;
            }

            // Date mappings
            if (in_array($lower_key, ['start_date', 'date_start', 'purchase_date'])) {
                $normalized['start_date'] = $value;
                $normalized['subs_startDate'] = $value;
            }

            if (in_array($lower_key, ['end_date', 'expiry_date', 'renewal_date', 'date_end'])) {
                $normalized['end_date'] = $value;
                $normalized['subs_endDate'] = $value;
            }

            // Contact name mapping
            if (in_array($lower_key, ['contact', 'contact_name', 'name', 'customer_contact'])) {
                $normalized['contact_name'] = $value;
            }

            // Keep original field as well
            $normalized[$key] = $value;
        }

        return $normalized;
    }

    /**
     * Check if a table name suggests it might be a CSV import table
     */
    private function is_potential_csv_table($table_name) {
        // Skip system tables and known application tables
        if (str_starts_with($table_name, 'autobooks_') && str_ends_with($table_name, '_data') ) return true;
        return false;
    }
}
//
//
//        // If table has typical CSV-like structure, consider it
//        try {
//            $columns_query = "SHOW COLUMNS FROM `{$table_name}`";
//            $columns = tcs_db_query($columns_query) ?: [];
//
//            // Look for common CSV import patterns
//            $has_id = false;
//            $has_timestamps = false;
//            $text_columns = 0;
//
//            foreach ($columns as $column) {
//                $field_name = strtolower($column['Field']);
//                $field_type = strtolower($column['Type']);
//
//                if ($field_name === 'id') $has_id = true;
//                if (in_array($field_name, ['created_at', 'updated_at'])) $has_timestamps = true;
//                if (strpos($field_type, 'varchar') !== false || strpos($field_type, 'text') !== false) {
//                    $text_columns++;
//                }
//            }
//
//            // If it has typical CSV import structure (id, timestamps, multiple text columns)
//            return $has_id && $text_columns >= 3;
//
//        } catch (Exception $e) {
//            return false;
//        }
    //}

