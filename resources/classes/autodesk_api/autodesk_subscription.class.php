<?php

namespace autodesk_api;
use system\data_importer;

class autodesk_subscription {

    public $subscriptionId;
    private autodesk_api_interface $api;
    public $debugLog;
    private autodesk_subscriptions $subscriptions;
    public static array $subscription_column_mapping = [
    "soldTo" => [
        "table" => "autodesk_accounts",
        "key" => "account_csn",
        "columns" => [
            "accounts.soldTo.csn" => "account_csn",
            "accounts.soldTo.name" => "name",
        ]
    ],
    "solutionProvider" => [
        "table" => "autodesk_accounts",
        "key" => "account_csn",
        "columns" => [
            "accounts.solutionProvider.csn" => "account_csn",
            "accounts.solutionProvider.name" => "name",
            "accounts.solutionProvider.localLanguageName" => "local_language_name",
            "accounts.solutionProvider.type" => "account_type",
            "accounts.solutionProvider.address1" => "address1",
            "accounts.solutionProvider.address2" => "address2",
            "accounts.solutionProvider.address3" => "address3",
            "accounts.solutionProvider.city" => "city",
            "accounts.solutionProvider.stateProvince" => "state_province",
            "accounts.solutionProvider.postalCode" => "postal_code",
            "accounts.solutionProvider.country" => "country",
            "accounts.solutionProvider.stateProvinceCode" => "state_province_code",
            "accounts.solutionProvider.countryCode" => "country_code",
        ]

    ],
    "nurtureReseller" => [
        "table" => "autodesk_accounts",
        "key" => "account_csn",
        "columns" => [
            "accounts.nurtureReseller.csn" => "account_csn",
            "accounts.nurtureReseller.name" => "name",
            "accounts.nurtureReseller.lockdate" => "lockdate",
        ]
    ],
    "endCustomer" => [
        "table" => "autodesk_accounts",
        "key" => "account_csn",
        "columns" => [
            "endCustomer.account.endCustomerCsn" => "account_csn",
            "endCustomer.account.name" => "name",
            "endCustomer.account.type" => "account_type",
            "endCustomer.account.address1" => "address1",
            "endCustomer.account.address2" => "address2",
            "endCustomer.account.address3" => "address3",
            "endCustomer.account.city" => "city",
            "endCustomer.account.stateProvince" => "state_province",
            "endCustomer.account.postalCode" => "postal_code",
            "endCustomer.account.country" => "country",
            "endCustomer.account.individualFlag" => "individual_flag",
            "endCustomer.account.namedAccountFlag" => "named_account_flag",
            "endCustomer.account.namedAccountGroup" => "named_account_group",
            "endCustomer.account.parentIndustryGroup" => "parent_industry_group",
            "endCustomer.account.parentIndustrySegment" => "parent_industry_segment",
            "endCustomer.account.primaryAdminFirstName" => "primary_admin_first_name",
            "endCustomer.account.primaryAdminLastName" => "primary_admin_last_name",
            "endCustomer.account.primaryAdminEmail" => "primary_admin_email",
            "endCustomer.account.teamId" => "team_id",
            "endCustomer.account.teamName" => "team_name",
            "endCustomer.purchaser.first" => "first_name",
            "endCustomer.purchaser.last" => "last_name",
            "endCustomer.purchaser.email" => "email",
            "endCustomer.purchaser.status" => "status",
            "endCustomer.purchaser.portalRegistration" => "portal_registration",
            "endCustomer.purchaser.doNotCall" => "do_not_call",
            "endCustomer.purchaser.doNotEmail" => "do_not_email",
            "endCustomer.purchaser.doNotMail" => "do_not_mail",
            "endCustomer.account.stateProvinceCode" => "state_province_code",
            "endCustomer.account.countryCode" => "country_code",
        ]
    ],
    "subscriptions" => [
        "table" => "autodesk_subscriptions",
        "key" => "subscriptionId",
        "columns" => [
            "subscriptionId" => "subscriptionId",
            "subscriptionReferenceNumber" => "subscriptionReferenceNumber",
            "quantity" => "quantity",
            "status" => "status",
            "startDate" => "startDate",
            "endDate" => "endDate",
            "term" => "term",
            "billingBehavior" => "billingBehavior",
            "billingFrequency" => "billingFrequency",
            "offeringId" => "offeringId",
            "offeringCode" => "offeringCode",
            "offeringName" => "offeringName",
            "renewalCounter" => "renewalCounter",
            "autoRenew" => "autoRenew",
            "recordType" => "recordType",
            "intendedUsage" => "intendedUsage",
            "connectivity" => "connectivity",
            "connectivityInterval" => "connectivityInterval",
            "opportunityNumber" => "opportunityNumber",
            "servicePlan" => "servicePlan",
            "accessModel" => "accessModel",
            "paymentMethod" => "paymentMethod"
        ],
        "extra" => [
            "nurtureReseller_id" => "<group_insert_id>nurtureReseller</group_insert_id>",
            "endCustomer_id" => "<group_insert_id>endCustomer</group_insert_id>",
            "solutionProvider_id" => "<group_insert_id>solutionProvider</group_insert_id>",
            "soldTo_id" => "<group_insert_id>soldTo</group_insert_id>",
            "nurtureReseller_csn" => "accounts.nurtureReseller.csn",
            "endCustomer_csn" => "endCustomer.account.endCustomerCsn",
            "solutionProvider_csn" => "accounts.solutionProvider.csn",
            "soldTo_csn" => "accounts.soldTo.csn",
        ]
    ]
];
    public static array $subscription_csv_column_mapping = [
        "soldTo" => [
            "table" => "autodesk_accounts",
            "key" => "account_csn",
            "columns" => [
                "accounts.soldTo.csn" => "account_csn",
                "accounts.soldTo.name" => "name",
            ]
        ],
        "solutionProvider" => [
            "table" => "autodesk_accounts",
            "key" => "account_csn",
            "columns" => [
                "accounts.solutionProvider.csn" => "account_csn",
                "accounts.solutionProvider.name" => "name",
                "accounts.solutionProvider.localLanguageName" => "local_language_name",
                "accounts.solutionProvider.type" => "account_type",
                "accounts.solutionProvider.address1" => "address1",
                "accounts.solutionProvider.address2" => "address2",
                "accounts.solutionProvider.address3" => "address3",
                "accounts.solutionProvider.city" => "city",
                "accounts.solutionProvider.stateProvince" => "state_province",
                "accounts.solutionProvider.postalCode" => "postal_code",
                "accounts.solutionProvider.country" => "country",
                "accounts.solutionProvider.stateProvinceCode" => "state_province_code",
                "accounts.solutionProvider.countryCode" => "country_code",
            ]

        ],
        "nurtureReseller" => [
            "table" => "autodesk_accounts",
            "key" => "account_csn",
            "columns" => [
                "accounts.nurtureReseller.csn" => "account_csn",
                "accounts.nurtureReseller.name" => "name",
                "accounts.nurtureReseller.lockdate" => "lockdate",
            ]
        ],
        "endCustomer" => [
            "table" => "autodesk_accounts",
            "key" => "account_csn",
            "columns" => [
                "endCustomer.account.endCustomerCsn" => "account_csn",
                "endCustomer.account.name" => "name",
                "endCustomer.account.type" => "account_type",
                "endCustomer.account.address1" => "address1",
                "endCustomer.account.address2" => "address2",
                "endCustomer.account.address3" => "address3",
                "endCustomer.account.city" => "city",
                "endCustomer.account.stateProvince" => "state_province",
                "endCustomer.account.postalCode" => "postal_code",
                "endCustomer.account.country" => "country",
                "endCustomer.account.individualFlag" => "individual_flag",
                "endCustomer.account.namedAccountFlag" => "named_account_flag",
                "endCustomer.account.namedAccountGroup" => "named_account_group",
                "endCustomer.account.parentIndustryGroup" => "parent_industry_group",
                "endCustomer.account.parentIndustrySegment" => "parent_industry_segment",
                "endCustomer.account.primaryAdminFirstName" => "primary_admin_first_name",
                "endCustomer.account.primaryAdminLastName" => "primary_admin_last_name",
                "endCustomer.account.primaryAdminEmail" => "primary_admin_email",
                "endCustomer.account.teamId" => "team_id",
                "endCustomer.account.teamName" => "team_name",
                "endCustomer.purchaser.first" => "first_name",
                "endCustomer.purchaser.last" => "last_name",
                "endCustomer.purchaser.email" => "email",
                "endCustomer.purchaser.status" => "status",
                "endCustomer.purchaser.portalRegistration" => "portal_registration",
                "endCustomer.purchaser.doNotCall" => "do_not_call",
                "endCustomer.purchaser.doNotEmail" => "do_not_email",
                "endCustomer.purchaser.doNotMail" => "do_not_mail",
                "endCustomer.account.stateProvinceCode" => "state_province_code",
                "endCustomer.account.countryCode" => "country_code",
            ]
        ],
        "subscriptions" => [
            "table" => "autodesk_subscriptions",
            "key" => "subscriptionId",
            "columns" => [
                "subscriptionId" => "subscriptionId",
                "subscriptionReferenceNumber" => "subscriptionReferenceNumber",
                "quantity" => "quantity",
                "status" => "status",
                "startDate" => "startDate",
                "endDate" => "endDate",
                "term" => "term",
                "billingBehavior" => "billingBehavior",
                "billingFrequency" => "billingFrequency",
                "offeringId" => "offeringId",
                "offeringCode" => "offeringCode",
                "offeringName" => "offeringName",
                "renewalCounter" => "renewalCounter",
                "autoRenew" => "autoRenew",
                "recordType" => "recordType",
                "intendedUsage" => "intendedUsage",
                "connectivity" => "connectivity",
                "connectivityInterval" => "connectivityInterval",
                "opportunityNumber" => "opportunityNumber",
                "servicePlan" => "servicePlan",
                "accessModel" => "accessModel",
                "paymentMethod" => "paymentMethod"
            ],
            "extra" => [
                "nurtureReseller_id" => "<group_insert_id>nurtureReseller</group_insert_id>",
                "endCustomer_id" => "<group_insert_id>endCustomer</group_insert_id>",
                "solutionProvider_id" => "<group_insert_id>solutionProvider</group_insert_id>",
                "soldTo_id" => "<group_insert_id>soldTo</group_insert_id>",
                "nurtureReseller_csn" => "accounts.nurtureReseller.csn",
                "endCustomer_csn" => "endCustomer.account.endCustomerCsn",
                "solutionProvider_csn" => "accounts.solutionProvider.csn",
                "soldTo_csn" => "accounts.soldTo.csn",
            ]
        ]
    ];
    public static array $subscription_table_schema = [
            "subs" => [
                "extra_fields" => [
                    "enddatediff" => [
                        "sql" => "CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END AS subs_enddatediff",
                    ]
                ],
                'query' => "FROM autodesk_subscriptions subs"
            ],
            "soldto" => [
                'query' => "LEFT JOIN autodesk_accounts soldto ON subs.soldTo_id = soldto.id"
            ],
            "endcust" => [
                'query' => "LEFT JOIN autodesk_accounts endcust ON subs.endCustomer_id = endcust.id"
            ],
            "solpro" => [
                'query' => "LEFT JOIN autodesk_accounts solpro ON subs.solutionProvider_id = solpro.id"
            ],
            "resell" => [
                'query' => "LEFT JOIN autodesk_accounts resell ON subs.nurtureReseller_id = resell.id"
            ],
            "hist" => [
                'query' => "LEFT JOIN autodesk_subscription_history hist ON subs.last_email_history_id = hist.id"
            ]
    ];

    public static array $subscription_history_table_schema = [
        "hist" => [
            'query' => "FROM autodesk_subscription_history hist"
        ],
        "users" => [
            'query' => "LEFT JOIN autobooks_users users ON hist.user_id = users.id"
        ],
        'subs' => [
            'query' => "LEFT JOIN autodesk_subscriptions subs ON hist.subscription_id = subs.id"
        ]
    ];

    public function __construct($api)    {
        $this->api = $api;
    }

    public function get($db, $criteria)    {
        return $this->database_get($db, $criteria);
    }
    public function gethistory($db, $criteria){
         return $this->database_get_subscription_history($db, $criteria);
    }

    private function database_get($db_data = [], $criteria = [], $get_distinct = false)    {
        $db_data = array_filter($db_data);
        $table_schema = self::$subscription_table_schema;
        $default_criteria = [];

        [$criteria_string, $criteria_cols, $criteria_tabs] = tcs_db_build_criteria(array_merge($default_criteria, $criteria), $table_schema);
        $table_data = tcs_db_build_tables($db_data, $table_schema, $criteria_cols, $criteria_tabs);

        $distinct = $get_distinct ? "DISTINCT " : "";
        $query = "SELECT {$distinct}{$table_data['columns']} {$table_data['tables']} {$criteria_string}";
        print_rr(i: $query, l: 'finalq', fl: true);
        //  convert_column_names($query);
        $record = tcs_db_query($query);
        return $record[0];
    }

    private function database_get_subscription_history($db_data = [], $criteria = [], $get_distinct = false)    {
        $db_data = array_filter($db_data);
        $table_schema = self::$subscription_history_table_schema;
        $default_criteria = [];

        [$criteria_string, $criteria_cols, $criteria_tabs] = tcs_db_build_criteria(array_merge($default_criteria, $criteria), $table_schema);
        $table_data = tcs_db_build_tables($db_data, $table_schema, $criteria_cols, $criteria_tabs);

        $distinct = $get_distinct ? "DISTINCT " : "";
        $query = "SELECT {$distinct}{$table_data['columns']} {$table_data['tables']} {$criteria_string}";
        print_rr(i: $query, l: 'finalq', fl: true);
        //  convert_column_names($query);
        $records = tcs_db_query($query);
        return $records;
    }

    public function get_from_api($id, $format = 'assoc')    {
        $criteria = [];
        if (strpos($id, '-'))
            $criteria["filter[subscriptionReferenceNumber]"] = $id;
        else
            $criteria["filter[subscriptionId]"] = $id;
        //print_rr($criteria, 'criteria');
        return $this->api->search_autodesk_subscriptions($criteria,$format);
    }

    private function database_get_history($db_data = [], $criteria = [], $get_distinct = false)    {
        if (!is_array($criteria)) {
            throw new InvalidArgumentException('Expected $criteria to be an array.');
        }
        if (count($db_data) == 0) $db_data = [];

        $default_criteria = [ ];
        $query = build_select_query($db_data, self::$subscription_history_table_schema, array_merge($default_criteria, $criteria), $get_distinct);

        print_rr(i: $query, l: 'finalhistq', fl: true);
        //  convert_column_names($query);
        return tcs_db_query($query['text']);
    }

    public function get_history($id, $db_data = [], $criteria = [])    {
        print_rr($criteria, 'tivvity criteria');
        return $this->database_get_history($db_data, ['where' => ['hist.customer_id' => ['=', $id]]]);
    }

    private function database_insert_history($db_data = [])   {
    print_rr($db_data, 'insert_history_item poop2');
        $query = "INSERT INTO `autodesk_subscription_history`(
                        `id`,
                        `subscription_id`,
                        `subscription_reference_number`,
                        `date`,
                        `media`,
                        `message`,
                        `user_id`,
                        `email_address`,
                        `triggered_by`,
                        `result`
        ) VALUES (
                        NULL,
                        '{$db_data['id']}',
                        '{$db_data['subscriptionReferenceNumber']}',
                        now(),
                        '{$db_data['media']}',
                        '{$db_data['message']}',
                         {$db_data['user_id']},
                        '{$db_data['email_address']}',
                        '{$db_data['triggered_by']}',
                        '{$db_data['result']}'
        )";
        return tcs_db_query($query);
    }

    public function insert_history_item($db_data = []){
        print_rr($db_data, 'insert_history_item poop');
        if (!is_array($db_data)) {
            return tcs_log(print_rr(i:'Expected $db_data to be an array.',r:true,fl: true));
        }
        if (!isset($db_data['id']) || !isset($db_data['subscriptionReferenceNumber'])) {
            return tcs_log(print_rr(i:'Expected $db_data to contain subs_id and subs_subscriptionReferenceNumber.',r:true,fl: true));
        }
        $defaults = [
            'media' => 'Note',
            'target' => 'subscription',
            'target_reference' => $db_data['subscriptionReferenceNumber'],
            'user_id' => null,
            'triggered_by' => '',
            'email_address' => '',
            'message' => '',
            'result' => ''
        ];
        return $this->database_insert_history(array_merge($defaults, $db_data));
    }
    public static function update_from_api($json_payload = null)    {
        $mapped_keys = [];
        $skipped_keys = [];

        // Log the start of the process
        tcs_log('Starting subscription change processing at ' . date('Y-m-d H:i:s'), 'subscription_update');

        try {
            // If no payload was passed, try to get it from the request
            if ($json_payload === null) {
                // Get the raw POST data
                $raw_post_data = file_get_contents('php://input');
                $json_payload = json_decode($raw_post_data, true);

                if (!$json_payload) {
                    throw new \Exception('Invalid JSON payload received');
                }
            }

            $mapping = self::$subscription_column_mapping;

            // Log the mapping for debugging
            tcs_log('Subscription column mapping retrieved', 'subscription_update');

            $query_sql = '';
            $query_array = [];

            // Log the incoming payload
            tcs_log('Incoming payload: ' . print_r($json_payload, true), 'subscription_update');

            // Extract subscription ID from the payload
            $subscriptionId = $json_payload['payload']['subscriptionId'] ?? null;
            $subscriptionReferenceNumber = $json_payload['payload']['subscriptionReferenceNumber'] ?? null;

            if (!$subscriptionId) {
                throw new \Exception('No subscription ID found in payload');
            }
            $db = tcs_db_query("select subscriptionId, subscriptionReferenceNumber from autodesk_subscriptions where subscriptionId = :subscriptionId OR subscriptionReferenceNumber = :subscriptionReferenceNumber limit 1", [":subscriptionId" => $subscriptionId, ":subscriptionReferenceNumber" => $subscriptionReferenceNumber]);
            if (!is_countable($db) || count($db) == 0) {
                tcs_log("Subscription not found in database, getting from API: ", 'subscription_update');
                $api = new autodesk_api();
                $subs = $api->subscription->get_from_api($subscriptionId, 'json');

                if (!isset($subs['body']['results']) || empty($subs['body']['results'])) {
                    tcs_log("ERROR: Failed to get subscription data from API", 'subscription_update');
                    throw new \Exception('Failed to get subscription data from API');
                }

                $json_data = $subs['body']['results'];
                tcs_log("Got subscription from api: " . print_r($json_data, true), 'subscription_update');

                // Process the data and capture the result
                $result = data_importer::process_json_data(
                    mapping: $mapping,
                    json_data: $json_data
                );

                // Check if the import was successful
                if (isset($result['success']) && $result['success']) {
                    tcs_log("Subscription import successful: " . $result['message'], 'subscription_update');
                } else {
                    tcs_log("ERROR: Subscription import failed: " . ($result['error'] ?? 'Unknown error'), 'subscription_update');
                }

                return $result;
            }

            //$autodesk = new autodesk_api;
            //subscription = $autodesk->subscription->get_from_api($subscriptionId, 'json');
           // $json_data = json_decode($subscription['body']);
           // tcs_log("Got subscription: " . print_r($subscription , true), 'subscription_update');

            //$output = autodesk_api::process_json_data($mapping, $json_data);
             //Process payload

             foreach ($json_payload['payload'] as $key => $value) {
                 $found = false;
                 foreach ($mapping as $map_key => $table){
                     if (!empty($table['columns'][$key])) {
                         $found = true;

                         $mapping_current = [$table['table'], $key];
                         $table_Name = $table['table'];

                         if (!empty($table_Name) && !empty($key)) {
                             $query_array['tables'][$table_Name]['fields'][$key] = var_export($value, true);
                             // Log successful mapping
                             tcs_log("found key: {$table['columns'][$key]} and mapped to {$table_Name}.{$key}", 'subscription_update');
                             $mapped_keys[$table_Name] = $key;
                         }else{
                             tcs_log("found key: {$table['columns'][$key]} but \$table_Name ({$table_Name}) and \$key ({$key}) are empty", 'subscription_update');
                         }
                         break;
                     }
                 }

                 if (!$found){
                     // Log skipped keys
                     $skipped_keys[] = "Skipped unmapped key: $key";
                     tcs_log("Skipped unmapped key: $key", 'subscription_update');
                 }
            }
            $query_result = null;
             $response=[];
             foreach ($query_array['tables'] as $table => $fields) {
                 $set_fields = [];
                 foreach ($fields['fields'] as $key => $value) {
                     $set_fields[] = "{$key} = {$value}";
                 }

                 $set_string = implode(', ', $set_fields);
                 $query_sql .= "INSERT INTO {$table} SET {$set_string} " .
                     "ON DUPLICATE KEY UPDATE {$set_string};" . PHP_EOL;
                 // Execute the SQL query
                 $query_result = tep_db_query($query_sql);
                 $response[] = [
                     'status' => 'success',
                     'message' => "{$table} updated successfull",
                     'query_sql' => $query_sql,
                     'affected_rows' => tep_db_affected_rows($query_result)
                 ];
             }

            // Log successful completion
            return tcs_log(json_encode($response, JSON_PRETTY_PRINT), 'subscription_update');

        } catch (\Exception $e) {
            // Log any exceptions that occur
            tcs_log('Error in subscription change processing: ' . $e->getMessage(), 'subscription_update');
            tcs_log(PHP_EOL . 'mapped: ' . print_r($mapped_keys, true) . PHP_EOL .
                   'Skipped: ' . print_r($skipped_keys, true), 'subscription_update');
            tcs_log('Trace: ' . $e->getTraceAsString(), 'subscription_update');

            // Return an error response
            $response = [
                'status' => 'error',
                'message' => $e->getMessage()
            ];

            return json_encode($response);
        }
    }

}