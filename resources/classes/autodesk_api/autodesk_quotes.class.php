<?php

namespace autodesk_api;
use http\Exception;
use system\users;
use product_attributes\product_attributes;
use const http\Client\Curl\AUTH_ANY;

class autodesk_quotes
{
    private autodesk_api_interface $api;
    private autodesk_authenticator $auth;
    private autodesk_quote $quote;
    public $debugLog;
    public $customer = [];
    public $products = [];
    public $currency = "GBP";
    public $account = ['accountCsn' => ADWS_CSN];
    public $opportunityNumber;
    public $debug = false;
    public $quote_data;
    public $quote_status;
    public $lineItems = [];
    public static array $quote_column_mapping = [];
    
    
    public function __construct($api,$auth)
    {
        $this->api = $api;
        $this->auth = $auth;
        $this->quote = new autodesk_quote($api);
        self::$quote_column_mapping = autodesk_quote::$quote_column_mapping;
    }

    public function get_all($db_data, $criteria)
    {
        return $this->database_get_current_quotes($db_data, $criteria);
    }

    public function get_distinct()
    {
        return $this->database_get_current_quotes(get_distinct: true);
    }

    public function get_current()
    {
        return $this->database_get_current_quotes();
    }

    private function get_quote_column_mapping() {
        return autodesk_quote::quote_column_mapping();
    }
    public static function import_all($json_payload) {
        return self::import_autodesk_quotes_into_database(download: $json_payload['payload']['downloadLink']);
    }

    public static function import_autodesk_quotes_into_database(null|string $json_file_path = FS_DOC_ROOT . DS . 'feeds/quotes.json', null|bool|string $download = false):array {
        $enc_file_path = FS_DOC_ROOT . DS . 'feeds/quotes.json.zip.enc';
        if ($download) {
            tcs_log("Download: " . $download,"quote_import");
            $json_file_path = self::download_autodesk_quote_export($download,$enc_file_path,$json_file_path);
        }
        tcs_log("Looking for file: " . $json_file_path,"quote_import");
        if ($json_file_path === null) return ['status' => 'fail', 'response' => tcs_log('Failed, No file path found.',"quote_import")];
        tcs_log("Importing quotes from: " . $json_file_path,"quote_import");
        return autodesk_api::import_data_into_database(
            mapping: self::$quote_column_mapping,
            file_path: $json_file_path,
            file_type: 'json',
            debug: true,
            log_target: 'quote_import'
        );
    }

    private function database_get_current_quotes($db_data = [], $criteria = [], $get_distinct = false)    {
        if (!is_array($criteria)) {
            throw new InvalidArgumentException('Expected $criteria to be an array.');
        }
        if (count($db_data) == 0) $db_data = [
            'table_id' => 'quotes',
            'db_table' => 'quotes',
            'columns' => []
        ];
        $table_data_schema = [
            "quotes" => [
                'query' => "FROM autodesk_quotes quotes"
            ],
            "endcust" => [
                'query' => "LEFT JOIN autodesk_accounts endcust on endcust.id = quotes.end_customer"
            ],
            "quotecontact" => [
                'query' => "LEFT JOIN autodesk_accounts quotecontact on quotecontact.id = quotes.quote_contact",
                 "extra_fields" => [
                    "name" => [
                        "sql" => "Concat(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name",
                    ]
                ],
            ],
        ];
        $default_criteria = [];
        $query = build_select_query($db_data, $table_data_schema, array_merge($default_criteria, $criteria), $get_distinct);
        print_rr($query, 'finalq');
        return tcs_db_query($query['text']);
    }

    public function get_from_api()    {
        try {
            $get_quotes = $this->api->get_autodesk_quotes();
            if ($get_quotes['status'] == 'fail') {
                return ['status' => 'fail', 'failing' => 'get_autodesk_quotes', 'response' => $get_quotes, 'log' => $this->debugLog];
            }
            //print_rr($get_quotes['response'];
            $response = $get_quotes['body'];
            //tcs_log("Updating quote export database with: " . $response,"quote_import");
            $this->database_update_api_export_data(json_encode($response));
        } catch (Exception $e) {
            $response = $e->getMessage();
            return ['status' => 'fail', 'response' => $response, 'log' => $this->debugLog];
        }

        return ["status" => "Requested, it's the webhook's problem now", 'log' => $this->debugLog];
    }

    public static function download_autodesk_quote_export($file_uri,$enc_file_path,$json_file_path,$decrypt = true) {
        tcs_log("Download: " . $file_uri,"quote_import");
        $input_file = file_get_contents($file_uri);                            /* Download the CSV file and save it to the specified path                */
        file_put_contents($enc_file_path, $input_file);
        if ($decrypt) {
            autodesk_api::log_message("Getting decryption info", "quote_import");
            $info = json_decode(autodesk_quotes::get_api_export_data());
            autodesk_api::log_message("Decrypting file with: " . print_r($info, true), "quote_import");
            //return ['status' => 'success', 'response' => print_r($info, true)];
            $decrypted_file = autodesk_authenticator::decrypt_file($enc_file_path, $json_file_path, $info->password, "quote_import");
        }
        return $enc_file_path;
    }

    private function database_update_api_export_data($body)    {
        $jsonData = json_encode($body);
        // Check if JSON encoding was successful
        if ($jsonData === false) {
            // Log the error or throw an exception
            error_log("Failed to encode body to JSON: " . json_last_error_msg(),"quote_import");
            return false;
        }

        $query_sql = "INSERT INTO autodesk_storage (`autodesk_storage_key`, `autodesk_storage_data`)
                      VALUES (:autodesk_storage_key, :autodesk_storage_data)
                      ON DUPLICATE KEY UPDATE `autodesk_storage_data` = :autodesk_storage_data_update";

        tcs_log("Updating quote export database with: " . ' and \n' . print_r($jsonData,true) ,"quote_import");
        try {
            return tep_db_query($query_sql, null, [
                ":autodesk_storage_key" => 'quote_export_data',
                ":autodesk_storage_data" => $jsonData,
                ":autodesk_storage_data_update" => $jsonData
            ]);
        } catch (\system\DatabaseException $e) {
            // Enhanced logging for database-specific errors
            tcs_log([
                'action' => 'quote_export_data_update_failed',
                'error_message' => $e->getMessage(),
                'query' => $e->getQuery(),
                'parameters' => $e->getParams(),
                'detailed_message' => $e->getDetailedMessage(),
                'data_size' => strlen($jsonData)
            ], 'autodesk_quotes_errors', true);
            return false;
        } catch (\Exception $e) {
            // Fallback for non-database errors
            tcs_log("Database query failed: " . $e->getMessage(), 'autodesk_quotes_errors');
            return false;
        }
    }

    public static function get_api_export_data()    {
        $query_sql = "select * from autodesk_storage where autodesk_storage_key = 'quote_export_data' limit 1";
        $value = tcs_db_query($query_sql);
        return json_decode($value[0]['autodesk_storage_data']);
    }
    public function database_get_api_export_data()    {
        $query_sql = "select * from autodesk_storage where autodesk_storage_key = 'quote_export_data' limit 1";
        $value = tcs_db_query($query_sql);
        return json_decode($value[0]['autodesk_storage_data']);
    }

    private function database_get_product_extra($orders_id, $products_id = null) {
        $query_sql = "SELECT * FROM orders_products_autodesk WHERE orders_id = :orders_id";
        $params = [":orders_id" => $orders_id];
        if ($products_id != null) {
            $query_sql .= " and products_id = :products_id";
            $params[':products_id'] = $products_id;
        }
        $result = tep_db_query($query_sql, null, $params);
        ////print_rr($result, 'database_get_product_extra query_sql');
        if ($products_id) return tep_db_fetch_array($result);
        return tep_db_fetch_all($result);
    }
}
?>