<?php
namespace autodesk_api;

class autodesk_customers {
    private autodesk_api_interface $api;
    public $debugLog;

    public static array $customer_history_table_schema = [
        "hist" => [
            'query' => "FROM autodesk_history hist"
        ],
        'cust' => [
            'query' => "JOIN autodesk_accounts cust ON hist.customer_csn = cust.account_csn"
        ],
        'subs' => [
            'query' => "JOIN autodesk_subscriptions subs ON hist.customer_csn = cust.account_csn"
        ],
        "users" => [
            'query' => "LEFT JOIN autobooks_users users ON hist.user_id = users.id"
        ],
    ];



    public function __construct($api) {
        $this->api = $api;
    }



   public function get($db_data,$criteria) {
       $out = $this->database_get($db_data, $criteria);
       return $out;
   }

    private function database_get($db_data, $criteria,$get_distinct = false) {
        $default_criteria = [
            'limit' => 1
             ];
        $table_schema = [
            "endcust" => [
                "query" => "FROM autodesk_accounts endcust"
            ]
        ];
        [$criteria_string,$criteria_cols,$criteria_tabs] = tcs_db_build_criteria(array_merge($default_criteria, $criteria),$table_schema);
        $table_data = tcs_db_build_tables($db_data,$table_schema,$criteria_cols,$criteria_tabs);
        $distinct = $get_distinct ? "DISTINCT " : "";
        $query = "SELECT {$distinct}{$table_data['columns']} {$table_data['tables']}  {$criteria_string}";
        print_rr(i:$query,l:'finalq',fl: true);
        //  convert_column_names($query);
        return tcs_db_query($query)[0];
    }

//    public function get_all($criteria = []) {
//        return $this->database_get_all($criteria);
//    }
//
//    private function database_get_all($criteria = [], $order_by = []) {
//           $default_criteria = [
//             "search_columns" => ["name", "localLanguageName", "address1", "address2", "address3", "city", "stateProvince", "postalCode", "country", "primaryAdminFirstName", "primaryAdminLastName", "primaryAdminEmail", "first", "last", "email"],
//
//            "order_by" => " name ASC",
//            "limit" => "100"
//        ];
//       
//
//        $criteria_string = "";
//
//        $criteria_string = tcs_db_build_criteria(array_merge($default_criteria, $criteria));
//        print_rr($criteria_string);
//        return tcs_db_query(
//            "SELECT  *
//            FROM autodesk_accounts            
//            {$criteria_string}"
//        );
//    }

    public function get_all($db_data, $criteria = ["limit" => 100] )  {
        print_rr($criteria, 'database');
        return $this->database_get_all($db_data, $criteria);
    }

    public function get_distinct($db_data, $criteria = ["limit" => 100] )  {
        print_rr($criteria, 'database');
        return $this->database_get_all($db_data, $criteria, true);
    }

    private function database_get_all($db_data, $criteria,$get_distinct = false)   {
        $default_criteria = [
            "limit" => 100,
            "order_by" => "endcust.name",
            "search_columns" => ["endcust_name", "endcust_local_language_name", "endcust_address_1", "endcust_address_2", "endcust_address_3", "endcust_city", "endcust_state_province", "endcust_postal_code", "endcust_country", "endcust_primary_admin_first_name", "endcust_primary_admin_last_name", "endcust_primary_admin_email", "endcust_first", "endcust_last", "endcust_email"],
        ];
        $table_schema = [
            "endcust" => [
                "query" => "FROM autodesk_accounts endcust"
            ]
        ];
        [$criteria_string,$criteria_cols,$criteria_tabs] = tcs_db_build_criteria(array_merge($default_criteria, $criteria),$table_schema);
        $table_data = tcs_db_build_tables($db_data,$table_schema,$criteria_cols,$criteria_tabs);
        $distinct = $get_distinct ? "DISTINCT " : "";
        $query = "SELECT {$distinct}{$table_data['columns']} {$table_data['tables']}  {$criteria_string}";
        print_rr(i:$query,l:'finalq',fl: true);
        //  convert_column_names($query);
        return tcs_db_query($query);
    }




    private function database_get_subscriptions($customer_id) {
        return tcs_db_query("SELECT * FROM autodesk_subscriptions WHERE endCustomer_id = '{$customer_id}'");
    }

    public function search($customers_id, $email) {
        $params = [":customers_id" => $customers_id, ":email" => $email];
        $cust_search_db = tep_db_query("SELECT * FROM `customers_autodesk` WHERE `customers_email_address` = :email and `customers_id` = :customers_id LIMIT 1", null, $params);
        if (tep_db_num_rows($cust_search_db) > 0) {
            return tep_db_fetch_array($cust_search_db);
        }
        $cust_search = $this->api->search_autodesk_customers(["contactEmail" => $email]);
        ////print_rr($this->debugLog, 'search_autodesk_customers');
        $params = [":customers_id" => $customers_id, ":accountcsn" => $cust_search['body']['results'][0]['account_csn'], ":email" => $email];
        tep_db_query("INSERT INTO `customers_autodesk` SET `accountCsn` = :accountcsn, `customers_email_address`  = :email, `customers_id` = :customers_id", null, $params);
        return $cust_search;
    }

    public function get_history($csn, $db_data = [], $criteria = [])    {
        print_rr($criteria, 'tivvity criteria');
        return $this->database_get_history($db_data, ['where' => ['hist.customer_csn' => ['=', $csn]]]);
    }


    private function database_get_history($db_data = [], $criteria = [], $get_distinct = false)    {
        if (!is_array($criteria)) {
            throw new InvalidArgumentException('Expected $criteria to be an array.');
        }
        if (count($db_data) == 0) $db_data = [];

        $default_criteria = [ ];
        $query = build_select_query($db_data, self::$customer_history_table_schema, array_merge($default_criteria, $criteria), $get_distinct);

        print_rr(i: $query, l: 'finalhistq', fl: true);
        //  convert_column_names($query);
        return tcs_db_query($query['text']);
    }
}
?>