<?php

namespace autodesk_api;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class autodesk_api_interface {


    private autodesk_authenticator $auth;
    private $httpClient;
    private $debug;
    private $csn;

    public $debugLog;
    public $current_quotes;
    public function __construct() {
        $this->auth = new autodesk_authenticator();
        $this->httpClient = new Client();
        $this->csn = ADWS_CSN;
    }

    private function call($connection, $cache = true, $cacheTTL = 3600, $downloadDir = null,$format = 'assoc') {
        // Generate a unique cache key using the serialized connection array
        $cacheKey = md5(serialize($connection));

        // History container to track the request/response history
        $history = [];
        $historyMiddleware = \GuzzleHttp\Middleware::history($history);
        $stack = \GuzzleHttp\HandlerStack::create();
        $stack->push($historyMiddleware);

        // Setup HTTP client with history middleware and redirect options
        $this->httpClient = new \GuzzleHttp\Client([
            'handler' => $stack,
            'allow_redirects' => true,  // Ensure redirects are followed
        ]);

        // Proceed with the API call if no cached response exists
        $endpoint = ADWS_BASE_URL . $connection['endpoint'];
        $method = $connection['method'];
        $timestamp = time();
        $accessToken = $this->auth->getAccessToken();
        $signature = $this->auth->generateSignature(ADWS_CALLBACK_URL, $accessToken, $timestamp);
        $Authorization = "Bearer {$accessToken}";
        $api = substr(str_replace('/', '_', $connection['endpoint']), 1);
        $headers = ['Authorization' => $Authorization, 'timestamp' => $timestamp, 'signature' => $signature, 'CSN' => $this->csn];

        if (isset($connection['headers'])) {
            foreach ($connection['headers'] as $key => $value) {
                $headers[$key] = $value;
            }
        }

        if (isset($connection['Content-Type'])) $headers['Content-Type'] = $connection['Content-Type'];
        $data['headers'] = $headers;
        if (isset($connection['query'])) $data["query"] = $connection['query'];
        if (isset($connection['json'])) {
            // If json is a string, decode it first
            if (is_string($connection['json'])) {
                $data['json'] = json_decode($connection['json'], true);
            } else {
                $data['json'] = $connection['json'];
            }
        }

        // Debug logging (optional)
        $this->debugLog['log'][$api]['start'] = [
            "Headers" => $headers,
            "method" => $method,
            "endpoint" => $endpoint,
            "data" => $data
        ];

        if ($this->debug) return false;

        try {
            // Execute the API request
            print_rr('request start');
            $response = $this->httpClient->request($method, $endpoint, $data);

            $this->debugLog['log'][$api]['response_api'] = [
                "status" => $response->getStatusCode(),
                "headers" => $response->getHeaders(),
                "body" => $response->getBody()->getContents()
            ];
            print_rr($this->debugLog['log'][$api]['response_api'],'responseappi' );
            // Retrieve the effective URL using the history
            $effectiveUrl = null;
            if (!empty($history)) {
                $lastRequest = end($history)['request'];
                $effectiveUrl = (string) $lastRequest->getUri(); // This is the effective URL
            }

            // Extract the filename from the URL if downloading a file
            if ($downloadDir && $effectiveUrl) {
                // Extract the filename from the effective URL
                $urlPath = parse_url($effectiveUrl, PHP_URL_PATH);
                $filename = basename($urlPath);

                // If no filename in the URL, use a default name
                if (!$filename) {
                    $filename = 'downloaded_file_' . time();
                }

                // Create full path for saving the file
                $downloadFilePath =  rtrim(DOC_ROOT, '/')  . '/' . rtrim($downloadDir, '/') . '/' . $filename;

                // Download the file to the specified directory
                $this->httpClient->request($method, $effectiveUrl, [
                    'sink' => $downloadFilePath // Download file to the specified path
                ]);

                return ['status' => 'success', 'message' => 'File downloaded successfully', 'file_path' => $downloadFilePath];
            }
            $responseBody = json_decode($response->getBody(), true);
            $responseHeaders = $response->getHeaders();

            // Cache the response if caching is enabled
            if ($cache) {
                $this->cacheResponse($cacheKey, $responseBody, $cacheTTL);
            }

            return ['status' => 'success', 'headers' => $responseHeaders, 'body' => $responseBody, 'effective_url' => $effectiveUrl];
        } catch (RequestException $e) {
            // For Guzzle request exceptions that have a response
            if ($e->hasResponse()) {
                $this->debugLog['log'][$api]['error'] = $e->getResponse()->getBody()->getContents();
            } else {
                $this->debugLog['log'][$api]['error'] = 'No response from server';
            }
            return ['status' => 'fail', 'failing' => 'api_call', 'response' => $e->getMessage(), 'debug' => $this->debugLog];
        } catch (\Exception $e) {
            // For any other exceptions
            $this->debugLog['log'][$api]['error'] = 'General exception: ' . $e->getMessage();
            return ['status' => 'fail', 'failing' => 'api_call', 'response' => $e->getMessage(), 'debug' => $this->debugLog];
        }
    }



    private function database_getCachedResponse($cacheKey) {
        // Check the cache (e.g., database or Redis)
        $query = tep_db_query('SELECT response FROM api_cache WHERE cache_key = :cache_key AND expiry_time > NOW() LIMIT 1', null, [':cache_key' => $cacheKey]);
        $result = tep_db_fetch_all($query);
        return $result ? json_decode($result['response'], true) : null;
    }

    private function database_cacheResponse($cacheKey, $response, $cacheTTL) {
        // Cache the response in the database or Redis
        $expiryTime = time() + $cacheTTL;
        $query = "INSERT INTO api_cache (cache_key, response, expiry_time) VALUES (:cacheKey, :response, FROM_UNIXTIME(:expiryTime)) ON DUPLICATE KEY UPDATE response = VALUES(response), expiry_time = VALUES(expiry_time)";
        tep_db_query($query, null, [':cacheKey' => $cacheKey, ':response' => json_encode($response), ':expiryTime' => $expiryTime]);
    }

    private function cacheResponse($cacheKey, $response, $cacheTTL) {
//        $cacheFile = FS_CACHE . DS . $cacheKey . '.json';
//
//        // Write the response to the cache file
//        file_put_contents($cacheFile, json_encode($response));
//
//        // Optionally, you can use `touch()` to set the file's last modified time if needed.
//        touch($cacheFile, time() + $cacheTTL);
    }

    public function get_autodesk_product_catalog() {
        $connection = [
            'endpoint' => '/v1/catalog/export',
            'method' => 'GET',
            'headers' =>
                ['x-adsk-disable-redirect' => 'true']
        ];
        return $this->call($connection);
    }
    public function get_autodesk_product_promos() {
        $connection = ['endpoint' => '/v1/promotions/export', 'method' => 'GET', 'headers' => ['x-adsk-disable-redirect' => 'true']];
        return $this->call($connection);
    }
    public function get_opportunity($opportunityNumber = null, $endCustomerCsn = null) {
        if ($opportunityNumber != null) {
            $query = ["opportunityNumber" => $opportunityNumber];
        } else if ($endCustomerCsn != null) {
            $query = ["endCustomerCsn" => $endCustomerCsn];
        } else {
            return ['status' => 'fail', 'message' => 'need opportunityNumber or endCustomerCsn', 'body' => $this->debugLog];
        }
        $connection = ['endpoint' => '/v2/opportunities', 'method' => 'GET', 'query' => $query];
        return $this->call($connection);
    }
    public function quotes_finalize($quoteNumber) {
        $connection = ["endpoint" => '/v1/quotes/finalize', 'method' => 'PUT', 'Content-Type' => 'application/json', "json" => ["quoteNumber" =>  $quoteNumber, "skipDDACheck" =>  true, "agentAccount" =>   ["accountCsn" => ADWS_CSN], "agentContact" =>  ["email" =>  "<EMAIL>"]]];
        return $this->call($connection);
    }
    public function quotes_status($transactionId) {
        $connection = ['endpoint' => '/v1/quotes/status', 'method' => 'GET', 'Content-Type' => null, 'query' => ['transactionId' => $transactionId]];
        return $this->call($connection);
    }
    public function quotes_view($quoteNumber) {
        $connection = [
            'endpoint' => '/v3/quotes',
            'method' => 'GET',
            'Content-Type' => null,
            'query' => ['filter[quoteNumber]' => $quoteNumber]
        ];
        return $this->call($connection);
    }
    public function quote_get($quoteNumber): array {
        $connection = [
            'endpoint' => '/v3/quotes',
            'method' => 'GET',
            'Content-Type' => null,
            'query' => ['filter[quoteNumber]' => $quoteNumber]
        ];
        return $this->call($connection);
    }
    public function search_autodesk_customers($criteria) {
        $connection = [
            "endpoint" => '/v1/accounts/search',
            'method' => 'POST',
            'Content-Type' => 'application/json',
            'json' => $criteria
        ];
        return $this->call($connection);
    }
    function send_quote($quoteData) {
        $connection = ["endpoint" => '/v3/quotes', 'method' => 'POST', 'Content-Type' => 'application/json', 'json' => $quoteData];
        return $this->call($connection);
    }

    public function raw_call($requesttype, $endpoint, $params = [], $data = [], $filepath = null) {
        try {
            $this->debugLog['log']['final_requestType'] = $requesttype;
            $this->debugLog['log']['final_endpoint'] = $endpoint;
            $this->debugLog['log']['final_data'] = $data;
            $this->debugLog['log']['final_params'] = $params;
            $this->debugLog['log']['final_filepath'] = $filepath;
            $response = $this->send_raw_call($requesttype, $endpoint, $params, $data, $filepath);
            $this->debugLog['response'] = $response;
        } catch (\Exception $e) {
            $this->debugLog['log']['error'] = $e->getMessage();
            return $this->debugLog;
        }
        return $this->debugLog;
    }
    function send_raw_call($requestype, $endpoint, $params = [], $data = [], $filepath = null) {
        $connection = ['endpoint' => $endpoint, 'method' => $requestype];
        if (!empty($data)) {
            $connection['Content-Type'] = 'application/json';
            $connection['json'] = $data;
        }
        if (!empty($params)) {
            $connection['query'] = $params;
        }
        return $this->call($connection, false, 0, $filepath);
    }





    public function get_autodesk_subscriptions() {
        $connection = [
            'endpoint' => '/v2/export/subscriptions',
            'method' => 'POST',
            'json' => [
                'startDateSince' => '2014-01-01',
                'webhook' => [
                    'url' => "https://" . DOMAIN  . '/adws_api_subscription_export.php'
                ]
            ]
        ];
        return $this->call($connection);
    }

    public function get_autodesk_quotes() {
        $connection = [
            'endpoint' => '/v1/quotes/export?filter[quoteStatus]=All',
            'method' => 'POST'
        ];
        return $this->call($connection);
    }

    public function search_autodesk_products($criteria) {
        $connection = ["endpoint" => '/v2/subscriptions', 'method' => 'GET', 'query' => $criteria];
        return $this->call($connection);
    }


    public function search_autodesk_subscriptions($criteria, $format = 'assoc') {
        $connection = ["endpoint" => '/v2/subscriptions', 'method' => 'GET', 'query' => $criteria];
        return $this->call($connection, null, null,null,$format);
    }
}
