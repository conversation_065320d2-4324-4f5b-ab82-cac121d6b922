<?php

namespace autodesk_api;

class autodesk_customer
{
    /**
     * Get an Autodesk customer from the database based on quote number, subscription ID, or subscription reference number
     *
     * @param string|null $quote_number The quote number to search for
     * @param string|null $subscription_id The subscription ID to search for
     * @param string|null $subscription_reference_number The subscription reference number to search for
     * @return array|null The customer data if found, null if not found
     */
    public static function getCustomerBy(?string $quote_number = null, ?string $subscription_id = null, ?string $subscription_reference_number = null): ?array
    {
        // Validate that at least one parameter is provided
        if (empty($quote_number) && empty($subscription_id) && empty($subscription_reference_number)) {
            return null;
        }

        return self::queryCustomerByMultipleFields($quote_number, $subscription_id, $subscription_reference_number);
    }

    /**
     * Get an Autodesk customer from the database based on an identifier
     * Automatically detects the type of identifier (quote number, subscription ID, CSN, or subscription reference number)
     *
     * @param string $identifier The identifier to search for (quote number, subscription ID, CSN, or subscription reference number)
     * @return array|null The customer data if found, null if not found
     */
    public static function get(string $identifier): ?array
    {
        // Trim the identifier to remove any whitespace
        $identifier = trim($identifier);

        // Check if the identifier is empty
        if (empty($identifier)) {
            return null;
        }

        // Check if the identifier is a subscription ID (numeric, 14 digits)
        if (preg_match('/^\d{14}$/', $identifier)) {
            return self::getCustomerBy(null, $identifier, null);
        }

        // Check if the identifier is a CSN (numeric, 10 digits starting with 5)
        if (preg_match('/^5\d{9}$/', $identifier)) {
            return self::queryCustomerByCsn($identifier);
        }

        // Check if the identifier is a quote number (starts with "Q-")
        if (preg_match('/^Q-\d+$/i', $identifier)) {
            return self::getCustomerBy($identifier, null, null);
        }

        // Check if the identifier is a subscription reference number (contains a hyphen)
        if (strpos($identifier, '-') !== false) {
            return self::getCustomerBy(null, null, $identifier);
        }

        // If we couldn't determine the type, try all options
        return self::getCustomerBy($identifier, $identifier, $identifier);
    }

    /**
     * Query the database for a customer by CSN
     *
     * @param string $csn The customer CSN to search for
     * @return array|null The customer data if found, null if not found
     */
    private static function queryCustomerByCsn(string $csn): ?array
    {
        $query = "
            SELECT
                endcust.*
            FROM
                autodesk_accounts endcust
            WHERE
                endcust.account_csn = :csn
            LIMIT 1
        ";

        $params = [":csn" => $csn];
        $result = tcs_db_query($query, $params);

        return !empty($result) ? $result[0] : null;
    }

    /**
     * Query the database for a customer by multiple fields (quote number, subscription ID, subscription reference number)
     *
     * @param string|null $quote_number The quote number to search for
     * @param string|null $subscription_id The subscription ID to search for
     * @param string|null $subscription_reference_number The subscription reference number to search for
     * @return array|null The customer data if found, null if not found
     */
    private static function queryCustomerByMultipleFields(?string $quote_number, ?string $subscription_id, ?string $subscription_reference_number): ?array
    {
        $where_conditions = [];
        $params = [];

        // Build the WHERE clause based on provided parameters
        if (!empty($quote_number)) {
            $where_conditions[] = "quotes.quote_number = :quote_number";
            $params[":quote_number"] = $quote_number;
        }

        if (!empty($subscription_id)) {
            $where_conditions[] = "subs.subscriptionId = :subscription_id";
            $params[":subscription_id"] = $subscription_id;
        }

        if (!empty($subscription_reference_number)) {
            $where_conditions[] = "subs.subscriptionReferenceNumber = :subscription_reference_number";
            $params[":subscription_reference_number"] = $subscription_reference_number;
        }

        // Combine WHERE conditions with OR
        $where_clause = implode(" OR ", $where_conditions);

        // Build the query
        $query = "
            SELECT
                endcust.*
            FROM
                autodesk_accounts endcust
            LEFT JOIN
                autodesk_subscriptions subs ON subs.endCustomer_id = endcust.id
            LEFT JOIN
                autodesk_quotes quotes ON quotes.endCustomer_id = endcust.id
            WHERE
                {$where_clause}
            LIMIT 1
        ";

        // Execute the query
        $result = tcs_db_query($query, $params);

        // Return the first result or null if no results
        return !empty($result) ? $result[0] : null;
    }
}