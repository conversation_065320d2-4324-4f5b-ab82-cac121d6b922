<?php
namespace autodesk_api;

use system\data_importer;

class autodesk_products {
    private autodesk_api_interface $api;
    public $debug_log;
    public $products = [];

    function __construct($api) {
        $this->api = $api;
    }

    public function get($id) {
        $criteria = [];
        if (strpos($id, '-'))
            $criteria["filter[productId]"] = $id;
        else
            $criteria["filter[productReferenceNumber]"] = $id;
        return $this->api->search_autodesk_products($criteria);
    }
    public function get_all($db_data, $criteria = ["limit" => 100] )  {
        print_rr($criteria, 'database');
        return $this->database_get_autodesk_products($db_data, $criteria);
    }

    public function get_distinct($db_data, $criteria = ["limit" => 100] )  {
        print_rr($criteria, 'database');
    return $this->database_get_autodesk_products($db_data, $criteria, true);
    }

    private function database_get_autodesk_products($db_data = [], $criteria = [],$get_distinct = false)   {
        $default_criteria = [
            "limit" => 100,
            "order_by" => "offeringName",
            "search_columns" => ['pac.offeringName', 'pac.offeringCode', 'pac.offeringId', 'pac.intendedUsage_code', 'pac.intendedUsage_description', 'pac.accessModel_code', 'pac.accessModel_description', 'pac.servicePlan_code', 'pac.servicePlan_description', 'pac.connectivity_code', 'pac.connectivity_description', 'pac.term_code', 'pac.term_description', 'pac.renewOnlyDate', 'pac.orderAction', 'pac.specialProgramDiscount_code', 'pac.specialProgramDiscount_description']
        ];

        $table_schema = [
            "pac" => [
                'query' => "FROM products_autodesk_catalog pac"
            ],
            "p2a" => [
                'query' => "JOIN products_to_autodesk_catalog p2a ON p2a.unique_hash = pac.unique_hash"
            ],
            "products" => [
                'query' => "JOIN products ON p2a.products_id = products.products_id"
            ]
        ];

        // $q =  "SELECT * FROM products_autodesk_catalog pac " . tcs_db_build_criteria(array_merge($default_criteria, $criteria),$table_schema);
        $criteria_string = "";
        [
            $criteria_string,
            $criteria_cols,
            $criteria_tabs
        ] = tcs_db_build_criteria(array_merge($default_criteria, $criteria),$table_schema);
        if (empty($db_data)){
            $table_data['columns'] = ' * ';
            foreach ($table_schema as $key => $table) {
                $table_data['tables'] .= " " . $table['query'];
            }
        } else{
            $table_data = tcs_db_build_tables($db_data,$table_schema,$criteria_cols,$criteria_tabs);
        }
          $distinct = $get_distinct ? "DISTINCT " : "";
        $query = "SELECT {$distinct}{$table_data['columns']} {$table_data['tables']}  {$criteria_string}";
        print_rr(i:$query,l:'finalq',fl: true);
        //  convert_column_names($query);
        return tcs_db_query($query);
    }



    public function get_catalog() { /* Configuration*/
        $csv_file_path = DOC_ROOT . '/feeds/product_catalog.csv'; /* Define the path where you want to save the CSV file*/
        $download = false;/* Check if the file exists and is less than a day old*/
        if (file_exists($csv_file_path)) {
            $file_age = time() - filemtime($csv_file_path);
            if ($file_age > 86400) $download = true;
        } else {
            $download = true;
        }
        if ($download) {
            $get_catalog =  $this->api->get_autodesk_product_catalog();
            if ($get_catalog['status'] == 'fail') {
                return $get_catalog;
            } else {
                $response = $get_catalog;
            }
            print_rr($get_catalog);
            $response_headers = $response['headers'];
            if (is_string($response_headers['Location'][0])) {
                $input_file = file_get_contents($response_headers['Location'][0]);                            /* Download the CSV file and save it to the specified path                */
                file_put_contents($csv_file_path, $input_file);
            } else {
                return ['status' => 'fail', 'response' => $get_catalog];
            }
        }
        return $this->import_autodesk_catalog_into_database($csv_file_path);
    }


    private function get_product_catalog_header_map() {
        return  ['offeringName' => 'offeringName', 'offeringCode' => 'offeringCode', 'offeringId' => 'offeringId', 'intendedUsage.code' => 'intendedUsage_code', 'intendedUsage.description' => 'intendedUsage_description', 'accessModel.code' => 'accessModel_code', 'accessModel.description' => 'accessModel_description', 'servicePlan.code' => 'servicePlan_code', 'servicePlan.description' => 'servicePlan_description', 'connectivity.code' => 'connectivity_code', 'connectivity.description' => 'connectivity_description', 'term.code' => 'term_code', 'term.description' => 'term_description', 'lifeCycleState' => 'lifeCycleState', 'renewOnlyDate' => 'renewOnlyDate', 'discontinueDate' => 'discontinueDate', 'orderAction' => 'orderAction', 'specialProgramDiscount.code' => 'specialProgramDiscount_code', 'specialProgramDiscount.description' => 'specialProgramDiscount_description', 'fromQty' => 'fromQty', 'toQty' => 'toQty', 'currency' => 'currency', 'SRP' => 'SRP', 'costAfterSpecialProgramDiscount' => 'costAfterSpecialProgramDiscount', 'renewalDiscountPercent' => 'renewalDiscountPercent', 'renewalDiscountAmount' => 'renewalDiscountAmount', 'costAfterRenewalDiscount' => 'costAfterRenewalDiscount', 'transactionVolumeDiscountPercent' => 'transactionVolumeDiscountPercent', 'transactionVolumeDiscountAmount' => 'transactionVolumeDiscountAmount', 'costAfterTransactionVolumeDiscount' => 'costAfterTransactionVolumeDiscount', 'serviceDurationDiscountPercent' => 'serviceDurationDiscountPercent', 'serviceDurationDiscountAmount' => 'serviceDurationDiscountAmount', 'costAfterServiceDurationDiscount' => 'costAfterServiceDurationDiscount', 'effectiveStartDate' => 'effectiveStartDate', 'effectiveEndDate' => 'effectiveEndDate'];
    }


    public function import_autodesk_catalog_into_database($csv_file_path) {    /* Main script execution*/
        $output = data_importer::import_csv_into_database(
            autodesk_api::get_product_catalog_header_map(),
            $csv_file_path,
            ['offeringId', 'intendedUsage.code', 'accessModel.code', 'servicePlan.code', 'connectivity.code', 'term.code', 'orderAction', 'specialProgramDiscount.code', 'fromQty', 'toQty'],
            true,
            'product_import'
        );
        $this->database_update_autodesk_pricing();
        return $output;
    }





    public function get_promos() { /* Configuration*/
        $csv_file_path = DOC_ROOT . '/feeds/product_promos.csv'; /* Define the path where you want to save the CSV file*/
        $download = false;/* Check if the file exists and is less than a day old*/
        if (file_exists($csv_file_path)) {
            $file_age = time() - filemtime($csv_file_path);
            if ($file_age > 86400) $download = true;
        } else {
            $download = true;
        }
        if ($download) {
            $get_promos = $this->api->get_autodesk_product_promos();

            if (empty($get_promos) || $get_promos['status'] == 'fail') {
                return ['status' => 'fail 1', 'response' => $get_promos];
            } else {
                $response = $get_promos;
            }
            $response_headers = $response['headers'];
            if (is_string($response_headers['Location'][0])) {
                $input_file = file_get_contents($response_headers['Location'][0]);   /* Download the CSV file and save it to the specified path                */
                file_put_contents($csv_file_path, $input_file);
            } else {
                return ['status' => 'fail 2', 'response' => $response, 'debug' => $get_promos];
            }
        }
        return ['status' => 'success', 'response' => $response]; //$this->import_autodesk_promos_into_database($csv_file_path);
    }


    private function get_product_promos_header_map() {
        return  ['offeringName' => 'offeringName', 'offeringCode' => 'offeringCode', 'offeringId' => 'offeringId', 'intendedUsage.code' => 'intendedUsage_code', 'intendedUsage.description' => 'intendedUsage_description', 'accessModel.code' => 'accessModel_code', 'accessModel.description' => 'accessModel_description', 'servicePlan.code' => 'servicePlan_code', 'servicePlan.description' => 'servicePlan_description', 'connectivity.code' => 'connectivity_code', 'connectivity.description' => 'connectivity_description', 'term.code' => 'term_code', 'term.description' => 'term_description', 'lifeCycleState' => 'lifeCycleState', 'renewOnlyDate' => 'renewOnlyDate', 'discontinueDate' => 'discontinueDate', 'orderAction' => 'orderAction', 'specialProgramDiscount.code' => 'specialProgramDiscount_code', 'specialProgramDiscount.description' => 'specialProgramDiscount_description', 'fromQty' => 'fromQty', 'toQty' => 'toQty', 'currency' => 'currency', 'SRP' => 'SRP', 'costAfterSpecialProgramDiscount' => 'costAfterSpecialProgramDiscount', 'renewalDiscountPercent' => 'renewalDiscountPercent', 'renewalDiscountAmount' => 'renewalDiscountAmount', 'costAfterRenewalDiscount' => 'costAfterRenewalDiscount', 'transactionVolumeDiscountPercent' => 'transactionVolumeDiscountPercent', 'transactionVolumeDiscountAmount' => 'transactionVolumeDiscountAmount', 'costAfterTransactionVolumeDiscount' => 'costAfterTransactionVolumeDiscount', 'serviceDurationDiscountPercent' => 'serviceDurationDiscountPercent', 'serviceDurationDiscountAmount' => 'serviceDurationDiscountAmount', 'costAfterServiceDurationDiscount' => 'costAfterServiceDurationDiscount', 'effectiveStartDate' => 'effectiveStartDate', 'effectiveEndDate' => 'effectiveEndDate'];
    }


    public function import_autodesk_promos_into_database($csv_file_path) {    /* Main script execution*/
        $output = data_importer::import_csv_into_database(
            $this->get_product_promos_header_map(),
            $csv_file_path,
            ['offeringId', 'intendedUsage.code', 'accessModel.code', 'servicePlan.code', 'connectivity.code', 'term.code', 'orderAction', 'specialProgramDiscount.code', 'fromQty', 'toQty'],
            true
        );
        print_rr($output);
        $this->database_update_autodesk_pricing();
        return $output;
    }




    private function database_get_autodesk_variations() {
        return tcs_db_query("
            SELECT *
            FROM
                products_autodesk_catalog pac
            JOIN
                products_variations pv
                ON pv.autodesk_catalog_unique_hash = pac.unique_hash
            WHERE
                pac.srp > 0
        ");
    }

    public function database_update_autodesk_pricing($products = null, $variations = null) {
        if ($products == null) $products = $this->database_get_autodesk_products();
        if ($variations == null) $variations = $this->database_get_autodesk_variations();
        $recordTypes = ["products" => $products, "products_variations" => $variations];
        ////print_rr($products);
        // Initialize an array to store product IDs and price mappings
        tcs_log("Starting pricing update " . count($products) . " products and " . count($variations) . " variations", 'pricing_update');
        tcs_log("first record: " . print_r($products[0], true), 'pricing_update');
        tcs_log("recordTypes: " . print_r($recordTypes, true), 'pricing_update');

        foreach ($recordTypes as $key => $recordType) {
            $ids = [];
            $caseStatements = [];
            tcs_log("processing: " . print_r($key, true), 'pricing_update');
            if (count($recordType) == 0) continue;
            // Collect all product prices and ids for the bulk update
            foreach ($recordType as $record) {
                tcs_log("processing {$key}_id: " . $record["{$key}_id"] . "setting price to $price", 'pricing_update');
                $price = $this->products_calculate_pricing($record);
                $ids[] = $record["{$key}_id"];  // Collect the  ID
                $caseStatements[] = "WHEN {$record["{$key}_id"]} THEN {$price}";
            }     // If no products are found, stop further execution
            if (empty($ids)) break;

            // Build the bulk update query using CASE
            $idsList = implode(",", $ids);
            $caseQuery = implode(" ", $caseStatements);

            $col = 'products_price';
            if ($key == "products_variations") {
                $col = "price";
            }

            $sql_query = "" .
                "UPDATE `{$key}`
                SET `$col` = CASE `{$key}_id` $caseQuery
                END
                WHERE `{$key}_id` IN ($idsList)";
            print_rr($sql_query);
            tep_db_query($sql_query);
        }
        // Execute the bulk update query

    }
    public function get_autodesk_product_from_catalog($products_id, $hash = null) {
        print_rr($autodesk_id,'autodesk_id' ,true,true);
        if ($hash) {
            $query_sql = "SELECT * FROM products_autodesk_catalog WHERE `unique_hash` = :hash";
            $query = tep_db_query($query_sql, null, [":hash" => $hash]);
            print_rr($query_sql);
            if (tep_db_num_rows($query)) return tep_db_fetch_array($query);
        }
        $query_sql = "SELECT * FROM products_to_autodesk_catalog p2a JOIN products_autodesk_catalog pac ON p2a.unique_hash = pac.unique_hash WHERE products_id = :products_id";
        $query = tep_db_query($query_sql, null, [":products_id" => $products_id]);
        return tep_db_fetch_array($query);
    }

    public function products_calculate_pricing($product) {
        $price = $product['SRP'];
        $price -= $product['renewalDiscountAmount'] ?? 0;
        $price -= $product['transactionVolumeDiscountAmount'] ?? 0;
        $price -= $product['serviceDurationDiscountAmount'] ?? 0;
        return $price;
    }
}
