<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Email</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #0078D7;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            background-color: #f9f9f9;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 0 0 5px 5px;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 3px;
            margin: 15px 0;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Test Email Campaign</h1>
    </div>
    
    <div class="content">
        <p>Hello <strong>{{name}}</strong>,</p>
        
        <p>This is a test email from the refactored email campaign system.</p>
        
        <div class="highlight">
            <p><strong>Your Details:</strong></p>
            <ul>
                <li>Email: {{email}}</li>
                <li>Name: {{name}}</li>
                <li>Additional Info: {{misc}}</li>
            </ul>
        </div>
        
        <p>This email demonstrates that the new data source-based email campaign system is working correctly!</p>
        
        <p>Key features of the new system:</p>
        <ul>
            <li>✅ Uses data sources instead of hardcoded logic</li>
            <li>✅ Works with any database table</li>
            <li>✅ Smart email and name field detection</li>
            <li>✅ Generic placeholder replacement</li>
            <li>✅ No more autodesk_api dependencies</li>
        </ul>
        
        <p>Best regards,<br>
        <strong>TCS CAD & BIM Solutions Limited</strong></p>
    </div>
    
    <div class="footer">
        <p>This is a test email sent via the refactored email campaign system.</p>
        <p>If you received this in error, please disregard.</p>
    </div>
</body>
</html>
