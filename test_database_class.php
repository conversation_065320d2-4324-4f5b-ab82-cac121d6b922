<?php
/**
 * Test Database Class Access
 * 
 * This tests if the database class is accessible and working correctly
 */

require_once 'system/startup_sequence_minimal.php';

use system\database;

echo "<!DOCTYPE html>";
echo "<html><head><title>Database Class Test</title></head><body>";
echo "<h1>Database Class Test</h1>";

echo "<pre>";

// Test 1: Check if database class exists
echo "1. Testing database class existence...\n";
if (class_exists('database')) {
    echo "✓ database class exists\n";
} else {
    echo "✗ database class not found\n";
    exit;
}

// Test 2: Test basic database connection
echo "\n2. Testing database connection...\n";
try {
    $test_query = "SELECT 1 as test";
    $result = tep_db_query($test_query);
    if ($result) {
        echo "✓ Database connection working\n";
    } else {
        echo "✗ Database connection failed\n";
    }
} catch (Exception $e) {
    echo "✗ Database connection error: " . $e->getMessage() . "\n";
}

// Test 3: Test database::table() method
echo "\n3. Testing database::table() method...\n";
try {
    $table = database::table('autobooks_data_table_storage');
    if ($table) {
        echo "✓ database::table() method works\n";
    } else {
        echo "✗ database::table() method failed\n";
    }
} catch (Exception $e) {
    echo "✗ database::table() error: " . $e->getMessage() . "\n";
}

// Test 4: Test querying autobooks_data_table_storage
echo "\n4. Testing autobooks_data_table_storage table access...\n";
try {
    $count = database::table('autobooks_data_table_storage')->count();
    echo "✓ autobooks_data_table_storage table accessible\n";
    echo "  Records in table: $count\n";
} catch (Exception $e) {
    echo "✗ autobooks_data_table_storage table error: " . $e->getMessage() . "\n";
}

// Test 5: Test querying autobooks_data_sources
echo "\n5. Testing autobooks_data_sources table access...\n";
try {
    $count = database::table('autobooks_data_sources')->count();
    echo "✓ autobooks_data_sources table accessible\n";
    echo "  Records in table: $count\n";
    
    // Get active data sources
    $active_sources = database::table('autobooks_data_sources')
        ->where('status', '=', 'active')
        ->get();
    echo "  Active data sources: " . count($active_sources) . "\n";
    
    if (!empty($active_sources)) {
        echo "  Sample data sources:\n";
        foreach (array_slice($active_sources, 0, 3) as $source) {
            echo "    - ID: {$source['id']}, Name: {$source['name']}, Category: {$source['category']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "✗ autobooks_data_sources table error: " . $e->getMessage() . "\n";
}

// Test 6: Test data_table_storage class methods
echo "\n6. Testing data_table_storage class methods...\n";
try {
    if (class_exists('data_table_storage')) {
        echo "✓ data_table_storage class exists\n";
        
        // Test get_current_user_id
        $user_id = data_table_storage::get_current_user_id();
        echo "  Current user ID: " . ($user_id ?? 'NULL') . "\n";
        
        // Test get_configuration
        $config = data_table_storage::get_configuration('test_table', $user_id);
        echo "  Test configuration: " . ($config ? 'Found' : 'Not found') . "\n";
        
        // Test list_configurations
        $configs = data_table_storage::list_configurations($user_id);
        echo "  User configurations: " . count($configs) . "\n";
        
    } else {
        echo "✗ data_table_storage class not found\n";
    }
} catch (Exception $e) {
    echo "✗ data_table_storage class error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Summary ===\n";
echo "If all tests passed, the database integration should work correctly.\n";
echo "If any tests failed, check the error messages above.\n";

echo "</pre>";

echo "<hr>";
echo "<h2>Next Steps</h2>";
echo "<ul>";
echo "<li>If all tests passed: <a href='test_integrated_data_source_selector.php'>Test Integrated Data Source Selector</a></li>";
echo "<li>If database issues: Check database configuration and table existence</li>";
echo "<li>If class issues: Check autoloader and class files</li>";
echo "</ul>";

echo "</body></html>";
?>
