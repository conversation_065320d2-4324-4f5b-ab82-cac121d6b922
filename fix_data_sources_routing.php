<?php
// Initialize the system
const DS = DIRECTORY_SEPARATOR;
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . DS . 'system'. DS . 'config'. DS . 'path_schema.php';
$ds = DS;
$path['fs_system'] = "{$path['fs_app_root']}{$ds}{$schema['system']['root']}" ;

$input_params = array_merge($_GET, $_POST);
define('INPUT_PARAMS',$input_params);
const DEBUG_MODE = true;
const API_RUN = false;

require_once $path['fs_system'] . DS . 'classes'. DS . 'startup_sequence.class.php';
require_once $path['fs_system'] . DS . 'functions'. DS . 'functions.php';

startup_sequence::start($path,$schema);

use system\database;

echo "<h1>Fix Data Sources Routing</h1>";

try {
    // First, let's check what files we have
    echo "<h2>Current files in system/views/data_sources/:</h2>";
    $data_sources_dir = 'system/views/data_sources';
    if (is_dir($data_sources_dir)) {
        $files = scandir($data_sources_dir);
        echo "<ul>";
        foreach ($files as $file) {
            if ($file != '.' && $file != '..' && is_file($data_sources_dir . '/' . $file)) {
                echo "<li>$file</li>";
            }
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>Directory $data_sources_dir does not exist!</p>";
    }
    
    // Check current navigation entry
    $nav = database::table('autobooks_navigation')
        ->where('route_key', 'data_sources')
        ->where('parent_path', 'root')
        ->first();

    if ($nav) {
        echo "<h2>Current navigation entry:</h2>";
        echo "<p><strong>file_path:</strong> " . $nav['file_path'] . "</p>";
        echo "<p><strong>is_system:</strong> " . ($nav['is_system'] ? 'true' : 'false') . "</p>";
        
        // Update the navigation entry to match the actual file structure
        echo "<h2>Updating navigation entry...</h2>";
        
        database::table('autobooks_navigation')
            ->where('id', $nav['id'])
            ->update([
                'file_path' => 'data_sources',  // This should match the directory name in system/views/
                'is_system' => 1,              // Keep as system view
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
        echo "<p style='color: green;'>✓ Navigation entry updated</p>";
    } else {
        echo "<p style='color: red;'>Navigation entry not found - creating it...</p>";
        
        // Create the navigation entry
        database::table('autobooks_navigation')->insert([
            'parent_path' => 'root',
            'route_key' => 'data_sources',
            'name' => 'Data Sources',
            'icon' => 'database',
            'file_path' => 'data_sources',
            'required_roles' => '[]',
            'sort_order' => 15,
            'show_navbar' => 1,
            'can_delete' => 0,
            'is_system' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
        echo "<p style='color: green;'>✓ Navigation entry created</p>";
    }
    
    // Now let's also create a main data_sources.edge.php file in system/views/ 
    // that can handle the routing
    echo "<h2>Creating main routing file...</h2>";
    
    $main_route_file = 'system/views/data_sources.edge.php';
    if (!file_exists($main_route_file)) {
        $route_content = '<?php
// Main data sources router
$path = $_SERVER["REQUEST_URI"] ?? "";
$path_parts = array_filter(explode("/", trim($path, "/")));

// Find data_sources in the path
$data_sources_index = array_search("data_sources", $path_parts);
if ($data_sources_index !== false) {
    $remaining_parts = array_slice($path_parts, $data_sources_index + 1);
    
    if (empty($remaining_parts)) {
        // Main index page
        include __DIR__ . "/data_sources/data_sources.edge.php";
    } else {
        $first_part = $remaining_parts[0];
        
        if (is_numeric($first_part)) {
            // ID-based route like /data_sources/1/edit
            $id = (int)$first_part;
            $action = $remaining_parts[1] ?? "preview";
            
            global $route_params;
            $route_params = ["data_source_id" => $id];
            
            switch ($action) {
                case "edit":
                    include __DIR__ . "/data_sources/edit.edge.php";
                    break;
                case "preview":
                    include __DIR__ . "/data_sources/preview.edge.php";
                    break;
                default:
                    include __DIR__ . "/data_sources/preview.edge.php";
            }
        } else {
            // Action-based route like /data_sources/create
            switch ($first_part) {
                case "create":
                    include __DIR__ . "/data_sources/create.edge.php";
                    break;
                default:
                    include __DIR__ . "/data_sources/data_sources.edge.php";
            }
        }
    }
} else {
    // Fallback to main index
    include __DIR__ . "/data_sources/data_sources.edge.php";
}
?>';
        
        file_put_contents($main_route_file, $route_content);
        echo "<p style='color: green;'>✓ Main routing file created: $main_route_file</p>";
    } else {
        echo "<p>Main routing file already exists: $main_route_file</p>";
    }
    
    echo "<h2 style='color: green;'>Routing Fix Complete!</h2>";
    echo "<p><a href='data_sources'>Test Data Sources Link</a></p>";
    echo "<p><a href='data_sources/create'>Test Create Link</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
