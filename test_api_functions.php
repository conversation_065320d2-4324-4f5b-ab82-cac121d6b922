<?php
// Test API functions for data sources
const DS = DIRECTORY_SEPARATOR;
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . DS . 'system'. DS . 'config'. DS . 'path_schema.php';
$ds = DS;
$path['fs_system'] = "{$path['fs_app_root']}{$ds}{$schema['system']['root']}" ;

$input_params = array_merge($_GET, $_POST);
define('INPUT_PARAMS',$input_params);
const DEBUG_MODE = true;
const API_RUN = false;

require_once $path['fs_system'] . DS . 'classes'. DS . 'startup_sequence.class.php';
require_once $path['fs_system'] . DS . 'functions'. DS . 'functions.php';

startup_sequence::start($path,$schema);

echo "<h1>Data Sources API Functions Test</h1>";

try {
    // Test 1: Check if API file exists and functions are defined
    echo "<h2>Step 1: API File Check</h2>";
    
    $api_file = 'system/api/data_sources.api.php';
    if (file_exists($api_file)) {
        echo "<p style='color: green;'>✓ API file exists: $api_file</p>";
        
        // Include the API file to check functions
        require_once $api_file;
        
        $expected_functions = [
            'get_tables',
            'get_table_info', 
            'get_data_sources',
            'create_data_source',
            'update',
            'delete',
            'get_data',
            'get_preview_data',
            'sample_data',
            'test_connection',
            'get_stats',
            'create_view',
            'edit_view',
            'preview_view'
        ];
        
        foreach ($expected_functions as $func) {
            $full_func_name = "api\\data_sources\\$func";
            if (function_exists($full_func_name)) {
                echo "<p style='color: green;'>✓ Function '$func' exists</p>";
            } else {
                echo "<p style='color: red;'>✗ Function '$func' missing</p>";
            }
        }
    } else {
        echo "<p style='color: red;'>✗ API file missing: $api_file</p>";
    }
    
    // Test 2: Check Edge components
    echo "<h2>Step 2: Edge Components Check</h2>";
    
    $components = [
        'data-source-builder.edge.php' => 'Data Source Builder',
        'data-source-selector.edge.php' => 'Data Source Selector',
        'data-source-preview.edge.php' => 'Data Source Preview',
        'error-message.edge.php' => 'Error Message'
    ];
    
    foreach ($components as $file => $name) {
        $component_path = "system/components/edges/$file";
        if (file_exists($component_path)) {
            echo "<p style='color: green;'>✓ $name component exists</p>";
        } else {
            echo "<p style='color: red;'>✗ $name component missing: $component_path</p>";
        }
    }
    
    // Test 3: Test API function calls
    echo "<h2>Step 3: API Function Tests</h2>";
    
    if (function_exists('api\\data_sources\\get_tables')) {
        echo "<p>Testing get_tables()...</p>";
        $result = \api\data_sources\get_tables([]);
        if ($result['success'] ?? false) {
            echo "<p style='color: green;'>✓ get_tables() works - found " . count($result['data']) . " tables</p>";
        } else {
            echo "<p style='color: red;'>✗ get_tables() failed: " . ($result['error'] ?? 'Unknown error') . "</p>";
        }
    }
    
    if (function_exists('api\\data_sources\\get_data_sources')) {
        echo "<p>Testing get_data_sources()...</p>";
        $result = \api\data_sources\get_data_sources([]);
        if ($result['success'] ?? false) {
            echo "<p style='color: green;'>✓ get_data_sources() works - found " . count($result['data']) . " data sources</p>";
        } else {
            echo "<p style='color: red;'>✗ get_data_sources() failed: " . ($result['error'] ?? 'Unknown error') . "</p>";
        }
    }
    
    // Test 4: Test Edge rendering
    echo "<h2>Step 4: Edge Rendering Test</h2>";
    
    try {
        use edge\edge;
        
        echo "<p>Testing error-message component...</p>";
        $error_html = Edge::render('error-message', [
            'title' => 'Test Error',
            'message' => 'This is a test error message'
        ]);
        
        if (!empty($error_html)) {
            echo "<p style='color: green;'>✓ error-message component renders successfully</p>";
        } else {
            echo "<p style='color: red;'>✗ error-message component returned empty</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Edge rendering failed: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2 style='color: green;'>Test Complete!</h2>";
    echo "<p>API endpoints that should work:</p>";
    echo "<ul>";
    echo "<li><a href='api/data_sources/get_tables' target='_blank'>GET /api/data_sources/get_tables</a></li>";
    echo "<li><a href='api/data_sources/get_data_sources' target='_blank'>GET /api/data_sources/get_data_sources</a></li>";
    echo "<li><a href='api/data_sources/create_view' target='_blank'>GET /api/data_sources/create_view</a></li>";
    echo "</ul>";
    
    echo "<p><strong>Main page:</strong> <a href='data_sources'>Data Sources</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Test failed: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
