<?php
/**
 * Test table parsing logic
 */

// Include the necessary files
require_once 'system/classes/startup_sequence.class.php';
require_once 'system/functions/functions.php';

// Initialize the system
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . '/system/config/path_schema.php';
$path['fs_system'] = $path['fs_app_root'] . '/' . $schema['system']['root'];
startup_sequence::start($path, $schema);

use system\data_source_manager;

echo "<h1>Test Table Parsing Logic</h1>";

// Test different parameter formats
$test_cases = [
    'JSON selected_tables' => [
        'selected_tables' => '["users", "orders"]',
        'name' => 'Test Source 1'
    ],
    'Array tables' => [
        'tables' => ['users', 'orders'],
        'name' => 'Test Source 2'
    ],
    'Single table_name' => [
        'table_name' => 'users',
        'name' => 'Test Source 3'
    ],
    'Mixed format' => [
        'selected_tables' => '["users"]',
        'tables' => ['orders', 'products'],
        'table_name' => 'customers',
        'name' => 'Test Source 4'
    ],
    'Empty/invalid' => [
        'selected_tables' => '',
        'name' => 'Test Source 5'
    ]
];

foreach ($test_cases as $case_name => $params) {
    echo "<h2>Test Case: $case_name</h2>";
    echo "<h3>Input Parameters:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
    print_r($params);
    echo "</pre>";
    
    // Test the parsing logic (copied from create_data_source function)
    $tables = [];
    
    // Try selected_tables JSON format first
    if (isset($params['selected_tables']) && !empty($params['selected_tables'])) {
        $decoded = json_decode($params['selected_tables'], true);
        if (is_array($decoded) && !empty($decoded)) {
            $tables = $decoded;
            echo "<p style='color: green;'>✓ Found tables via selected_tables JSON</p>";
        } else {
            echo "<p style='color: orange;'>⚠ selected_tables present but failed to decode</p>";
        }
    }
    
    // Try tables array format (from form submission)
    if (empty($tables) && isset($params['tables']) && is_array($params['tables'])) {
        $tables = array_values(array_filter($params['tables']));
        echo "<p style='color: green;'>✓ Found tables via tables array</p>";
    }
    
    // Try single table_name (legacy format)
    if (empty($tables) && !empty($params['table_name'])) {
        $tables = [$params['table_name']];
        echo "<p style='color: green;'>✓ Found tables via table_name</p>";
    }
    
    if (empty($tables)) {
        echo "<p style='color: red;'>✗ No tables found</p>";
    } else {
        echo "<p><strong>Result:</strong> " . json_encode($tables) . "</p>";
    }
    
    echo "<hr>";
}

// Test with actual data source
echo "<h2>Test with Real Data Source</h2>";
$data_sources = data_source_manager::get_data_sources();

if (!empty($data_sources)) {
    $test_source = $data_sources[0];
    echo "<h3>Original Data Source:</h3>";
    echo "<p><strong>Name:</strong> {$test_source['name']}</p>";
    echo "<p><strong>Tables field:</strong> " . json_encode($test_source['tables'] ?? null) . "</p>";
    echo "<p><strong>Table name field:</strong> " . ($test_source['table_name'] ?? 'null') . "</p>";
    
    // Test duplicate preparation
    $duplicate_source = $test_source;
    unset($duplicate_source['id']);
    $duplicate_source['name'] = 'Copy of ' . $test_source['name'];
    
    // Ensure tables field is properly formatted as array
    if (isset($duplicate_source['tables']) && is_string($duplicate_source['tables'])) {
        $duplicate_source['tables'] = json_decode($duplicate_source['tables'], true) ?: [];
    }
    if (!isset($duplicate_source['tables']) && !empty($duplicate_source['table_name'])) {
        $duplicate_source['tables'] = [$duplicate_source['table_name']];
    }
    
    echo "<h3>Prepared Duplicate Data:</h3>";
    echo "<p><strong>Name:</strong> {$duplicate_source['name']}</p>";
    echo "<p><strong>Tables field:</strong> " . json_encode($duplicate_source['tables'] ?? null) . "</p>";
    echo "<p><strong>Table name field:</strong> " . ($duplicate_source['table_name'] ?? 'null') . "</p>";
    
} else {
    echo "<p>No data sources found to test with.</p>";
}

echo "<hr>";
echo "<p><a href='data_sources'>← Back to Data Sources</a></p>";
?>
