<?php
require_once 'system/autoload.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// <PERSON>ript to fix inconsistent column preferences

function fix_column_preferences($table_name) {
    echo "<h2>Fixing Column Preferences for: $table_name</h2>";
    
    $user_id = \data_table_storage::get_current_user_id();
    $config = \data_table_storage::get_configuration($table_name, $user_id);
    
    if (!$config) {
        echo "<p>No configuration found</p>";
        return;
    }
    
    $configuration = $config['configuration'];
    $structure = $configuration['structure'] ?? [];
    $hidden = $configuration['hidden'] ?? [];
    
    echo "<h3>Before Fix:</h3>";
    echo "<p>Structure count: " . count($structure) . "</p>";
    echo "<p>Hidden count: " . count($hidden) . "</p>";
    
    // Fix 1: Remove duplicate 'columns' array if it exists
    if (isset($configuration['columns'])) {
        unset($configuration['columns']);
        echo "<p>Removed duplicate 'columns' array</p>";
    }
    
    // Fix 2: Regenerate column IDs to ensure consistency
    $fixed_structure = [];
    $valid_hidden = [];
    
    foreach ($structure as $index => $column) {
        // Regenerate ID based on index and label
        $new_id = 'col_' . $index . '_' . md5($column['label']);
        
        $fixed_column = [
            'id' => $new_id,
            'label' => $column['label'],
            'field' => $column['field'] ?? ($column['fields'][0] ?? ''),
            'filter' => $column['filter'] ?? true,
            'fields' => $column['fields'] ?? (is_array($column['field']) ? $column['field'] : [$column['field']]),
            'visible' => true // Default to visible, will be set based on hidden array
        ];
        
        // Check if this column should be hidden
        $old_id = $column['id'];
        $should_be_hidden = in_array($old_id, $hidden) || ($column['visible'] === false);
        
        if ($should_be_hidden) {
            $fixed_column['visible'] = false;
            $valid_hidden[] = $new_id;
        }
        
        $fixed_structure[] = $fixed_column;
        
        echo "<p>Fixed column: {$column['label']} - Old ID: $old_id, New ID: $new_id, Visible: " . ($fixed_column['visible'] ? 'true' : 'false') . "</p>";
    }
    
    // Update configuration
    $configuration['structure'] = $fixed_structure;
    $configuration['hidden'] = $valid_hidden;
    $configuration['updated_at'] = date('Y-m-d H:i:s');
    
    echo "<h3>After Fix:</h3>";
    echo "<p>Structure count: " . count($fixed_structure) . "</p>";
    echo "<p>Hidden count: " . count($valid_hidden) . "</p>";
    
    // Save the fixed configuration
    $result = \data_table_storage::save_configuration($table_name, $configuration, $user_id, $config['data_source_id'] ?? null);
    
    if ($result) {
        echo "<p style='color: green;'>Configuration fixed and saved successfully!</p>";
    } else {
        echo "<p style='color: red;'>Failed to save fixed configuration</p>";
    }
    
    return $result;
}

if (isset($_GET['table_name'])) {
    if (isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
        fix_column_preferences($_GET['table_name']);
    } else {
        echo "<h2>Fix Column Preferences for: " . htmlspecialchars($_GET['table_name']) . "</h2>";
        echo "<p>This will fix inconsistencies in the column preferences data.</p>";
        echo "<p><strong>Warning:</strong> This will modify your database. Make sure you have a backup.</p>";
        echo "<a href='?table_name=" . urlencode($_GET['table_name']) . "&confirm=yes' style='background: red; color: white; padding: 10px; text-decoration: none;'>Confirm Fix</a>";
        echo " | <a href='debug_column_preferences.php?table_name=" . urlencode($_GET['table_name']) . "'>Debug First</a>";
    }
} else {
    echo "<p>Add ?table_name=your_table_name to the URL to fix a specific table</p>";
    
    // List all configurations
    $user_id = \data_table_storage::get_current_user_id();
    $configs = \data_table_storage::list_configurations($user_id);
    
    echo "<h3>Available Configurations:</h3>";
    foreach ($configs as $config) {
        echo "<a href='?table_name=" . urlencode($config['table_name']) . "'>{$config['table_name']}</a> | ";
        echo "<a href='debug_column_preferences.php?table_name=" . urlencode($config['table_name']) . "'>Debug</a><br>";
    }
}
?>
