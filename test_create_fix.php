<?php
/**
 * Test the create_data_source API fix
 */

// Include the necessary files
require_once 'system/classes/startup_sequence.class.php';
require_once 'system/functions/functions.php';

// Initialize the system
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . '/system/config/path_schema.php';
$path['fs_system'] = $path['fs_app_root'] . '/' . $schema['system']['root'];
startup_sequence::start($path, $schema);

use system\data_source_manager;

// Include the API functions
require_once 'system/api/data_sources.api.php';

echo "<h1>Test Create Data Source API Fix</h1>";

try {
    // Get available tables to use for testing
    $available_tables = data_source_manager::get_available_tables();
    
    if (empty($available_tables)) {
        echo "<p style='color: orange;'>No tables available for testing.</p>";
        exit;
    }
    
    $test_table = $available_tables[0]['name'];
    echo "<h2>Testing with table: {$test_table}</h2>";
    
    // Prepare test parameters (minimal data source)
    $test_params = [
        'name' => 'Test Data Source ' . date('Y-m-d H:i:s'),
        'table_name' => $test_table,
        'description' => 'Test data source created by API test',
        'category' => 'other',
        'selected_tables' => json_encode([$test_table]),
        'selected_columns' => [],
        'filters' => [],
        'joins' => [],
        'table_aliases' => [],
        'column_aliases' => [],
        'custom_columns' => [],
        'sorting' => [],
        'grouping' => [],
        'limits' => []
    ];
    
    echo "<h3>Test Parameters:</h3>";
    echo "<ul>";
    foreach ($test_params as $key => $value) {
        $display_value = is_array($value) ? '[array]' : (is_string($value) && strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value);
        echo "<li><strong>$key:</strong> $display_value</li>";
    }
    echo "</ul>";
    
    // Test the create_data_source function
    echo "<h3>Testing create_data_source API function:</h3>";
    $result = \api\data_sources\create_data_source($test_params);
    
    if (is_string($result)) {
        echo "<p style='color: green;'>✓ Function returned HTML content (length: " . strlen($result) . " characters)</p>";
        
        // Check if it's a success or error message
        if (strpos($result, 'Success!') !== false) {
            echo "<p style='color: green;'>✓ Success message detected</p>";
        } elseif (strpos($result, 'Error') !== false) {
            echo "<p style='color: orange;'>⚠ Error message detected</p>";
        }
        
        echo "<h4>Returned HTML:</h4>";
        echo "<div style='border: 1px solid #ddd; padding: 10px; background: #f9f9f9;'>";
        echo $result;
        echo "</div>";
        
        echo "<h4>Raw HTML (for debugging):</h4>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; max-height: 200px;'>";
        echo htmlspecialchars($result);
        echo "</pre>";
        
    } else {
        echo "<p style='color: red;'>✗ Function returned unexpected type: " . gettype($result) . "</p>";
        echo "<pre>" . print_r($result, true) . "</pre>";
    }
    
    // Check if a data source was actually created
    echo "<h3>Checking if data source was created:</h3>";
    $all_sources = data_source_manager::get_data_sources();
    $found_test_source = null;
    
    foreach ($all_sources as $source) {
        if (strpos($source['name'], 'Test Data Source') !== false) {
            $found_test_source = $source;
            break;
        }
    }
    
    if ($found_test_source) {
        echo "<p style='color: green;'>✓ Test data source found in database:</p>";
        echo "<ul>";
        echo "<li><strong>ID:</strong> {$found_test_source['id']}</li>";
        echo "<li><strong>Name:</strong> {$found_test_source['name']}</li>";
        echo "<li><strong>Table:</strong> {$found_test_source['table_name']}</li>";
        echo "</ul>";
        
        // Clean up - delete the test data source
        $db = \system\database::table('autobooks_data_sources');
        $db->where('id', $found_test_source['id'])->delete();
        echo "<p style='color: blue;'>✓ Test data source cleaned up</p>";
        
    } else {
        echo "<p style='color: red;'>✗ Test data source not found in database</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='data_sources'>← Back to Data Sources</a></p>";
?>
