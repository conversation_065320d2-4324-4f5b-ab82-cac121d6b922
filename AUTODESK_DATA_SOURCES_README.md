# Autodesk Data Sources Migration

This document explains the Autodesk data sources migration that converts all Autodesk tables into reusable data sources for the data table system.

## Overview

The migration creates **7 comprehensive data sources** from the Autodesk tables, enabling:

- ✅ **Dynamic data tables** that can switch between hardcoded and database data
- ✅ **Advanced filtering** and search capabilities
- ✅ **Joined data** from multiple related tables
- ✅ **Calculated fields** for business intelligence
- ✅ **Specialized views** for common reporting needs

## Data Sources Created

### 1. **Autodesk Subscriptions**
- **Table**: `autodesk_subscriptions`
- **Joins**: All account relationships (Sold To, End Customer, Solution Provider, Reseller)
- **Key Features**:
  - Days to expiry calculation
  - Complete customer relationship data
  - Renewal tracking
  - Status filtering

### 2. **Autodesk Accounts**
- **Table**: `autodesk_accounts`
- **Key Features**:
  - Full contact information
  - Address concatenation
  - Email preferences
  - Account type filtering

### 3. **Autodesk Quotes**
- **Table**: `autodesk_quotes`
- **Joins**: Customer accounts, quote line items
- **Key Features**:
  - Quote expiration tracking
  - Line item counting
  - Customer relationship data
  - Amount filtering

### 4. **Autodesk Orders**
- **Table**: `autodesk_orders`
- **Joins**: Customer accounts
- **Key Features**:
  - Order status tracking
  - Fulfillment date tracking
  - Customer relationship data

### 5. **Autodesk Email History**
- **Table**: `autodesk_email_history`
- **Joins**: Subscriptions and accounts
- **Key Features**:
  - Email performance tracking
  - Subscription relationship data
  - Email type filtering

### 6. **Expiring Subscriptions** (Specialized View)
- **Table**: `autodesk_subscriptions`
- **Purpose**: Focus on subscriptions expiring within 90 days
- **Key Features**:
  - Pre-filtered for active subscriptions
  - Excludes unsubscribed customers
  - Days remaining calculation
  - Email tracking status

### 7. **Subscription Revenue Summary** (Analytics View)
- **Table**: `autodesk_subscriptions`
- **Purpose**: Revenue analysis and reporting
- **Key Features**:
  - Grouped by product and country
  - Aggregated counts and quantities
  - Time-based analysis
  - Auto-renewal statistics

## Installation

### 1. Run Migration

```bash
# Via web interface
php run_autodesk_data_sources_migration.php

# Or via command line
mysql -u username -p database_name < system/sql/autodesk_data_sources.sql
```

### 2. Verify Installation

Check that data sources were created:

```sql
SELECT name, description, table_name, status 
FROM autobooks_data_sources 
WHERE category = 'autodesk';
```

### 3. Test Data Sources

1. Visit `/system/data_sources` in your application
2. Find the Autodesk category data sources
3. Test queries and preview data
4. Use in data tables via column manager dropdown

## Usage Examples

### Basic Data Table Integration

```php
// In your view or function
function generate_subscription_table() {
    // Hardcoded fallback data
    $hardcoded_data = get_subscription_data();
    
    $columns = [
        ['label' => 'Subscription ID', 'field' => 'subscription_id', 'filter' => true],
        ['label' => 'Customer', 'field' => 'customer_name', 'filter' => true],
        ['label' => 'Product', 'field' => 'product_name', 'filter' => true],
        ['label' => 'Status', 'field' => 'status', 'filter' => true],
        ['label' => 'Expiry Date', 'field' => 'expiry_date', 'filter' => false]
    ];
    
    return data_table::process_data_table(
        ['columns' => $columns],
        $hardcoded_data,
        '',
        'generate_subscription_table',
        [], [], [], false, false, false, false, null,
        'subscription_table' // Enable data source selector
    );
}
```

### Using Data Source Selector

1. **Open Column Manager** - Click gear icon on any data table
2. **Select Data Source** - Choose from dropdown:
   - "Default (Hardcoded Data)"
   - "Autodesk Subscriptions"
   - "Expiring Subscriptions"
   - etc.
3. **Table Updates** - Automatically refreshes with new data

### Advanced Filtering

Data sources include pre-configured filters:

- **Date Ranges**: Start dates, end dates, expiration dates
- **Status Filters**: Active, expired, cancelled subscriptions
- **Customer Filters**: Company names, countries, account types
- **Product Filters**: Offering codes, product names
- **Amount Filters**: Minimum values, currency types

## Database Schema

### Data Source Structure

Each data source includes:

```json
{
    "name": "Human-readable name",
    "description": "Detailed description",
    "category": "autodesk",
    "table_name": "primary_table",
    "joins": [
        {
            "type": "LEFT JOIN",
            "table": "related_table",
            "alias": "alias",
            "condition": "join_condition"
        }
    ],
    "filters": [
        {
            "field": "table.column",
            "operator": "=|LIKE|IN|BETWEEN",
            "label": "User-friendly label"
        }
    ],
    "columns": [
        {
            "field": "table.column",
            "alias": "output_name",
            "label": "Display label"
        }
    ]
}
```

### Calculated Fields Examples

- **Days to Expiry**: `DATEDIFF(endDate, NOW())`
- **Full Address**: `CONCAT(address1, ", ", city, ", ", state)`
- **Email Status**: `CASE WHEN opened = 1 THEN "Opened" ELSE "Not Opened" END`
- **Revenue Aggregation**: `SUM(quantity * unit_price)`

## Benefits

### For Developers
- **Reusable Data Sources**: Create once, use everywhere
- **No SQL Writing**: Visual query builder handles complexity
- **Automatic Joins**: Related data automatically included
- **Consistent Filtering**: Standardized filter options

### For Users
- **Dynamic Tables**: Switch data sources without code changes
- **Advanced Search**: Multiple filter combinations
- **Real-time Data**: Always current database information
- **Specialized Views**: Purpose-built data sets

### For Business
- **Better Reporting**: Rich, joined data for analysis
- **Faster Development**: Rapid table creation
- **Data Consistency**: Single source of truth
- **Scalable Architecture**: Easy to add new data sources

## Troubleshooting

### Data Source Not Loading
1. Check data source status is 'active'
2. Verify table permissions
3. Test joins and filters in data source builder
4. Check error logs for SQL issues

### Missing Data
1. Verify join conditions are correct
2. Check filter default values
3. Ensure related tables have data
4. Test with simplified queries first

### Performance Issues
1. Add indexes to frequently filtered columns
2. Limit result sets with default filters
3. Consider creating database views for complex queries
4. Use pagination for large datasets

## Rollback

To remove all Autodesk data sources:

```bash
mysql -u username -p database_name < system/sql/rollback_autodesk_data_sources.sql
```

## Future Enhancements

The system supports:
- **Custom Calculations**: Add business-specific formulas
- **External APIs**: Connect to Autodesk APIs directly
- **Cached Results**: Performance optimization for large datasets
- **Scheduled Updates**: Automatic data refresh
- **Export Functions**: CSV, Excel, PDF generation
