# Data Table Database Storage System

This document describes the new database-based storage system for data table configurations, replacing the previous session-based approach.

## Overview

The system now stores all data table configurations in a dedicated database table `autobooks_data_table_storage`, providing:

- ✅ **Persistent storage** across sessions and browser restarts
- ✅ **User-specific configurations** with proper isolation
- ✅ **Data source integration** ready for future enhancements
- ✅ **Automatic fallback** to default configurations
- ✅ **Migration support** from session-based storage

## Database Schema

```sql
CREATE TABLE `autobooks_data_table_storage` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `table_name` varchar(255) NOT NULL,
    `user_id` int(11) DEFAULT NULL,
    `configuration` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`configuration`)),
    `data_source` varchar(255) DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    <PERSON><PERSON><PERSON><PERSON> KEY (`id`),
    UNIQUE KEY `unique_table_user` (`table_name`, `user_id`),
    KEY `idx_table_name` (`table_name`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_data_source` (`data_source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## Configuration Logic

### 1. No Configuration Exists

**When:** First time accessing a data table or after configuration deletion

**Behavior:**
- If columns are provided → Create default configuration from columns
- If no columns provided → Create blank table configuration
- Save configuration to database for future use

### 2. Configuration Exists

**When:** Subsequent access to the same table

**Behavior:**
- Load configuration from database
- **Database configuration overrides** any provided columns
- Use stored column structure, visibility, and ordering

### 3. Blank Table Mode

**When:** `Edge::render('data-table')` called with no columns

**Behavior:**
- Creates empty configuration: `{'hidden': [], 'structure': [], 'columns': []}`
- Saves to database for consistency
- Renders empty table ready for dynamic configuration

## Usage Examples

### Basic Usage (Auto-Configuration)

```php
// First call - creates default configuration from columns
$result = data_table::process_data_table(
    ['columns' => $columns],
    $data,
    '', // db_table
    '', // callback
    [], // replacements
    [], // criteria
    [], // hidden columns
    false, false, false, false, null,
    'my_table_name' // This triggers database storage
);

// Subsequent calls - uses stored configuration
$result = data_table::process_data_table(
    ['columns' => $different_columns], // These are ignored!
    $data,
    '', '', [], [], [], false, false, false, false, null,
    'my_table_name' // Uses stored config, not $different_columns
);
```

### Blank Table

```php
// Creates blank table configuration
$result = data_table::process_data_table(
    ['columns' => []], // No columns
    [],
    '', '', [], [], [], false, false, false, false, null,
    'blank_table'
);
```

### Manual Configuration Management

```php
// Get current configuration
$config = data_table_storage::get_configuration('my_table', $user_id);

// Save custom configuration
$custom_config = [
    'hidden' => ['col_1'],
    'structure' => [
        ['id' => 'col_0', 'label' => 'Name', 'fields' => ['name'], 'visible' => true],
        ['id' => 'col_2', 'label' => 'Status', 'fields' => ['status'], 'visible' => true]
    ]
];
data_table_storage::save_configuration('my_table', $custom_config, $user_id, 'my_data_source');

// Reset to default
data_table_storage::delete_configuration('my_table', $user_id);
```

## API Endpoints

### Column Management (HTMX)
- `POST /api/data_table/column_preferences/toggle_column`
- `POST /api/data_table/column_preferences/add_column`
- `POST /api/data_table/column_preferences/remove_column`
- `POST /api/data_table/column_preferences/move_field`
- `POST /api/data_table/column_preferences/reorder_columns`
- `POST /api/data_table/column_preferences/show_all_columns`
- `POST /api/data_table/column_preferences/hide_all_columns`

### Configuration Management (REST)
- `GET /api/data_table_storage/list_configurations`
- `GET /api/data_table_storage/get_configuration?table_name=X`
- `POST /api/data_table_storage/save_configuration`
- `POST /api/data_table_storage/delete_configuration`
- `POST /api/data_table_storage/reset_to_default`

## Migration from Session Storage

### Automatic Migration

Run the migration script to automatically convert session-based preferences:

```bash
php run_migration.php
```

The migration will:
1. Create the database table
2. Convert existing `$_SESSION['column_preferences_*']` data
3. Remove session data after successful migration
4. Add foreign key constraints if users table exists

### Manual Migration

```php
// Include migration script
include 'system/migrations/create_data_table_storage.php';
```

## Data Source Integration

The system is ready for future data source integration:

```php
// Save configuration with data source
data_table_storage::save_configuration(
    'my_table', 
    $config, 
    $user_id, 
    'api_endpoint_users' // Data source identifier
);

// Query by data source
$configs = data_table_storage::list_configurations($user_id);
foreach ($configs as $config) {
    if ($config['data_source'] === 'api_endpoint_users') {
        // Handle API-sourced tables differently
    }
}
```

## Classes and Files

### Core Classes
- `data_table_storage` - Database operations for configurations
- `data_table` - Updated to use database storage

### API Files
- `system/api/data_table/column_preferences.api.php` - Column management
- `system/api/data_table_storage.api.php` - Configuration management

### Migration Files
- `system/sql/create_autobooks_data_table_storage.sql` - Database schema
- `system/migrations/create_data_table_storage.php` - Migration script
- `run_migration.php` - Migration runner

### Test Files
- `test_database_storage.php` - Comprehensive testing

## Benefits

### For Developers
- **Consistent API** - Same interface, better persistence
- **Automatic fallbacks** - No configuration errors
- **Future-ready** - Data source integration built-in
- **Easy testing** - Comprehensive test suite included

### For Users
- **Persistent preferences** - Configurations survive browser restarts
- **Per-user isolation** - Each user has their own settings
- **Reliable experience** - No lost configurations
- **Better performance** - Database queries vs session serialization

## Troubleshooting

### Configuration Not Persisting
- Check database connection
- Verify user authentication (user_id)
- Ensure table_name is provided to data_table::process_data_table()

### Migration Issues
- Run migration script: `php run_migration.php`
- Check database permissions
- Verify autoload.php is working

### Blank Tables
- This is expected behavior when no columns are provided
- Use column manager to add columns dynamically
- Or provide default columns in the data_table call

## Future Enhancements

The system is designed to support:
- **Data source connectors** - API endpoints, external databases
- **Template sharing** - Export/import configurations between users
- **Version control** - Track configuration changes over time
- **Global defaults** - System-wide default configurations
- **Caching layer** - Redis/Memcached for high-traffic scenarios
