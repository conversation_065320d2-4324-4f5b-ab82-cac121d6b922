<?php
require_once 'system/startup_sequence_minimal.php';

use system\database;

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Test the database storage system
echo "<h1>Data Table Database Storage Test</h1>";

// Test 1: Create a table with no configuration (blank table)
echo "<h2>Test 1: Blank Table (No Configuration)</h2>";
echo "<p>This should create a blank table configuration in the database.</p>";

$blank_table_name = 'test_blank_table';
$blank_columns = []; // No columns provided

try {
    $blank_result = data_table::process_data_table(
        ['columns' => $blank_columns],
        [], // No data
        '', // No db_table
        '', // No callback
        [], // No replacements
        [], // No criteria
        [], // No hidden columns
        false, // Not just body
        false, // Not just rows
        false, // Not just table
        false, // No HTMX OOB
        null, // No total count
        $blank_table_name // Table name for storage
    );
    
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
    echo $blank_result;
    echo "</div>";
    
    // Check what was stored in database
    $user_id = \data_table_storage::get_current_user_id();
    $stored_config = \data_table_storage::get_configuration($blank_table_name, $user_id);
    echo "<p><strong>Stored Configuration:</strong></p>";
    echo "<pre>" . json_encode($stored_config, JSON_PRETTY_PRINT) . "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// Test 2: Create a table with default configuration
echo "<h2>Test 2: Table with Default Configuration</h2>";
echo "<p>This should create a default configuration from provided columns.</p>";

$default_table_name = 'test_default_table';
$default_columns = [
    [
        'label' => 'Name',
        'field' => 'name',
        'filter' => true,
        'extra_parameters' => ''
    ],
    [
        'label' => 'Email',
        'field' => 'email',
        'filter' => true,
        'extra_parameters' => ''
    ],
    [
        'label' => 'Status',
        'field' => 'status',
        'filter' => false,
        'extra_parameters' => ''
    ]
];

$sample_data = [
    ['name' => 'John Doe', 'email' => '<EMAIL>', 'status' => 'Active'],
    ['name' => 'Jane Smith', 'email' => '<EMAIL>', 'status' => 'Inactive'],
    ['name' => 'Bob Johnson', 'email' => '<EMAIL>', 'status' => 'Active']
];

try {
    $default_result = data_table::process_data_table(
        ['columns' => $default_columns],
        $sample_data,
        '', // No db_table
        '', // No callback
        [], // No replacements
        [], // No criteria
        [], // No hidden columns
        false, // Not just body
        false, // Not just rows
        false, // Not just table
        false, // No HTMX OOB
        null, // No total count
        $default_table_name // Table name for storage
    );
    
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
    echo $default_result;
    echo "</div>";
    
    // Check what was stored in database
    $user_id = \data_table_storage::get_current_user_id();
    $stored_config = \data_table_storage::get_configuration($default_table_name, $user_id);
    echo "<p><strong>Stored Configuration:</strong></p>";
    echo "<pre>" . json_encode($stored_config, JSON_PRETTY_PRINT) . "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// Test 3: Modify configuration and reload
echo "<h2>Test 3: Modified Configuration</h2>";
echo "<p>This modifies the configuration and shows how the database overrides default columns.</p>";

try {
    // Modify the configuration
    $user_id = \data_table_storage::get_current_user_id();
    $modified_config = [
        'hidden' => ['col_1_' . md5('Email')], // Hide email column
        'structure' => [
            [
                'id' => 'col_0_' . md5('Name'),
                'label' => 'Full Name', // Changed label
                'fields' => ['name'],
                'filter' => true,
                'visible' => true
            ],
            [
                'id' => 'col_2_' . md5('Status'),
                'label' => 'User Status', // Changed label
                'fields' => ['status'],
                'filter' => true, // Changed to filterable
                'visible' => true
            ],
            [
                'id' => 'col_new_custom',
                'label' => 'Custom Column', // New custom column
                'fields' => ['name', 'status'], // Combined fields
                'filter' => false,
                'visible' => true
            ]
        ],
        'columns' => $default_columns,
        'modified_at' => date('Y-m-d H:i:s')
    ];
    
    // Save modified configuration
    \data_table_storage::save_configuration($default_table_name, $modified_config, $user_id, 'test_data_source');
    
    // Reload the table - it should use the modified configuration
    $modified_result = data_table::process_data_table(
        ['columns' => $default_columns], // Original columns provided
        $sample_data,
        '', // No db_table
        '', // No callback
        [], // No replacements
        [], // No criteria
        [], // No hidden columns
        false, // Not just body
        false, // Not just rows
        false, // Not just table
        false, // No HTMX OOB
        null, // No total count
        $default_table_name // Same table name - should use stored config
    );
    
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
    echo $modified_result;
    echo "</div>";
    
    // Show the stored configuration
    $stored_config = \data_table_storage::get_configuration($default_table_name, $user_id);
    echo "<p><strong>Modified Configuration in Database:</strong></p>";
    echo "<pre>" . json_encode($stored_config, JSON_PRETTY_PRINT) . "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// Test 4: List all configurations
echo "<h2>Test 4: List All Configurations</h2>";

try {
    $user_id = \data_table_storage::get_current_user_id();
    $all_configs = \data_table_storage::list_configurations($user_id);
    
    echo "<p><strong>All stored configurations for current user:</strong></p>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Table Name</th><th>Data Source</th><th>Updated At</th></tr>";
    
    foreach ($all_configs as $config) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($config['table_name']) . "</td>";
        echo "<td>" . htmlspecialchars($config['data_source'] ?? 'None') . "</td>";
        echo "<td>" . htmlspecialchars($config['updated_at']) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// Test 5: API endpoints
echo "<h2>Test 5: API Endpoints</h2>";
echo "<p>Test the REST API endpoints for configuration management.</p>";

echo "<div style='background: #f5f5f5; padding: 10px; margin: 10px 0;'>";
echo "<h3>Available API Endpoints:</h3>";
echo "<ul>";
echo "<li><code>GET /api/data_table_storage/list_configurations</code> - List all configurations</li>";
echo "<li><code>GET /api/data_table_storage/get_configuration?table_name=X</code> - Get specific configuration</li>";
echo "<li><code>POST /api/data_table_storage/save_configuration</code> - Save configuration</li>";
echo "<li><code>POST /api/data_table_storage/delete_configuration</code> - Delete configuration</li>";
echo "<li><code>POST /api/data_table_storage/reset_to_default</code> - Reset to default</li>";
echo "</ul>";
echo "</div>";

// Cleanup option
echo "<h2>Cleanup</h2>";
echo "<p>To clean up test data, you can delete the test configurations:</p>";
echo "<form method='post'>";
echo "<input type='hidden' name='cleanup' value='1'>";
echo "<button type='submit' style='background: #dc3545; color: white; padding: 10px; border: none; cursor: pointer;'>Delete Test Configurations</button>";
echo "</form>";

if (isset($_POST['cleanup'])) {
    try {
        $user_id = \data_table_storage::get_current_user_id();
        \data_table_storage::delete_configuration($blank_table_name, $user_id);
        \data_table_storage::delete_configuration($default_table_name, $user_id);
        echo "<p style='color: green;'>✓ Test configurations deleted successfully!</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error during cleanup: " . $e->getMessage() . "</p>";
    }
}

echo "<hr>";
echo "<h2>Summary</h2>";
echo "<p>This test demonstrates the new database-based data table storage system:</p>";
echo "<ul>";
echo "<li>✓ <strong>Blank tables</strong>: When no columns are provided, creates empty configuration</li>";
echo "<li>✓ <strong>Default configuration</strong>: When columns are provided, creates default configuration from them</li>";
echo "<li>✓ <strong>Database override</strong>: Stored configuration takes precedence over provided columns</li>";
echo "<li>✓ <strong>User-specific storage</strong>: Each user has their own configurations</li>";
echo "<li>✓ <strong>Data source integration</strong>: Ready for future data source features</li>";
echo "<li>✓ <strong>API endpoints</strong>: Full REST API for configuration management</li>";
echo "</ul>";
?>
