<?php
/**
 * Test script to verify data source filtering and grouping is working
 */

// Include necessary files
require_once 'system/startup_sequence_minimal.php';
require_once 'system/classes/data_source_manager.class.php';

echo "Testing Data Source Filtering and Grouping\n";
echo "==========================================\n\n";

try {
    $data_source_id = 2; // Your test_send data source
    
    // Test 1: Get data source configuration
    echo "Test 1: Data Source Configuration\n";
    echo "----------------------------------\n";
    
    $data_source = \system\data_source_manager::get_data_source($data_source_id);
    
    if ($data_source) {
        echo "✓ Data source found: " . $data_source['name'] . "\n";
        echo "  Table: " . $data_source['table_name'] . "\n";
        echo "  Filters: " . json_encode($data_source['filters'], JSO<PERSON>_PRETTY_PRINT) . "\n";
        echo "  Grouping: " . json_encode($data_source['grouping'], JSON_PRETTY_PRINT) . "\n";
        echo "  Selected columns: " . json_encode($data_source['selected_columns'], JSON_PRETTY_PRINT) . "\n";
    } else {
        echo "✗ Data source not found\n";
        exit(1);
    }
    
    // Test 2: Get data with filters applied
    echo "\nTest 2: Data Retrieval with Filters\n";
    echo "------------------------------------\n";
    
    $data_result = \system\data_source_manager::get_data_source_data($data_source_id);
    
    if ($data_result['success']) {
        echo "✓ Data retrieved successfully\n";
        echo "  Total rows returned: " . count($data_result['data']) . "\n";
        echo "  Expected: Should exclude row 6 (misc='test6 nooo') due to NOT LIKE 'nooo' filter\n";
        echo "  Expected: Should group by name, so duplicate names should be reduced\n\n";
        
        echo "Returned data:\n";
        foreach ($data_result['data'] as $index => $row) {
            echo "  Row $index: " . json_encode($row) . "\n";
        }
        
        // Analyze the results
        echo "\nAnalysis:\n";
        $emails = array_column($data_result['data'], 'email');
        $names = array_column($data_result['data'], 'name');
        $misc_values = array_column($data_result['data'], 'misc');
        
        echo "  Emails: " . implode(', ', $emails) . "\n";
        echo "  Names: " . implode(', ', $names) . "\n";
        echo "  Misc values: " . implode(', ', $misc_values) . "\n";
        
        // Check if filtering worked
        $has_nooo = false;
        foreach ($misc_values as $misc) {
            if (strpos($misc, 'nooo') !== false) {
                $has_nooo = true;
                break;
            }
        }
        
        if ($has_nooo) {
            echo "  ⚠️  WARNING: Filter not working - found 'nooo' in results\n";
        } else {
            echo "  ✓ Filter working - no 'nooo' values found\n";
        }
        
        // Check if grouping worked
        $unique_names = array_unique($names);
        if (count($unique_names) == count($names)) {
            echo "  ✓ Grouping working - all names are unique\n";
        } else {
            echo "  ⚠️  WARNING: Grouping might not be working - duplicate names found\n";
        }
        
    } else {
        echo "✗ Failed to get data: " . ($data_result['error'] ?? 'Unknown error') . "\n";
        exit(1);
    }
    
    // Test 3: Raw database query for comparison
    echo "\nTest 3: Raw Database Query (for comparison)\n";
    echo "-------------------------------------------\n";
    
    require_once 'system/classes/database.class.php';
    
    $raw_data = \system\database::table('test_mail')->get();
    echo "Raw table data (no filters):\n";
    foreach ($raw_data as $index => $row) {
        echo "  Row $index: " . json_encode($row) . "\n";
    }
    
    echo "\n✓ Test completed. Check if the data source is applying filters and grouping correctly.\n";
    
} catch (Exception $e) {
    echo "\n✗ Test failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
