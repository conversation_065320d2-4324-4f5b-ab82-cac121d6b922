<?php
// Test the dashboard_stats API call
require_once 'system/functions/database.php';
require_once 'system/functions/general.php';
require_once 'system/functions/html_output.php';
require_once 'system/classes/autodesk_api/autodesk_api.class.php';
require_once 'resources/classes/autodesk_api/autodesk_subscriptions.class.php';

echo "Testing dashboard_stats API call...\n";

try {
    // Include the API function
    require_once 'resources/api/unified.api.php';
    
    // Call the dashboard_stats function
    ob_start();
    \api\unified\dashboard_stats([]);
    $output = ob_get_clean();
    
    echo "✓ SUCCESS: dashboard_stats API call completed without errors\n";
    echo "Output: " . $output . "\n";
    
} catch (Exception $e) {
    echo "✗ FAILED: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
