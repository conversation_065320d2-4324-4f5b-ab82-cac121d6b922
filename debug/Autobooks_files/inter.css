/*
Font families defined by this CSS:
  - InterVariable  variable font for modern web browsers
  - Inter          static fonts for older web browsers (small optical size)
  - InterDisplay   static fonts for older web browsers (large optical size)

Usage example:
  :root { font-family: Inter, sans-serif; }
  @supports (font-variation-settings: normal) {
    :root { font-family: InterVariable, sans-serif; }
  }

*/
@font-face {
  font-family: InterVariable;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url('font-files/InterVariable.woff2?v=4.1') format('woff2');
}
@font-face {
  font-family: InterVariable;
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: url('font-files/InterVariable-Italic.woff2?v=4.1') format('woff2');
}
/* legacy name "Inter var" (Oct 2023) */
@font-face { font-family:'Inter var'; font-style:normal; font-weight:100 900; font-display:swap; src: url('font-files/InterVariable.woff2?v=4.1') format('woff2'); }
@font-face { font-family:'Inter var'; font-style:italic; font-weight:100 900; font-display:swap; src: url('font-files/InterVariable-Italic.woff2?v=4.1') format('woff2'); }
/* static fonts */
@font-face { font-family:Inter; font-style:normal; font-weight:100; font-display:swap; src:url("font-files/Inter-Thin.woff2?v=4.1") format("woff2"); }
@font-face { font-family:Inter; font-style:italic; font-weight:100; font-display:swap; src:url("font-files/Inter-ThinItalic.woff2?v=4.1") format("woff2"); }
@font-face { font-family:Inter; font-style:normal; font-weight:200; font-display:swap; src:url("font-files/Inter-ExtraLight.woff2?v=4.1") format("woff2"); }
@font-face { font-family:Inter; font-style:italic; font-weight:200; font-display:swap; src:url("font-files/Inter-ExtraLightItalic.woff2?v=4.1") format("woff2"); }
@font-face { font-family:Inter; font-style:normal; font-weight:300; font-display:swap; src:url("font-files/Inter-Light.woff2?v=4.1") format("woff2"); }
@font-face { font-family:Inter; font-style:italic; font-weight:300; font-display:swap; src:url("font-files/Inter-LightItalic.woff2?v=4.1") format("woff2"); }
@font-face { font-family:Inter; font-style:normal; font-weight:400; font-display:swap; src:url("font-files/Inter-Regular.woff2?v=4.1") format("woff2"); }
@font-face { font-family:Inter; font-style:italic; font-weight:400; font-display:swap; src:url("font-files/Inter-Italic.woff2?v=4.1") format("woff2"); }
@font-face { font-family:Inter; font-style:normal; font-weight:500; font-display:swap; src:url("font-files/Inter-Medium.woff2?v=4.1") format("woff2"); }
@font-face { font-family:Inter; font-style:italic; font-weight:500; font-display:swap; src:url("font-files/Inter-MediumItalic.woff2?v=4.1") format("woff2"); }
@font-face { font-family:Inter; font-style:normal; font-weight:600; font-display:swap; src:url("font-files/Inter-SemiBold.woff2?v=4.1") format("woff2"); }
@font-face { font-family:Inter; font-style:italic; font-weight:600; font-display:swap; src:url("font-files/Inter-SemiBoldItalic.woff2?v=4.1") format("woff2"); }
@font-face { font-family:Inter; font-style:normal; font-weight:700; font-display:swap; src:url("font-files/Inter-Bold.woff2?v=4.1") format("woff2"); }
@font-face { font-family:Inter; font-style:italic; font-weight:700; font-display:swap; src:url("font-files/Inter-BoldItalic.woff2?v=4.1") format("woff2"); }
@font-face { font-family:Inter; font-style:normal; font-weight:800; font-display:swap; src:url("font-files/Inter-ExtraBold.woff2?v=4.1") format("woff2"); }
@font-face { font-family:Inter; font-style:italic; font-weight:800; font-display:swap; src:url("font-files/Inter-ExtraBoldItalic.woff2?v=4.1") format("woff2"); }
@font-face { font-family:Inter; font-style:normal; font-weight:900; font-display:swap; src:url("font-files/Inter-Black.woff2?v=4.1") format("woff2"); }
@font-face { font-family:Inter; font-style:italic; font-weight:900; font-display:swap; src:url("font-files/Inter-BlackItalic.woff2?v=4.1") format("woff2"); }
@font-face { font-family:InterDisplay; font-style:normal; font-weight:100; font-display:swap; src:url("font-files/InterDisplay-Thin.woff2?v=4.1") format("woff2"); }
@font-face { font-family:InterDisplay; font-style:italic; font-weight:100; font-display:swap; src:url("font-files/InterDisplay-ThinItalic.woff2?v=4.1") format("woff2"); }
@font-face { font-family:InterDisplay; font-style:normal; font-weight:200; font-display:swap; src:url("font-files/InterDisplay-ExtraLight.woff2?v=4.1") format("woff2"); }
@font-face { font-family:InterDisplay; font-style:italic; font-weight:200; font-display:swap; src:url("font-files/InterDisplay-ExtraLightItalic.woff2?v=4.1") format("woff2"); }
@font-face { font-family:InterDisplay; font-style:normal; font-weight:300; font-display:swap; src:url("font-files/InterDisplay-Light.woff2?v=4.1") format("woff2"); }
@font-face { font-family:InterDisplay; font-style:italic; font-weight:300; font-display:swap; src:url("font-files/InterDisplay-LightItalic.woff2?v=4.1") format("woff2"); }
@font-face { font-family:InterDisplay; font-style:normal; font-weight:400; font-display:swap; src:url("font-files/InterDisplay-Regular.woff2?v=4.1") format("woff2"); }
@font-face { font-family:InterDisplay; font-style:italic; font-weight:400; font-display:swap; src:url("font-files/InterDisplay-Italic.woff2?v=4.1") format("woff2"); }
@font-face { font-family:InterDisplay; font-style:normal; font-weight:500; font-display:swap; src:url("font-files/InterDisplay-Medium.woff2?v=4.1") format("woff2"); }
@font-face { font-family:InterDisplay; font-style:italic; font-weight:500; font-display:swap; src:url("font-files/InterDisplay-MediumItalic.woff2?v=4.1") format("woff2"); }
@font-face { font-family:InterDisplay; font-style:normal; font-weight:600; font-display:swap; src:url("font-files/InterDisplay-SemiBold.woff2?v=4.1") format("woff2"); }
@font-face { font-family:InterDisplay; font-style:italic; font-weight:600; font-display:swap; src:url("font-files/InterDisplay-SemiBoldItalic.woff2?v=4.1") format("woff2"); }
@font-face { font-family:InterDisplay; font-style:normal; font-weight:700; font-display:swap; src:url("font-files/InterDisplay-Bold.woff2?v=4.1") format("woff2"); }
@font-face { font-family:InterDisplay; font-style:italic; font-weight:700; font-display:swap; src:url("font-files/InterDisplay-BoldItalic.woff2?v=4.1") format("woff2"); }
@font-face { font-family:InterDisplay; font-style:normal; font-weight:800; font-display:swap; src:url("font-files/InterDisplay-ExtraBold.woff2?v=4.1") format("woff2"); }
@font-face { font-family:InterDisplay; font-style:italic; font-weight:800; font-display:swap; src:url("font-files/InterDisplay-ExtraBoldItalic.woff2?v=4.1") format("woff2"); }
@font-face { font-family:InterDisplay; font-style:normal; font-weight:900; font-display:swap; src:url("font-files/InterDisplay-Black.woff2?v=4.1") format("woff2"); }
@font-face { font-family:InterDisplay; font-style:italic; font-weight:900; font-display:swap; src:url("font-files/InterDisplay-BlackItalic.woff2?v=4.1") format("woff2"); }

@font-feature-values InterVariable {
    @character-variant {
        cv01: 1; cv02: 2; cv03: 3; cv04: 4; cv05: 5; cv06: 6; cv07: 7; cv08: 8;
        cv09: 9; cv10: 10; cv11: 11; cv12: 12; cv13: 13;
        alt-1:            1; /* Alternate one */
        alt-3:            9; /* Flat-top three */
        open-4:           2; /* Open four */
        open-6:           3; /* Open six */
        open-9:           4; /* Open nine */
        lc-l-with-tail:   5; /* Lower-case L with tail */
        simplified-u:     6; /* Simplified u */
        alt-double-s:     7; /* Alternate German double s */
        uc-i-with-serif:  8; /* Upper-case i with serif */
        uc-g-with-spur:  10; /* Capital G with spur */
        single-story-a:  11; /* Single-story a */
        compact-lc-f:    12; /* Compact f */
        compact-lc-t:    13; /* Compact t */
    }
    @styleset {
        ss01: 1; ss02: 2; ss03: 3; ss04: 4; ss05: 5; ss06: 6; ss07: 7; ss08: 8;
        open-digits: 1;                /* Open digits */
        disambiguation: 2;             /* Disambiguation (with zero) */
        disambiguation-except-zero: 4; /* Disambiguation (no zero) */
        round-quotes-and-commas: 3;    /* Round quotes &amp; commas */
        square-punctuation: 7;         /* Square punctuation */
        square-quotes: 8;              /* Square quotes */
        circled-characters: 5;         /* Circled characters */
        squared-characters: 6;         /* Squared characters */
    }
}
@font-feature-values Inter {
    @character-variant {
        cv01: 1; cv02: 2; cv03: 3; cv04: 4; cv05: 5; cv06: 6; cv07: 7; cv08: 8;
        cv09: 9; cv10: 10; cv11: 11; cv12: 12; cv13: 13;
        alt-1:            1; /* Alternate one */
        alt-3:            9; /* Flat-top three */
        open-4:           2; /* Open four */
        open-6:           3; /* Open six */
        open-9:           4; /* Open nine */
        lc-l-with-tail:   5; /* Lower-case L with tail */
        simplified-u:     6; /* Simplified u */
        alt-double-s:     7; /* Alternate German double s */
        uc-i-with-serif:  8; /* Upper-case i with serif */
        uc-g-with-spur:  10; /* Capital G with spur */
        single-story-a:  11; /* Single-story a */
        compact-lc-f:    12; /* Compact f */
        compact-lc-t:    13; /* Compact t */
    }
    @styleset {
        ss01: 1; ss02: 2; ss03: 3; ss04: 4; ss05: 5; ss06: 6; ss07: 7; ss08: 8;
        open-digits: 1;                /* Open digits */
        disambiguation: 2;             /* Disambiguation (with zero) */
        disambiguation-except-zero: 4; /* Disambiguation (no zero) */
        round-quotes-and-commas: 3;    /* Round quotes &amp; commas */
        square-punctuation: 7;         /* Square punctuation */
        square-quotes: 8;              /* Square quotes */
        circled-characters: 5;         /* Circled characters */
        squared-characters: 6;         /* Squared characters */
    }
}
@font-feature-values InterDisplay {
    @character-variant {
        cv01: 1; cv02: 2; cv03: 3; cv04: 4; cv05: 5; cv06: 6; cv07: 7; cv08: 8;
        cv09: 9; cv10: 10; cv11: 11; cv12: 12; cv13: 13;
        alt-1:            1; /* Alternate one */
        alt-3:            9; /* Flat-top three */
        open-4:           2; /* Open four */
        open-6:           3; /* Open six */
        open-9:           4; /* Open nine */
        lc-l-with-tail:   5; /* Lower-case L with tail */
        simplified-u:     6; /* Simplified u */
        alt-double-s:     7; /* Alternate German double s */
        uc-i-with-serif:  8; /* Upper-case i with serif */
        uc-g-with-spur:  10; /* Capital G with spur */
        single-story-a:  11; /* Single-story a */
        compact-lc-f:    12; /* Compact f */
        compact-lc-t:    13; /* Compact t */
    }
    @styleset {
        ss01: 1; ss02: 2; ss03: 3; ss04: 4; ss05: 5; ss06: 6; ss07: 7; ss08: 8;
        open-digits: 1;                /* Open digits */
        disambiguation: 2;             /* Disambiguation (with zero) */
        disambiguation-except-zero: 4; /* Disambiguation (no zero) */
        round-quotes-and-commas: 3;    /* Round quotes &amp; commas */
        square-punctuation: 7;         /* Square punctuation */
        square-quotes: 8;              /* Square quotes */
        circled-characters: 5;         /* Circled characters */
        squared-characters: 6;         /* Squared characters */
    }
}
