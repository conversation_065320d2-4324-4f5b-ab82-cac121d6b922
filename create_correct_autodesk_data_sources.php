<?php
/**
 * Create Correct Autodesk Data Sources
 * 
 * This script creates properly formatted Autodesk data sources matching the working format
 */

require_once 'system/startup_sequence_minimal.php';

use system\database;

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<!DOCTYPE html>";
echo "<html><head><title>Create Correct Autodesk Data Sources</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css' rel='stylesheet'>";
echo "</head><body class='bg-gray-100'>";

echo "<div class='container mx-auto py-8'>";
echo "<h1 class='text-3xl font-bold mb-8'>Create Correct Autodesk Data Sources</h1>";

// Check if migration should be run
if (isset($_POST['run_migration'])) {
    echo "<div class='bg-white rounded-lg shadow p-6 mb-8'>";
    echo "<h2 class='text-xl font-semibold mb-4'>Creating Correct Data Sources...</h2>";
    echo "<div class='space-y-2'>";
    
    $created = 0;
    $errors = 0;
    
    // First, delete any existing Autodesk data sources
    try {
        database::table('autobooks_data_sources')
            ->where('category', 'autodesk')
            ->delete();
        echo "<p class='text-blue-600'>✓ Cleared existing Autodesk data sources</p>";
    } catch (Exception $e) {
        echo "<p class='text-yellow-600'>⚠ Could not clear existing data sources: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // Define data sources with correct format (matching working entries)
    $correct_sources = [
        [
            'name' => 'Autodesk Subscriptions',
            'description' => 'Complete subscription data with customer relationships',
            'table_name' => 'autodesk_subscriptions',
            'tables' => ['autodesk_subscriptions', 'autodesk_accounts'],
            'selected_columns' => [
                'autodesk_subscriptions' => [
                    'id',
                    'subscriptionId',
                    'subscriptionReferenceNumber',
                    'quantity',
                    'status',
                    'startDate',
                    'endDate',
                    'offeringCode',
                    'offeringName',
                    'autoRenew',
                    'soldTo_id'
                ],
                'autodesk_accounts' => [
                    'id',
                    'account_csn',
                    'name',
                    'first_name',
                    'last_name',
                    'email',
                    'city',
                    'postal_code'
                ]
            ],
            'column_aliases' => [
                'subscriptionId' => 'subscription_id',
                'status' => 'status',
                'offeringName' => 'product_name', 
                'endDate' => 'end_date',
                'quantity' => 'quantity',
                'soldTo_id' => 'customer_id'
            ],
            'joins' => [
                [
                    'type' => 'LEFT',
                    'left_table' => 'autodesk_subscriptions',
                    'left_column' => 'autodesk_subscriptions.soldTo_id',
                    'right_table' => 'autodesk_accounts',
                    'right_column' => 'autodesk_accounts.id',
                    'left_alias' => '',
                    'right_alias' => 'soldto'
                ]
            ],
            'filters' => [
                [
                    'column' => 'status',
                    'operator' => '=',
                    'value' => 'Active'
                ],
                [
                    'column' => 'offeringName',
                    'operator' => 'LIKE',
                    'value' => 'AutoCAD'
                ],
                [
                    'column' => 'endDate',
                    'operator' => '>',
                    'value' => '2024-01-01'
                ]
            ]
        ],
        [
            'name' => 'Autodesk Accounts',
            'description' => 'Customer account information',
            'table_name' => 'autodesk_accounts',
            'tables' => ['autodesk_accounts'],
            'selected_columns' => [
                'autodesk_accounts' => [
                    'id',
                    'account_csn',
                    'name',
                    'first_name',
                    'last_name',
                    'email',
                    'phone',
                    'city',
                    'postal_code',
                    'country',
                    'account_type'
                ]
            ],
            'column_aliases' => [
                'id' => 'account_id',
                'name' => 'company_name',
                'email' => 'email',
                'phone' => 'phone',
                'country' => 'country',
                'account_type' => 'account_type'
            ],
            'joins' => [],
            'filters' => [
                [
                    'column' => 'name',
                    'operator' => 'LIKE',
                    'value' => 'Ltd'
                ],
                [
                    'column' => 'country',
                    'operator' => '=',
                    'value' => 'United Kingdom'
                ],
                [
                    'column' => 'account_type',
                    'operator' => '=',
                    'value' => 'Customer'
                ]
            ]
        ],
        [
            'name' => 'Expiring Subscriptions',
            'description' => 'Active subscriptions expiring within 90 days',
            'table_name' => 'autodesk_subscriptions',
            'tables' => ['autodesk_subscriptions', 'autodesk_accounts'],
            'selected_columns' => [
                'autodesk_subscriptions' => [
                    'id',
                    'subscriptionId',
                    'subscriptionReferenceNumber',
                    'quantity',
                    'status',
                    'startDate',
                    'endDate',
                    'offeringCode',
                    'offeringName',
                    'autoRenew',
                    'soldTo_id'
                ],
                'autodesk_accounts' => [
                    'id',
                    'account_csn',
                    'name',
                    'first_name',
                    'last_name',
                    'email',
                    'city',
                    'postal_code'
                ]
            ],
            'column_aliases' => [
                'subscriptionId' => 'subscription_id',
                'offeringName' => 'product_name',
                'endDate' => 'expiry_date',
                'quantity' => 'quantity',
                'soldTo_id' => 'customer_id'
            ],
            'joins' => [
                [
                    'type' => 'LEFT',
                    'left_table' => 'autodesk_subscriptions',
                    'left_column' => 'autodesk_subscriptions.soldTo_id',
                    'right_table' => 'autodesk_accounts',
                    'right_column' => 'autodesk_accounts.id',
                    'left_alias' => '',
                    'right_alias' => 'soldto'
                ]
            ],
            'filters' => [
                [
                    'column' => 'status',
                    'operator' => '=',
                    'value' => 'Active'
                ],
                [
                    'column' => 'endDate',
                    'operator' => '>',
                    'value' => '2024-01-01'
                ],
                [
                    'column' => 'endDate',
                    'operator' => '<',
                    'value' => '2024-12-31'
                ]
            ]
        ],
        [
            'name' => 'Autodesk Email History',
            'description' => 'Email communication history',
            'table_name' => 'autodesk_email_history',
            'tables' => ['autodesk_email_history', 'autodesk_subscriptions'],
            'selected_columns' => [
                'autodesk_email_history' => [
                    'id',
                    'email_type',
                    'recipient_email',
                    'subject',
                    'sent_date',
                    'status',
                    'subscription_id'
                ],
                'autodesk_subscriptions' => [
                    'id',
                    'subscriptionId',
                    'offeringName'
                ]
            ],
            'column_aliases' => [
                'id' => 'email_id',
                'email_type' => 'type',
                'recipient_email' => 'recipient',
                'subject' => 'subject',
                'sent_date' => 'sent_date',
                'status' => 'status',
                'subscription_id' => 'subscription_id'
            ],
            'joins' => [
                [
                    'type' => 'LEFT',
                    'left_table' => 'autodesk_email_history',
                    'left_column' => 'autodesk_email_history.subscription_id',
                    'right_table' => 'autodesk_subscriptions',
                    'right_column' => 'autodesk_subscriptions.id',
                    'left_alias' => '',
                    'right_alias' => 'sub'
                ]
            ],
            'filters' => [
                [
                    'column' => 'email_type',
                    'operator' => '=',
                    'value' => 'renewal'
                ],
                [
                    'column' => 'status',
                    'operator' => '=',
                    'value' => 'sent'
                ],
                [
                    'column' => 'sent_date',
                    'operator' => '>',
                    'value' => '2024-01-01'
                ]
            ]
        ]
    ];
    
    // Create each data source with correct format
    foreach ($correct_sources as $source) {
        try {
            // Insert data source with correct structure
            $result = database::table('autobooks_data_sources')->insert([
                'name' => $source['name'],
                'description' => $source['description'],
                'category' => 'autodesk',
                'table_name' => $source['table_name'],
                'tables' => json_encode($source['tables']),
                'selected_columns' => json_encode($source['selected_columns']),
                'column_aliases' => json_encode($source['column_aliases']),
                'joins' => json_encode($source['joins']),
                'filters' => json_encode($source['filters']),
                'status' => 'active',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            if ($result) {
                echo "<p class='text-green-600'>✓ Created data source: {$source['name']}</p>";
                $created++;
            } else {
                echo "<p class='text-red-600'>✗ Failed to create: {$source['name']}</p>";
                $errors++;
            }
            
        } catch (Exception $e) {
            echo "<p class='text-red-600'>✗ Error creating {$source['name']}: " . htmlspecialchars($e->getMessage()) . "</p>";
            $errors++;
        }
    }
    
    echo "<div class='mt-4 p-4 bg-gray-50 rounded'>";
    echo "<h3 class='font-semibold'>Migration Summary</h3>";
    echo "<p>Data sources created: $created</p>";
    echo "<p>Errors: $errors</p>";
    
    if ($errors === 0) {
        echo "<p class='text-green-600 font-semibold'>✓ Data sources created successfully with correct format!</p>";
        echo "<p class='text-blue-600 text-sm mt-2'>Data sources should now work properly with previews and data tables.</p>";
    } else {
        echo "<p class='text-yellow-600 font-semibold'>⚠ Migration completed with some errors</p>";
    }
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
}

// Show current data sources
echo "<div class='bg-white rounded-lg shadow p-6 mb-8'>";
echo "<h2 class='text-xl font-semibold mb-4'>Current Autodesk Data Sources</h2>";

try {
    $data_sources = database::table('autobooks_data_sources')
        ->where('category', 'autodesk')
        ->orderBy('name')
        ->get();
    
    if (!empty($data_sources)) {
        echo "<div class='space-y-4'>";
        
        foreach ($data_sources as $source) {
            echo "<div class='border border-gray-200 rounded-lg p-4'>";
            echo "<div class='flex items-center justify-between mb-2'>";
            echo "<h3 class='font-medium text-gray-900'>" . htmlspecialchars($source['name']) . "</h3>";
            echo "<span class='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium " . 
                 ($source['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800') . "'>";
            echo htmlspecialchars($source['status']);
            echo "</span>";
            echo "</div>";
            echo "<p class='text-sm text-gray-600 mb-2'>" . htmlspecialchars($source['description']) . "</p>";
            echo "<div class='text-xs text-gray-500 space-y-1'>";
            echo "<div>Table: " . htmlspecialchars($source['table_name']) . "</div>";
            if (!empty($source['selected_columns'])) {
                $columns = json_decode($source['selected_columns'], true);
                echo "<div>Columns: " . count($columns) . " selected</div>";
            }
            if (!empty($source['joins'])) {
                $joins = json_decode($source['joins'], true);
                echo "<div>Joins: " . count($joins) . " configured</div>";
            }
            if (!empty($source['filters'])) {
                $filters = json_decode($source['filters'], true);
                echo "<div>Filters: " . count($filters) . " available</div>";
            }
            echo "</div>";
            echo "</div>";
        }
        
        echo "</div>";
        echo "<p class='mt-4 text-sm text-gray-600'>Found " . count($data_sources) . " Autodesk data sources.</p>";
    } else {
        echo "<p class='text-gray-500'>No Autodesk data sources found.</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='text-red-600'>Error loading data sources: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Migration controls
if (empty($data_sources)) {
    echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8'>";
    echo "<h2 class='text-lg font-semibold text-blue-900 mb-3'>Create Correct Data Sources</h2>";
    echo "<p class='text-blue-800 mb-4'>Create Autodesk data sources with the correct format that matches working entries.</p>";
    echo "<form method='post'>";
    echo "<button type='submit' name='run_migration' value='1' class='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700'>";
    echo "Create Correct Data Sources";
    echo "</button>";
    echo "</form>";
    echo "</div>";
} else {
    echo "<div class='bg-green-50 border border-green-200 rounded-lg p-6 mb-8'>";
    echo "<h2 class='text-lg font-semibold text-green-900 mb-3'>Data Sources Ready</h2>";
    echo "<p class='text-green-800 mb-4'>Autodesk data sources are configured with the correct format.</p>";
    echo "<div class='space-y-2'>";
    echo "<a href='test_integrated_data_source_selector.php' class='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 mr-2'>";
    echo "Test Data Source Selector";
    echo "</a>";
    echo "<a href='" . APP_ROOT . "/system/data_sources' class='inline-flex items-center px-4 py-2 border border-green-600 text-sm font-medium rounded-md text-green-600 bg-white hover:bg-green-50'>";
    echo "View Data Sources";
    echo "</a>";
    echo "</div>";
    echo "</div>";
}

echo "</div>"; // container
echo "</body></html>";
?>
