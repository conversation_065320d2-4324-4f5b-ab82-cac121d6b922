# Enhanced Data Table Column Manager

This document describes the advanced hierarchical column management features added to the data table component.

## Features

### 1. Column Visibility Toggle
- Users can show/hide entire columns using checkboxes in the column manager dropdown
- Hidden columns are completely removed from both the table header and data rows
- Changes are applied immediately via HTMX

### 2. Hierarchical Column Management
- **Column Reordering**: Drag columns by their main handles to reorder them
- **Field Combination**: Drag individual fields between columns to combine them
- **Multi-field Support**: Columns can contain multiple database fields displayed together
- **Single-level Nesting**: Fields can be nested under parent columns (one level only)

### 3. Advanced Field Management
- **Visual Field Indicators**: Each column shows field count badges and individual field chips
- **Drag-and-Drop Fields**: Move fields between columns using dedicated field drag handles
- **Field Removal**: Remove individual fields from multi-field columns
- **Drop Zone Feedback**: Clear visual indicators when columns are empty

### 4. Dynamic Column and Field Management
- **Add New Columns**: Create custom columns with user-defined names
- **Remove Columns**: Delete unwanted columns entirely
- **Add New Fields**: Define new field names for use in columns
- **Add Fields to Columns**: Use dropdown to add available fields to any column

### 5. Enhanced User Interface
- **Full-height Interface**: Column manager uses 80% of viewport height for better visibility
- **Wider Layout**: 384px width provides more space for complex operations
- **Organized Sections**: Header with actions, scrollable content area, footer with statistics
- **Real-time Statistics**: Shows total column and field counts

### 6. Persistent Preferences
- Column structure (visibility, order, and field combinations) stored in user's session
- Preferences persist across page reloads and navigation
- Can be easily extended to store in database for permanent user preferences

## Usage

### Basic Implementation

```php
<x-data-table
    :items="$data"
    :columns="$columns"
    :table_name="'my_table'"
    :show_column_manager="true"
    callback="my_callback_function"
    :column_preferences="$column_preferences"
></x-data-table>
```

### Required Parameters

- `table_name`: Unique identifier for the table (used for storing preferences)
- `show_column_manager`: Set to `true` to enable column management
- `callback`: Function name that will be called when preferences change
- `column_preferences`: Array containing user's current preferences

### Callback Function

Your callback function should:
1. Accept a `$criteria` parameter
2. Return the updated table HTML using `data_table::process_data_table()`
3. Include the `$table_name` parameter in the process_data_table call
4. Handle column structure preferences if needed

Example:
```php
function my_callback_function($criteria = []) {
    $table_name = 'my_table';

    // Get your data and columns
    $data = get_my_data($criteria);
    $columns = get_my_columns();

    // Apply column structure preferences if they exist
    $preference_key = "column_preferences_{$table_name}";
    $column_preferences = $_SESSION[$preference_key] ?? [];

    return data_table::process_data_table(
        ['columns' => $columns],
        $data,
        'my_callback_function',
        [],
        $criteria,
        [],
        true, // just_body
        false,
        false,
        null,
        $table_name
    );
}
```

### Multi-field Column Example
```php
$columns = [
    [
        'label' => 'User Details',
        'field' => ['first_name', 'last_name', 'email'], // Multiple fields
        'filter' => false,
        'extra_parameters' => ''
    ],
    [
        'label' => 'Status',
        'field' => 'status', // Single field
        'filter' => true,
        'extra_parameters' => ''
    ]
];
```

## Files Modified/Created

### New Files
- `system/components/edges/data-table-column-manager.edge.php` - Column manager component
- `api/data_table/column_preferences.php` - API endpoint for saving preferences
- `test_column_manager.php` - Test/demo page

### Modified Files
- `system/components/edges/data-table.edge.php` - Added column manager integration
- `system/classes/data_table.class.php` - Added column preference support

## Technical Details

### Column Preferences Structure
```php
$column_preferences = [
    'hidden' => ['col_0_abc123', 'col_2_def456'], // Array of hidden column IDs
    'structure' => [
        [
            'id' => 'col_0_abc123',
            'label' => 'User Info',
            'fields' => ['name', 'email'], // Combined fields
            'filter' => true,
            'visible' => true
        ],
        [
            'id' => 'col_1_xyz789',
            'label' => 'Status',
            'fields' => ['status'],
            'filter' => true,
            'visible' => false // Hidden column
        ]
    ],
    'updated_at' => '2024-01-15 10:30:00'
];
```

### Column ID Generation
- Each column gets a unique ID: `'col_' . $index . '_' . md5($column['label'])`
- IDs are consistent across requests for the same column structure
- Used for tracking visibility and maintaining relationships

### Multi-field Column Support
- Columns can contain single fields: `'field' => 'name'`
- Or multiple fields: `'field' => ['name', 'email', 'role']`
- Data table automatically handles both cases in rendering

### Session Storage
Preferences are stored in `$_SESSION["column_preferences_{$table_name}"]`

### API Endpoint
- **URL**: `/api/data_table/column_preferences.php`
- **Method**: POST
- **Parameters**:
  - `table_name`: Table identifier
  - `callback`: Callback function name
  - `hidden_columns`: JSON array of hidden column IDs
  - `column_structure`: JSON array of complete column structure

## Dependencies

- **SortableJS**: Already included in `system/components/edges/layout-head.edge.php`
- **Alpine.js**: For reactive UI components
- **HTMX**: For server communication
- **Tailwind CSS**: For styling

## Browser Compatibility

- Modern browsers with JavaScript enabled
- Drag-and-drop requires mouse or touch input
- Gracefully degrades if JavaScript is disabled (column manager won't appear)

## Future Enhancements

1. **Database Storage**: Move preferences from session to database for permanent storage
2. **Column Width**: Add ability to resize column widths
3. **Column Grouping**: Allow grouping related columns
4. **Export Preferences**: Allow users to export/import column configurations
5. **Admin Defaults**: Allow administrators to set default column configurations

## Testing

Use `test_column_manager.php` to test the functionality:

### Basic Operations:
1. Open the test page in your browser
2. Click the "Columns" button to open the full-height manager
3. Try hiding/showing columns using checkboxes
4. Try dragging columns to reorder them
5. Refresh the page to verify preferences persist

### Advanced Operations:
6. **Add New Field**: Click "+ Field", enter a field name (e.g., "phone"), click Add
7. **Add New Column**: Click "+ Column", enter a column name (e.g., "Contact Info"), click Add
8. **Combine Fields**: Use the dropdown in any column to add available fields
9. **Drag Fields**: Drag field chips between columns to reorganize
10. **Remove Elements**: Use trash icons to remove columns or X buttons to remove fields

### Test Scenarios:
- Create a "Contact" column and add "name", "email", "phone" fields to it
- Split the existing "Full Info" column by dragging its fields to other columns
- Add a custom field like "notes" and assign it to a column
- Remove unused columns to clean up the interface
