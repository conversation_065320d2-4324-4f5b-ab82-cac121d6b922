<?php
/**
 * Test Integrated Data Source Selector
 * 
 * This demonstrates the data source selector integrated into the column manager
 */

require_once 'system/startup_sequence_minimal.php';

use system\database;

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<!DOCTYPE html>";
echo "<html><head><title>Integrated Data Source Selector Test</title>";
echo "<script src='https://unpkg.com/htmx.org@1.9.10'></script>";
echo "<script src='https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js' defer></script>";
echo "<script src='https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js'></script>";
echo "<link href='https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css' rel='stylesheet'>";
echo "</head><body class='bg-gray-100'>";

echo "<div class='container mx-auto py-8'>";
echo "<h1 class='text-3xl font-bold mb-8'>Integrated Data Source Selector Test</h1>";

// Test 1: Table with Integrated Data Source Selector
echo "<div class='bg-white rounded-lg shadow p-6 mb-8'>";
echo "<h2 class='text-xl font-semibold mb-4'>Test 1: Data Table with Integrated Data Source Selector</h2>";
echo "<p class='text-gray-600 mb-4'>Open the column manager to see the data source selector dropdown at the top.</p>";

// Sample hardcoded data
$sample_data = [
    ['id' => 1, 'name' => 'John Doe', 'email' => '<EMAIL>', 'status' => 'Active', 'created_at' => '2024-01-15'],
    ['id' => 2, 'name' => 'Jane Smith', 'email' => '<EMAIL>', 'status' => 'Inactive', 'created_at' => '2024-01-16'],
    ['id' => 3, 'name' => 'Bob Johnson', 'email' => '<EMAIL>', 'status' => 'Active', 'created_at' => '2024-01-17'],
    ['id' => 4, 'name' => 'Alice Brown', 'email' => '<EMAIL>', 'status' => 'Pending', 'created_at' => '2024-01-18']
];

// Sample columns
$sample_columns = [
    [
        'label' => 'ID',
        'field' => 'id',
        'filter' => false,
        'extra_parameters' => ''
    ],
    [
        'label' => 'Name',
        'field' => 'name',
        'filter' => true,
        'extra_parameters' => ''
    ],
    [
        'label' => 'Email',
        'field' => 'email',
        'filter' => true,
        'extra_parameters' => ''
    ],
    [
        'label' => 'Status',
        'field' => 'status',
        'filter' => true,
        'extra_parameters' => ''
    ],
    [
        'label' => 'Created Date',
        'field' => 'created_at',
        'filter' => false,
        'extra_parameters' => ''
    ]
];

try {
    $table_result = data_table::process_data_table(
        ['columns' => $sample_columns],
        $sample_data,
        '', // No db_table
        'test_integrated_callback', // Callback function
        [], // No replacements
        [], // No criteria
        [], // No hidden columns
        false, // Not just body
        false, // Not just rows
        false, // Not just table
        false, // No HTMX OOB
        null, // No total count
        'test_integrated_table' // Table name for storage
    );
    
    echo $table_result;
    
} catch (Exception $e) {
    echo "<p class='text-red-600'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Test 2: Current Configuration Status
echo "<div class='bg-white rounded-lg shadow p-6 mb-8'>";
echo "<h2 class='text-xl font-semibold mb-4'>Test 2: Current Configuration Status</h2>";
echo "<p class='text-gray-600 mb-4'>Current data source configuration for the test table:</p>";

try {
    $user_id = \data_table_storage::get_current_user_id();
    $config = \data_table_storage::get_configuration('test_integrated_table', $user_id);
    
    if ($config) {
        $configuration = $config['configuration'];
        $data_source_type = $configuration['data_source_type'] ?? 'hardcoded';
        $data_source_id = $configuration['data_source_id'] ?? null;
        
        echo "<div class='bg-gray-50 rounded-md p-4'>";
        echo "<div class='grid grid-cols-2 gap-4'>";
        echo "<div>";
        echo "<h3 class='font-medium text-gray-900 mb-2'>Data Source Type</h3>";
        if ($data_source_type === 'hardcoded') {
            echo "<span class='inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800'>Hardcoded Data</span>";
        } else {
            echo "<span class='inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800'>Database Data Source</span>";
        }
        echo "</div>";
        echo "<div>";
        echo "<h3 class='font-medium text-gray-900 mb-2'>Data Source ID</h3>";
        echo "<span class='text-gray-600'>" . ($data_source_id ?? 'None') . "</span>";
        echo "</div>";
        echo "</div>";
        
        echo "<details class='mt-4'>";
        echo "<summary class='cursor-pointer text-sm font-medium text-gray-700'>Full Configuration</summary>";
        echo "<pre class='mt-2 text-xs bg-white p-3 rounded border overflow-x-auto'>" . json_encode($configuration, JSON_PRETTY_PRINT) . "</pre>";
        echo "</details>";
        echo "</div>";
    } else {
        echo "<p class='text-gray-500'>No configuration found for test table.</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='text-red-600'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Test 3: Available Data Sources
echo "<div class='bg-white rounded-lg shadow p-6 mb-8'>";
echo "<h2 class='text-xl font-semibold mb-4'>Test 3: Available Data Sources</h2>";
echo "<p class='text-gray-600 mb-4'>Data sources available in the dropdown selector:</p>";

try {
    $data_sources = database::table('autobooks_data_sources')
        ->where('status', '=', 'active')
        ->get();
    
    if (!empty($data_sources)) {
        echo "<div class='overflow-x-auto'>";
        echo "<table class='min-w-full divide-y divide-gray-200'>";
        echo "<thead class='bg-gray-50'>";
        echo "<tr>";
        echo "<th class='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>ID</th>";
        echo "<th class='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>Name</th>";
        echo "<th class='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>Description</th>";
        echo "<th class='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>Category</th>";
        echo "<th class='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>Status</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody class='bg-white divide-y divide-gray-200'>";
        
        foreach ($data_sources as $source) {
            echo "<tr>";
            echo "<td class='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>" . htmlspecialchars($source['id']) . "</td>";
            echo "<td class='px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900'>" . htmlspecialchars($source['name']) . "</td>";
            echo "<td class='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>" . htmlspecialchars($source['description'] ?? '') . "</td>";
            echo "<td class='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>" . htmlspecialchars($source['category']) . "</td>";
            echo "<td class='px-6 py-4 whitespace-nowrap'>";
            echo "<span class='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800'>";
            echo htmlspecialchars($source['status']);
            echo "</span>";
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
    } else {
        echo "<div class='text-center py-8'>";
        echo "<p class='text-gray-500 mb-4'>No data sources found.</p>";
        echo "<a href='" . APP_ROOT . "/system/data_sources' class='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700'>";
        echo "Create Data Source";
        echo "</a>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p class='text-red-600'>Error loading data sources: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Instructions
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6'>";
echo "<h2 class='text-lg font-semibold text-blue-900 mb-3'>How to Test</h2>";
echo "<ol class='list-decimal list-inside space-y-2 text-blue-800'>";
echo "<li>Click the <strong>Column Manager</strong> button (gear icon) in the top-right of the table</li>";
echo "<li>Look for the <strong>Data Source</strong> dropdown at the top of the column manager panel</li>";
echo "<li>The dropdown shows 'Default (Hardcoded Data)' and any available data sources grouped by category</li>";
echo "<li>Select a different data source to switch the table data</li>";
echo "<li>The table will automatically reload with data from the selected source</li>";
echo "<li>The selection is saved to the database and will persist across sessions</li>";
echo "</ol>";
echo "</div>";

echo "</div>"; // container
echo "</body></html>";

// Callback function for the data table
function test_integrated_callback($criteria = []) {
    // This would normally process the criteria and return filtered data
    // For testing, we'll just return the same data
    $sample_data = [
        ['id' => 1, 'name' => 'John Doe', 'email' => '<EMAIL>', 'status' => 'Active', 'created_at' => '2024-01-15'],
        ['id' => 2, 'name' => 'Jane Smith', 'email' => '<EMAIL>', 'status' => 'Inactive', 'created_at' => '2024-01-16'],
        ['id' => 3, 'name' => 'Bob Johnson', 'email' => '<EMAIL>', 'status' => 'Active', 'created_at' => '2024-01-17'],
        ['id' => 4, 'name' => 'Alice Brown', 'email' => '<EMAIL>', 'status' => 'Pending', 'created_at' => '2024-01-18']
    ];
    
    $sample_columns = [
        [
            'label' => 'ID',
            'field' => 'id',
            'filter' => false,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Name',
            'field' => 'name',
            'filter' => true,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Email',
            'field' => 'email',
            'filter' => true,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Status',
            'field' => 'status',
            'filter' => true,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Created Date',
            'field' => 'created_at',
            'filter' => false,
            'extra_parameters' => ''
        ]
    ];
    
    return data_table::process_data_table(
        ['columns' => $sample_columns],
        $sample_data,
        '',
        'test_integrated_callback',
        [],
        $criteria,
        [],
        false,
        false,
        false,
        false,
        null,
        'test_integrated_table'
    );
}
?>
