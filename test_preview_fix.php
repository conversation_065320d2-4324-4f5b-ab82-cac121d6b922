<?php
/**
 * Test preview function with custom tables fix
 */

// Include the necessary files
require_once 'system/classes/startup_sequence.class.php';
require_once 'system/functions/functions.php';

// Initialize the system
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . '/system/config/path_schema.php';
$path['fs_system'] = $path['fs_app_root'] . '/' . $schema['system']['root'];
startup_sequence::start($path, $schema);

use system\data_source_manager;

echo "<h1>Test Preview Function with Custom Tables Fix</h1>";

try {
    $data_source_id = 30; // Your data source ID
    
    echo "<h2>1. Testing data_source_manager::get_data_source_data():</h2>";
    
    // Test the get_data_source_data method directly
    $result = data_source_manager::get_data_source_data($data_source_id, ['limit' => 5]);
    
    if ($result['success'] ?? false) {
        echo "<p style='color: green;'>✓ get_data_source_data succeeded</p>";
        echo "<p><strong>Row count:</strong> " . ($result['count'] ?? 0) . "</p>";
        
        // Show the generated query
        if (!empty($result['query'])) {
            echo "<h3>Generated Query:</h3>";
            echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto;'>";
            echo htmlspecialchars($result['query']);
            echo "</pre>";
            
            // Check if custom table is in the query
            if (strpos($result['query'], 'lastquote') !== false) {
                echo "<p style='color: green;'>✓ Custom table 'lastquote' found in query</p>";
            } else {
                echo "<p style='color: red;'>✗ Custom table 'lastquote' not found in query</p>";
            }
            
            // Check if custom table columns are in SELECT
            if (strpos($result['query'], 'lastquote.quote_id') !== false) {
                echo "<p style='color: green;'>✓ Custom table columns found in SELECT clause</p>";
            } else {
                echo "<p style='color: red;'>✗ Custom table columns not found in SELECT clause</p>";
            }
            
            // Check if custom table is in FROM/JOIN clause
            if (strpos($result['query'], 'LEFT JOIN (') !== false && 
                strpos($result['query'], ') AS `lastquote`') !== false) {
                echo "<p style='color: green;'>✓ Custom table found in FROM/JOIN clause</p>";
            } else {
                echo "<p style='color: red;'>✗ Custom table not found in FROM/JOIN clause</p>";
            }
        }
        
        // Show sample data
        if (!empty($result['data'])) {
            echo "<h3>Sample Data (first row):</h3>";
            $first_row = $result['data'][0];
            echo "<ul>";
            foreach ($first_row as $column => $value) {
                $display_value = is_null($value) ? 'NULL' : (strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value);
                echo "<li><strong>$column:</strong> " . htmlspecialchars($display_value) . "</li>";
            }
            echo "</ul>";
            
            // Check for custom table columns in the data
            $custom_columns_found = [];
            foreach ($first_row as $column => $value) {
                if (strpos($column, 'lastquote_') === 0) {
                    $custom_columns_found[] = $column;
                }
            }
            
            if (!empty($custom_columns_found)) {
                echo "<p style='color: green;'>✓ Custom table columns found in data: " . implode(', ', $custom_columns_found) . "</p>";
            } else {
                echo "<p style='color: orange;'>⚠ No custom table columns found in data (might be NULL values)</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>✗ get_data_source_data failed</p>";
        if (!empty($result['error'])) {
            echo "<p><strong>Error:</strong> " . htmlspecialchars($result['error']) . "</p>";
        }
    }
    
    echo "<h2>2. Testing preview_view API function:</h2>";
    
    // Include the API functions
    require_once 'system/api/data_sources.api.php';
    
    $preview_result = \api\data_sources\preview_view(['id' => $data_source_id]);
    
    if (is_string($preview_result) && strlen($preview_result) > 100) {
        echo "<p style='color: green;'>✓ preview_view returned HTML content</p>";
        
        // Check if it contains error messages
        if (strpos($preview_result, 'Database Error') !== false) {
            echo "<p style='color: red;'>✗ preview_view contains database error</p>";
        } else {
            echo "<p style='color: green;'>✓ preview_view does not contain database errors</p>";
        }
        
        // Check if it contains data table
        if (strpos($preview_result, '<table') !== false) {
            echo "<p style='color: green;'>✓ preview_view contains data table</p>";
        } else {
            echo "<p style='color: orange;'>⚠ preview_view does not contain data table</p>";
        }
        
        // Check for custom table column headers
        if (strpos($preview_result, 'lastquote_quote_id') !== false || 
            strpos($preview_result, 'quote_id') !== false) {
            echo "<p style='color: green;'>✓ Custom table columns found in preview</p>";
        } else {
            echo "<p style='color: orange;'>⚠ Custom table columns not found in preview</p>";
        }
        
        echo "<h3>Preview HTML (first 1000 characters):</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; max-height: 300px;'>";
        echo htmlspecialchars(substr($preview_result, 0, 1000));
        echo "</pre>";
        
    } else {
        echo "<p style='color: red;'>✗ preview_view failed or returned unexpected result</p>";
        echo "<pre>" . htmlspecialchars(print_r($preview_result, true)) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='data_sources'>← Back to Data Sources</a></p>";
?>
