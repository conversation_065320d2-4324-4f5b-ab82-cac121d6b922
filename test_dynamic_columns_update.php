<?php
/**
 * Test Dynamic Columns Update
 * 
 * This demonstrates how columns update when switching data sources
 */

require_once 'system/startup_sequence_minimal.php';

use system\database;

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Dynamic Columns Update</title>";
echo "<script src='https://unpkg.com/htmx.org@1.9.10'></script>";
echo "<script src='https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js' defer></script>";
echo "<script src='https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js'></script>";
echo "<link href='https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css' rel='stylesheet'>";
echo "</head><body class='bg-gray-100'>";

echo "<div class='container mx-auto py-8'>";
echo "<h1 class='text-3xl font-bold mb-8'>Test Dynamic Columns Update</h1>";

// Test 1: Table that demonstrates column updates
echo "<div class='bg-white rounded-lg shadow p-6 mb-8'>";
echo "<h2 class='text-xl font-semibold mb-4'>Dynamic Column Update Demo</h2>";
echo "<p class='text-gray-600 mb-4'>This table demonstrates how columns automatically update when you switch data sources in the column manager.</p>";

// Sample hardcoded data (simple structure)
$sample_data = [
    ['id' => 1, 'name' => 'Sample Item 1', 'status' => 'Active'],
    ['id' => 2, 'name' => 'Sample Item 2', 'status' => 'Inactive'],
    ['id' => 3, 'name' => 'Sample Item 3', 'status' => 'Active']
];

// Simple columns for hardcoded data
$simple_columns = [
    [
        'label' => 'ID',
        'field' => 'id',
        'filter' => false,
        'extra_parameters' => ''
    ],
    [
        'label' => 'Name',
        'field' => 'name',
        'filter' => true,
        'extra_parameters' => ''
    ],
    [
        'label' => 'Status',
        'field' => 'status',
        'filter' => true,
        'extra_parameters' => ''
    ]
];

try {
    $table_result = data_table::process_data_table(
        ['columns' => $simple_columns],
        $sample_data,
        '', // No db_table
        'test_dynamic_columns_callback', // Callback function
        [], // No replacements
        [], // No criteria
        [], // No hidden columns
        false, // Not just body
        false, // Not just rows
        false, // Not just table
        false, // No HTMX OOB
        null, // No total count
        'dynamic_columns_test_table' // Table name for storage
    );
    
    echo $table_result;
    
} catch (Exception $e) {
    echo "<p class='text-red-600'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Test 2: Show available data sources
echo "<div class='bg-white rounded-lg shadow p-6 mb-8'>";
echo "<h2 class='text-xl font-semibold mb-4'>Available Autodesk Data Sources</h2>";
echo "<p class='text-gray-600 mb-4'>These data sources have different column structures that will update the table when selected:</p>";

try {
    $data_sources = database::table('autobooks_data_sources')
        ->where('category', 'autodesk')
        ->where('status', 'active')
        ->orderBy('name')
        ->get();
    
    if (!empty($data_sources)) {
        echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";
        
        foreach ($data_sources as $source) {
            echo "<div class='border border-gray-200 rounded-lg p-4'>";
            echo "<h3 class='font-medium text-gray-900 mb-2'>" . htmlspecialchars($source['name']) . "</h3>";
            echo "<p class='text-sm text-gray-600 mb-3'>" . htmlspecialchars($source['description']) . "</p>";
            
            // Show column count
            $selected_columns = json_decode($source['selected_columns'], true);
            $total_columns = 0;
            if (is_array($selected_columns)) {
                foreach ($selected_columns as $table => $columns) {
                    if (is_array($columns)) {
                        $total_columns += count($columns);
                    }
                }
            }
            
            echo "<div class='text-xs text-gray-500 space-y-1'>";
            echo "<div>Table: " . htmlspecialchars($source['table_name']) . "</div>";
            echo "<div>Columns: $total_columns available</div>";
            
            // Show some sample columns
            if (is_array($selected_columns)) {
                $sample_cols = [];
                foreach ($selected_columns as $table => $columns) {
                    if (is_array($columns)) {
                        $sample_cols = array_merge($sample_cols, array_slice($columns, 0, 3));
                        if (count($sample_cols) >= 3) break;
                    }
                }
                if (!empty($sample_cols)) {
                    echo "<div>Sample: " . implode(', ', $sample_cols) . "...</div>";
                }
            }
            echo "</div>";
            echo "</div>";
        }
        
        echo "</div>";
    } else {
        echo "<p class='text-gray-500'>No Autodesk data sources found. <a href='create_correct_autodesk_data_sources.php' class='text-blue-600 hover:text-blue-500'>Create them first →</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p class='text-red-600'>Error loading data sources: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Test 3: Current table configuration
echo "<div class='bg-white rounded-lg shadow p-6 mb-8'>";
echo "<h2 class='text-xl font-semibold mb-4'>Current Table Configuration</h2>";
echo "<p class='text-gray-600 mb-4'>Current configuration for the test table:</p>";

try {
    $user_id = \data_table_storage::get_current_user_id();
    $config = \data_table_storage::get_configuration('dynamic_columns_test_table', $user_id);
    
    if ($config) {
        $configuration = $config['configuration'];
        
        echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-6'>";
        
        // Data source info
        echo "<div>";
        echo "<h3 class='font-medium text-gray-900 mb-2'>Data Source</h3>";
        $data_source_type = $configuration['data_source_type'] ?? 'hardcoded';
        $data_source_id = $configuration['data_source_id'] ?? null;
        
        if ($data_source_type === 'hardcoded') {
            echo "<span class='inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800'>Hardcoded Data</span>";
        } else {
            echo "<span class='inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800'>Data Source ID: $data_source_id</span>";
        }
        echo "</div>";
        
        // Column structure info
        echo "<div>";
        echo "<h3 class='font-medium text-gray-900 mb-2'>Column Structure</h3>";
        $structure = $configuration['structure'] ?? [];
        echo "<p class='text-sm text-gray-600'>" . count($structure) . " columns configured</p>";
        
        if (!empty($structure)) {
            echo "<div class='mt-2 text-xs text-gray-500'>";
            $visible_count = 0;
            foreach ($structure as $col) {
                if ($col['visible'] ?? true) $visible_count++;
            }
            echo "<div>Visible: $visible_count</div>";
            echo "<div>Hidden: " . (count($structure) - $visible_count) . "</div>";
            echo "</div>";
        }
        echo "</div>";
        
        echo "</div>";
        
        // Show column details
        if (!empty($structure)) {
            echo "<details class='mt-4'>";
            echo "<summary class='cursor-pointer text-sm font-medium text-gray-700'>Column Details</summary>";
            echo "<div class='mt-2 bg-gray-50 rounded p-3 text-xs'>";
            echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-2'>";
            foreach ($structure as $col) {
                $status = ($col['visible'] ?? true) ? 'visible' : 'hidden';
                echo "<div class='flex justify-between'>";
                echo "<span>" . htmlspecialchars($col['label'] ?? $col['field']) . "</span>";
                echo "<span class='text-gray-500'>$status</span>";
                echo "</div>";
            }
            echo "</div>";
            echo "</div>";
            echo "</details>";
        }
        
    } else {
        echo "<p class='text-gray-500'>No configuration found for test table.</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='text-red-600'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Instructions
echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6'>";
echo "<h2 class='text-lg font-semibold text-blue-900 mb-3'>How to Test Dynamic Column Updates</h2>";
echo "<ol class='list-decimal list-inside space-y-2 text-blue-800'>";
echo "<li>Click the <strong>Column Manager</strong> button (gear icon) in the table above</li>";
echo "<li>Note the current columns available (ID, Name, Status)</li>";
echo "<li>Change the <strong>Data Source</strong> dropdown from 'Default (Hardcoded Data)' to an Autodesk data source</li>";
echo "<li>Watch as the table refreshes with new columns from the selected data source</li>";
echo "<li>The column manager will now show the new available columns</li>";
echo "<li>Switch back to 'Default' to see the original simple columns return</li>";
echo "</ol>";
echo "<p class='mt-4 text-blue-700 text-sm'><strong>Note:</strong> The columns and available fields automatically update based on the selected data source structure.</p>";
echo "</div>";

echo "</div>"; // container
echo "</body></html>";

// Callback function for the data table
function test_dynamic_columns_callback($criteria = []) {
    // Simple hardcoded data for testing
    $sample_data = [
        ['id' => 1, 'name' => 'Sample Item 1', 'status' => 'Active'],
        ['id' => 2, 'name' => 'Sample Item 2', 'status' => 'Inactive'],
        ['id' => 3, 'name' => 'Sample Item 3', 'status' => 'Active']
    ];
    
    $simple_columns = [
        [
            'label' => 'ID',
            'field' => 'id',
            'filter' => false,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Name',
            'field' => 'name',
            'filter' => true,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Status',
            'field' => 'status',
            'filter' => true,
            'extra_parameters' => ''
        ]
    ];
    
    return data_table::process_data_table(
        ['columns' => $simple_columns],
        $sample_data,
        '',
        'test_dynamic_columns_callback',
        [],
        $criteria,
        [],
        false,
        false,
        false,
        false,
        null,
        'dynamic_columns_test_table'
    );
}
?>
