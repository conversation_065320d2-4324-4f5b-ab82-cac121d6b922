<?php
/**
 * Fix the test campaign by adding a proper subject template
 */

// Include the startup sequence to get database access
require_once 'system/startup_sequence.php';

use system\database;
use system\email_campaign;

echo "Fixing test campaign...\n";

try {
    $campaign_manager = new email_campaign();
    
    // Update campaign ID 3 with a proper subject template
    $success = $campaign_manager->update_campaign(3, [
        'subject_template' => 'Test Email for {{name}} - {{email}}'
    ]);
    
    if ($success) {
        echo "✓ Campaign updated successfully with subject template\n";
        
        // Get the updated campaign to verify
        $campaign = $campaign_manager->get_campaign(3);
        echo "Campaign details:\n";
        echo "  ID: " . $campaign['id'] . "\n";
        echo "  Name: " . $campaign['name'] . "\n";
        echo "  Status: " . $campaign['status'] . "\n";
        echo "  Subject Template: " . $campaign['subject_template'] . "\n";
        echo "  Data Source ID: " . $campaign['data_source_id'] . "\n";
        echo "  From Email: " . $campaign['from_email'] . "\n";
        
    } else {
        echo "✗ Failed to update campaign\n";
        exit(1);
    }
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\nTest campaign is now ready for execution!\n";
echo "You can now:\n";
echo "1. Go to the Email Campaigns page in the admin interface\n";
echo "2. Click the 'Execute' button for the 'test' campaign\n";
echo "3. Or use the Test button to send a single test email\n";
?>
