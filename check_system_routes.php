<?php
// Initialize the system
const DS = DIRECTORY_SEPARATOR;
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . DS . 'system'. DS . 'config'. DS . 'path_schema.php';
$ds = DS;
$path['fs_system'] = "{$path['fs_app_root']}{$ds}{$schema['system']['root']}" ;

$input_params = array_merge($_GET, $_POST);
define('INPUT_PARAMS',$input_params);
const DEBUG_MODE = true;
const API_RUN = false;

require_once $path['fs_system'] . DS . 'classes'. DS . 'startup_sequence.class.php';
require_once $path['fs_system'] . DS . 'functions'. DS . 'functions.php';

startup_sequence::start($path,$schema);

use system\database;

echo "<h1>System Routes Check</h1>";

try {
    // Check all system routes
    $system_routes = database::table('autobooks_navigation')
        ->where('is_system', 1)
        ->get();

    echo "<h2>Current system routes:</h2>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Route Key</th><th>Parent Path</th><th>File Path</th><th>Is System</th></tr>";
    
    foreach ($system_routes as $route) {
        echo "<tr>";
        echo "<td>" . $route['route_key'] . "</td>";
        echo "<td>" . $route['parent_path'] . "</td>";
        echo "<td>" . $route['file_path'] . "</td>";
        echo "<td>" . ($route['is_system'] ? 'true' : 'false') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check what files actually exist in system/views
    echo "<h2>Files in system/views/:</h2>";
    $system_views_dir = 'system/views';
    if (is_dir($system_views_dir)) {
        $dirs = scandir($system_views_dir);
        echo "<ul>";
        foreach ($dirs as $dir) {
            if ($dir != '.' && $dir != '..' && is_dir($system_views_dir . '/' . $dir)) {
                echo "<li><strong>$dir/</strong>";
                $files = scandir($system_views_dir . '/' . $dir);
                echo "<ul>";
                foreach ($files as $file) {
                    if ($file != '.' && $file != '..' && is_file($system_views_dir . '/' . $dir . '/' . $file)) {
                        echo "<li>$file</li>";
                    }
                }
                echo "</ul>";
                echo "</li>";
            }
        }
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
