<?php
// Test script to verify BETWEEN clause fix
require_once 'system/functions/database.php';

// Mock schema for testing
$schema = [
    'subs' => [
        'query' => 'FROM autodesk_subscriptions subs',
        'fields' => ['endDate', 'id']
    ]
];

// Test the build_where_string function with BETWEEN
$key = 'subs.endDate';
$value = ['BETWEEN', ['2025-07-15', '2025-10-13']];

$populate_column = function($column) {
    // Mock function
};

echo "Testing BETWEEN clause fix...\n";
echo "Input: key = '$key', value = " . json_encode($value) . "\n";

$result = build_where_string($key, $value, $schema, $populate_column);

echo "Result: " . json_encode($result) . "\n";

if (!empty($result)) {
    echo "Generated WHERE clause: " . $result[0] . "\n";
    
    // Check if it contains the expected format
    if (strpos($result[0], "'2025-07-15' AND '2025-10-13'") !== false) {
        echo "✓ SUCCESS: BETWEEN clause formatted correctly\n";
    } else {
        echo "✗ FAILED: BETWEEN clause not formatted correctly\n";
    }
} else {
    echo "✗ FAILED: No result returned\n";
}
?>
