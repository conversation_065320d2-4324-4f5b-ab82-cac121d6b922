<?php
/**
 * Create Complete Autodesk Data Sources
 * 
 * This script creates properly configured Autodesk data sources with joins, filters, and columns
 */

require_once 'system/startup_sequence_minimal.php';

use system\database;

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<!DOCTYPE html>";
echo "<html><head><title>Create Complete Autodesk Data Sources</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css' rel='stylesheet'>";
echo "</head><body class='bg-gray-100'>";

echo "<div class='container mx-auto py-8'>";
echo "<h1 class='text-3xl font-bold mb-8'>Create Complete Autodesk Data Sources</h1>";

// Check if migration should be run
if (isset($_POST['run_migration'])) {
    echo "<div class='bg-white rounded-lg shadow p-6 mb-8'>";
    echo "<h2 class='text-xl font-semibold mb-4'>Creating Complete Data Sources...</h2>";
    echo "<div class='space-y-2'>";
    
    $created = 0;
    $errors = 0;
    
    // First, delete any existing basic Autodesk data sources
    try {
        database::table('autobooks_data_sources')
            ->where('category', 'autodesk')
            ->delete();
        echo "<p class='text-blue-600'>✓ Cleared existing Autodesk data sources</p>";
    } catch (Exception $e) {
        echo "<p class='text-yellow-600'>⚠ Could not clear existing data sources: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // Define complete data sources with proper structure
    $complete_sources = [
        [
            'name' => 'Autodesk Subscriptions',
            'description' => 'Complete subscription data with customer relationships',
            'table_name' => 'autodesk_subscriptions',
            'tables' => ['autodesk_subscriptions', 'autodesk_accounts'],
            'table_aliases' => [
                'autodesk_subscriptions' => 'sub',
                'autodesk_accounts' => 'acc'
            ],
            'joins' => [
                [
                    'type' => 'LEFT JOIN',
                    'from_table' => 'autodesk_subscriptions',
                    'from_alias' => 'sub',
                    'to_table' => 'autodesk_accounts',
                    'to_alias' => 'acc',
                    'from_column' => 'soldTo_id',
                    'to_column' => 'id'
                ]
            ],
            'selected_columns' => [
                'sub.subscriptionId',
                'sub.status',
                'sub.offeringName',
                'sub.endDate',
                'sub.quantity',
                'acc.name',
                'acc.email'
            ],
            'column_aliases' => [
                'sub.subscriptionId' => 'subscription_id',
                'sub.status' => 'status',
                'sub.offeringName' => 'product_name',
                'sub.endDate' => 'end_date',
                'sub.quantity' => 'quantity',
                'acc.name' => 'customer_name',
                'acc.email' => 'customer_email'
            ],
            'filters' => [
                [
                    'field' => 'sub.status',
                    'operator' => '=',
                    'label' => 'Status',
                    'options' => ['Active', 'Expired', 'Cancelled']
                ],
                [
                    'field' => 'acc.name',
                    'operator' => 'LIKE',
                    'label' => 'Customer Name'
                ]
            ]
        ],
        [
            'name' => 'Autodesk Accounts',
            'description' => 'Customer account information',
            'table_name' => 'autodesk_accounts',
            'tables' => ['autodesk_accounts'],
            'table_aliases' => [
                'autodesk_accounts' => 'acc'
            ],
            'joins' => [],
            'selected_columns' => [
                'acc.id',
                'acc.name',
                'acc.email',
                'acc.phone',
                'acc.country',
                'acc.account_type'
            ],
            'column_aliases' => [
                'acc.id' => 'account_id',
                'acc.name' => 'company_name',
                'acc.email' => 'email',
                'acc.phone' => 'phone',
                'acc.country' => 'country',
                'acc.account_type' => 'account_type'
            ],
            'filters' => [
                [
                    'field' => 'acc.name',
                    'operator' => 'LIKE',
                    'label' => 'Company Name'
                ],
                [
                    'field' => 'acc.country',
                    'operator' => '=',
                    'label' => 'Country'
                ]
            ]
        ],
        [
            'name' => 'Expiring Subscriptions',
            'description' => 'Subscriptions expiring within 90 days',
            'table_name' => 'autodesk_subscriptions',
            'tables' => ['autodesk_subscriptions', 'autodesk_accounts'],
            'table_aliases' => [
                'autodesk_subscriptions' => 'sub',
                'autodesk_accounts' => 'acc'
            ],
            'joins' => [
                [
                    'type' => 'LEFT JOIN',
                    'from_table' => 'autodesk_subscriptions',
                    'from_alias' => 'sub',
                    'to_table' => 'autodesk_accounts',
                    'to_alias' => 'acc',
                    'from_column' => 'soldTo_id',
                    'to_column' => 'id'
                ]
            ],
            'selected_columns' => [
                'sub.subscriptionId',
                'sub.offeringName',
                'sub.endDate',
                'sub.quantity',
                'acc.name',
                'acc.email'
            ],
            'column_aliases' => [
                'sub.subscriptionId' => 'subscription_id',
                'sub.offeringName' => 'product_name',
                'sub.endDate' => 'expiry_date',
                'sub.quantity' => 'quantity',
                'acc.name' => 'customer_name',
                'acc.email' => 'customer_email'
            ],
            'custom_columns' => [
                [
                    'expression' => 'DATEDIFF(sub.endDate, NOW())',
                    'alias' => 'days_remaining',
                    'label' => 'Days Remaining'
                ]
            ],
            'filters' => [
                [
                    'field' => 'sub.endDate',
                    'operator' => 'BETWEEN',
                    'label' => 'Expiry Date Range',
                    'default_value' => ['NOW()', 'DATE_ADD(NOW(), INTERVAL 90 DAY)']
                ],
                [
                    'field' => 'sub.status',
                    'operator' => '=',
                    'label' => 'Status',
                    'default_value' => 'Active'
                ]
            ]
        ]
    ];
    
    // Create each complete data source
    foreach ($complete_sources as $source) {
        try {
            // Insert complete data source
            $result = database::table('autobooks_data_sources')->insert([
                'name' => $source['name'],
                'description' => $source['description'],
                'category' => 'autodesk',
                'table_name' => $source['table_name'],
                'tables' => json_encode($source['tables']),
                'table_aliases' => json_encode($source['table_aliases']),
                'joins' => json_encode($source['joins']),
                'selected_columns' => json_encode($source['selected_columns']),
                'column_aliases' => json_encode($source['column_aliases']),
                'custom_columns' => json_encode($source['custom_columns'] ?? []),
                'filters' => json_encode($source['filters']),
                'status' => 'active',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            if ($result) {
                echo "<p class='text-green-600'>✓ Created complete data source: {$source['name']}</p>";
                $created++;
            } else {
                echo "<p class='text-red-600'>✗ Failed to create: {$source['name']}</p>";
                $errors++;
            }
            
        } catch (Exception $e) {
            echo "<p class='text-red-600'>✗ Error creating {$source['name']}: " . htmlspecialchars($e->getMessage()) . "</p>";
            $errors++;
        }
    }
    
    echo "<div class='mt-4 p-4 bg-gray-50 rounded'>";
    echo "<h3 class='font-semibold'>Migration Summary</h3>";
    echo "<p>Complete data sources created: $created</p>";
    echo "<p>Errors: $errors</p>";
    
    if ($errors === 0) {
        echo "<p class='text-green-600 font-semibold'>✓ Complete data sources created successfully!</p>";
        echo "<p class='text-blue-600 text-sm mt-2'>Data sources are now ready to use with proper joins, filters, and columns.</p>";
    } else {
        echo "<p class='text-yellow-600 font-semibold'>⚠ Migration completed with some errors</p>";
    }
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
}

// Show current data sources
echo "<div class='bg-white rounded-lg shadow p-6 mb-8'>";
echo "<h2 class='text-xl font-semibold mb-4'>Current Autodesk Data Sources</h2>";

try {
    $data_sources = database::table('autobooks_data_sources')
        ->where('category', 'autodesk')
        ->orderBy('name')
        ->get();
    
    if (!empty($data_sources)) {
        echo "<div class='space-y-4'>";
        
        foreach ($data_sources as $source) {
            echo "<div class='border border-gray-200 rounded-lg p-4'>";
            echo "<div class='flex items-center justify-between mb-2'>";
            echo "<h3 class='font-medium text-gray-900'>" . htmlspecialchars($source['name']) . "</h3>";
            echo "<span class='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium " . 
                 ($source['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800') . "'>";
            echo htmlspecialchars($source['status']);
            echo "</span>";
            echo "</div>";
            echo "<p class='text-sm text-gray-600 mb-2'>" . htmlspecialchars($source['description']) . "</p>";
            echo "<div class='text-xs text-gray-500 space-y-1'>";
            echo "<div>Table: " . htmlspecialchars($source['table_name']) . "</div>";
            if (!empty($source['tables'])) {
                $tables = json_decode($source['tables'], true);
                echo "<div>Tables: " . implode(', ', $tables) . "</div>";
            }
            if (!empty($source['selected_columns'])) {
                $columns = json_decode($source['selected_columns'], true);
                echo "<div>Columns: " . count($columns) . " configured</div>";
            }
            if (!empty($source['joins'])) {
                $joins = json_decode($source['joins'], true);
                echo "<div>Joins: " . count($joins) . " configured</div>";
            }
            echo "</div>";
            echo "</div>";
        }
        
        echo "</div>";
        echo "<p class='mt-4 text-sm text-gray-600'>Found " . count($data_sources) . " Autodesk data sources.</p>";
    } else {
        echo "<p class='text-gray-500'>No Autodesk data sources found.</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='text-red-600'>Error loading data sources: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Migration controls
if (empty($data_sources)) {
    echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8'>";
    echo "<h2 class='text-lg font-semibold text-blue-900 mb-3'>Create Complete Data Sources</h2>";
    echo "<p class='text-blue-800 mb-4'>Create properly configured Autodesk data sources with joins, filters, and columns.</p>";
    echo "<form method='post'>";
    echo "<button type='submit' name='run_migration' value='1' class='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700'>";
    echo "Create Complete Data Sources";
    echo "</button>";
    echo "</form>";
    echo "</div>";
} else {
    echo "<div class='bg-green-50 border border-green-200 rounded-lg p-6 mb-8'>";
    echo "<h2 class='text-lg font-semibold text-green-900 mb-3'>Data Sources Ready</h2>";
    echo "<p class='text-green-800 mb-4'>Complete Autodesk data sources are configured and ready to use.</p>";
    echo "<div class='space-y-2'>";
    echo "<a href='test_integrated_data_source_selector.php' class='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 mr-2'>";
    echo "Test Data Source Selector";
    echo "</a>";
    echo "<a href='" . APP_ROOT . "/system/data_sources' class='inline-flex items-center px-4 py-2 border border-green-600 text-sm font-medium rounded-md text-green-600 bg-white hover:bg-green-50'>";
    echo "View Data Sources";
    echo "</a>";
    echo "</div>";
    echo "</div>";
}

echo "</div>"; // container
echo "</body></html>";
?>
