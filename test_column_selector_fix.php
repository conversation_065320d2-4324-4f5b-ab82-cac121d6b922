<?php
/**
 * Test column selector with custom tables fix
 */

// Include the necessary files
require_once 'system/classes/startup_sequence.class.php';
require_once 'system/functions/functions.php';

// Initialize the system
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . '/system/config/path_schema.php';
$path['fs_system'] = $path['fs_app_root'] . '/' . $schema['system']['root'];
startup_sequence::start($path, $schema);

use system\data_source_manager;

// Include the API functions
require_once 'system/api/data_sources.api.php';

echo "<h1>Test Column Selector with Custom Tables Fix</h1>";

try {
    // Get your actual data source
    $data_source = data_source_manager::get_data_source(30);
    
    if (!$data_source) {
        echo "<p style='color: red;'>✗ Data source 30 not found</p>";
        exit;
    }
    
    echo "<h2>Data Source Info:</h2>";
    echo "<p><strong>Name:</strong> {$data_source['name']}</p>";
    echo "<p><strong>Custom Tables Count:</strong> " . count($data_source['custom_tables'] ?? []) . "</p>";
    
    if (!empty($data_source['custom_tables'])) {
        echo "<h3>Custom Tables:</h3>";
        foreach ($data_source['custom_tables'] as $index => $custom_table) {
            echo "<p><strong>$index:</strong> {$custom_table['alias']} - Columns: {$custom_table['columns']}</p>";
        }
    }
    
    // Prepare the same parameters that the template would use
    $selected_tables = $data_source['tables'] ?? [];
    $selected_columns_data = $data_source['selected_columns'] ?? [];
    
    // Convert selected_columns to array format (same logic as template)
    $selected_columns_array = [];
    if (is_array($selected_columns_data)) {
        if (isset($selected_columns_data[0]) && is_string($selected_columns_data[0])) {
            $selected_columns_array = $selected_columns_data;
        } else {
            foreach ($selected_columns_data as $table_name => $columns) {
                if (is_array($columns)) {
                    foreach ($columns as $column) {
                        $selected_columns_array[] = $table_name . '.' . $column;
                    }
                }
            }
        }
    }
    
    $column_params = [
        'selected_tables' => json_encode($selected_tables),
        'selected_columns' => json_encode($selected_columns_array),
        'joins' => json_encode($data_source['joins'] ?? []),
        'table_aliases' => $data_source['table_aliases'] ?? [],
        'column_aliases' => $data_source['column_aliases'] ?? [],
        'custom_tables' => $data_source['custom_tables'] ?? []  // This is the fix!
    ];
    
    echo "<h2>Column Parameters:</h2>";
    echo "<pre>" . json_encode($column_params, JSON_PRETTY_PRINT) . "</pre>";
    
    echo "<h2>Testing column_selection_fragment with custom_tables:</h2>";
    
    $column_selection_html = \api\data_sources\column_selection_fragment($column_params);
    
    if (is_string($column_selection_html) && strlen($column_selection_html) > 100) {
        echo "<p style='color: green;'>✓ Column selection fragment generated successfully</p>";
        
        // Check for custom table presence
        if (strpos($column_selection_html, 'lastquote (custom)') !== false) {
            echo "<p style='color: green;'>✓ Custom table 'lastquote (custom)' found in column selector</p>";
        } else {
            echo "<p style='color: red;'>✗ Custom table 'lastquote (custom)' not found in column selector</p>";
        }
        
        // Check for specific custom table columns
        $expected_columns = ['quote_id', 'quote_status', 'quote_number', 'quoted_date'];
        foreach ($expected_columns as $column) {
            if (strpos($column_selection_html, $column) !== false) {
                echo "<p style='color: green;'>✓ Custom table column '$column' found in selector</p>";
            } else {
                echo "<p style='color: red;'>✗ Custom table column '$column' not found in selector</p>";
            }
        }
        
        // Check for checkboxes with custom table columns
        if (strpos($column_selection_html, 'name="selected_columns[]"') !== false && 
            strpos($column_selection_html, 'lastquote.') !== false) {
            echo "<p style='color: green;'>✓ Custom table column checkboxes found</p>";
        } else {
            echo "<p style='color: red;'>✗ Custom table column checkboxes not found</p>";
        }
        
        echo "<h3>Column Selection HTML (first 2000 characters):</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; max-height: 400px;'>";
        echo htmlspecialchars(substr($column_selection_html, 0, 2000));
        if (strlen($column_selection_html) > 2000) {
            echo "\n... (truncated)";
        }
        echo "</pre>";
        
        echo "<h3>Rendered Column Selection:</h3>";
        echo "<div style='border: 1px solid #ddd; padding: 10px; background: #f9f9f9; max-height: 400px; overflow-y: auto;'>";
        echo $column_selection_html;
        echo "</div>";
        
    } else {
        echo "<p style='color: red;'>✗ Column selection fragment failed or returned empty content</p>";
        echo "<pre>" . htmlspecialchars(print_r($column_selection_html, true)) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='data_sources'>← Back to Data Sources</a></p>";
?>
