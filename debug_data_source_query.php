<?php
/**
 * Debug the data source query generation
 */

// Manually include the required files
require_once 'system/classes/database.class.php';
require_once 'system/api/data_sources.api.php';

echo "Debug Data Source Query Generation\n";
echo "==================================\n\n";

// Your data source configuration from the database
$tables = ['test_mail'];
$joins = [];
$selected_columns = ['test_mail' => ['id', 'email', 'name', 'misc']];
$filters = [['column' => 'misc', 'operator' => 'NOT LIKE', 'value' => 'nooo']];
$table_aliases = [];
$column_aliases = [];
$custom_columns = [];
$sorting = [];
$grouping = [['column' => 'test_mail.name']];
$limits = ['enabled' => false, 'limit' => '', 'offset' => ''];

echo "Input Configuration:\n";
echo "Tables: " . json_encode($tables) . "\n";
echo "Selected Columns: " . json_encode($selected_columns) . "\n";
echo "Filters: " . json_encode($filters) . "\n";
echo "Grouping: " . json_encode($grouping) . "\n\n";

try {
    // Generate the query
    $query = \api\data_sources\build_multi_table_query(
        $tables,
        $joins,
        $selected_columns,
        $filters,
        $table_aliases,
        $column_aliases,
        $custom_columns,
        $sorting,
        $grouping,
        $limits
    );
    
    echo "Generated Query:\n";
    echo "================\n";
    echo $query . "\n\n";
    
    // Test what this query would return
    echo "Expected Results:\n";
    echo "=================\n";
    echo "Based on your test data:\n";
    echo "1. Row 1: test1 - should be included (misc='test1', no 'nooo')\n";
    echo "2. Row 2: test2 - should be included (misc='', no 'nooo')\n";
    echo "3. Row 3: test3 - should be included (misc='', no 'nooo')\n";
    echo "4. Row 4: test4 - should be included (misc='test4.1', no 'nooo')\n";
    echo "5. Row 5: test4 - should be GROUPED with row 4 (same name 'test4')\n";
    echo "6. Row 6: test6 - should be EXCLUDED (misc='test6 nooo', contains 'nooo')\n";
    echo "7. Row 7: test7 - should be included (misc='test7', no 'nooo')\n";
    echo "8. Row 8: test8 - should be included (misc='test8', no 'nooo')\n\n";
    
    echo "Expected final result: 6 rows (1,2,3,4,7,8) with row 5 grouped into row 4, row 6 filtered out\n";
    
} catch (Exception $e) {
    echo "Error generating query: " . $e->getMessage() . "\n";
}
?>
