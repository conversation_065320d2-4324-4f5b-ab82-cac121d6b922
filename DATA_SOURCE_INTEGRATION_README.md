# Data Source Integration with Data Tables

This document explains how to use the integrated data source selector that allows data tables to switch between hardcoded data and database data sources directly from the column manager.

## Overview

The data source integration provides:

- ✅ **Seamless switching** between hardcoded data and database data sources
- ✅ **Integrated dropdown selector** in the column manager
- ✅ **Automatic fallback** to hardcoded data if data source fails
- ✅ **Grouped data sources** by category for easy selection
- ✅ **Persistent configuration** stored in database per user
- ✅ **HTMX integration** for real-time updates

## Quick Start

### 1. Access Data Source Selector

The data source selector is automatically available in the column manager for any data table with a `table_name`:

```php
$table_result = data_table::process_data_table(
    ['columns' => $columns],
    $hardcoded_data,
    '', // db_table
    'my_callback', // callback
    [], // replacements
    [], // criteria
    [], // hidden columns
    false, false, false, false, null,
    'my_table_name' // Required for data source integration
);
```

The selector appears automatically at the top of the column manager panel.

### 2. Create Data Sources

Before using data sources, create them via the data source builder:

1. Visit `/system/data_sources` in your application
2. Click "Create New Data Source"
3. Configure tables, joins, filters, and columns
4. Save the data source

### 3. Use the Selector

Users can access the data source selector by:

1. **Opening the Column Manager** - Click the gear icon in the top-right of any data table
2. **Using the Data Source Dropdown** - At the top of the column manager, select from:
   - "Default (Hardcoded Data)" - Uses the data provided in code
   - Available data sources grouped by category (Data Tables, Email & Campaigns, etc.)
3. **Automatic Updates** - Table refreshes immediately when selection changes

## Technical Implementation

### Data Flow

```
1. User selects data source type and ID
2. Configuration saved to autobooks_data_table_storage
3. Table regenerated with new data source
4. Data fetched from data_source_manager if configured
5. Fallback to hardcoded data if data source fails
```

### Database Schema

The integration uses the existing `autobooks_data_table_storage` table with additional fields:

```sql
-- New fields added to configuration JSON
{
    "data_source_type": "hardcoded|data_source",
    "data_source_id": 123,
    "hidden": [...],
    "structure": [...],
    ...
}
```

### API Endpoints

#### Update Data Source Configuration
```
POST /api/data_table_storage/update_data_source
Parameters:
- table_name: string
- data_source_type: "hardcoded" | "data_source"  
- data_source_id: integer (when type is "data_source")
- callback: string
```

#### Preview Data Source
```
GET /api/data_table_storage/preview_data_source
Parameters:
- data_source_id: integer
- limit: integer (default: 5)
```

## Usage Examples

### Basic Integration

```php
// In your view or function file
function generate_my_table() {
    $hardcoded_data = [
        ['id' => 1, 'name' => 'John', 'email' => '<EMAIL>'],
        ['id' => 2, 'name' => 'Jane', 'email' => '<EMAIL>']
    ];
    
    $columns = [
        ['label' => 'ID', 'field' => 'id', 'filter' => false],
        ['label' => 'Name', 'field' => 'name', 'filter' => true],
        ['label' => 'Email', 'field' => 'email', 'filter' => true]
    ];
    
    $result = data_table::process_data_table(
        ['columns' => $columns],
        $hardcoded_data,
        '',
        'generate_my_table', // This function name
        [],
        [],
        [],
        false, false, false, false, null,
        'my_users_table' // Unique table identifier
    );
    
    // Enable data source selector
    return str_replace(
        'show_data_source_selector="false"',
        'show_data_source_selector="true"',
        $result
    );
}
```

### Advanced Integration with Criteria

```php
function generate_filtered_table($criteria = []) {
    // Your hardcoded data
    $hardcoded_data = get_hardcoded_data($criteria);
    
    // The data_table class will automatically check for data source configuration
    // and use data source data if configured, otherwise use hardcoded data
    return data_table::process_data_table(
        ['columns' => $columns],
        $hardcoded_data,
        '',
        'generate_filtered_table',
        [],
        $criteria, // Criteria passed to data source if used
        [],
        false, false, false, false, null,
        'filtered_table'
    );
}
```

### Edge Template Integration

```php
// In your Edge template
@php
    $table_html = generate_my_table();
    // Enable data source selector
    $table_html = str_replace(
        'show_data_source_selector="false"',
        'show_data_source_selector="true"',
        $table_html
    );
@endphp

{!! $table_html !!}
```

## Configuration Options

### Data Source Selector Component

The `<x-data-table-source-selector>` component accepts these props:

```php
@props([
    'table_name' => '',                    // Required: Unique table identifier
    'current_data_source_type' => 'hardcoded', // Current type
    'current_data_source_id' => null,      // Current data source ID
    'callback' => '',                      // Callback function name
    'show_preview' => true                 // Show preview functionality
])
```

### Data Table Integration

Enable the selector in data tables:

```php
// In data-table.edge.php props
'show_data_source_selector' => true,  // Enable selector
'show_column_manager' => true,        // Can be used together
```

## Benefits

### For Developers
- **Flexible data sources**: Switch between hardcoded and database data without code changes
- **Easy integration**: Add one parameter to enable data source switching
- **Automatic fallback**: Graceful handling of data source failures
- **Consistent API**: Same data table interface regardless of data source

### For Users
- **Visual configuration**: No code changes needed to switch data sources
- **Real-time preview**: See data before applying changes
- **Persistent settings**: Configuration saved per user
- **Seamless experience**: Table updates without page refresh

## Troubleshooting

### Data Source Not Loading
1. Check if data source exists and is active
2. Verify data source configuration (tables, joins, filters)
3. Check database permissions
4. Review error logs for data source manager issues

### Selector Not Appearing
1. Ensure `show_data_source_selector="true"` is set
2. Verify `table_name` parameter is provided
3. Check that autobooks_data_table_storage table exists
4. Confirm user authentication is working

### Preview Not Working
1. Verify data source ID is valid
2. Check data source has data
3. Review API endpoint accessibility
4. Ensure HTMX is loaded on the page

## Future Enhancements

The system is designed to support:
- **Custom data source types** (APIs, external databases)
- **Data source templates** for common configurations
- **Bulk data source management** across multiple tables
- **Data source versioning** and rollback capabilities
- **Performance optimization** with caching layers

## Related Documentation

- [Database Storage System](DATABASE_STORAGE_README.md)
- [Data Source Builder Usage](docs/data-source-builder-usage.md)
- [Data Table Column Management](system/components/edges/data-table-column-manager.edge.php)
