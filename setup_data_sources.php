<?php
/**
 * Setup script for Data Sources
 * Adds navigation entry and creates database table if needed
 */

// Initialize the system like index.php does
const DS = DIRECTORY_SEPARATOR;
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . DS . 'system'. DS . 'config'. DS . 'path_schema.php';
$ds = DS;
$path['fs_system'] = "{$path['fs_app_root']}{$ds}{$schema['system']['root']}" ;

$input_params = array_merge($_GET, $_POST);
define('INPUT_PARAMS',$input_params);
const DEBUG_MODE = true;
const API_RUN = false;

require_once $path['fs_system'] . DS . 'classes'. DS . 'startup_sequence.class.php';
require_once $path['fs_system'] . DS . 'functions'. DS . 'functions.php';

startup_sequence::start($path,$schema);

use system\database;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Data Sources Setup</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .success { color: green; }
        .warning { color: orange; }
        .error { color: red; }
    </style>
</head>
<body>
<h1>Data Sources Setup</h1>
<?php

try {
    // Check if navigation entry exists
    $nav = database::table('autobooks_navigation')
        ->where('route_key', 'data_sources')
        ->where('parent_path', 'root')
        ->first();

    if ($nav) {
        echo "<p class='success'>✓ Navigation entry already exists: {$nav['name']}</p>\n";
    } else {
        echo "<p class='warning'>Adding navigation entry...</p>\n";
        
        // Add the navigation entry
        database::table('autobooks_navigation')->insert([
            'parent_path' => 'root',
            'route_key' => 'data_sources',
            'name' => 'Data Sources',
            'icon' => 'database',
            'file_path' => 'system',
            'required_roles' => '[]',
            'sort_order' => 15,
            'show_navbar' => 1,
            'can_delete' => 0,
            'is_system' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
        echo "<p class='success'>✓ Navigation entry added successfully</p>\n";
    }

    // Check if data sources table exists
    $table_exists = database::rawQuery("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources'")->fetchColumn() > 0;

    if ($table_exists) {
        echo "<p class='success'>✓ Data sources table already exists</p>\n";

        // Check how many data sources exist
        $count = database::table('autobooks_data_sources')->count();
        echo "<p>Found $count configured data sources</p>\n";
    } else {
        echo "<p class='warning'>Creating data sources table...</p>\n";
        
        // Create the table using raw SQL
        $create_sql = "
        CREATE TABLE `autobooks_data_sources` (
            `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL COMMENT 'Display name for the data source',
            `table_name` varchar(255) NOT NULL COMMENT 'Database table name',
            `description` text DEFAULT NULL COMMENT 'Optional description',
            `category` varchar(50) NOT NULL DEFAULT 'other' COMMENT 'Data source category',
            `column_mapping` longtext DEFAULT NULL COMMENT 'JSON column mapping configuration',
            `filters` longtext DEFAULT NULL COMMENT 'JSON filter configuration',
            `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT 'Data source status',
            `created_by` int(11) DEFAULT NULL COMMENT 'User ID who created this data source',
            `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
            `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
            PRIMARY KEY (`id`),
            KEY `idx_status_category` (`status`,`category`),
            KEY `idx_table_name` (`table_name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Data source configurations for tables and email campaigns'
        ";
        
        database::rawQuery($create_sql);
        echo "<p class='success'>✓ Data sources table created successfully</p>\n";

        // Add some sample data sources
        echo "<p>Adding sample data sources...</p>\n";
        
        $sample_sources = [
            [
                'name' => 'User Management Data',
                'table_name' => 'autobooks_users',
                'description' => 'System users and their information',
                'category' => 'users',
                'status' => 'active'
            ],
            [
                'name' => 'Navigation Data',
                'table_name' => 'autobooks_navigation',
                'description' => 'System navigation menu items',
                'category' => 'system',
                'status' => 'active'
            ]
        ];
        
        foreach ($sample_sources as $source) {
            $source['created_at'] = date('Y-m-d H:i:s');
            $source['updated_at'] = date('Y-m-d H:i:s');
            database::table('autobooks_data_sources')->insert($source);
        }
        
        echo "<p class='success'>✓ Sample data sources added</p>\n";
    }

    echo "<h2 class='success'>Setup Complete!</h2>\n";
    echo "<p><a href='data_sources'>Go to Data Sources Management</a></p>\n";
    echo "<p><a href='test_data_sources.php'>Run Data Sources Test</a></p>\n";

} catch (Exception $e) {
    echo "<h2 class='error'>Setup Failed</h2>\n";
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}
?>
</body>
</html>
