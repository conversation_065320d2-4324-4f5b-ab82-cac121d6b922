<?php
/**
 * Test script for Data Source Manager
 * Run this to verify the data source system is working
 */

// Include the autoloader and configuration
require_once 'system/config/application.php';

use system\data_source_manager;

echo "<h1>Data Source Manager Test</h1>\n";

try {
    echo "<h2>1. Testing get_available_tables()</h2>\n";
    $tables = data_source_manager::get_available_tables();
    
    if (empty($tables)) {
        echo "<p style='color: orange;'>No tables found or error occurred</p>\n";
    } else {
        echo "<p style='color: green;'>Found " . count($tables) . " available tables:</p>\n";
        echo "<ul>\n";
        foreach ($tables as $table) {
            echo "<li><strong>{$table['display_name']}</strong> ({$table['name']}) - {$table['row_count']} rows, Category: {$table['category']}</li>\n";
        }
        echo "</ul>\n";
    }
    
    echo "<h2>2. Testing table info for first table</h2>\n";
    if (!empty($tables)) {
        $first_table = $tables[0];
        $table_info = data_source_manager::get_table_info($first_table['name']);
        
        if ($table_info) {
            echo "<p style='color: green;'>Successfully got info for table: {$first_table['name']}</p>\n";
            echo "<p>Columns: " . count($table_info['columns']) . "</p>\n";
            echo "<p>Primary Key: " . ($table_info['primary_key'] ?? 'None') . "</p>\n";
            echo "<p>Has data_json: " . ($table_info['has_data_json'] ? 'Yes' : 'No') . "</p>\n";
        } else {
            echo "<p style='color: red;'>Failed to get table info</p>\n";
        }
    }
    
    echo "<h2>3. Testing sample data</h2>\n";
    if (!empty($tables)) {
        $first_table = $tables[0];
        $sample_data = data_source_manager::get_sample_data($first_table['name'], 3);
        
        if ($sample_data['success']) {
            echo "<p style='color: green;'>Successfully got sample data from {$first_table['name']}: {$sample_data['count']} rows</p>\n";
            if (!empty($sample_data['data'])) {
                echo "<pre>" . print_r($sample_data['data'][0], true) . "</pre>\n";
            }
        } else {
            echo "<p style='color: red;'>Failed to get sample data: " . ($sample_data['error'] ?? 'Unknown error') . "</p>\n";
        }
    }
    
    echo "<h2>4. Testing data source creation</h2>\n";
    if (!empty($tables)) {
        $first_table = $tables[0];
        
        try {
            $data_source_id = data_source_manager::create_data_source([
                'name' => 'Test Data Source',
                'table_name' => $first_table['name'],
                'description' => 'Test data source created by test script',
                'category' => $first_table['category'],
                'filters' => []
            ]);
            
            echo "<p style='color: green;'>Successfully created data source with ID: $data_source_id</p>\n";
            
            // Test getting the data source
            $data_source = data_source_manager::get_data_source($data_source_id);
            if ($data_source) {
                echo "<p style='color: green;'>Successfully retrieved created data source</p>\n";
                echo "<p>Name: {$data_source['name']}</p>\n";
                echo "<p>Table: {$data_source['table_name']}</p>\n";
                echo "<p>Status: {$data_source['status']}</p>\n";
            } else {
                echo "<p style='color: red;'>Failed to retrieve created data source</p>\n";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>Error creating data source: " . $e->getMessage() . "</p>\n";
        }
    }
    
    echo "<h2>5. Testing get_data_sources()</h2>\n";
    $data_sources = data_source_manager::get_data_sources();
    echo "<p style='color: green;'>Found " . count($data_sources) . " configured data sources</p>\n";
    
    if (!empty($data_sources)) {
        echo "<ul>\n";
        foreach ($data_sources as $source) {
            echo "<li><strong>{$source['name']}</strong> - {$source['table_name']} ({$source['status']})</li>\n";
        }
        echo "</ul>\n";
    }
    
    echo "<h2>Test Complete</h2>\n";
    echo "<p style='color: green;'>All tests completed successfully!</p>\n";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>Test Failed</h2>\n";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "<hr>\n";
echo "<p><a href='data_sources'>Go to Data Sources Management</a></p>\n";
?>
