<?php
/**
 * Enhanced Email Campaign Sender
 * 
 * This script replaces the original send_email.php with campaign-based functionality
 * while maintaining backward compatibility with existing scheduling systems.
 */

use system\email_campaign;
use autodesk_api\autodesk_api;

require_once 'system/startup_sequence_minimal.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo tcs_log('Email Campaign Sender initialized.', "email_campaign_sender") . "<br>";

try {
    $campaign_manager = new email_campaign();
    $autodesk = new autodesk_api();
    
    // Get command line arguments or default to subscription renewal
    $campaign_type = $argv[1] ?? 'subscription_renewal';
    $force_send = isset($argv[2]) && $argv[2] === '--force';
    
    echo tcs_log("Campaign type: $campaign_type, Force send: " . ($force_send ? 'yes' : 'no'), "email_campaign_sender") . "<br>";
    
    // Get active campaigns of the specified type
    $campaigns = $campaign_manager->get_campaigns([
        'status' => 'active',
        'type' => $campaign_type
    ]);
    
    if (empty($campaigns)) {
        die(tcs_log("No active campaigns found for type: $campaign_type", "email_campaign_sender"));
    }
    
    echo tcs_log("Found " . count($campaigns) . " active campaigns", "email_campaign_sender") . "<br>";
    
    $total_sent = 0;
    $total_failed = 0;
    
    foreach ($campaigns as $campaign) {
        echo tcs_log("Processing campaign: {$campaign['name']} (ID: {$campaign['id']})", "email_campaign_sender") . "<br>";
        
        // Check if it's time to send (for subscription renewal campaigns)
        if ($campaign['type'] === 'subscription_renewal' && !$force_send) {
            if (!$campaign_manager->should_send_now($campaign)) {
                echo tcs_log("Not time to send for campaign {$campaign['id']}", "email_campaign_sender") . "<br>";
                continue;
            }
        }
        
        try {
            // Execute the campaign
            $results = $campaign_manager->execute_campaign($campaign['id']);
            
            echo tcs_log("Campaign {$campaign['id']} results: " . 
                        "Processed: {$results['total_processed']}, " .
                        "Sent: {$results['total_sent']}, " .
                        "Failed: {$results['total_failed']}", "email_campaign_sender") . "<br>";
            
            $total_sent += $results['total_sent'];
            $total_failed += $results['total_failed'];
            
            // Log any errors
            if (!empty($results['errors'])) {
                foreach ($results['errors'] as $error) {
                    echo tcs_log("Campaign {$campaign['id']} error: $error", "email_campaign_sender") . "<br>";
                }
            }
            
        } catch (Exception $e) {
            echo tcs_log("Campaign {$campaign['id']} failed: " . $e->getMessage(), "email_campaign_sender") . "<br>";
            $total_failed++;
        }
        
        // Add delay between campaigns to avoid overwhelming the mail server
        if (count($campaigns) > 1) {
            sleep(1);
        }
    }
    
    echo tcs_log("Email campaign sending completed. Total sent: $total_sent, Total failed: $total_failed", "email_campaign_sender") . "<br>";
    
} catch (Exception $e) {
    echo tcs_log("Email campaign sender failed: " . $e->getMessage(), "email_campaign_sender") . "<br>";
    exit(1);
}

/**
 * Add helper method to email_campaign class for time-based sending
 */
if (!method_exists('system\email_campaign', 'should_send_now')) {
    // This would be added to the email_campaign class, but for now we'll implement it here
    function should_send_now($campaign) {
        $send_schedule = $campaign['send_schedule'] ?? [];
        
        // Check day of week
        $current_day = (int) date('w'); // 0 = Sunday, 6 = Saturday
        $send_days = $send_schedule['send_days'] ?? [0, 1, 1, 1, 1, 1, 0]; // Default: Mon-Fri
        
        if (!$send_days[$current_day]) {
            return false;
        }
        
        // Check time of day
        $current_hour = (int) date('H');
        $send_time = $send_schedule['send_time'] ?? 9;
        
        // Allow sending within 1 hour window
        if (abs($current_hour - $send_time) > 1) {
            return false;
        }
        
        return true;
    }
}

?>
