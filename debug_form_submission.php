<?php
/**
 * Debug form submission data
 * This will capture and display what data is being submitted by the duplicate form
 */

// Include the necessary files
require_once 'system/classes/startup_sequence.class.php';
require_once 'system/functions/functions.php';

// Initialize the system
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . '/system/config/path_schema.php';
$path['fs_system'] = $path['fs_app_root'] . '/' . $schema['system']['root'];
startup_sequence::start($path, $schema);

echo "<h1>Debug Form Submission Data</h1>";

// Check if this is a POST request (form submission)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>Form Data Received:</h2>";
    
    echo "<h3>POST Data:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; max-height: 400px;'>";
    print_r($_POST);
    echo "</pre>";
    
    echo "<h3>Key Fields Analysis:</h3>";
    echo "<ul>";
    
    // Check for table-related fields
    $table_fields = ['selected_tables', 'tables', 'table_name'];
    foreach ($table_fields as $field) {
        if (isset($_POST[$field])) {
            $value = $_POST[$field];
            $type = gettype($value);
            if (is_array($value)) {
                echo "<li><strong>$field:</strong> Array with " . count($value) . " items: " . json_encode($value) . "</li>";
            } elseif (is_string($value)) {
                echo "<li><strong>$field:</strong> String: " . htmlspecialchars($value) . "</li>";
                // Try to decode if it looks like JSON
                if (strpos($value, '[') === 0 || strpos($value, '{') === 0) {
                    $decoded = json_decode($value, true);
                    if ($decoded !== null) {
                        echo "<li style='margin-left: 20px;'><em>Decoded JSON:</em> " . json_encode($decoded) . "</li>";
                    }
                }
            } else {
                echo "<li><strong>$field:</strong> $type - " . var_export($value, true) . "</li>";
            }
        } else {
            echo "<li><strong>$field:</strong> <em>Not present</em></li>";
        }
    }
    echo "</ul>";
    
    // Test the table parsing logic
    echo "<h3>Table Parsing Test:</h3>";
    $tables = [];
    
    // Try selected_tables JSON format first
    if (isset($_POST['selected_tables']) && !empty($_POST['selected_tables'])) {
        $decoded = json_decode($_POST['selected_tables'], true);
        if (is_array($decoded) && !empty($decoded)) {
            $tables = $decoded;
            echo "<p style='color: green;'>✓ Found tables via selected_tables JSON: " . json_encode($tables) . "</p>";
        } else {
            echo "<p style='color: orange;'>⚠ selected_tables present but failed to decode: " . htmlspecialchars($_POST['selected_tables']) . "</p>";
        }
    }
    
    // Try tables array format
    if (empty($tables) && isset($_POST['tables']) && is_array($_POST['tables'])) {
        $tables = array_values(array_filter($_POST['tables']));
        echo "<p style='color: green;'>✓ Found tables via tables array: " . json_encode($tables) . "</p>";
    }
    
    // Try single table_name
    if (empty($tables) && !empty($_POST['table_name'])) {
        $tables = [$_POST['table_name']];
        echo "<p style='color: green;'>✓ Found tables via table_name: " . json_encode($tables) . "</p>";
    }
    
    if (empty($tables)) {
        echo "<p style='color: red;'>✗ No tables found using any method</p>";
    }
    
    echo "<hr>";
    echo "<p><a href='?'>← Clear and try again</a></p>";
    
} else {
    // Show instructions
    echo "<p>This page will capture and display the form data when you submit a duplicate data source form.</p>";
    echo "<p><strong>Instructions:</strong></p>";
    echo "<ol>";
    echo "<li>Go to the Data Sources page</li>";
    echo "<li>Click 'Duplicate' on any data source</li>";
    echo "<li>In the form that opens, change the form action to point to this debug script</li>";
    echo "<li>Or copy the form HTML and modify it below</li>";
    echo "</ol>";
    
    echo "<h2>Manual Test Form</h2>";
    echo "<p>You can also test with this simple form:</p>";
    
    echo '<form method="POST" style="border: 1px solid #ddd; padding: 20px; background: #f9f9f9;">
        <h3>Test Form Submission</h3>
        <div style="margin-bottom: 10px;">
            <label>Name: <input type="text" name="name" value="Test Data Source" style="width: 200px;"></label>
        </div>
        <div style="margin-bottom: 10px;">
            <label>Table Name: <input type="text" name="table_name" value="test_table" style="width: 200px;"></label>
        </div>
        <div style="margin-bottom: 10px;">
            <label>Selected Tables (JSON): <input type="text" name="selected_tables" value=\'["test_table"]\' style="width: 200px;"></label>
        </div>
        <div style="margin-bottom: 10px;">
            <label>Tables Array 0: <input type="text" name="tables[0]" value="test_table" style="width: 200px;"></label>
        </div>
        <div style="margin-bottom: 10px;">
            <label>Category: <input type="text" name="category" value="other" style="width: 200px;"></label>
        </div>
        <button type="submit" style="padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer;">Submit Test</button>
    </form>';
}

echo "<hr>";
echo "<p><a href='data_sources'>← Back to Data Sources</a></p>";
?>
