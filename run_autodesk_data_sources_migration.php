<?php
/**
 * Run Autodesk Data Sources Migration
 * 
 * This script imports all Autodesk tables as data sources
 * and provides testing functionality
 */

require_once 'system/autoloader.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<!DOCTYPE html>";
echo "<html><head><title>Autodesk Data Sources Migration</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css' rel='stylesheet'>";
echo "</head><body class='bg-gray-100'>";

echo "<div class='container mx-auto py-8'>";
echo "<h1 class='text-3xl font-bold mb-8'>Autodesk Data Sources Migration</h1>";

// Check if migration should be run
if (isset($_POST['run_migration'])) {
    echo "<div class='bg-white rounded-lg shadow p-6 mb-8'>";
    echo "<h2 class='text-xl font-semibold mb-4'>Running Migration...</h2>";
    echo "<pre class='bg-gray-50 p-4 rounded text-sm overflow-x-auto'>";
    
    try {
        // Read and execute the SQL file
        $sql_content = file_get_contents('system/sql/autodesk_data_sources.sql');
        
        if (!$sql_content) {
            throw new Exception('Could not read SQL file');
        }
        
        // Split into individual statements
        $statements = explode(';', $sql_content);
        $executed = 0;
        $errors = 0;
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (empty($statement) || strpos($statement, '--') === 0) {
                continue;
            }
            
            try {
                $result = tep_db_query($statement);
                if ($result) {
                    $executed++;
                    echo "✓ Executed statement successfully\n";
                } else {
                    $errors++;
                    echo "✗ Failed to execute statement\n";
                }
            } catch (Exception $e) {
                $errors++;
                echo "✗ Error: " . $e->getMessage() . "\n";
            }
        }
        
        echo "\n=== Migration Summary ===\n";
        echo "Statements executed: $executed\n";
        echo "Errors: $errors\n";
        
        if ($errors === 0) {
            echo "✓ Migration completed successfully!\n";
        } else {
            echo "⚠ Migration completed with errors\n";
        }
        
    } catch (Exception $e) {
        echo "✗ Migration failed: " . $e->getMessage() . "\n";
    }
    
    echo "</pre>";
    echo "</div>";
}

// Show current data sources
echo "<div class='bg-white rounded-lg shadow p-6 mb-8'>";
echo "<h2 class='text-xl font-semibold mb-4'>Current Autodesk Data Sources</h2>";

try {
    $data_sources = database::table('autobooks_data_sources')
        ->where('category', 'autodesk')
        ->orderBy('name')
        ->get();
    
    if (!empty($data_sources)) {
        echo "<div class='overflow-x-auto'>";
        echo "<table class='min-w-full divide-y divide-gray-200'>";
        echo "<thead class='bg-gray-50'>";
        echo "<tr>";
        echo "<th class='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>ID</th>";
        echo "<th class='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>Name</th>";
        echo "<th class='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>Description</th>";
        echo "<th class='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>Table</th>";
        echo "<th class='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>Status</th>";
        echo "<th class='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>Created</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody class='bg-white divide-y divide-gray-200'>";
        
        foreach ($data_sources as $source) {
            echo "<tr>";
            echo "<td class='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>" . htmlspecialchars($source['id']) . "</td>";
            echo "<td class='px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900'>" . htmlspecialchars($source['name']) . "</td>";
            echo "<td class='px-6 py-4 text-sm text-gray-500'>" . htmlspecialchars($source['description']) . "</td>";
            echo "<td class='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>" . htmlspecialchars($source['table_name']) . "</td>";
            echo "<td class='px-6 py-4 whitespace-nowrap'>";
            echo "<span class='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium " . 
                 ($source['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800') . "'>";
            echo htmlspecialchars($source['status']);
            echo "</span>";
            echo "</td>";
            echo "<td class='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>" . htmlspecialchars($source['created_at']) . "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
        
        echo "<p class='mt-4 text-sm text-gray-600'>Found " . count($data_sources) . " Autodesk data sources.</p>";
    } else {
        echo "<p class='text-gray-500'>No Autodesk data sources found.</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='text-red-600'>Error loading data sources: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Migration controls
if (empty($data_sources)) {
    echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8'>";
    echo "<h2 class='text-lg font-semibold text-blue-900 mb-3'>Run Migration</h2>";
    echo "<p class='text-blue-800 mb-4'>No Autodesk data sources found. Run the migration to create them.</p>";
    echo "<form method='post'>";
    echo "<button type='submit' name='run_migration' value='1' class='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700'>";
    echo "Run Migration";
    echo "</button>";
    echo "</form>";
    echo "</div>";
}

// Data sources created
$created_sources = [
    'Autodesk Subscriptions' => 'Complete subscription data with account relationships and calculated fields',
    'Autodesk Accounts' => 'Customer account information with contact details and preferences',
    'Autodesk Quotes' => 'Quote information with line items and customer details',
    'Autodesk Orders' => 'Order information with customer and fulfillment details',
    'Autodesk Email History' => 'Email communication history with subscriptions and accounts',
    'Expiring Subscriptions' => 'Subscriptions expiring within the next 90 days with customer details',
    'Subscription Revenue Summary' => 'Revenue analysis by product, customer, and time period'
];

echo "<div class='bg-white rounded-lg shadow p-6 mb-8'>";
echo "<h2 class='text-xl font-semibold mb-4'>Data Sources to be Created</h2>";
echo "<div class='space-y-3'>";

foreach ($created_sources as $name => $description) {
    echo "<div class='border border-gray-200 rounded-lg p-4'>";
    echo "<h3 class='font-medium text-gray-900'>" . htmlspecialchars($name) . "</h3>";
    echo "<p class='text-sm text-gray-600 mt-1'>" . htmlspecialchars($description) . "</p>";
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Instructions
echo "<div class='bg-yellow-50 border border-yellow-200 rounded-lg p-6'>";
echo "<h2 class='text-lg font-semibold text-yellow-900 mb-3'>Next Steps</h2>";
echo "<ol class='list-decimal list-inside space-y-2 text-yellow-800'>";
echo "<li>Run the migration to create all Autodesk data sources</li>";
echo "<li>Test the data sources in the data source builder</li>";
echo "<li>Use the data sources in data tables via the column manager dropdown</li>";
echo "<li>Create custom data sources for specific reporting needs</li>";
echo "</ol>";
echo "</div>";

echo "</div>"; // container
echo "</body></html>";
?>
