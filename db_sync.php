<?php
/**
 * Database Synchronization Script
 * Syncs data between remote and local databases
 */

// Configuration
$local_config = [
    'host' => '127.0.0.1',
    'port' => '3306',
    'username' => 'root',
    'password' => '',
    'database' => 'autobooks_local'
];

$remote_config = [
    'host' => 'your-remote-host.com', // Replace with your actual remote host
    'username' => 'wwwcadservicescouk',
    'password' => 'S96#1kvYuCGE',
    'database' => 'wwwcadservicescouk'
];

// Tables to sync (add more as needed)
$tables_to_sync = [
    'autobooks_users',
    'autobooks_notifications',
    'autobooks_notification_preferences',
    'autodesk_storage'
    // Add more tables here as you identify them
];

function connectToDatabase($config, $name) {
    try {
        $dsn = "mysql:host={$config['host']};dbname={$config['database']};charset=utf8mb4";
        if (isset($config['port'])) {
            $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8mb4";
        }
        
        $pdo = new PDO($dsn, $config['username'], $config['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
        
        echo "✓ Connected to $name database\n";
        return $pdo;
    } catch (PDOException $e) {
        echo "❌ Failed to connect to $name database: " . $e->getMessage() . "\n";
        return null;
    }
}

function syncTable($remote_pdo, $local_pdo, $table_name) {
    try {
        echo "Syncing table: $table_name\n";
        
        // Check if table exists in remote
        $stmt = $remote_pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table_name]);
        if ($stmt->rowCount() == 0) {
            echo "  ⚠️  Table $table_name doesn't exist in remote database\n";
            return;
        }
        
        // Get table structure from remote
        $create_stmt = $remote_pdo->query("SHOW CREATE TABLE `$table_name`");
        $create_sql = $create_stmt->fetch()['Create Table'];
        
        // Drop and recreate table in local (for structure sync)
        $local_pdo->exec("DROP TABLE IF EXISTS `$table_name`");
        $local_pdo->exec($create_sql);
        echo "  ✓ Recreated table structure\n";
        
        // Copy data
        $data = $remote_pdo->query("SELECT * FROM `$table_name`")->fetchAll();
        
        if (empty($data)) {
            echo "  ℹ️  No data to sync\n";
            return;
        }
        
        // Get column names
        $columns = array_keys($data[0]);
        $placeholders = ':' . implode(', :', $columns);
        $column_list = '`' . implode('`, `', $columns) . '`';
        
        $insert_sql = "INSERT INTO `$table_name` ($column_list) VALUES ($placeholders)";
        $stmt = $local_pdo->prepare($insert_sql);
        
        $count = 0;
        foreach ($data as $row) {
            $params = [];
            foreach ($columns as $col) {
                $params[":$col"] = $row[$col];
            }
            $stmt->execute($params);
            $count++;
        }
        
        echo "  ✓ Synced $count records\n";
        
    } catch (PDOException $e) {
        echo "  ❌ Error syncing $table_name: " . $e->getMessage() . "\n";
    }
}

function exportLocalChanges($local_pdo, $table_name, $output_dir = 'exports') {
    try {
        if (!is_dir($output_dir)) {
            mkdir($output_dir, 0755, true);
        }
        
        $data = $local_pdo->query("SELECT * FROM `$table_name`")->fetchAll();
        
        if (empty($data)) {
            return;
        }
        
        $filename = "$output_dir/{$table_name}_" . date('Y-m-d_H-i-s') . ".sql";
        $file = fopen($filename, 'w');
        
        // Get column names
        $columns = array_keys($data[0]);
        $column_list = '`' . implode('`, `', $columns) . '`';
        
        fwrite($file, "-- Export of $table_name from local database\n");
        fwrite($file, "-- Generated on " . date('Y-m-d H:i:s') . "\n\n");
        fwrite($file, "DELETE FROM `$table_name`;\n\n");
        
        foreach ($data as $row) {
            $values = [];
            foreach ($columns as $col) {
                $value = $row[$col];
                if ($value === null) {
                    $values[] = 'NULL';
                } else {
                    $values[] = "'" . addslashes($value) . "'";
                }
            }
            $values_list = implode(', ', $values);
            fwrite($file, "INSERT INTO `$table_name` ($column_list) VALUES ($values_list);\n");
        }
        
        fclose($file);
        echo "✓ Exported $table_name to $filename\n";
        
    } catch (Exception $e) {
        echo "❌ Error exporting $table_name: " . $e->getMessage() . "\n";
    }
}

// Main execution
echo "=== Database Synchronization ===\n\n";

$action = $argv[1] ?? 'pull';

switch ($action) {
    case 'pull':
        echo "Pulling data from remote to local...\n\n";
        
        $remote_pdo = connectToDatabase($remote_config, 'remote');
        $local_pdo = connectToDatabase($local_config, 'local');
        
        if (!$remote_pdo || !$local_pdo) {
            exit(1);
        }
        
        foreach ($tables_to_sync as $table) {
            syncTable($remote_pdo, $local_pdo, $table);
        }
        break;
        
    case 'export':
        echo "Exporting local changes...\n\n";
        
        $local_pdo = connectToDatabase($local_config, 'local');
        if (!$local_pdo) {
            exit(1);
        }
        
        foreach ($tables_to_sync as $table) {
            exportLocalChanges($local_pdo, $table);
        }
        break;
        
    default:
        echo "Usage: php db_sync.php [pull|export]\n";
        echo "  pull   - Pull data from remote to local\n";
        echo "  export - Export local changes to SQL files\n";
        exit(1);
}

echo "\n=== Sync Complete! ===\n";
?>
