<?php
require_once 'system/autoload.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Test callback function for column manager
function test_column_callback($criteria = []) {
    // Sample data for testing
    $sample_data = [
        [
            'id' => 1,
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'role' => 'Admin',
            'department' => 'IT',
            'status' => 'Active',
            'created_at' => '2024-01-15'
        ],
        [
            'id' => 2,
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'role' => 'User',
            'department' => 'Sales',
            'status' => 'Active',
            'created_at' => '2024-01-20'
        ],
        [
            'id' => 3,
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'role' => 'Manager',
            'department' => 'Marketing',
            'status' => 'Inactive',
            'created_at' => '2024-01-25'
        ]
    ];

    // Sample columns configuration
    $columns = [
        [
            'label' => 'ID',
            'field' => 'id',
            'filter' => false,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Name',
            'field' => 'name',
            'filter' => true,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Email',
            'field' => 'email',
            'filter' => false,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Role',
            'field' => 'role',
            'filter' => true,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Department',
            'field' => 'department',
            'filter' => true,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Status',
            'field' => 'status',
            'filter' => true,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Created',
            'field' => 'created_at',
            'filter' => false,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Full Info',
            'field' => ['name', 'email', 'role'], // Multi-field column example
            'filter' => false,
            'extra_parameters' => ''
        ]
    ];

    $table_name = 'test_users';

    // Get column preferences from session
    $preference_key = "column_preferences_{$table_name}";
    $column_preferences = $_SESSION[$preference_key] ?? [];

    // Apply column structure if preferences exist
    if (!empty($column_preferences['structure'])) {
        $columns = apply_test_column_structure($columns, $column_preferences['structure']);
    }

    // Use the data_table class to render the table
    return data_table::process_data_table(
        ['columns' => $columns],
        $sample_data,
        'test_column_callback',
        [],
        $criteria,
        [],
        true, // just_body
        false,
        false,
        null,
        $table_name
    );
}

// Helper function to apply column structure for test
function apply_test_column_structure(array $original_columns, array $structure): array {
    if (empty($structure)) {
        return $original_columns;
    }

    $processed_columns = [];
    $original_by_field = [];

    // Index original columns by their field for lookup
    foreach ($original_columns as $column) {
        $original_by_field[$column['field']] = $column;
    }

    // Process each column in the structure
    foreach ($structure as $struct_col) {
        if (!$struct_col['visible']) {
            continue; // Skip hidden columns
        }

        // Create new column based on structure
        $new_column = [
            'label' => $struct_col['label'],
            'field' => count($struct_col['fields']) === 1 ? $struct_col['fields'][0] : $struct_col['fields'],
            'filter' => $struct_col['filter'] ?? false,
            'extra_parameters' => ''
        ];

        $processed_columns[] = $new_column;
    }

    // Add any original columns that weren't in the structure
    foreach ($original_columns as $original) {
        $original_fields = [$original['field']];
        $found_in_structure = false;

        foreach ($structure as $struct_col) {
            if (array_intersect($original_fields, $struct_col['fields'])) {
                $found_in_structure = true;
                break;
            }
        }

        if (!$found_in_structure) {
            $processed_columns[] = $original;
        }
    }

    return $processed_columns;
}

// Get initial data for display
$table_name = 'test_users';
$preference_key = "column_preferences_{$table_name}";
$column_preferences = $_SESSION[$preference_key] ?? [];

// Get the initial table data by calling our callback
$initial_table_html = test_column_callback();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Column Manager Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script defer src="https://unpkg.com/htmx.org@2.0.3"></script>
    <script>
        var APP_ROOT = '<?= APP_ROOT ?>';
    </script>
    <style>
        /* Column manager styles */
        .sortable-ghost {
            opacity: 0.4;
            background: #f3f4f6;
        }

        .sortable-chosen {
            background: #e5e7eb;
        }

        .drag-handle:hover {
            color: #374151;
        }

        /* HTMX loading indicator */
        .htmx-request {
            opacity: 0.7;
        }

        .htmx-request .htmx-indicator {
            display: inline;
        }

        .htmx-indicator {
            display: none;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto py-8 h-screen">
        <div class="bg-white shadow rounded-lg h-full flex flex-col">
            <div class="px-4 py-5 sm:p-6 flex-1 flex flex-col">
                <h1 class="text-2xl font-bold text-gray-900 mb-6">Enhanced Data Table with Hierarchical Column Manager</h1>
                <p class="text-gray-600 mb-4">This demonstrates the advanced column management features:</p>
                <ul class="text-sm text-gray-600 mb-6 list-disc list-inside space-y-1">
                    <li><strong>Column Visibility:</strong> Click checkboxes to show/hide entire columns</li>
                    <li><strong>Column Reordering:</strong> Drag columns by their main handles to reorder them</li>
                    <li><strong>Field Combination:</strong> Drag individual fields between columns to combine them</li>
                    <li><strong>Multi-field Columns:</strong> See how the "Full Info" column combines multiple fields</li>
                    <li><strong>Persistent Preferences:</strong> All changes are saved automatically and persist across page reloads</li>
                    <li><strong>Visual Feedback:</strong> Field count badges and drop zones provide clear feedback</li>
                </ul>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h3 class="text-sm font-semibold text-blue-900 mb-2">Try This:</h3>
                    <p class="text-sm text-blue-800">
                        Open the column manager and try dragging the "email" field from one column into another column's field area.
                        You can combine related fields into single columns or split them apart as needed.
                    </p>
                </div>

                <div id="data-table-container" class="flex-1 overflow-auto">
                    {!! $initial_table_html !!}
                </div>
            </div>
        </div>
    </div>
</body>
</html>
