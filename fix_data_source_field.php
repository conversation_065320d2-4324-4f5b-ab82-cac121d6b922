<?php
/**
 * Fix data_source field in autobooks_data_table_storage table
 * Changes data_source (varchar) to data_source_id (int) to match the code expectations
 */

// Include basic database configuration
require_once 'system/config.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Fix Data Source Field</title></head><body>";
echo "<h1>Fix Data Source Field in autobooks_data_table_storage</h1>";

echo "<pre>";

try {
    // Check if the table exists
    $check_table = tep_db_query("SHOW TABLES LIKE 'autobooks_data_table_storage'");
    if (!$check_table || tep_db_num_rows($check_table) == 0) {
        echo "✗ Table autobooks_data_table_storage does not exist\n";
        exit(1);
    }
    
    echo "✓ Table autobooks_data_table_storage exists\n";
    
    // Check current table structure
    $describe = tep_db_query("DESCRIBE autobooks_data_table_storage");
    $columns = [];
    while ($row = tep_db_fetch_array($describe)) {
        $columns[$row['Field']] = $row;
    }
    
    echo "Current table structure:\n";
    foreach ($columns as $field => $info) {
        echo "  - $field: {$info['Type']}\n";
    }
    
    // Check if we need to make changes
    $has_data_source = isset($columns['data_source']);
    $has_data_source_id = isset($columns['data_source_id']);
    
    if ($has_data_source_id && !$has_data_source) {
        echo "✓ Table already has data_source_id field and no data_source field\n";
        echo "No changes needed.\n";
    } elseif ($has_data_source && !$has_data_source_id) {
        echo "• Table has data_source field but needs data_source_id field\n";
        echo "Converting data_source (varchar) to data_source_id (int)...\n";
        
        // Add the new column
        $add_column_sql = "ALTER TABLE autobooks_data_table_storage 
                          ADD COLUMN data_source_id INT(11) DEFAULT NULL 
                          AFTER configuration";
        
        $result = tep_db_query($add_column_sql);
        if ($result) {
            echo "✓ Added data_source_id column\n";
        } else {
            echo "✗ Failed to add data_source_id column\n";
            exit(1);
        }
        
        // Migrate data if any exists (convert string IDs to integers)
        $migrate_sql = "UPDATE autobooks_data_table_storage 
                       SET data_source_id = CAST(data_source AS UNSIGNED) 
                       WHERE data_source IS NOT NULL 
                       AND data_source REGEXP '^[0-9]+$'";
        
        $migrate_result = tep_db_query($migrate_sql);
        if ($migrate_result) {
            $affected = tep_db_affected_rows();
            echo "✓ Migrated $affected records from data_source to data_source_id\n";
        }
        
        // Drop the old column
        $drop_column_sql = "ALTER TABLE autobooks_data_table_storage DROP COLUMN data_source";
        $drop_result = tep_db_query($drop_column_sql);
        if ($drop_result) {
            echo "✓ Dropped old data_source column\n";
        } else {
            echo "✗ Failed to drop old data_source column\n";
        }
        
        // Add index for the new column
        $add_index_sql = "ALTER TABLE autobooks_data_table_storage 
                         ADD KEY idx_data_source_id (data_source_id)";
        $index_result = tep_db_query($add_index_sql);
        if ($index_result) {
            echo "✓ Added index for data_source_id\n";
        } else {
            echo "• Index may already exist or failed to add\n";
        }
        
    } elseif ($has_data_source && $has_data_source_id) {
        echo "• Table has both data_source and data_source_id fields\n";
        echo "Removing old data_source field...\n";
        
        $drop_column_sql = "ALTER TABLE autobooks_data_table_storage DROP COLUMN data_source";
        $drop_result = tep_db_query($drop_column_sql);
        if ($drop_result) {
            echo "✓ Dropped old data_source column\n";
        } else {
            echo "✗ Failed to drop old data_source column\n";
        }
    } else {
        echo "✗ Unexpected table structure - neither data_source nor data_source_id found\n";
        exit(1);
    }
    
    // Show final table structure
    echo "\nFinal table structure:\n";
    $final_describe = tep_db_query("DESCRIBE autobooks_data_table_storage");
    while ($row = tep_db_fetch_array($final_describe)) {
        echo "  - {$row['Field']}: {$row['Type']}\n";
    }
    
    echo "\n✓ Migration completed successfully!\n";
    echo "The autobooks_data_table_storage table now uses data_source_id (int) instead of data_source (varchar)\n";
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    exit(1);
}

echo "</pre>";
echo "</body></html>";
?>
