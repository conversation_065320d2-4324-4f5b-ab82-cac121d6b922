<?php
/**
 * Run Data Table Storage Migration
 * 
 * This script runs the migration to create the autobooks_data_table_storage table
 * and migrate any existing session-based preferences.
 */
// Include the startup sequence to get database access
require_once 'system/startup_sequence_minimal.php';

use system\database;
use system\email_campaign;


// Start session to access any existing session data
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<!DOCTYPE html>";
echo "<html><head><title>Data Table Storage Migration</title></head><body>";
echo "<h1>Data Table Storage Migration</h1>";

echo "<pre>";

try {
    // Include and run the migration
    include 'system/migrations/create_data_table_storage.php';
    
} catch (Exception $e) {
    echo "Migration failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "</pre>";

echo "<hr>";
echo "<h2>Next Steps</h2>";
echo "<ul>";
echo "<li><a href='test_database_storage.php'>Test the new database storage system</a></li>";
echo "<li>The data table system now uses database storage instead of sessions</li>";
echo "<li>All existing functionality should work the same, but with persistent storage</li>";
echo "<li>Future data source integration is ready to be implemented</li>";
echo "</ul>";

echo "</body></html>";
?>
