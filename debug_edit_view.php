<?php
/**
 * Debug edit view data source loading
 */

// Include the necessary files
require_once 'system/classes/startup_sequence.class.php';
require_once 'system/functions/functions.php';

// Initialize the system
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . '/system/config/path_schema.php';
$path['fs_system'] = $path['fs_app_root'] . '/' . $schema['system']['root'];
startup_sequence::start($path, $schema);

use system\data_source_manager;

echo "<h1>Debug Edit View Data Source Loading</h1>";

// Use your data source ID (30 from the database row you showed)
$data_source_id = 30;

try {
    echo "<h2>1. Testing data_source_manager::get_data_source($data_source_id):</h2>";
    
    $data_source = data_source_manager::get_data_source($data_source_id);
    
    if ($data_source) {
        echo "<p style='color: green;'>✓ Data source loaded successfully</p>";
        
        echo "<h3>Basic Info:</h3>";
        echo "<ul>";
        echo "<li><strong>ID:</strong> {$data_source['id']}</li>";
        echo "<li><strong>Name:</strong> {$data_source['name']}</li>";
        echo "<li><strong>Description:</strong> {$data_source['description']}</li>";
        echo "</ul>";
        
        echo "<h3>Custom Tables Field:</h3>";
        if (isset($data_source['custom_tables'])) {
            echo "<p><strong>Type:</strong> " . gettype($data_source['custom_tables']) . "</p>";
            
            if (is_array($data_source['custom_tables'])) {
                echo "<p><strong>Count:</strong> " . count($data_source['custom_tables']) . "</p>";
                echo "<p style='color: green;'>✓ Custom tables is an array</p>";
                
                if (!empty($data_source['custom_tables'])) {
                    echo "<h4>Custom Tables Data:</h4>";
                    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; max-height: 400px;'>";
                    print_r($data_source['custom_tables']);
                    echo "</pre>";
                    
                    foreach ($data_source['custom_tables'] as $index => $custom_table) {
                        echo "<h5>Custom Table $index:</h5>";
                        echo "<ul>";
                        echo "<li><strong>Alias:</strong> " . ($custom_table['alias'] ?? 'NOT SET') . "</li>";
                        echo "<li><strong>Join Type:</strong> " . ($custom_table['join_type'] ?? 'NOT SET') . "</li>";
                        echo "<li><strong>Join Condition:</strong> " . ($custom_table['join_condition'] ?? 'NOT SET') . "</li>";
                        echo "<li><strong>Columns:</strong> " . ($custom_table['columns'] ?? 'NOT SET') . "</li>";
                        echo "<li><strong>Description:</strong> " . ($custom_table['description'] ?? 'NOT SET') . "</li>";
                        echo "<li><strong>SQL Length:</strong> " . strlen($custom_table['sql'] ?? '') . " characters</li>";
                        echo "</ul>";
                    }
                } else {
                    echo "<p style='color: orange;'>⚠ Custom tables array is empty</p>";
                }
            } elseif (is_string($data_source['custom_tables'])) {
                echo "<p style='color: orange;'>⚠ Custom tables is a string (not decoded): " . htmlspecialchars(substr($data_source['custom_tables'], 0, 200)) . "</p>";
            } else {
                echo "<p style='color: red;'>✗ Custom tables is not an array or string</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ Custom tables field not present</p>";
        }
        
        echo "<h3>Raw Database Data (custom_tables field):</h3>";
        // Get raw data directly from database
        $db = \system\database::table('autobooks_data_sources');
        $raw_data = $db->where('id', $data_source_id)->first();
        
        if ($raw_data && isset($raw_data['custom_tables'])) {
            echo "<p><strong>Raw custom_tables field:</strong></p>";
            echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; max-height: 200px;'>";
            echo htmlspecialchars($raw_data['custom_tables']);
            echo "</pre>";
            
            // Try to decode it manually
            $decoded = json_decode($raw_data['custom_tables'], true);
            if ($decoded !== null) {
                echo "<p style='color: green;'>✓ JSON decodes successfully</p>";
                echo "<pre style='background: #e8f5e8; padding: 10px; border: 1px solid #4CAF50; overflow-x: auto; max-height: 200px;'>";
                print_r($decoded);
                echo "</pre>";
            } else {
                echo "<p style='color: red;'>✗ JSON decode failed: " . json_last_error_msg() . "</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>✗ Data source not found</p>";
    }
    
    // Test the edit_view API function
    echo "<h2>2. Testing edit_view API function:</h2>";
    
    require_once 'system/api/data_sources.api.php';
    
    $edit_result = \api\data_sources\edit_view(['id' => $data_source_id]);
    
    if (is_string($edit_result)) {
        echo "<p style='color: green;'>✓ edit_view returned HTML content</p>";
        
        // Check if custom tables section is present
        if (strpos($edit_result, 'Custom Tables') !== false) {
            echo "<p style='color: green;'>✓ Custom Tables section found in HTML</p>";
        } else {
            echo "<p style='color: red;'>✗ Custom Tables section not found in HTML</p>";
        }
        
        // Check if custom table data is present
        if (strpos($edit_result, 'lastquote') !== false) {
            echo "<p style='color: green;'>✓ Custom table 'lastquote' found in HTML</p>";
        } else {
            echo "<p style='color: red;'>✗ Custom table 'lastquote' not found in HTML</p>";
        }
        
        // Check if the "no custom tables" message is shown
        if (strpos($edit_result, 'No custom tables configured') !== false) {
            echo "<p style='color: red;'>✗ 'No custom tables configured' message is showing (should not be visible)</p>";
        } else {
            echo "<p style='color: green;'>✓ 'No custom tables configured' message is not showing</p>";
        }
        
        // Show a snippet of the custom tables section
        if (preg_match('/<div id="custom-tables-container"[^>]*>(.*?)<\/div>/s', $edit_result, $matches)) {
            echo "<h4>Custom Tables Container HTML:</h4>";
            echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; max-height: 300px;'>";
            echo htmlspecialchars($matches[1]);
            echo "</pre>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ edit_view failed or returned unexpected result</p>";
        echo "<pre>" . print_r($edit_result, true) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='data_sources'>← Back to Data Sources</a></p>";
?>
