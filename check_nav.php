<?php
// Initialize the system
const DS = DIRECTORY_SEPARATOR;
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . DS . 'system'. DS . 'config'. DS . 'path_schema.php';
$ds = DS;
$path['fs_system'] = "{$path['fs_app_root']}{$ds}{$schema['system']['root']}" ;

$input_params = array_merge($_GET, $_POST);
define('INPUT_PARAMS',$input_params);
const DEBUG_MODE = true;
const API_RUN = false;

require_once $path['fs_system'] . DS . 'classes'. DS . 'startup_sequence.class.php';
require_once $path['fs_system'] . DS . 'functions'. DS . 'functions.php';

startup_sequence::start($path,$schema);

use system\database;

echo "<h1>Navigation Entry Check</h1>";

try {
    // Check the current navigation entry
    $nav = database::table('autobooks_navigation')
        ->where('route_key', 'data_sources')
        ->where('parent_path', 'root')
        ->first();

    if ($nav) {
        echo "<h2>Current navigation entry:</h2>";
        echo "<ul>";
        echo "<li><strong>file_path:</strong> " . $nav['file_path'] . "</li>";
        echo "<li><strong>is_system:</strong> " . ($nav['is_system'] ? 'true' : 'false') . "</li>";
        echo "<li><strong>route_key:</strong> " . $nav['route_key'] . "</li>";
        echo "<li><strong>parent_path:</strong> " . $nav['parent_path'] . "</li>";
        echo "</ul>";
        
        // Update the navigation entry to fix the routing
        echo "<h2>Updating navigation entry...</h2>";
        
        database::table('autobooks_navigation')
            ->where('id', $nav['id'])
            ->update([
                'file_path' => 'data_sources',  // Change from 'system' to 'data_sources'
                'is_system' => 1,              // Keep as system view
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
        echo "<p style='color: green;'>✓ Navigation entry updated successfully</p>";
        
        // Show updated entry
        $updated_nav = database::table('autobooks_navigation')
            ->where('route_key', 'data_sources')
            ->where('parent_path', 'root')
            ->first();
            
        echo "<h2>Updated navigation entry:</h2>";
        echo "<ul>";
        echo "<li><strong>file_path:</strong> " . $updated_nav['file_path'] . "</li>";
        echo "<li><strong>is_system:</strong> " . ($updated_nav['is_system'] ? 'true' : 'false') . "</li>";
        echo "<li><strong>route_key:</strong> " . $updated_nav['route_key'] . "</li>";
        echo "</ul>";
        
    } else {
        echo "<p style='color: red;'>Navigation entry not found</p>";
    }
    
    echo "<p><a href='data_sources'>Test Data Sources Link</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
