<?php
require_once 'system/autoload.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<!DOCTYPE html>";
echo "<html><head><title>Column Preferences Test</title></head><body>";
echo "<h1>Column Preferences Issue Test</h1>";

// Test with a specific table name - replace with your actual table name
$table_name = $_GET['table'] ?? 'your_table_name_here';

echo "<h2>Testing table: $table_name</h2>";

try {
    // Get current user ID
    $user_id = \data_table_storage::get_current_user_id();
    echo "<p><strong>User ID:</strong> " . ($user_id ?? 'null') . "</p>";
    
    // Get configuration from database
    $config = \data_table_storage::get_configuration($table_name, $user_id);
    
    if (!$config) {
        echo "<p style='color: red;'>No configuration found in database for table: $table_name</p>";
        echo "<p>Available tables:</p>";
        $configs = \data_table_storage::list_configurations($user_id);
        foreach ($configs as $c) {
            echo "<a href='?table=" . urlencode($c['table_name']) . "'>{$c['table_name']}</a><br>";
        }
    } else {
        $configuration = $config['configuration'];
        
        echo "<h3>Configuration Analysis</h3>";
        
        $structure = $configuration['structure'] ?? [];
        $hidden = $configuration['hidden'] ?? [];
        
        echo "<p><strong>Structure count:</strong> " . count($structure) . "</p>";
        echo "<p><strong>Hidden count:</strong> " . count($hidden) . "</p>";
        
        echo "<h4>Issues Found:</h4>";
        $issues = [];
        
        // Check for inconsistencies
        foreach ($structure as $index => $column) {
            $is_in_hidden = in_array($column['id'], $hidden);
            $visible_property = $column['visible'] ?? 'not set';
            
            if ($visible_property === true && $is_in_hidden) {
                $issues[] = "Column '{$column['label']}': visible=true but in hidden array";
            } elseif ($visible_property === false && !$is_in_hidden) {
                $issues[] = "Column '{$column['label']}': visible=false but not in hidden array";
            }
            
            // Check column ID generation
            $expected_id = 'col_' . $index . '_' . md5($column['label']);
            if ($expected_id !== $column['id']) {
                $issues[] = "Column '{$column['label']}': ID mismatch. Expected: $expected_id, Got: {$column['id']}";
            }
        }
        
        if (empty($issues)) {
            echo "<p style='color: green;'>No issues found!</p>";
        } else {
            echo "<ul style='color: red;'>";
            foreach ($issues as $issue) {
                echo "<li>$issue</li>";
            }
            echo "</ul>";
        }
        
        echo "<h4>Structure Details:</h4>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Index</th><th>ID</th><th>Label</th><th>Visible</th><th>In Hidden</th><th>Status</th></tr>";
        
        foreach ($structure as $index => $column) {
            $is_in_hidden = in_array($column['id'], $hidden);
            $visible_property = $column['visible'] ?? 'not set';
            
            $status = 'OK';
            $color = 'green';
            
            if ($visible_property === true && $is_in_hidden) {
                $status = 'INCONSISTENT';
                $color = 'red';
            } elseif ($visible_property === false && !$is_in_hidden) {
                $status = 'INCONSISTENT';
                $color = 'red';
            }
            
            echo "<tr style='color: $color;'>";
            echo "<td>$index</td>";
            echo "<td>{$column['id']}</td>";
            echo "<td>{$column['label']}</td>";
            echo "<td>" . ($visible_property === true ? 'true' : ($visible_property === false ? 'false' : $visible_property)) . "</td>";
            echo "<td>" . ($is_in_hidden ? 'YES' : 'NO') . "</td>";
            echo "<td>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h4>Hidden Array:</h4>";
        echo "<pre>" . print_r($hidden, true) . "</pre>";
        
        if (!empty($issues)) {
            echo "<h3>Fix This Table</h3>";
            echo "<p><a href='fix_column_preferences.php?table_name=" . urlencode($table_name) . "' style='background: red; color: white; padding: 10px; text-decoration: none;'>Fix Column Preferences</a></p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
