<?php
/**
 * Debug script to check data source structure
 */

// Include the necessary files
require_once 'system/classes/startup_sequence.class.php';
require_once 'system/functions/functions.php';

// Initialize the system
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . '/system/config/path_schema.php';
$path['fs_system'] = $path['fs_app_root'] . '/' . $schema['system']['root'];
startup_sequence::start($path, $schema);

use system\data_source_manager;

echo "<h1>Data Source Debug</h1>";

try {
    // Get all data sources
    $data_sources = data_source_manager::get_data_sources();
    
    if (empty($data_sources)) {
        echo "<p style='color: orange;'>No data sources found.</p>";
        exit;
    }
    
    echo "<h2>Available Data Sources:</h2>";
    foreach ($data_sources as $source) {
        echo "<h3>Data Source ID: {$source['id']} - {$source['name']}</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto;'>";
        print_r($source);
        echo "</pre>";
        
        // Test the duplicate_data_source method
        echo "<h4>Testing Duplication:</h4>";
        try {
            $new_id = data_source_manager::duplicate_data_source($source['id']);
            echo "<p style='color: green;'>✓ Successfully duplicated. New ID: $new_id</p>";
            
            // Get the new data source
            $new_source = data_source_manager::get_data_source($new_id);
            echo "<h5>New Data Source:</h5>";
            echo "<pre style='background: #e8f5e8; padding: 10px; border: 1px solid #4CAF50; overflow-x: auto;'>";
            print_r($new_source);
            echo "</pre>";
            
            // Clean up - delete the test duplicate
            $db = \system\database::table('autobooks_data_sources');
            $db->where('id', $new_id)->delete();
            echo "<p style='color: blue;'>✓ Test duplicate cleaned up</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        echo "<hr>";
        break; // Only test the first one
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<p><a href='data_sources'>← Back to Data Sources</a></p>";
?>
