<?php
/**
 * Test template logic for custom tables
 */

// Include the necessary files
require_once 'system/classes/startup_sequence.class.php';
require_once 'system/functions/functions.php';

// Initialize the system
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . '/system/config/path_schema.php';
$path['fs_system'] = $path['fs_app_root'] . '/' . $schema['system']['root'];
startup_sequence::start($path, $schema);

use edge\edge;

echo "<h1>Test Template Logic for Custom Tables</h1>";

// Test data matching your database
$test_data_source = [
    'id' => 30,
    'name' => 'Autodesk Subscriptions',
    'custom_tables' => [
        [
            'alias' => 'lastquote',
            'sql' => 'SELECT qi.subscription_id, q.id AS quote_id, q.quote_status FROM autodesk_quote_line_items qi JOIN autodesk_quotes q ON q.id = qi.quote_id WHERE qi.subscription_id IS NOT NULL ORDER BY q.quoted_date DESC LIMIT 1',
            'join_type' => 'LEFT JOIN',
            'join_condition' => 'subs.subscriptionId = lastquote.subscription_id',
            'columns' => 'quote_id, quote_status, quote_number, quoted_date',
            'description' => ''
        ]
    ]
];

echo "<h2>Test Data:</h2>";
echo "<pre>" . json_encode($test_data_source, JSON_PRETTY_PRINT) . "</pre>";

// Test the template condition logic
echo "<h2>Template Condition Tests:</h2>";

$custom_tables = $test_data_source['custom_tables'] ?? [];
echo "<p><strong>custom_tables:</strong> " . json_encode($custom_tables) . "</p>";
echo "<p><strong>empty() check:</strong> " . (empty($custom_tables) ? 'true (will show no-custom-tables message)' : 'false (will show foreach loop)') . "</p>";
echo "<p><strong>count():</strong> " . count($custom_tables) . "</p>";

if (empty($custom_tables)) {
    echo "<p style='color: red;'>✗ Template will show 'No custom tables configured' message</p>";
} else {
    echo "<p style='color: green;'>✓ Template will show custom tables in foreach loop</p>";
    
    foreach ($custom_tables as $index => $custom_table) {
        echo "<p><strong>Custom table $index:</strong> alias = " . ($custom_table['alias'] ?? 'NOT SET') . "</p>";
    }
}

// Create a minimal test template to verify the logic
$test_template_content = '
@props([
    "data_source" => []
])

<div>
    <h3>Custom Tables Test</h3>
    @if(empty($data_source["custom_tables"] ?? []))
        <p style="color: red;">NO CUSTOM TABLES MESSAGE SHOWING</p>
    @else
        <p style="color: green;">CUSTOM TABLES FOUND: {{ count($data_source["custom_tables"]) }}</p>
        @foreach($data_source["custom_tables"] as $index => $custom_table)
            <div style="border: 1px solid #ccc; padding: 10px; margin: 5px;">
                <p><strong>Index:</strong> {{ $index }}</p>
                <p><strong>Alias:</strong> {{ $custom_table["alias"] ?? "NOT SET" }}</p>
                <p><strong>Join Type:</strong> {{ $custom_table["join_type"] ?? "NOT SET" }}</p>
                <p><strong>Columns:</strong> {{ $custom_table["columns"] ?? "NOT SET" }}</p>
            </div>
        @endforeach
    @endif
</div>
';

// Save the test template
$test_template_path = 'system/components/edges/test-custom-tables-template.edge.php';
file_put_contents($test_template_path, $test_template_content);

echo "<h2>Testing Template Rendering:</h2>";

try {
    $rendered = Edge::render('test-custom-tables-template', [
        'data_source' => $test_data_source
    ]);
    
    echo "<h3>Rendered Output:</h3>";
    echo "<div style='border: 1px solid #ddd; padding: 10px; background: #f9f9f9;'>";
    echo $rendered;
    echo "</div>";
    
    if (strpos($rendered, 'CUSTOM TABLES FOUND') !== false) {
        echo "<p style='color: green;'>✓ Template correctly detected custom tables</p>";
    } else {
        echo "<p style='color: red;'>✗ Template did not detect custom tables</p>";
    }
    
    if (strpos($rendered, 'lastquote') !== false) {
        echo "<p style='color: green;'>✓ Custom table alias 'lastquote' found in output</p>";
    } else {
        echo "<p style='color: red;'>✗ Custom table alias 'lastquote' not found in output</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Template rendering error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Clean up the test template
unlink($test_template_path);

// Test with the actual data source from database
echo "<h2>Testing with Real Database Data:</h2>";

try {
    use system\data_source_manager;
    
    $real_data_source = data_source_manager::get_data_source(30);
    
    if ($real_data_source) {
        echo "<p><strong>Real custom_tables:</strong> " . json_encode($real_data_source['custom_tables'] ?? null) . "</p>";
        echo "<p><strong>Real empty() check:</strong> " . (empty($real_data_source['custom_tables'] ?? []) ? 'true (PROBLEM!)' : 'false (good)') . "</p>";
        
        if (!empty($real_data_source['custom_tables'])) {
            echo "<p style='color: green;'>✓ Real data source has custom tables</p>";
        } else {
            echo "<p style='color: red;'>✗ Real data source custom_tables is empty or missing</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Could not load real data source</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error loading real data source: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><a href='data_sources'>← Back to Data Sources</a></p>";
?>
