<?php
/**
 * Test script for GROUP BY functionality
 */

require_once 'system/autoloader.php';
require_once 'system/api/data_sources.api.php';

// Test the build_multi_table_query function with GROUP BY
echo "Testing GROUP BY functionality...\n\n";

// Test 1: Basic GROUP BY
echo "Test 1: Basic GROUP BY\n";
echo "======================\n";

$tables = ['users'];
$joins = [];
$selected_columns = ['users' => ['status', 'COUNT(*) as user_count']];
$filters = [];
$table_aliases = [];
$column_aliases = [];
$custom_columns = [
    ['sql' => 'COUNT(*)', 'alias' => 'user_count']
];
$sorting = [];
$grouping = [
    ['column' => 'users.status']
];
$limits = [];

$query = \api\data_sources\build_multi_table_query(
    $tables, $joins, $selected_columns, $filters, 
    $table_aliases, $column_aliases, $custom_columns, 
    $sorting, $grouping, $limits
);

echo "Generated Query:\n";
echo $query . "\n\n";

// Test 2: GROUP BY with multiple columns
echo "Test 2: GROUP BY with multiple columns\n";
echo "======================================\n";

$grouping = [
    ['column' => 'users.status'],
    ['column' => 'users.role']
];

$query = \api\data_sources\build_multi_table_query(
    $tables, $joins, $selected_columns, $filters, 
    $table_aliases, $column_aliases, $custom_columns, 
    $sorting, $grouping, $limits
);

echo "Generated Query:\n";
echo $query . "\n\n";

// Test 3: GROUP BY with table aliases
echo "Test 3: GROUP BY with table aliases\n";
echo "===================================\n";

$table_aliases = ['users' => 'u'];
$grouping = [
    ['column' => 'users.status']
];

$query = \api\data_sources\build_multi_table_query(
    $tables, $joins, $selected_columns, $filters, 
    $table_aliases, $column_aliases, $custom_columns, 
    $sorting, $grouping, $limits
);

echo "Generated Query:\n";
echo $query . "\n\n";

// Test 4: GROUP BY with ORDER BY and LIMIT
echo "Test 4: GROUP BY with ORDER BY and LIMIT\n";
echo "========================================\n";

$sorting = [
    ['column' => 'user_count', 'direction' => 'DESC']
];
$limits = [
    'enabled' => true,
    'limit' => 10
];

$query = \api\data_sources\build_multi_table_query(
    $tables, $joins, $selected_columns, $filters, 
    $table_aliases, $column_aliases, $custom_columns, 
    $sorting, $grouping, $limits
);

echo "Generated Query:\n";
echo $query . "\n\n";

// Test 5: Test get_available_columns_for_grouping function
echo "Test 5: Available columns for grouping\n";
echo "======================================\n";

$params = [
    'selected_columns' => ['users.name', 'users.email', 'users.status'],
    'custom_columns' => [
        ['sql' => 'CONCAT(first_name, " ", last_name)', 'alias' => 'full_name'],
        ['sql' => 'COUNT(*)', 'alias' => 'count_total'], // Should be excluded
        ['sql' => 'UPPER(status)', 'alias' => 'status_upper']
    ]
];

$available_columns = \api\data_sources\get_available_columns_for_grouping($params);

echo "Available columns for grouping:\n";
print_r($available_columns);

echo "\nTest completed successfully!\n";
?>
