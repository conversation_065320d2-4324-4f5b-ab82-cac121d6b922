<?php
/**
 * Test Script for Email Campaign System
 * 
 * This script tests the basic functionality of the email campaign system
 * to ensure everything is working correctly after the fixes.
 */

require_once 'system/startup_sequence_minimal.php';

use system\email_campaign;
use system\database;

echo "Testing Email Campaign System...\n\n";

try {
    // Test 1: Database connection
    echo "1. Testing database connection...\n";
    $test_query = database::table('autobooks_users')->select(['id'])->limit(1)->get();
    echo "   ✓ Database connection successful\n\n";
    
    // Test 2: Email campaign class instantiation
    echo "2. Testing email campaign class...\n";
    $campaign_manager = new email_campaign();
    echo "   ✓ Email campaign class instantiated successfully\n\n";

    // Test 2.1: Test API functions exist
    echo "2.1. Testing API functions...\n";
    if (function_exists('api\\email_campaigns\\get_campaigns')) {
        echo "   ✓ API functions loaded successfully\n\n";
    } else {
        echo "   ✗ API functions not found - check file location\n\n";
    }
    
    // Test 3: Check if campaign tables exist
    echo "3. Checking campaign tables...\n";
    try {
        $campaigns = database::table('email_campaigns')->limit(1)->get();
        echo "   ✓ email_campaigns table exists\n";
    } catch (Exception $e) {
        echo "   ✗ email_campaigns table missing - run migration first\n";
        echo "   Error: " . $e->getMessage() . "\n";
    }
    
    try {
        $templates = database::table('email_campaign_templates')->limit(1)->get();
        echo "   ✓ email_campaign_templates table exists\n";
    } catch (Exception $e) {
        echo "   ✗ email_campaign_templates table missing - run migration first\n";
    }
    
    try {
        $history = database::table('email_campaign_history')->limit(1)->get();
        echo "   ✓ email_campaign_history table exists\n";
    } catch (Exception $e) {
        echo "   ✗ email_campaign_history table missing - run migration first\n";
    }
    
    echo "\n";
    
    // Test 4: Try to get campaigns (should work even if empty)
    echo "4. Testing campaign retrieval...\n";
    try {
        $campaigns = $campaign_manager->get_campaigns();
        echo "   ✓ Successfully retrieved " . count($campaigns) . " campaigns\n\n";
        
        if (!empty($campaigns)) {
            echo "   Existing campaigns:\n";
            foreach ($campaigns as $campaign) {
                echo "   - {$campaign['name']} (Type: {$campaign['type']}, Status: {$campaign['status']})\n";
            }
            echo "\n";
        }
    } catch (Exception $e) {
        echo "   ✗ Failed to retrieve campaigns: " . $e->getMessage() . "\n\n";
    }
    
    // Test 5: Test creating a simple campaign (if tables exist)
    echo "5. Testing campaign creation...\n";
    try {
        $test_campaign_id = $campaign_manager->create_campaign([
            'name' => 'Test Campaign - ' . date('Y-m-d H:i:s'),
            'description' => 'Test campaign created by test script',
            'type' => 'general',
            'status' => 'draft',
            'from_email' => '<EMAIL>',
            'from_name' => 'Test Sender',
            'subject_template' => 'Test Subject'
        ]);
        
        echo "   ✓ Successfully created test campaign (ID: $test_campaign_id)\n";
        
        // Clean up - delete the test campaign
        $campaign_manager->delete_campaign($test_campaign_id);
        echo "   ✓ Test campaign cleaned up\n\n";
        
    } catch (Exception $e) {
        echo "   ✗ Failed to create test campaign: " . $e->getMessage() . "\n\n";
    }
    
    echo "✅ Email Campaign System Test Complete!\n\n";
    
    echo "Next Steps:\n";
    echo "1. Run the migration script: php system/migrations/migrate_email_campaigns.php\n";
    echo "2. Access the web interface at: /email_campaigns\n";
    echo "3. Check the navigation menu for 'Email Campaigns'\n\n";
    
} catch (Exception $e) {
    echo "❌ Test failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
