<?php
/**
 * Clear compiled template cache for email campaign form
 * This script clears the compiled Edge template cache to force recompilation
 */

// Define the temp directory path (adjust for server environment)
$temp_dirs = [
    'temp/autobooks/',  // Local development
    '/var/www/vhosts/cadservices.co.uk/temp/autobooks/'  // Server path from error
];

$files_cleared = 0;
$errors = [];

foreach ($temp_dirs as $temp_dir) {
    if (is_dir($temp_dir)) {
        echo "Checking directory: $temp_dir\n";
        
        // Clear all compiled Edge templates
        $files = glob($temp_dir . '*.edge.php');
        foreach ($files as $file) {
            if (unlink($file)) {
                echo "Deleted: " . basename($file) . "\n";
                $files_cleared++;
            } else {
                $errors[] = "Failed to delete: " . basename($file);
            }
        }
        
        // Specifically target the email-campaign-form template
        $specific_files = [
            $temp_dir . 'email-campaign-form.edge.php',
            $temp_dir . 'email_campaign_form.edge.php'
        ];
        
        foreach ($specific_files as $specific_file) {
            if (file_exists($specific_file)) {
                if (unlink($specific_file)) {
                    echo "Deleted specific file: " . basename($specific_file) . "\n";
                    $files_cleared++;
                } else {
                    $errors[] = "Failed to delete specific file: " . basename($specific_file);
                }
            }
        }
    } else {
        echo "Directory not found: $temp_dir\n";
    }
}

echo "\nSummary:\n";
echo "Files cleared: $files_cleared\n";

if (!empty($errors)) {
    echo "Errors encountered:\n";
    foreach ($errors as $error) {
        echo "- $error\n";
    }
}

echo "\nTemplate cache clearing complete!\n";
echo "The email campaign form template will be recompiled on next access.\n";
?>
