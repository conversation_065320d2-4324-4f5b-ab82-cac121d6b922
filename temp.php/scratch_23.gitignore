toggle_column_debug: column_preferences.api.php > api\data_table\column_preferences\toggle_column() 147
array(1) {
  ["toggle_column_debug"]: array(6) {
    ["column_id"]: string(38) "col_0_490aa6e856ccf208a054389e47ce0d06"
    ["hidden"]: array(2) {
      [0]: string(38) "col_1_15cc3fba1d13d18d588ca3693361fa38"
      [1]: string(38) "col_0_490aa6e856ccf208a054389e47ce0d06"
    }
    ["is_visible"]: bool(false)
    ["structure_count"]: int(15)
    ["table_name"]: string(22) "autodesk_subscriptions"
    ["updated_preferences"]: array(5) {
      ["structure"]: array(15) {
        [0]: &array(6) {
          ["id"]: string(38) "col_0_490aa6e856ccf208a054389e47ce0d06"
          ["label"]: string(2) "Id"
          ["field"]: string(7) "subs_id"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(7) "subs_id"
          }
          ["visible"]: bool(false)
        }
        [1]: array(6) {
          ["id"]: string(38) "col_1_15cc3fba1d13d18d588ca3693361fa38"
          ["label"]: string(27) "SubscriptionReferenceNumber"
          ["field"]: string(32) "subs_subscriptionReferenceNumber"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(32) "subs_subscriptionReferenceNumber"
          }
          ["visible"]: bool(false)
        }
        [2]: array(6) {
          ["id"]: string(38) "col_2_51980cd41cd1fc05c08fa2bc5e8a4e30"
          ["label"]: string(14) "SubscriptionId"
          ["field"]: string(19) "subs_subscriptionId"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(19) "subs_subscriptionId"
          }
          ["visible"]: bool(true)
        }
        [3]: array(6) {
          ["id"]: string(38) "col_3_694e8d1f2ee056f98ee488bdc4982d73"
          ["label"]: string(8) "Quantity"
          ["field"]: string(13) "subs_quantity"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(13) "subs_quantity"
          }
          ["visible"]: bool(true)
        }
        [4]: array(6) {
          ["id"]: string(38) "col_4_ec53a8c4f07baed5d8825072c89799be"
          ["label"]: string(6) "Status"
          ["field"]: string(11) "subs_status"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(11) "subs_status"
          }
          ["visible"]: bool(true)
        }
        [5]: array(6) {
          ["id"]: string(38) "col_5_4b613a54fb8fdaba94406d746a16cf09"
          ["label"]: string(9) "StartDate"
          ["field"]: string(14) "subs_startDate"
          ["filter"]: bool(true)
          ["fields"]: array(2) {
            [0]: string(14) "subs_startDate"
            [1]: string(12) "subs_endDate"
          }
          ["visible"]: bool(true)
        }
        [6]: array(6) {
          ["id"]: string(38) "col_6_a7f44c44b37e090a40ad2d76aa86d08d"
          ["label"]: string(7) "EndDate"
          ["field"]: string(12) "subs_endDate"
          ["filter"]: bool(true)
          ["fields"]: array(0) {
          }
          ["visible"]: bool(true)
        }
        [7]: array(6) {
          ["id"]: string(38) "col_7_490aa6e856ccf208a054389e47ce0d06"
          ["label"]: string(2) "Id"
          ["field"]: string(10) "endcust_id"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(10) "endcust_id"
          }
          ["visible"]: bool(true)
        }
        [8]: array(6) {
          ["id"]: string(38) "col_8_a2edb050ddd149d2e0bd932557f38be3"
          ["label"]: string(11) "Account Csn"
          ["field"]: string(19) "endcust_account_csn"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(19) "endcust_account_csn"
          }
          ["visible"]: bool(true)
        }
        [9]: array(6) {
          ["id"]: string(38) "col_9_49ee3087348e8d44e1feda1917443987"
          ["label"]: string(4) "Name"
          ["field"]: string(12) "endcust_name"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(12) "endcust_name"
          }
          ["visible"]: bool(true)
        }
        [10]: array(6) {
          ["id"]: string(39) "col_10_bc910f8bdf70f29374f496f05be0330c"
          ["label"]: string(10) "First Name"
          ["field"]: string(18) "endcust_first_name"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(18) "endcust_first_name"
          }
          ["visible"]: bool(true)
        }
        [11]: array(6) {
          ["id"]: string(39) "col_11_77587239bf4c54ea493c7033e1dbf636"
          ["label"]: string(9) "Last Name"
          ["field"]: string(17) "endcust_last_name"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(17) "endcust_last_name"
          }
          ["visible"]: bool(true)
        }
        [12]: array(6) {
          ["id"]: string(39) "col_12_8184e89412e9d55fc20f501c898b327d"
          ["label"]: string(8) "Quote Id"
          ["field"]: string(18) "lastquote_quote_id"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(18) "lastquote_quote_id"
          }
          ["visible"]: bool(true)
        }
        [13]: array(6) {
          ["id"]: string(39) "col_13_a1a3d4145963c3c20655c504af782d58"
          ["label"]: string(12) "Quote Number"
          ["field"]: string(22) "lastquote_quote_number"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(22) "lastquote_quote_number"
          }
          ["visible"]: bool(true)
        }
        [14]: array(6) {
          ["id"]: string(39) "col_14_4edb4bac36117d2361f81056bf00423a"
          ["label"]: string(11) "Quoted Date"
          ["field"]: string(21) "lastquote_quoted_date"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(21) "lastquote_quoted_date"
          }
          ["visible"]: bool(true)
        }
      }
      ["updated_at"]: string(19) "2025-08-03 20:51:16"
      ["data_source_type"]: string(11) "data_source"
      ["data_source_id"]: int(30)
      ["hidden"]: array(2) {
        [0]: string(38) "col_1_15cc3fba1d13d18d588ca3693361fa38"
        [1]: string(38) "col_0_490aa6e856ccf208a054389e47ce0d06"
      }
    }
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> api\data_table\column_preferences\toggle_column, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php, Line: 60
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_subscriptions","callback":"","column_id":"col_0_490aa6e856ccf208a054389e47ce...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 138
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 131
         <strong>Arguments:</strong>
         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
$configurationsavvy: data_table_storage.class.php > save_configuration() 56
array(5) {
  ["structure"]: array(15) {
    [0]: &array(6) {
      ["id"]: string(38) "col_0_490aa6e856ccf208a054389e47ce0d06"
      ["label"]: string(2) "Id"
      ["field"]: string(7) "subs_id"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(7) "subs_id"
      }
      ["visible"]: bool(false)
    }
    [1]: array(6) {
      ["id"]: string(38) "col_1_15cc3fba1d13d18d588ca3693361fa38"
      ["label"]: string(27) "SubscriptionReferenceNumber"
      ["field"]: string(32) "subs_subscriptionReferenceNumber"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(32) "subs_subscriptionReferenceNumber"
      }
      ["visible"]: bool(false)
    }
    [2]: array(6) {
      ["id"]: string(38) "col_2_51980cd41cd1fc05c08fa2bc5e8a4e30"
      ["label"]: string(14) "SubscriptionId"
      ["field"]: string(19) "subs_subscriptionId"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(19) "subs_subscriptionId"
      }
      ["visible"]: bool(true)
    }
    [3]: array(6) {
      ["id"]: string(38) "col_3_694e8d1f2ee056f98ee488bdc4982d73"
      ["label"]: string(8) "Quantity"
      ["field"]: string(13) "subs_quantity"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(13) "subs_quantity"
      }
      ["visible"]: bool(true)
    }
    [4]: array(6) {
      ["id"]: string(38) "col_4_ec53a8c4f07baed5d8825072c89799be"
      ["label"]: string(6) "Status"
      ["field"]: string(11) "subs_status"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(11) "subs_status"
      }
      ["visible"]: bool(true)
    }
    [5]: array(6) {
      ["id"]: string(38) "col_5_4b613a54fb8fdaba94406d746a16cf09"
      ["label"]: string(9) "StartDate"
      ["field"]: string(14) "subs_startDate"
      ["filter"]: bool(true)
      ["fields"]: array(2) {
        [0]: string(14) "subs_startDate"
        [1]: string(12) "subs_endDate"
      }
      ["visible"]: bool(true)
    }
    [6]: array(6) {
      ["id"]: string(38) "col_6_a7f44c44b37e090a40ad2d76aa86d08d"
      ["label"]: string(7) "EndDate"
      ["field"]: string(12) "subs_endDate"
      ["filter"]: bool(true)
      ["fields"]: array(0) {
      }
      ["visible"]: bool(true)
    }
    [7]: array(6) {
      ["id"]: string(38) "col_7_490aa6e856ccf208a054389e47ce0d06"
      ["label"]: string(2) "Id"
      ["field"]: string(10) "endcust_id"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(10) "endcust_id"
      }
      ["visible"]: bool(true)
    }
    [8]: array(6) {
      ["id"]: string(38) "col_8_a2edb050ddd149d2e0bd932557f38be3"
      ["label"]: string(11) "Account Csn"
      ["field"]: string(19) "endcust_account_csn"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(19) "endcust_account_csn"
      }
      ["visible"]: bool(true)
    }
    [9]: array(6) {
      ["id"]: string(38) "col_9_49ee3087348e8d44e1feda1917443987"
      ["label"]: string(4) "Name"
      ["field"]: string(12) "endcust_name"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(12) "endcust_name"
      }
      ["visible"]: bool(true)
    }
    [10]: array(6) {
      ["id"]: string(39) "col_10_bc910f8bdf70f29374f496f05be0330c"
      ["label"]: string(10) "First Name"
      ["field"]: string(18) "endcust_first_name"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(18) "endcust_first_name"
      }
      ["visible"]: bool(true)
    }
    [11]: array(6) {
      ["id"]: string(39) "col_11_77587239bf4c54ea493c7033e1dbf636"
      ["label"]: string(9) "Last Name"
      ["field"]: string(17) "endcust_last_name"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(17) "endcust_last_name"
      }
      ["visible"]: bool(true)
    }
    [12]: array(6) {
      ["id"]: string(39) "col_12_8184e89412e9d55fc20f501c898b327d"
      ["label"]: string(8) "Quote Id"
      ["field"]: string(18) "lastquote_quote_id"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(18) "lastquote_quote_id"
      }
      ["visible"]: bool(true)
    }
    [13]: array(6) {
      ["id"]: string(39) "col_13_a1a3d4145963c3c20655c504af782d58"
      ["label"]: string(12) "Quote Number"
      ["field"]: string(22) "lastquote_quote_number"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(22) "lastquote_quote_number"
      }
      ["visible"]: bool(true)
    }
    [14]: array(6) {
      ["id"]: string(39) "col_14_4edb4bac36117d2361f81056bf00423a"
      ["label"]: string(11) "Quoted Date"
      ["field"]: string(21) "lastquote_quoted_date"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(21) "lastquote_quoted_date"
      }
      ["visible"]: bool(true)
    }
  }
  ["updated_at"]: string(19) "2025-08-03 20:51:16"
  ["data_source_type"]: string(11) "data_source"
  ["data_source_id"]: int(30)
  ["hidden"]: array(2) {
    [0]: string(38) "col_1_15cc3fba1d13d18d588ca3693361fa38"
    [1]: string(38) "col_0_490aa6e856ccf208a054389e47ce0d06"
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> save_configuration, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php, Line: 27
         <strong>Arguments:</strong>
         0: "autodesk_subscriptions"
         1: {"structure":[{"id":"col_0_490aa6e856ccf208a054389e47ce0d06","label":"Id","field":"subs_id","filter"...
         2: 2
         3: 30
      <strong>Function:</strong> api\data_table\column_preferences\save_column_preferences, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php, Line: 158
         <strong>Arguments:</strong>
         0: "autodesk_subscriptions"
         1: {"structure":[{"id":"col_0_490aa6e856ccf208a054389e47ce0d06","label":"Id","field":"subs_id","filter"...
         2: "30"
      <strong>Function:</strong> api\data_table\column_preferences\toggle_column, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php, Line: 60
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_subscriptions","callback":"","column_id":"col_0_490aa6e856ccf208a054389e47ce...

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
api_process_criteria: data_table.class.php > api_process_criteria() 299
array(3) {
  ["table_name"]: string(22) "autodesk_subscriptions"
  ["callback"]: string(0) ""
  ["data_source"]: string(2) "30"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> api_process_criteria, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php, Line: 65
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_subscriptions","callback":"","data_source":"30"}
      <strong>Function:</strong> api\data_table\column_preferences\regenerate_table, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php, Line: 160
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_subscriptions","callback":"","data_source":"30"}
      <strong>Function:</strong> api\data_table\column_preferences\toggle_column, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php, Line: 60
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_subscriptions","callback":"","column_id":"col_0_490aa6e856ccf208a054389e47ce...

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
regenerate_table_debug: column_preferences.api.php > api\data_table\column_preferences\regenerate_table() 73
array(5) {
  ["regenerate_table"]: string(22) "autodesk_subscriptions"
  ["hidden_count"]: int(2)
  ["structure_count"]: int(15)
  ["fresh_preferences"]: array(5) {
    ["structure"]: array(15) {
      [0]: array(6) {
        ["id"]: string(38) "col_0_490aa6e856ccf208a054389e47ce0d06"
        ["label"]: string(2) "Id"
        ["field"]: string(7) "subs_id"
        ["filter"]: bool(true)
        ["fields"]: array(1) {
          [0]: string(7) "subs_id"
        }
        ["visible"]: bool(false)
      }
      [1]: array(6) {
        ["id"]: string(38) "col_1_15cc3fba1d13d18d588ca3693361fa38"
        ["label"]: string(27) "SubscriptionReferenceNumber"
        ["field"]: string(32) "subs_subscriptionReferenceNumber"
        ["filter"]: bool(true)
        ["fields"]: array(1) {
          [0]: string(32) "subs_subscriptionReferenceNumber"
        }
        ["visible"]: bool(false)
      }
      [2]: array(6) {
        ["id"]: string(38) "col_2_51980cd41cd1fc05c08fa2bc5e8a4e30"
        ["label"]: string(14) "SubscriptionId"
        ["field"]: string(19) "subs_subscriptionId"
        ["filter"]: bool(true)
        ["fields"]: array(1) {
          [0]: string(19) "subs_subscriptionId"
        }
        ["visible"]: bool(true)
      }
      [3]: array(6) {
        ["id"]: string(38) "col_3_694e8d1f2ee056f98ee488bdc4982d73"
        ["label"]: string(8) "Quantity"
        ["field"]: string(13) "subs_quantity"
        ["filter"]: bool(true)
        ["fields"]: array(1) {
          [0]: string(13) "subs_quantity"
        }
        ["visible"]: bool(true)
      }
      [4]: array(6) {
        ["id"]: string(38) "col_4_ec53a8c4f07baed5d8825072c89799be"
        ["label"]: string(6) "Status"
        ["field"]: string(11) "subs_status"
        ["filter"]: bool(true)
        ["fields"]: array(1) {
          [0]: string(11) "subs_status"
        }
        ["visible"]: bool(true)
      }
      [5]: array(6) {
        ["id"]: string(38) "col_5_4b613a54fb8fdaba94406d746a16cf09"
        ["label"]: string(9) "StartDate"
        ["field"]: string(14) "subs_startDate"
        ["filter"]: bool(true)
        ["fields"]: array(2) {
          [0]: string(14) "subs_startDate"
          [1]: string(12) "subs_endDate"
        }
        ["visible"]: bool(true)
      }
      [6]: array(6) {
        ["id"]: string(38) "col_6_a7f44c44b37e090a40ad2d76aa86d08d"
        ["label"]: string(7) "EndDate"
        ["field"]: string(12) "subs_endDate"
        ["filter"]: bool(true)
        ["fields"]: array(0) {
        }
        ["visible"]: bool(true)
      }
      [7]: array(6) {
        ["id"]: string(38) "col_7_490aa6e856ccf208a054389e47ce0d06"
        ["label"]: string(2) "Id"
        ["field"]: string(10) "endcust_id"
        ["filter"]: bool(true)
        ["fields"]: array(1) {
          [0]: string(10) "endcust_id"
        }
        ["visible"]: bool(true)
      }
      [8]: array(6) {
        ["id"]: string(38) "col_8_a2edb050ddd149d2e0bd932557f38be3"
        ["label"]: string(11) "Account Csn"
        ["field"]: string(19) "endcust_account_csn"
        ["filter"]: bool(true)
        ["fields"]: array(1) {
          [0]: string(19) "endcust_account_csn"
        }
        ["visible"]: bool(true)
      }
      [9]: array(6) {
        ["id"]: string(38) "col_9_49ee3087348e8d44e1feda1917443987"
        ["label"]: string(4) "Name"
        ["field"]: string(12) "endcust_name"
        ["filter"]: bool(true)
        ["fields"]: array(1) {
          [0]: string(12) "endcust_name"
        }
        ["visible"]: bool(true)
      }
      [10]: array(6) {
        ["id"]: string(39) "col_10_bc910f8bdf70f29374f496f05be0330c"
        ["label"]: string(10) "First Name"
        ["field"]: string(18) "endcust_first_name"
        ["filter"]: bool(true)
        ["fields"]: array(1) {
          [0]: string(18) "endcust_first_name"
        }
        ["visible"]: bool(true)
      }
      [11]: array(6) {
        ["id"]: string(39) "col_11_77587239bf4c54ea493c7033e1dbf636"
        ["label"]: string(9) "Last Name"
        ["field"]: string(17) "endcust_last_name"
        ["filter"]: bool(true)
        ["fields"]: array(1) {
          [0]: string(17) "endcust_last_name"
        }
        ["visible"]: bool(true)
      }
      [12]: array(6) {
        ["id"]: string(39) "col_12_8184e89412e9d55fc20f501c898b327d"
        ["label"]: string(8) "Quote Id"
        ["field"]: string(18) "lastquote_quote_id"
        ["filter"]: bool(true)
        ["fields"]: array(1) {
          [0]: string(18) "lastquote_quote_id"
        }
        ["visible"]: bool(true)
      }
      [13]: array(6) {
        ["id"]: string(39) "col_13_a1a3d4145963c3c20655c504af782d58"
        ["label"]: string(12) "Quote Number"
        ["field"]: string(22) "lastquote_quote_number"
        ["filter"]: bool(true)
        ["fields"]: array(1) {
          [0]: string(22) "lastquote_quote_number"
        }
        ["visible"]: bool(true)
      }
      [14]: array(6) {
        ["id"]: string(39) "col_14_4edb4bac36117d2361f81056bf00423a"
        ["label"]: string(11) "Quoted Date"
        ["field"]: string(21) "lastquote_quoted_date"
        ["filter"]: bool(true)
        ["fields"]: array(1) {
          [0]: string(21) "lastquote_quoted_date"
        }
        ["visible"]: bool(true)
      }
    }
    ["updated_at"]: string(19) "2025-08-03 20:51:16"
    ["data_source_type"]: string(11) "data_source"
    ["data_source_id"]: int(30)
    ["hidden"]: array(2) {
      [0]: string(38) "col_1_15cc3fba1d13d18d588ca3693361fa38"
      [1]: string(38) "col_0_490aa6e856ccf208a054389e47ce0d06"
    }
  }
  ["criteria"]: array(0) {
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> api\data_table\column_preferences\regenerate_table, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php, Line: 160
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_subscriptions","callback":"","data_source":"30"}
      <strong>Function:</strong> api\data_table\column_preferences\toggle_column, File: /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/layout-api.edge.php, Line: 60
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_subscriptions","callback":"","column_id":"col_0_490aa6e856ccf208a054389e47ce...
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 138
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"

----------------------------------------------------------------------------
-->



<!--
********************************************************************************************************************************************************
configy: data-table.edge.php > include() 79
array(5) {
  ["structure"]: array(15) {
    [0]: array(6) {
      ["id"]: string(38) "col_0_490aa6e856ccf208a054389e47ce0d06"
      ["label"]: string(2) "Id"
      ["field"]: string(7) "subs_id"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(7) "subs_id"
      }
      ["visible"]: bool(false)
    }
    [1]: array(6) {
      ["id"]: string(38) "col_1_15cc3fba1d13d18d588ca3693361fa38"
      ["label"]: string(27) "SubscriptionReferenceNumber"
      ["field"]: string(32) "subs_subscriptionReferenceNumber"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(32) "subs_subscriptionReferenceNumber"
      }
      ["visible"]: bool(false)
    }
    [2]: array(6) {
      ["id"]: string(38) "col_2_51980cd41cd1fc05c08fa2bc5e8a4e30"
      ["label"]: string(14) "SubscriptionId"
      ["field"]: string(19) "subs_subscriptionId"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(19) "subs_subscriptionId"
      }
      ["visible"]: bool(true)
    }
    [3]: array(6) {
      ["id"]: string(38) "col_3_694e8d1f2ee056f98ee488bdc4982d73"
      ["label"]: string(8) "Quantity"
      ["field"]: string(13) "subs_quantity"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(13) "subs_quantity"
      }
      ["visible"]: bool(true)
    }
    [4]: array(6) {
      ["id"]: string(38) "col_4_ec53a8c4f07baed5d8825072c89799be"
      ["label"]: string(6) "Status"
      ["field"]: string(11) "subs_status"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(11) "subs_status"
      }
      ["visible"]: bool(true)
    }
    [5]: array(6) {
      ["id"]: string(38) "col_5_4b613a54fb8fdaba94406d746a16cf09"
      ["label"]: string(9) "StartDate"
      ["field"]: string(14) "subs_startDate"
      ["filter"]: bool(true)
      ["fields"]: array(2) {
        [0]: string(14) "subs_startDate"
        [1]: string(12) "subs_endDate"
      }
      ["visible"]: bool(true)
    }
    [6]: array(6) {
      ["id"]: string(38) "col_6_a7f44c44b37e090a40ad2d76aa86d08d"
      ["label"]: string(7) "EndDate"
      ["field"]: string(12) "subs_endDate"
      ["filter"]: bool(true)
      ["fields"]: array(0) {
      }
      ["visible"]: bool(true)
    }
    [7]: array(6) {
      ["id"]: string(38) "col_7_490aa6e856ccf208a054389e47ce0d06"
      ["label"]: string(2) "Id"
      ["field"]: string(10) "endcust_id"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(10) "endcust_id"
      }
      ["visible"]: bool(true)
    }
    [8]: array(6) {
      ["id"]: string(38) "col_8_a2edb050ddd149d2e0bd932557f38be3"
      ["label"]: string(11) "Account Csn"
      ["field"]: string(19) "endcust_account_csn"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(19) "endcust_account_csn"
      }
      ["visible"]: bool(true)
    }
    [9]: array(6) {
      ["id"]: string(38) "col_9_49ee3087348e8d44e1feda1917443987"
      ["label"]: string(4) "Name"
      ["field"]: string(12) "endcust_name"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(12) "endcust_name"
      }
      ["visible"]: bool(true)
    }
    [10]: array(6) {
      ["id"]: string(39) "col_10_bc910f8bdf70f29374f496f05be0330c"
      ["label"]: string(10) "First Name"
      ["field"]: string(18) "endcust_first_name"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(18) "endcust_first_name"
      }
      ["visible"]: bool(true)
    }
    [11]: array(6) {
      ["id"]: string(39) "col_11_77587239bf4c54ea493c7033e1dbf636"
      ["label"]: string(9) "Last Name"
      ["field"]: string(17) "endcust_last_name"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(17) "endcust_last_name"
      }
      ["visible"]: bool(true)
    }
    [12]: array(6) {
      ["id"]: string(39) "col_12_8184e89412e9d55fc20f501c898b327d"
      ["label"]: string(8) "Quote Id"
      ["field"]: string(18) "lastquote_quote_id"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(18) "lastquote_quote_id"
      }
      ["visible"]: bool(true)
    }
    [13]: array(6) {
      ["id"]: string(39) "col_13_a1a3d4145963c3c20655c504af782d58"
      ["label"]: string(12) "Quote Number"
      ["field"]: string(22) "lastquote_quote_number"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(22) "lastquote_quote_number"
      }
      ["visible"]: bool(true)
    }
    [14]: array(6) {
      ["id"]: string(39) "col_14_4edb4bac36117d2361f81056bf00423a"
      ["label"]: string(11) "Quoted Date"
      ["field"]: string(21) "lastquote_quoted_date"
      ["filter"]: bool(true)
      ["fields"]: array(1) {
        [0]: string(21) "lastquote_quoted_date"
      }
      ["visible"]: bool(true)
    }
  }
  ["updated_at"]: string(19) "2025-08-03 20:51:16"
  ["data_source_type"]: string(11) "data_source"
  ["data_source_id"]: int(30)
  ["hidden"]: array(2) {
    [0]: string(38) "col_1_15cc3fba1d13d18d588ca3693361fa38"
    [1]: string(38) "col_0_490aa6e856ccf208a054389e47ce0d06"
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 138
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 131
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_subscriptions","criteria":[],"column_preferences":{"structure":[{"id":"col_0...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php"
      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php, Line: 81
         <strong>Arguments:</strong>
         0: "data-table"
         1: {"table_name":"autodesk_subscriptions","criteria":[],"column_preferences":{"structure":[{"id":"col_0...

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
data_table_template_debug: data-table.edge.php > include() 112
array(1) {
  ["data_table_template_debug"]: array(4) {
    ["table_name"]: string(22) "autodesk_subscriptions"
    ["column_preferences"]: array(5) {
      ["structure"]: array(15) {
        [0]: array(6) {
          ["id"]: string(38) "col_0_490aa6e856ccf208a054389e47ce0d06"
          ["label"]: string(2) "Id"
          ["field"]: string(7) "subs_id"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(7) "subs_id"
          }
          ["visible"]: bool(false)
        }
        [1]: array(6) {
          ["id"]: string(38) "col_1_15cc3fba1d13d18d588ca3693361fa38"
          ["label"]: string(27) "SubscriptionReferenceNumber"
          ["field"]: string(32) "subs_subscriptionReferenceNumber"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(32) "subs_subscriptionReferenceNumber"
          }
          ["visible"]: bool(false)
        }
        [2]: array(6) {
          ["id"]: string(38) "col_2_51980cd41cd1fc05c08fa2bc5e8a4e30"
          ["label"]: string(14) "SubscriptionId"
          ["field"]: string(19) "subs_subscriptionId"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(19) "subs_subscriptionId"
          }
          ["visible"]: bool(true)
        }
        [3]: array(6) {
          ["id"]: string(38) "col_3_694e8d1f2ee056f98ee488bdc4982d73"
          ["label"]: string(8) "Quantity"
          ["field"]: string(13) "subs_quantity"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(13) "subs_quantity"
          }
          ["visible"]: bool(true)
        }
        [4]: array(6) {
          ["id"]: string(38) "col_4_ec53a8c4f07baed5d8825072c89799be"
          ["label"]: string(6) "Status"
          ["field"]: string(11) "subs_status"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(11) "subs_status"
          }
          ["visible"]: bool(true)
        }
        [5]: array(6) {
          ["id"]: string(38) "col_5_4b613a54fb8fdaba94406d746a16cf09"
          ["label"]: string(9) "StartDate"
          ["field"]: string(14) "subs_startDate"
          ["filter"]: bool(true)
          ["fields"]: array(2) {
            [0]: string(14) "subs_startDate"
            [1]: string(12) "subs_endDate"
          }
          ["visible"]: bool(true)
        }
        [6]: array(6) {
          ["id"]: string(38) "col_6_a7f44c44b37e090a40ad2d76aa86d08d"
          ["label"]: string(7) "EndDate"
          ["field"]: string(12) "subs_endDate"
          ["filter"]: bool(true)
          ["fields"]: array(0) {
          }
          ["visible"]: bool(true)
        }
        [7]: array(6) {
          ["id"]: string(38) "col_7_490aa6e856ccf208a054389e47ce0d06"
          ["label"]: string(2) "Id"
          ["field"]: string(10) "endcust_id"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(10) "endcust_id"
          }
          ["visible"]: bool(true)
        }
        [8]: array(6) {
          ["id"]: string(38) "col_8_a2edb050ddd149d2e0bd932557f38be3"
          ["label"]: string(11) "Account Csn"
          ["field"]: string(19) "endcust_account_csn"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(19) "endcust_account_csn"
          }
          ["visible"]: bool(true)
        }
        [9]: array(6) {
          ["id"]: string(38) "col_9_49ee3087348e8d44e1feda1917443987"
          ["label"]: string(4) "Name"
          ["field"]: string(12) "endcust_name"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(12) "endcust_name"
          }
          ["visible"]: bool(true)
        }
        [10]: array(6) {
          ["id"]: string(39) "col_10_bc910f8bdf70f29374f496f05be0330c"
          ["label"]: string(10) "First Name"
          ["field"]: string(18) "endcust_first_name"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(18) "endcust_first_name"
          }
          ["visible"]: bool(true)
        }
        [11]: array(6) {
          ["id"]: string(39) "col_11_77587239bf4c54ea493c7033e1dbf636"
          ["label"]: string(9) "Last Name"
          ["field"]: string(17) "endcust_last_name"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(17) "endcust_last_name"
          }
          ["visible"]: bool(true)
        }
        [12]: array(6) {
          ["id"]: string(39) "col_12_8184e89412e9d55fc20f501c898b327d"
          ["label"]: string(8) "Quote Id"
          ["field"]: string(18) "lastquote_quote_id"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(18) "lastquote_quote_id"
          }
          ["visible"]: bool(true)
        }
        [13]: array(6) {
          ["id"]: string(39) "col_13_a1a3d4145963c3c20655c504af782d58"
          ["label"]: string(12) "Quote Number"
          ["field"]: string(22) "lastquote_quote_number"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(22) "lastquote_quote_number"
          }
          ["visible"]: bool(true)
        }
        [14]: array(6) {
          ["id"]: string(39) "col_14_4edb4bac36117d2361f81056bf00423a"
          ["label"]: string(11) "Quoted Date"
          ["field"]: string(21) "lastquote_quoted_date"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(21) "lastquote_quoted_date"
          }
          ["visible"]: bool(true)
        }
      }
      ["updated_at"]: string(19) "2025-08-03 20:51:16"
      ["data_source_type"]: string(11) "data_source"
      ["data_source_id"]: int(30)
      ["hidden"]: array(2) {
        [0]: string(38) "col_1_15cc3fba1d13d18d588ca3693361fa38"
        [1]: string(38) "col_0_490aa6e856ccf208a054389e47ce0d06"
      }
    }
    ["config"]: array(5) {
      ["structure"]: array(15) {
        [0]: array(6) {
          ["id"]: string(38) "col_0_490aa6e856ccf208a054389e47ce0d06"
          ["label"]: string(2) "Id"
          ["field"]: string(7) "subs_id"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(7) "subs_id"
          }
          ["visible"]: bool(false)
        }
        [1]: array(6) {
          ["id"]: string(38) "col_1_15cc3fba1d13d18d588ca3693361fa38"
          ["label"]: string(27) "SubscriptionReferenceNumber"
          ["field"]: string(32) "subs_subscriptionReferenceNumber"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(32) "subs_subscriptionReferenceNumber"
          }
          ["visible"]: bool(false)
        }
        [2]: array(6) {
          ["id"]: string(38) "col_2_51980cd41cd1fc05c08fa2bc5e8a4e30"
          ["label"]: string(14) "SubscriptionId"
          ["field"]: string(19) "subs_subscriptionId"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(19) "subs_subscriptionId"
          }
          ["visible"]: bool(true)
        }
        [3]: array(6) {
          ["id"]: string(38) "col_3_694e8d1f2ee056f98ee488bdc4982d73"
          ["label"]: string(8) "Quantity"
          ["field"]: string(13) "subs_quantity"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(13) "subs_quantity"
          }
          ["visible"]: bool(true)
        }
        [4]: array(6) {
          ["id"]: string(38) "col_4_ec53a8c4f07baed5d8825072c89799be"
          ["label"]: string(6) "Status"
          ["field"]: string(11) "subs_status"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(11) "subs_status"
          }
          ["visible"]: bool(true)
        }
        [5]: array(6) {
          ["id"]: string(38) "col_5_4b613a54fb8fdaba94406d746a16cf09"
          ["label"]: string(9) "StartDate"
          ["field"]: string(14) "subs_startDate"
          ["filter"]: bool(true)
          ["fields"]: array(2) {
            [0]: string(14) "subs_startDate"
            [1]: string(12) "subs_endDate"
          }
          ["visible"]: bool(true)
        }
        [6]: array(6) {
          ["id"]: string(38) "col_6_a7f44c44b37e090a40ad2d76aa86d08d"
          ["label"]: string(7) "EndDate"
          ["field"]: string(12) "subs_endDate"
          ["filter"]: bool(true)
          ["fields"]: array(0) {
          }
          ["visible"]: bool(true)
        }
        [7]: array(6) {
          ["id"]: string(38) "col_7_490aa6e856ccf208a054389e47ce0d06"
          ["label"]: string(2) "Id"
          ["field"]: string(10) "endcust_id"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(10) "endcust_id"
          }
          ["visible"]: bool(true)
        }
        [8]: array(6) {
          ["id"]: string(38) "col_8_a2edb050ddd149d2e0bd932557f38be3"
          ["label"]: string(11) "Account Csn"
          ["field"]: string(19) "endcust_account_csn"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(19) "endcust_account_csn"
          }
          ["visible"]: bool(true)
        }
        [9]: array(6) {
          ["id"]: string(38) "col_9_49ee3087348e8d44e1feda1917443987"
          ["label"]: string(4) "Name"
          ["field"]: string(12) "endcust_name"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(12) "endcust_name"
          }
          ["visible"]: bool(true)
        }
        [10]: array(6) {
          ["id"]: string(39) "col_10_bc910f8bdf70f29374f496f05be0330c"
          ["label"]: string(10) "First Name"
          ["field"]: string(18) "endcust_first_name"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(18) "endcust_first_name"
          }
          ["visible"]: bool(true)
        }
        [11]: array(6) {
          ["id"]: string(39) "col_11_77587239bf4c54ea493c7033e1dbf636"
          ["label"]: string(9) "Last Name"
          ["field"]: string(17) "endcust_last_name"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(17) "endcust_last_name"
          }
          ["visible"]: bool(true)
        }
        [12]: array(6) {
          ["id"]: string(39) "col_12_8184e89412e9d55fc20f501c898b327d"
          ["label"]: string(8) "Quote Id"
          ["field"]: string(18) "lastquote_quote_id"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(18) "lastquote_quote_id"
          }
          ["visible"]: bool(true)
        }
        [13]: array(6) {
          ["id"]: string(39) "col_13_a1a3d4145963c3c20655c504af782d58"
          ["label"]: string(12) "Quote Number"
          ["field"]: string(22) "lastquote_quote_number"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(22) "lastquote_quote_number"
          }
          ["visible"]: bool(true)
        }
        [14]: array(6) {
          ["id"]: string(39) "col_14_4edb4bac36117d2361f81056bf00423a"
          ["label"]: string(11) "Quoted Date"
          ["field"]: string(21) "lastquote_quoted_date"
          ["filter"]: bool(true)
          ["fields"]: array(1) {
            [0]: string(21) "lastquote_quoted_date"
          }
          ["visible"]: bool(true)
        }
      }
      ["updated_at"]: string(19) "2025-08-03 20:51:16"
      ["data_source_type"]: string(11) "data_source"
      ["data_source_id"]: int(30)
      ["hidden"]: array(2) {
        [0]: string(38) "col_1_15cc3fba1d13d18d588ca3693361fa38"
        [1]: string(38) "col_0_490aa6e856ccf208a054389e47ce0d06"
      }
    }
    ["columns_count"]: int(16)
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 138
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 131
         <strong>Arguments:</strong>
         0: {"table_name":"autodesk_subscriptions","criteria":[],"column_preferences":{"structure":[{"id":"col_0...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/data-table.edge.php"
      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_table/column_preferences.api.php, Line: 81
         <strong>Arguments:</strong>
         0: "data-table"
         1: {"table_name":"autodesk_subscriptions","criteria":[],"column_preferences":{"structure":[{"id":"col_0...

----------------------------------------------------------------------------
-->