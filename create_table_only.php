<?php
/**
 * Simple script to create the autobooks_data_table_storage table
 * This runs before the full migration to ensure the table exists
 */

// Include basic database configuration
require_once 'system/config.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Create Data Table Storage Table</title></head><body>";
echo "<h1>Create Data Table Storage Table</h1>";

echo "<pre>";

// Create the table
echo "Creating autobooks_data_table_storage table...\n";

$create_table_sql = "
CREATE TABLE IF NOT EXISTS `autobooks_data_table_storage` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `table_name` varchar(255) NOT NULL,
    `user_id` int(11) DEFAULT NULL,
    `configuration` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`configuration`)),
    `data_source` varchar(255) DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_table_user` (`table_name`, `user_id`),
    KEY `idx_table_name` (`table_name`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_data_source` (`data_source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
";

try {
    $result = tep_db_query($create_table_sql);
    
    if ($result) {
        echo "✓ Table created successfully\n";
        
        // Test basic functionality
        echo "\nTesting basic database operations...\n";
        
        // Test insert
        $test_insert = "INSERT INTO autobooks_data_table_storage (table_name, user_id, configuration, data_source) 
                       VALUES ('test_table', NULL, '{\"test\": true}', 'test_source')";
        $insert_result = tep_db_query($test_insert);
        
        if ($insert_result) {
            echo "✓ Insert test passed\n";
            
            // Test select
            $test_select = "SELECT * FROM autobooks_data_table_storage WHERE table_name = 'test_table'";
            $select_result = tep_db_query($test_select);
            
            if ($select_result && tep_db_num_rows($select_result) > 0) {
                echo "✓ Select test passed\n";
                
                // Clean up test data
                $cleanup = "DELETE FROM autobooks_data_table_storage WHERE table_name = 'test_table'";
                tep_db_query($cleanup);
                echo "✓ Cleanup completed\n";
            } else {
                echo "✗ Select test failed\n";
            }
        } else {
            echo "✗ Insert test failed\n";
        }
        
    } else {
        echo "✗ Failed to create table\n";
        echo "Error: " . tep_db_error() . "\n";
    }
    
} catch (Exception $e) {
    echo "✗ Exception occurred: " . $e->getMessage() . "\n";
}

echo "</pre>";

echo "<hr>";
echo "<h2>Next Steps</h2>";
echo "<ul>";
echo "<li>Table creation completed</li>";
echo "<li>You can now run the full migration: <a href='run_migration.php'>run_migration.php</a></li>";
echo "<li>Or test the system directly: <a href='test_database_storage.php'>test_database_storage.php</a></li>";
echo "</ul>";

echo "</body></html>";
?>
