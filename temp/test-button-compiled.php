<?php namespace edgeTemplate\test_button;use edge\Edge;?><?php extract(['edge_manifest' => array (
)]);; ?>
<?php $component_props = array_merge([
    'type' => 'button',
    'variant' => 'primary',
    'size' => 'md',
    'icon' => null,
    'iconPosition' => 'left',
    'fullWidth' => false,
    'disabled' => false,
    'class' => '',
    'hxPost' => null,
    'hxTarget' => null,
    'hxSwap' => null,
    'hxVals' => null,
    'onClick' => null,
    'atClick' => null
], ["variant" => "secondary", 'hxPost' => APP_ROOT . '/test', "hxSwap" => "innerHTML", "hxTarget" => "#modal", "icon" => "email"]); extract($component_props, EXTR_SKIP); ?>

<?php
// Define icon paths
$iconPaths = [
    'email' => 'M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z',
    'note' => 'M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21a2 2 0 012 2v11a2 2 0 01-2 2H3z',
    'quote' => 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
    'print' => 'M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z',
    'copy' => 'M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z'
];

$iconPath = isset($iconPaths[$icon]) ? $iconPaths[$icon] : $icon;
?>

<?php
    $baseClasses = 'inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-150';

    $sizeClasses = [
        'xs' => 'px-2 py-1 text-xs',
        'sm' => 'px-3 py-2 text-sm leading-4',
        'md' => 'px-4 py-2 text-sm',
        'lg' => 'px-6 py-3 text-base'
    ];

    $variantClasses = [
        'primary' => 'text-white bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500 border border-transparent',
        'secondary' => 'text-gray-700 bg-white hover:bg-gray-50 focus:ring-indigo-500 border border-gray-300',
        'danger' => 'text-white bg-red-600 hover:bg-red-700 focus:ring-red-500 border border-transparent',
        'success' => 'text-white bg-green-600 hover:bg-green-700 focus:ring-green-500 border border-transparent'
    ];

    $classes = $baseClasses . ' ' . ($sizeClasses[$size] ?? $sizeClasses['md']) . ' ' . ($variantClasses[$variant] ?? $variantClasses['primary']);

    if ($fullWidth) {
        $classes .= ' w-full';
    }

    if ($disabled) {
        $classes .= ' opacity-50 cursor-not-allowed';
    }

    $classes .= ' ' . $class;

    $attributes = [];
    if ($hxPost) $attributes['hx-post'] = $hxPost;
    if ($hxTarget) $attributes['hx-target'] = $hxTarget;
    if ($hxSwap) $attributes['hx-swap'] = $hxSwap;
    if ($hxVals) $attributes['hx-vals'] = $hxVals;
    if ($onClick) $attributes['onclick'] = $onClick;
    if ($atClick) $attributes['@click'] = $atClick;
    if ($disabled) $attributes['disabled'] = 'disabled';
?>

<button type="<?= $type ?>" class="<?= $classes ?>" <?php foreach($attributes as $key => $value): ?><?php if($key === 'hx-vals'): ?><?= $key ?>='<?= $value ?>' <?php else: ?><?= $key ?>="<?= htmlspecialchars($value) ?>" <?php endif; ?><?php endforeach; ?>>
    <?php if($icon && $iconPosition === 'left'): ?>
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?= $iconPath ?>"></path>
        </svg>
    <?php endif; ?>

    <?= $tag_content ?? '' ?>

    <?php if($icon && $iconPosition === 'right'): ?>
        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?= $iconPath ?>"></path>
        </svg>
    <?php endif; ?>
</button>

