<?php
// Final fix for data sources API routing
const DS = DIRECTORY_SEPARATOR;
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . DS . 'system'. DS . 'config'. DS . 'path_schema.php';
$ds = DS;
$path['fs_system'] = "{$path['fs_app_root']}{$ds}{$schema['system']['root']}" ;

$input_params = array_merge($_GET, $_POST);
define('INPUT_PARAMS',$input_params);
const DEBUG_MODE = true;
const API_RUN = false;

require_once $path['fs_system'] . DS . 'classes'. DS . 'startup_sequence.class.php';
require_once $path['fs_system'] . DS . 'functions'. DS . 'functions.php';

startup_sequence::start($path,$schema);

use system\database;

echo "<h1>Final Data Sources API Fix</h1>";

try {
    // Step 1: Ensure navigation entry is correct
    echo "<h2>Step 1: Navigation Entry</h2>";
    
    $nav = database::table('autobooks_navigation')
        ->where('route_key', 'data_sources')
        ->where('parent_path', 'root')
        ->first();
    
    if ($nav) {
        echo "<p>Current navigation entry:</p>";
        echo "<ul>";
        echo "<li>file_path: " . $nav['file_path'] . "</li>";
        echo "<li>is_system: " . ($nav['is_system'] ? 'true' : 'false') . "</li>";
        echo "</ul>";
        
        // Update to ensure correct settings
        database::table('autobooks_navigation')
            ->where('id', $nav['id'])
            ->update([
                'file_path' => 'data_sources',
                'is_system' => 1,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        
        echo "<p style='color: green;'>✓ Navigation entry updated</p>";
    } else {
        echo "<p style='color: red;'>Navigation entry not found - creating it...</p>";
        
        database::table('autobooks_navigation')->insert([
            'parent_path' => 'root',
            'route_key' => 'data_sources',
            'name' => 'Data Sources',
            'icon' => 'database',
            'file_path' => 'data_sources',
            'required_roles' => '[]',
            'sort_order' => 15,
            'show_navbar' => 1,
            'can_delete' => 0,
            'is_system' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
        echo "<p style='color: green;'>✓ Navigation entry created</p>";
    }
    
    // Step 2: Verify API file exists
    echo "<h2>Step 2: API File Check</h2>";
    
    $api_file = 'system/api/data_sources.api.php';
    if (file_exists($api_file)) {
        echo "<p style='color: green;'>✓ API file exists: $api_file</p>";
        
        // Check if the view functions exist in the API file
        $api_content = file_get_contents($api_file);
        $functions = ['create', 'edit', 'preview'];
        
        foreach ($functions as $func) {
            if (strpos($api_content, "function $func(") !== false) {
                echo "<p style='color: green;'>✓ API function '$func' found</p>";
            } else {
                echo "<p style='color: red;'>✗ API function '$func' missing</p>";
            }
        }
    } else {
        echo "<p style='color: red;'>✗ API file missing: $api_file</p>";
    }
    
    // Step 3: Verify view files exist
    echo "<h2>Step 3: View Files Check</h2>";
    
    $view_files = [
        'system/views/data_sources/data_sources.edge.php' => 'Main index view',
        'system/views/data_sources/create.edge.php' => 'Create view',
        'system/views/data_sources/edit.edge.php' => 'Edit view',
        'system/views/data_sources/preview.edge.php' => 'Preview view'
    ];
    
    foreach ($view_files as $file => $description) {
        if (file_exists($file)) {
            echo "<p style='color: green;'>✓ $description: $file</p>";
        } else {
            echo "<p style='color: red;'>✗ $description missing: $file</p>";
        }
    }
    
    // Step 4: Test API endpoints
    echo "<h2>Step 4: API Endpoint Test</h2>";
    
    echo "<p>Testing API endpoints:</p>";
    echo "<ul>";
    echo "<li><a href='api/data_sources/get_data_sources' target='_blank'>GET /api/data_sources/get_data_sources</a></li>";
    echo "<li><a href='api/data_sources/get_tables' target='_blank'>GET /api/data_sources/get_tables</a></li>";
    echo "<li><a href='api/data_sources/create' target='_blank'>GET /api/data_sources/create (view)</a></li>";
    echo "</ul>";
    
    echo "<h2 style='color: green;'>Setup Complete!</h2>";
    echo "<p>The data sources system should now work with proper API routing:</p>";
    echo "<ul>";
    echo "<li><strong>Main page:</strong> <a href='data_sources'>Data Sources</a></li>";
    echo "<li><strong>API endpoints:</strong> All buttons now use HTMX with API calls</li>";
    echo "<li><strong>Create:</strong> Uses /api/data_sources/create</li>";
    echo "<li><strong>Edit:</strong> Uses /api/data_sources/edit with ID parameter</li>";
    echo "<li><strong>Preview:</strong> Uses /api/data_sources/preview with ID parameter</li>";
    echo "<li><strong>Delete:</strong> Uses /api/data_sources/delete with AJAX</li>";
    echo "</ul>";
    
    echo "<h3>How it works now:</h3>";
    echo "<ol>";
    echo "<li>Main page loads via normal routing: /data_sources → system/views/data_sources/data_sources.edge.php</li>";
    echo "<li>Action buttons use HTMX to call API endpoints: /api/data_sources/[function]</li>";
    echo "<li>API functions return rendered HTML views that replace the page content</li>";
    echo "<li>Form submissions use AJAX to API endpoints and redirect on success</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
