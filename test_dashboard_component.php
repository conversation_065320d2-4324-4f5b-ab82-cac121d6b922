<?php
// Test the dashboard-stats component rendering
require_once 'system/classes/edge/edge.class.php';

echo "Testing dashboard-stats component...\n\n";

try {
    // Test loading state
    echo "1. Testing loading state:\n";
    $loading_output = edge::render('dashboard-stats', [
        'show_loading' => true
    ]);
    echo "✓ Loading state rendered successfully\n";
    echo "Length: " . strlen($loading_output) . " characters\n\n";
    
    // Test with data
    echo "2. Testing with data:\n";
    $data_output = edge::render('dashboard-stats', [
        'total_count' => 150,
        'expiring_count' => 25,
        'matched_count' => 10,
        'manual_count' => 5,
        'show_loading' => false
    ]);
    echo "✓ Data state rendered successfully\n";
    echo "Length: " . strlen($data_output) . " characters\n\n";
    
    // Test error state
    echo "3. Testing error state:\n";
    $error_output = edge::render('dashboard-stats', [
        'total_count' => 'Error',
        'expiring_count' => 'Error',
        'matched_count' => 'Error',
        'manual_count' => 'Error',
        'show_loading' => false
    ]);
    echo "✓ Error state rendered successfully\n";
    echo "Length: " . strlen($error_output) . " characters\n\n";
    
    echo "All component tests passed!\n";
    
} catch (Exception $e) {
    echo "✗ FAILED: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
