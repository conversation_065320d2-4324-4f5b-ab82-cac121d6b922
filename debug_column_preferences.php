<?php
require_once 'system/autoload.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Test script to debug column preferences issue

function debug_column_preferences($table_name) {
    echo "<h2>Debugging Column Preferences for: $table_name</h2>";
    
    // Get current user ID
    $user_id = \data_table_storage::get_current_user_id();
    echo "<p>User ID: " . ($user_id ?? 'null') . "</p>";
    
    // Get configuration from database
    $config = \data_table_storage::get_configuration($table_name, $user_id);
    
    if (!$config) {
        echo "<p>No configuration found in database</p>";
        return;
    }
    
    echo "<h3>Raw Database Configuration:</h3>";
    echo "<pre>" . print_r($config, true) . "</pre>";
    
    $configuration = $config['configuration'];
    
    echo "<h3>Parsed Configuration:</h3>";
    echo "<pre>" . print_r($configuration, true) . "</pre>";
    
    // Check structure vs hidden consistency
    $structure = $configuration['structure'] ?? [];
    $hidden = $configuration['hidden'] ?? [];
    
    echo "<h3>Structure Analysis:</h3>";
    echo "<p>Structure count: " . count($structure) . "</p>";
    echo "<p>Hidden count: " . count($hidden) . "</p>";
    
    echo "<h4>Structure Details:</h4>";
    foreach ($structure as $index => $column) {
        $is_in_hidden = in_array($column['id'], $hidden);
        $visible_property = $column['visible'] ?? 'not set';
        
        echo "<div style='border: 1px solid #ccc; margin: 5px; padding: 10px;'>";
        echo "<strong>Column {$index}:</strong><br>";
        echo "ID: {$column['id']}<br>";
        echo "Label: {$column['label']}<br>";
        echo "Field: " . (is_array($column['field']) ? implode(', ', $column['field']) : $column['field']) . "<br>";
        echo "Visible property: " . ($visible_property === true ? 'true' : ($visible_property === false ? 'false' : $visible_property)) . "<br>";
        echo "In hidden array: " . ($is_in_hidden ? 'YES' : 'NO') . "<br>";
        
        // Check for inconsistency
        if ($visible_property === true && $is_in_hidden) {
            echo "<span style='color: red;'>INCONSISTENCY: visible=true but in hidden array</span><br>";
        } elseif ($visible_property === false && !$is_in_hidden) {
            echo "<span style='color: red;'>INCONSISTENCY: visible=false but not in hidden array</span><br>";
        } else {
            echo "<span style='color: green;'>CONSISTENT</span><br>";
        }
        echo "</div>";
    }
    
    echo "<h4>Hidden Array:</h4>";
    echo "<pre>" . print_r($hidden, true) . "</pre>";
    
    // Test column ID generation
    echo "<h3>Column ID Generation Test:</h3>";
    foreach ($structure as $index => $column) {
        $generated_id = 'col_' . $index . '_' . md5($column['label']);
        $matches = $generated_id === $column['id'];
        
        echo "<div>";
        echo "Index: $index, Label: {$column['label']}<br>";
        echo "Stored ID: {$column['id']}<br>";
        echo "Generated ID: $generated_id<br>";
        echo "Match: " . ($matches ? 'YES' : 'NO') . "<br>";
        if (!$matches) {
            echo "<span style='color: red;'>ID MISMATCH!</span><br>";
        }
        echo "</div><br>";
    }
}

// Test with the table name from your issue
if (isset($_GET['table_name'])) {
    debug_column_preferences($_GET['table_name']);
} else {
    echo "<p>Add ?table_name=your_table_name to the URL to debug a specific table</p>";
    
    // List all configurations
    $user_id = \data_table_storage::get_current_user_id();
    $configs = \data_table_storage::list_configurations($user_id);
    
    echo "<h3>Available Configurations:</h3>";
    foreach ($configs as $config) {
        echo "<a href='?table_name=" . urlencode($config['table_name']) . "'>{$config['table_name']}</a><br>";
    }
}
?>
