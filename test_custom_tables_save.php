<?php
/**
 * Test custom tables save functionality
 */

// Include the necessary files
require_once 'system/classes/startup_sequence.class.php';
require_once 'system/functions/functions.php';

// Initialize the system
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . '/system/config/path_schema.php';
$path['fs_system'] = $path['fs_app_root'] . '/' . $schema['system']['root'];
startup_sequence::start($path, $schema);

use system\data_source_manager;

// Include the API functions
require_once 'system/api/data_sources.api.php';

echo "<h1>Test Custom Tables Save Functionality</h1>";

try {
    // Test creating a data source with custom tables
    $test_params = [
        'name' => 'Test Custom Tables Data Source',
        'description' => 'Testing custom tables save functionality',
        'category' => 'other',
        'selected_tables' => '["autodesk_subscriptions"]',
        'table_aliases' => ['autodesk_subscriptions' => 'subs'],
        'custom_tables' => [
            [
                'alias' => 'lastquote',
                'join_type' => 'LEFT JOIN',
                'sql' => 'SELECT qi.subscription_id, q.id AS quote_id, q.quote_status FROM autodesk_quote_line_items qi JOIN autodesk_quotes q ON q.id = qi.quote_id WHERE qi.subscription_id IS NOT NULL ORDER BY q.quoted_date DESC LIMIT 1',
                'join_condition' => 'subs.subscriptionId = lastquote.subscription_id',
                'columns' => 'subscription_id, quote_id, quote_status',
                'description' => 'Latest quote for each subscription'
            ]
        ],
        'selected_columns' => [],
        'joins' => [],
        'filters' => [],
        'sorting' => [],
        'grouping' => [],
        'limits' => ['enabled' => false]
    ];
    
    echo "<h2>1. Testing Create with Custom Tables:</h2>";
    echo "<h3>Input Data:</h3>";
    echo "<pre>" . json_encode($test_params['custom_tables'], JSON_PRETTY_PRINT) . "</pre>";
    
    // Create the data source
    $new_id = \api\data_sources\create_data_source($test_params);
    
    if (is_string($new_id) && strpos($new_id, 'Success') !== false) {
        echo "<p style='color: green;'>✓ Create function returned success HTML</p>";
        
        // Try to find the created data source
        $all_sources = data_source_manager::get_data_sources();
        $created_source = null;
        foreach ($all_sources as $source) {
            if ($source['name'] === 'Test Custom Tables Data Source') {
                $created_source = $source;
                break;
            }
        }
        
        if ($created_source) {
            echo "<p style='color: green;'>✓ Data source found in database with ID: {$created_source['id']}</p>";
            
            // Check if custom_tables were saved
            if (!empty($created_source['custom_tables'])) {
                echo "<p style='color: green;'>✓ Custom tables were saved!</p>";
                echo "<h4>Saved Custom Tables:</h4>";
                echo "<pre>" . json_encode($created_source['custom_tables'], JSON_PRETTY_PRINT) . "</pre>";
                
                // Test updating the data source
                echo "<h2>2. Testing Update with Modified Custom Tables:</h2>";
                
                $update_params = [
                    'id' => $created_source['id'],
                    'name' => 'Updated Test Custom Tables Data Source',
                    'description' => 'Updated description',
                    'category' => 'other',
                    'selected_tables' => '["autodesk_subscriptions"]',
                    'table_aliases' => ['autodesk_subscriptions' => 'subs'],
                    'custom_tables' => [
                        [
                            'alias' => 'lastquote',
                            'join_type' => 'LEFT JOIN',
                            'sql' => 'SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number FROM autodesk_quote_line_items qi JOIN autodesk_quotes q ON q.id = qi.quote_id WHERE qi.subscription_id IS NOT NULL ORDER BY q.quoted_date DESC LIMIT 1',
                            'join_condition' => 'subs.subscriptionId = lastquote.subscription_id',
                            'columns' => 'subscription_id, quote_id, quote_status, quote_number',
                            'description' => 'Latest quote for each subscription (updated)'
                        ],
                        [
                            'alias' => 'firstquote',
                            'join_type' => 'LEFT JOIN',
                            'sql' => 'SELECT qi.subscription_id, q.id AS quote_id FROM autodesk_quote_line_items qi JOIN autodesk_quotes q ON q.id = qi.quote_id WHERE qi.subscription_id IS NOT NULL ORDER BY q.quoted_date ASC LIMIT 1',
                            'join_condition' => 'subs.subscriptionId = firstquote.subscription_id',
                            'columns' => 'subscription_id, quote_id',
                            'description' => 'First quote for each subscription'
                        ]
                    ],
                    'selected_columns' => [],
                    'joins' => [],
                    'filters' => [],
                    'sorting' => [],
                    'grouping' => [],
                    'limits' => ['enabled' => false]
                ];
                
                $update_result = \api\data_sources\update($update_params);
                
                if (is_string($update_result) && strpos($update_result, 'Success') !== false) {
                    echo "<p style='color: green;'>✓ Update function returned success HTML</p>";
                    
                    // Retrieve the updated data source
                    $updated_source = data_source_manager::get_data_source($created_source['id']);
                    
                    if ($updated_source && !empty($updated_source['custom_tables'])) {
                        echo "<p style='color: green;'>✓ Custom tables were updated and saved!</p>";
                        echo "<h4>Updated Custom Tables:</h4>";
                        echo "<pre>" . json_encode($updated_source['custom_tables'], JSON_PRETTY_PRINT) . "</pre>";
                        
                        if (count($updated_source['custom_tables']) === 2) {
                            echo "<p style='color: green;'>✓ Both custom tables are present</p>";
                        } else {
                            echo "<p style='color: red;'>✗ Expected 2 custom tables, found " . count($updated_source['custom_tables']) . "</p>";
                        }
                    } else {
                        echo "<p style='color: red;'>✗ Custom tables were not saved in update</p>";
                    }
                } else {
                    echo "<p style='color: red;'>✗ Update function failed</p>";
                    echo "<pre>" . htmlspecialchars(substr($update_result, 0, 200)) . "</pre>";
                }
                
            } else {
                echo "<p style='color: red;'>✗ Custom tables were not saved</p>";
            }
            
            // Clean up - delete the test data source
            $db = \system\database::table('autobooks_data_sources');
            $db->where('id', $created_source['id'])->delete();
            echo "<p style='color: blue;'>✓ Test data source cleaned up</p>";
            
        } else {
            echo "<p style='color: red;'>✗ Data source not found in database</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Create function failed</p>";
        echo "<pre>" . htmlspecialchars(substr($new_id, 0, 200)) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='data_sources'>← Back to Data Sources</a></p>";
?>
