<?php
/**
 * Migrate Autodesk Data Sources (PHP Version)
 * 
 * This script creates Autodesk data sources using PHP instead of complex SQL
 */

require_once 'system/startup_sequence_minimal.php';

use system\database;

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<!DOCTYPE html>";
echo "<html><head><title>Autodesk Data Sources Migration (PHP)</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css' rel='stylesheet'>";
echo "</head><body class='bg-gray-100'>";

echo "<div class='container mx-auto py-8'>";
echo "<h1 class='text-3xl font-bold mb-8'>Autodesk Data Sources Migration (PHP)</h1>";

// Check if migration should be run
if (isset($_POST['run_migration'])) {
    echo "<div class='bg-white rounded-lg shadow p-6 mb-8'>";
    echo "<h2 class='text-xl font-semibold mb-4'>Running Migration...</h2>";
    echo "<div class='space-y-2'>";
    
    $created = 0;
    $errors = 0;
    
    // Define data sources
    $data_sources = [
        [
            'name' => 'Autodesk Subscriptions',
            'description' => 'Complete subscription data with account relationships and calculated fields',
            'category' => 'autodesk',
            'table_name' => 'autodesk_subscriptions',
            'joins' => [
                [
                    'type' => 'LEFT JOIN',
                    'table' => 'autodesk_accounts',
                    'alias' => 'soldto',
                    'condition' => 'autodesk_subscriptions.soldTo_id = soldto.id'
                ],
                [
                    'type' => 'LEFT JOIN',
                    'table' => 'autodesk_accounts',
                    'alias' => 'endcust',
                    'condition' => 'autodesk_subscriptions.endCustomer_id = endcust.id'
                ]
            ],
            'filters' => [
                [
                    'field' => 'autodesk_subscriptions.status',
                    'operator' => 'IN',
                    'values' => ['Active', 'Expired', 'Cancelled'],
                    'label' => 'Subscription Status'
                ],
                [
                    'field' => 'soldto.name',
                    'operator' => 'LIKE',
                    'label' => 'Customer Name'
                ]
            ],
            'columns' => [
                ['field' => 'autodesk_subscriptions.subscriptionId', 'alias' => 'subscription_id', 'label' => 'Subscription ID'],
                ['field' => 'autodesk_subscriptions.status', 'alias' => 'status', 'label' => 'Status'],
                ['field' => 'autodesk_subscriptions.offeringName', 'alias' => 'product_name', 'label' => 'Product'],
                ['field' => 'soldto.name', 'alias' => 'customer_name', 'label' => 'Customer'],
                ['field' => 'autodesk_subscriptions.endDate', 'alias' => 'end_date', 'label' => 'End Date'],
                ['field' => 'DATEDIFF(autodesk_subscriptions.endDate, NOW())', 'alias' => 'days_to_expiry', 'label' => 'Days to Expiry']
            ]
        ],
        [
            'name' => 'Autodesk Accounts',
            'description' => 'Customer account information with contact details and preferences',
            'category' => 'autodesk',
            'table_name' => 'autodesk_accounts',
            'joins' => [],
            'filters' => [
                [
                    'field' => 'name',
                    'operator' => 'LIKE',
                    'label' => 'Company Name'
                ],
                [
                    'field' => 'country',
                    'operator' => '=',
                    'label' => 'Country'
                ]
            ],
            'columns' => [
                ['field' => 'id', 'alias' => 'account_id', 'label' => 'Account ID'],
                ['field' => 'name', 'alias' => 'company_name', 'label' => 'Company Name'],
                ['field' => 'email', 'alias' => 'email', 'label' => 'Email'],
                ['field' => 'phone', 'alias' => 'phone', 'label' => 'Phone'],
                ['field' => 'country', 'alias' => 'country', 'label' => 'Country']
            ]
        ],
        [
            'name' => 'Expiring Subscriptions',
            'description' => 'Subscriptions expiring within the next 90 days',
            'category' => 'autodesk',
            'table_name' => 'autodesk_subscriptions',
            'joins' => [
                [
                    'type' => 'LEFT JOIN',
                    'table' => 'autodesk_accounts',
                    'alias' => 'soldto',
                    'condition' => 'autodesk_subscriptions.soldTo_id = soldto.id'
                ]
            ],
            'filters' => [
                [
                    'field' => 'autodesk_subscriptions.endDate',
                    'operator' => 'BETWEEN',
                    'values' => ['NOW()', 'DATE_ADD(NOW(), INTERVAL 90 DAY)'],
                    'label' => 'Expiring Within 90 Days',
                    'default' => true
                ],
                [
                    'field' => 'autodesk_subscriptions.status',
                    'operator' => '=',
                    'values' => ['Active'],
                    'label' => 'Active Only',
                    'default' => true
                ]
            ],
            'columns' => [
                ['field' => 'autodesk_subscriptions.subscriptionId', 'alias' => 'subscription_id', 'label' => 'Subscription ID'],
                ['field' => 'soldto.name', 'alias' => 'customer_name', 'label' => 'Customer'],
                ['field' => 'soldto.email', 'alias' => 'customer_email', 'label' => 'Email'],
                ['field' => 'autodesk_subscriptions.offeringName', 'alias' => 'product_name', 'label' => 'Product'],
                ['field' => 'autodesk_subscriptions.endDate', 'alias' => 'expiry_date', 'label' => 'Expiry Date'],
                ['field' => 'DATEDIFF(autodesk_subscriptions.endDate, NOW())', 'alias' => 'days_remaining', 'label' => 'Days Remaining']
            ]
        ],
        [
            'name' => 'Autodesk Email History',
            'description' => 'Email communication history with subscriptions',
            'category' => 'autodesk',
            'table_name' => 'autodesk_email_history',
            'joins' => [
                [
                    'type' => 'LEFT JOIN',
                    'table' => 'autodesk_subscriptions',
                    'alias' => 'sub',
                    'condition' => 'autodesk_email_history.subscription_id = sub.id'
                ]
            ],
            'filters' => [
                [
                    'field' => 'autodesk_email_history.email_type',
                    'operator' => 'IN',
                    'values' => ['renewal', 'expiry', 'welcome'],
                    'label' => 'Email Type'
                ]
            ],
            'columns' => [
                ['field' => 'autodesk_email_history.id', 'alias' => 'email_id', 'label' => 'Email ID'],
                ['field' => 'autodesk_email_history.email_type', 'alias' => 'email_type', 'label' => 'Type'],
                ['field' => 'autodesk_email_history.recipient_email', 'alias' => 'recipient', 'label' => 'Recipient'],
                ['field' => 'sub.subscriptionId', 'alias' => 'subscription_id', 'label' => 'Subscription'],
                ['field' => 'autodesk_email_history.sent_date', 'alias' => 'sent_date', 'label' => 'Sent Date'],
                ['field' => 'autodesk_email_history.status', 'alias' => 'status', 'label' => 'Status']
            ]
        ]
    ];
    
    // Create each data source
    foreach ($data_sources as $source) {
        try {
            // Check if data source already exists
            $existing = database::table('autobooks_data_sources')
                ->where('name', $source['name'])
                ->where('category', 'autodesk')
                ->first();
            
            if ($existing) {
                echo "<p class='text-yellow-600'>⚠ Data source '{$source['name']}' already exists, skipping</p>";
                continue;
            }
            
            // Insert data source
            $result = database::table('autobooks_data_sources')->insert([
                'name' => $source['name'],
                'description' => $source['description'],
                'category' => $source['category'],
                'table_name' => $source['table_name'],
                'joins' => json_encode($source['joins']),
                'filters' => json_encode($source['filters']),
                'selected_columns' => json_encode($source['columns']),
                'status' => 'active',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            if ($result) {
                echo "<p class='text-green-600'>✓ Created data source: {$source['name']}</p>";
                $created++;
            } else {
                echo "<p class='text-red-600'>✗ Failed to create: {$source['name']}</p>";
                $errors++;
            }
            
        } catch (Exception $e) {
            echo "<p class='text-red-600'>✗ Error creating {$source['name']}: " . htmlspecialchars($e->getMessage()) . "</p>";
            $errors++;
        }
    }
    
    echo "<div class='mt-4 p-4 bg-gray-50 rounded'>";
    echo "<h3 class='font-semibold'>Migration Summary</h3>";
    echo "<p>Data sources created: $created</p>";
    echo "<p>Errors: $errors</p>";
    
    if ($errors === 0) {
        echo "<p class='text-green-600 font-semibold'>✓ Migration completed successfully!</p>";
    } else {
        echo "<p class='text-yellow-600 font-semibold'>⚠ Migration completed with some errors</p>";
    }
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
}

// Show current data sources
echo "<div class='bg-white rounded-lg shadow p-6 mb-8'>";
echo "<h2 class='text-xl font-semibold mb-4'>Current Autodesk Data Sources</h2>";

try {
    $data_sources = database::table('autobooks_data_sources')
        ->where('category', 'autodesk')
        ->orderBy('name')
        ->get();
    
    if (!empty($data_sources)) {
        echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";
        
        foreach ($data_sources as $source) {
            echo "<div class='border border-gray-200 rounded-lg p-4'>";
            echo "<h3 class='font-medium text-gray-900'>" . htmlspecialchars($source['name']) . "</h3>";
            echo "<p class='text-sm text-gray-600 mt-1'>" . htmlspecialchars($source['description']) . "</p>";
            echo "<div class='mt-2 flex items-center justify-between'>";
            echo "<span class='text-xs text-gray-500'>Table: " . htmlspecialchars($source['table_name']) . "</span>";
            echo "<span class='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium " . 
                 ($source['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800') . "'>";
            echo htmlspecialchars($source['status']);
            echo "</span>";
            echo "</div>";
            echo "</div>";
        }
        
        echo "</div>";
        echo "<p class='mt-4 text-sm text-gray-600'>Found " . count($data_sources) . " Autodesk data sources.</p>";
    } else {
        echo "<p class='text-gray-500'>No Autodesk data sources found.</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='text-red-600'>Error loading data sources: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Migration controls
if (empty($data_sources)) {
    echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8'>";
    echo "<h2 class='text-lg font-semibold text-blue-900 mb-3'>Run Migration</h2>";
    echo "<p class='text-blue-800 mb-4'>No Autodesk data sources found. Run the migration to create them.</p>";
    echo "<form method='post'>";
    echo "<button type='submit' name='run_migration' value='1' class='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700'>";
    echo "Run PHP Migration";
    echo "</button>";
    echo "</form>";
    echo "</div>";
} else {
    echo "<div class='bg-green-50 border border-green-200 rounded-lg p-6 mb-8'>";
    echo "<h2 class='text-lg font-semibold text-green-900 mb-3'>Migration Complete</h2>";
    echo "<p class='text-green-800 mb-4'>Autodesk data sources have been created successfully.</p>";
    echo "<div class='space-y-2'>";
    echo "<a href='test_integrated_data_source_selector.php' class='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 mr-2'>";
    echo "Test Data Source Selector";
    echo "</a>";
    echo "<a href='" . APP_ROOT . "/system/data_sources' class='inline-flex items-center px-4 py-2 border border-green-600 text-sm font-medium rounded-md text-green-600 bg-white hover:bg-green-50'>";
    echo "Manage Data Sources";
    echo "</a>";
    echo "</div>";
    echo "</div>";
}

echo "</div>"; // container
echo "</body></html>";
?>
