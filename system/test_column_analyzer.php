<?php

/**
 * Test Script for Column Analyzer
 * 
 * This script demonstrates the intelligent column naming system
 * by analyzing various tables in the autobooks database.
 */

// Include necessary files
require_once(__DIR__ . '/../config/path_definitions.php');
require_once(FS_SYSTEM . DS . 'classes' . DS . 'database.class.php');
require_once(FS_SYSTEM . DS . 'classes' . DS . 'column_analyzer.class.php');
require_once(FS_SYSTEM . DS . 'functions' . DS . 'database.php');

/**
 * Test the column analyzer on a specific table
 */
function test_table_analysis(string $table_name): void {
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "ANALYZING TABLE: {$table_name}\n";
    echo str_repeat("=", 80) . "\n";
    
    try {
        // Check if table exists
        if (!database::tableExists($table_name)) {
            echo "❌ Table '{$table_name}' does not exist.\n";
            return;
        }
        
        // Get table schema
        $schema_query = "DESCRIBE `{$table_name}`";
        $schema_result = database::rawQuery($schema_query);
        $columns = $schema_result->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($columns)) {
            echo "❌ No columns found in table '{$table_name}'.\n";
            return;
        }
        
        echo "📊 Found " . count($columns) . " columns in table.\n\n";
        
        // Analyze all columns
        $column_names = array_column($columns, 'Field');
        $analysis_results = column_analyzer::analyze_table_columns($table_name, $column_names);
        
        if (isset($analysis_results['error'])) {
            echo "❌ Analysis failed: " . $analysis_results['error'] . "\n";
            return;
        }
        
        // Display results
        echo "📈 ANALYSIS SUMMARY:\n";
        echo "   Total columns: " . $analysis_results['total_columns'] . "\n";
        echo "   Analyzed columns: " . $analysis_results['analyzed_columns'] . "\n";
        echo "   Suggestions made: " . count($analysis_results['suggestions']) . "\n\n";
        
        // Show suggestions
        if (!empty($analysis_results['suggestions'])) {
            echo "💡 COLUMN SUGGESTIONS:\n";
            echo str_repeat("-", 80) . "\n";
            printf("%-25s %-25s %-10s %s\n", "ORIGINAL", "SUGGESTED", "CONFIDENCE", "REASONING");
            echo str_repeat("-", 80) . "\n";
            
            foreach ($analysis_results['suggestions'] as $column => $result) {
                printf("%-25s %-25s %-10.1f%% %s\n", 
                    $result['original_name'],
                    $result['suggested_name'],
                    $result['confidence'],
                    substr($result['reasoning'], 0, 30) . (strlen($result['reasoning']) > 30 ? '...' : '')
                );
            }
            echo str_repeat("-", 80) . "\n";
        } else {
            echo "✅ No column name improvements suggested for this table.\n";
        }
        
        // Show detailed analysis for interesting columns
        $interesting_columns = ['name', 'title', 'description', 'type', 'id'];
        $found_interesting = array_intersect($interesting_columns, $column_names);
        
        if (!empty($found_interesting)) {
            echo "\n🔍 DETAILED ANALYSIS FOR AMBIGUOUS COLUMNS:\n";
            foreach ($found_interesting as $column) {
                if (isset($analysis_results['all_results'][$column])) {
                    show_detailed_analysis($analysis_results['all_results'][$column]);
                }
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Error analyzing table: " . $e->getMessage() . "\n";
    }
}

/**
 * Show detailed analysis for a single column
 */
function show_detailed_analysis(array $analysis): void {
    echo "\n" . str_repeat("-", 40) . "\n";
    echo "Column: {$analysis['original_name']}\n";
    echo "Suggested: {$analysis['suggested_name']}\n";
    echo "Confidence: {$analysis['confidence']}%\n";
    echo "Reasoning: {$analysis['reasoning']}\n";
    
    if (isset($analysis['detailed_analysis'])) {
        $details = $analysis['detailed_analysis'];
        
        echo "\nDetailed Breakdown:\n";
        
        // Name analysis
        if (isset($details['name_analysis'])) {
            $name = $details['name_analysis'];
            echo "  📝 Name Analysis: " . ($name['type'] ?? 'unknown') . 
                 " (confidence: " . ($name['confidence'] ?? 0) . "%)\n";
        }
        
        // Data analysis
        if (isset($details['data_analysis'])) {
            $data = $details['data_analysis'];
            echo "  📊 Data Analysis: " . ($data['type'] ?? 'unknown') . 
                 " (confidence: " . ($data['confidence'] ?? 0) . "%)\n";
            if (isset($data['sample_size'])) {
                echo "     Sample size: " . $data['sample_size'] . " records\n";
            }
        }
        
        // Context analysis
        if (isset($details['context_analysis'])) {
            $context = $details['context_analysis'];
            echo "  🔗 Context Analysis: " . ($context['type'] ?? 'none') . 
                 " (confidence: " . ($context['confidence'] ?? 0) . "%)\n";
            if (!empty($context['reasoning'])) {
                echo "     Context: " . implode(', ', $context['reasoning']) . "\n";
            }
        }
        
        // Final scores
        if (isset($details['final_scores']) && !empty($details['final_scores'])) {
            echo "  🎯 Final Scores:\n";
            foreach ($details['final_scores'] as $type => $score) {
                echo "     {$type}: " . round($score, 1) . "\n";
            }
        }
    }
}

/**
 * Main test execution
 */
function run_tests(): void {
    echo "🚀 COLUMN ANALYZER TEST SUITE\n";
    echo "Testing intelligent column naming system...\n";
    
    // Test tables with different characteristics
    $test_tables = [
        'autodesk_accounts',           // Company/customer data
        'autodesk_subscriptions',      // Subscription data  
        'autobooks_cloudflow_data',    // Mixed data types
        'autobooks_users'              // User data
    ];
    
    foreach ($test_tables as $table) {
        test_table_analysis($table);
    }
    
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "✅ TESTING COMPLETE\n";
    echo str_repeat("=", 80) . "\n";
}

// Run the tests if this script is executed directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    run_tests();
}
