<?php
/**
 * Data Type Definitions for Enhanced Data Import
 * 
 * This file contains definitions and mappings for data types used in the enhanced
 * data import system. It provides a centralized location for managing data type
 * configurations and their corresponding database column types.
 */

namespace system\definitions;

class data_type_definitions {
    
    /**
     * Supported data types for CSV analysis
     */
    public const SUPPORTED_TYPES = [
        'string',
        'integer', 
        'decimal',
        'date',
        'datetime',
        'boolean',
        'text'
    ];

    /**
     * Data type descriptions for user interface
     */
    public const TYPE_DESCRIPTIONS = [
        'string' => 'Text data with limited length (up to 255 characters)',
        'integer' => 'Whole numbers without decimal points',
        'decimal' => 'Numbers with decimal points (precision: 10, scale: 2)',
        'date' => 'Date values in YYYY-MM-DD format',
        'datetime' => 'Date and time values with timestamp',
        'boolean' => 'True/false values (yes/no, 1/0, true/false)',
        'text' => 'Long text data (unlimited length)'
    ];

    /**
     * Database column type mappings
     */
    public const DATABASE_TYPE_MAPPINGS = [
        'string' => [
            'type' => 'string',
            'length' => 255,
            'nullable' => true
        ],
        'integer' => [
            'type' => 'integer',
            'nullable' => true
        ],
        'decimal' => [
            'type' => 'decimal',
            'precision' => 10,
            'scale' => 2,
            'nullable' => true
        ],
        'date' => [
            'type' => 'date',
            'nullable' => true
        ],
        'datetime' => [
            'type' => 'timestamp',
            'nullable' => true
        ],
        'boolean' => [
            'type' => 'boolean',
            'nullable' => true
        ],
        'text' => [
            'type' => 'text',
            'nullable' => true
        ]
    ];

    /**
     * Regular expressions for data type detection
     */
    public const DETECTION_PATTERNS = [
        'date' => [
            '/^\d{4}-\d{2}-\d{2}$/',                    // YYYY-MM-DD
            '/^\d{2}\/\d{2}\/\d{4}$/',                  // MM/DD/YYYY
            '/^\d{2}-\d{2}-\d{4}$/',                    // MM-DD-YYYY
            '/^\d{1,2}\/\d{1,2}\/\d{4}$/',              // M/D/YYYY
            '/^\d{4}\/\d{2}\/\d{2}$/'                   // YYYY/MM/DD
        ],
        'datetime' => [
            '/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/',  // YYYY-MM-DD HH:MM:SS
            '/^\d{2}\/\d{2}\/\d{4} \d{2}:\d{2}:\d{2}$/', // MM/DD/YYYY HH:MM:SS
            '/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/',  // ISO 8601 format
            '/^\d{2}-\d{2}-\d{4} \d{2}:\d{2}:\d{2}$/'   // MM-DD-YYYY HH:MM:SS
        ],
        'boolean' => [
            '/^(true|false)$/i',
            '/^(yes|no)$/i',
            '/^(y|n)$/i',
            '/^(1|0)$/i'
        ]
    ];

    /**
     * Boolean value mappings
     */
    public const BOOLEAN_VALUES = [
        'true' => true,
        'false' => false,
        'yes' => true,
        'no' => false,
        'y' => true,
        'n' => false,
        '1' => true,
        '0' => false
    ];

    /**
     * Data type priority for detection (higher number = higher priority)
     */
    public const TYPE_PRIORITY = [
        'boolean' => 6,
        'integer' => 5,
        'decimal' => 4,
        'datetime' => 3,
        'date' => 2,
        'text' => 1,
        'string' => 0  // Default fallback
    ];

    /**
     * Column name sanitization rules
     */
    public const COLUMN_NAME_RULES = [
        'max_length' => 64,
        'allowed_chars' => '/[^a-z0-9_]/',
        'replacement_char' => '_',
        'prefix_for_numeric' => 'col_',
        'default_name' => 'unnamed_column'
    ];

    /**
     * Get database type mapping for a given data type
     *
     * @param string $data_type The detected data type
     * @return array Database column definition
     */
    public static function get_database_mapping(string $data_type): array {
        return self::DATABASE_TYPE_MAPPINGS[$data_type] ?? self::DATABASE_TYPE_MAPPINGS['string'];
    }

    /**
     * Get type description for user interface
     *
     * @param string $data_type The data type
     * @return string Human-readable description
     */
    public static function get_type_description(string $data_type): string {
        return self::TYPE_DESCRIPTIONS[$data_type] ?? 'Unknown data type';
    }

    /**
     * Check if a data type is supported
     *
     * @param string $data_type The data type to check
     * @return bool True if supported
     */
    public static function is_supported_type(string $data_type): bool {
        return in_array($data_type, self::SUPPORTED_TYPES);
    }

    /**
     * Get detection patterns for a specific type
     *
     * @param string $data_type The data type
     * @return array Array of regex patterns
     */
    public static function get_detection_patterns(string $data_type): array {
        return self::DETECTION_PATTERNS[$data_type] ?? [];
    }

    /**
     * Convert boolean string to actual boolean value
     *
     * @param string $value The string value
     * @return bool|null Boolean value or null if not a boolean
     */
    public static function convert_boolean_value(string $value): ?bool {
        $lower_value = strtolower(trim($value));
        return self::BOOLEAN_VALUES[$lower_value] ?? null;
    }

    /**
     * Get type priority for detection ordering
     *
     * @param string $data_type The data type
     * @return int Priority value
     */
    public static function get_type_priority(string $data_type): int {
        return self::TYPE_PRIORITY[$data_type] ?? 0;
    }

    /**
     * Validate column name according to rules
     *
     * @param string $name The column name to validate
     * @return array Validation result with 'valid' boolean and 'sanitized' name
     */
    public static function validate_column_name(string $name): array {
        $original = $name;
        $sanitized = self::sanitize_column_name($name);
        
        return [
            'valid' => $original === $sanitized,
            'original' => $original,
            'sanitized' => $sanitized,
            'changed' => $original !== $sanitized
        ];
    }

    /**
     * Sanitize column name according to rules
     *
     * @param string $name The column name to sanitize
     * @return string Sanitized column name
     */
    public static function sanitize_column_name(string $name): string {
        $rules = self::COLUMN_NAME_RULES;
        
        // Convert to lowercase and trim
        $clean = strtolower(trim($name));
        
        // Replace disallowed characters
        $clean = preg_replace($rules['allowed_chars'], $rules['replacement_char'], $clean);
        
        // Remove multiple underscores
        $clean = preg_replace('/_+/', '_', $clean);
        
        // Remove leading/trailing underscores
        $clean = trim($clean, '_');
        
        // Ensure it doesn't start with a number
        if (preg_match('/^[0-9]/', $clean)) {
            $clean = $rules['prefix_for_numeric'] . $clean;
        }
        
        // Ensure it's not empty
        if (empty($clean)) {
            $clean = $rules['default_name'];
        }
        
        // Truncate if too long
        if (strlen($clean) > $rules['max_length']) {
            $clean = substr($clean, 0, $rules['max_length']);
            $clean = rtrim($clean, '_'); // Remove trailing underscore if truncation created one
        }
        
        return $clean;
    }

    /**
     * Get all supported types with their descriptions
     *
     * @return array Associative array of type => description
     */
    public static function get_all_types_with_descriptions(): array {
        $result = [];
        foreach (self::SUPPORTED_TYPES as $type) {
            $result[$type] = self::get_type_description($type);
        }
        return $result;
    }
}
