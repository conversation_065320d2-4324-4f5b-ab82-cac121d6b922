@props([
    'name' => 'custom-html',
    'description' => 'A custom HTML hilt template',
    'type' => 'hilt-html',
    'route_key' => '{{ $route_key }}'
])

@php
// Custom HTML hilt template
// The HTML content will be replaced with actual content
$route_key = $route_key ?? 'default';
@endphp

<div class="hilt-html-template">
    <h1>{{ ucwords(str_replace('_', ' ', $route_key)) }}</h1>

    <!-- HTML_CONTENT -->
    /* HTML_CONTENT */

    <div class="template-info">
        <p><small>Hilt HTML Template - Route: {{ $route_key }}</small></p>
    </div>
</div>