<?php
/**
 * Migration: Create autobooks_data_table_storage table
 * 
 * This migration creates the database table for storing data table configurations
 * and migrates any existing session-based preferences to the database.
 */

// Ensure we have database connection
if (!function_exists('tep_db_query')) {
    die('Database connection not available');
}

echo "Creating autobooks_data_table_storage table...\n";

// Create the table
$create_table_sql = "
CREATE TABLE IF NOT EXISTS `autobooks_data_table_storage` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `table_name` varchar(255) NOT NULL,
    `user_id` int(11) DEFAULT NULL,
    `configuration` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`configuration`)),
    `data_source` varchar(255) DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_table_user` (`table_name`, `user_id`),
    <PERSON>EY `idx_table_name` (`table_name`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_data_source` (`data_source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
";

$result = tep_db_query($create_table_sql);

if ($result) {
    echo "✓ Table created successfully\n";
} else {
    echo "✗ Failed to create table\n";
    exit(1);
}

// Migrate existing session data if any exists
echo "Checking for existing session-based preferences to migrate...\n";

$migrated_count = 0;

// Check if we have session data to migrate
if (isset($_SESSION)) {
    foreach ($_SESSION as $key => $value) {
        if (strpos($key, 'column_preferences_') === 0) {
            $table_name = substr($key, strlen('column_preferences_'));
            
            // Get current user ID if available
            $user_id = null;
            if (class_exists('users')) {
                $user = users::checkAuth();
                $user_id = $user['id'] ?? null;
            }
            
            // Prepare configuration data
            $configuration = [
                'hidden' => $value['hidden'] ?? [],
                'structure' => $value['structure'] ?? [],
                'migrated_from_session' => true,
                'migrated_at' => date('Y-m-d H:i:s')
            ];
            
            // Insert into database
            $insert_sql = "INSERT INTO autobooks_data_table_storage (table_name, user_id, configuration) 
                          VALUES (?, ?, ?)";
            
            $insert_result = tep_db_query($insert_sql, null, [
                $table_name,
                $user_id,
                json_encode($configuration)
            ]);
            
            if ($insert_result) {
                echo "✓ Migrated preferences for table: $table_name\n";
                $migrated_count++;
                
                // Remove from session after successful migration
                unset($_SESSION[$key]);
            } else {
                echo "✗ Failed to migrate preferences for table: $table_name\n";
            }
        }
    }
}

if ($migrated_count > 0) {
    echo "✓ Migrated $migrated_count session-based preferences to database\n";
} else {
    echo "• No session-based preferences found to migrate\n";
}

// Add foreign key constraint if users table exists
echo "Checking for users table to add foreign key constraint...\n";

$check_users_table = tep_db_query("SHOW TABLES LIKE 'autobooks_users'");
if ($check_users_table && tep_db_num_rows($check_users_table) > 0) {
    $fk_sql = "ALTER TABLE `autobooks_data_table_storage` 
               ADD CONSTRAINT `fk_data_table_storage_user` 
               FOREIGN KEY (`user_id`) REFERENCES `autobooks_users` (`id`) ON DELETE CASCADE";
    
    $fk_result = tep_db_query($fk_sql);
    if ($fk_result) {
        echo "✓ Foreign key constraint added\n";
    } else {
        echo "• Foreign key constraint may already exist or failed to add\n";
    }
} else {
    echo "• Users table not found, skipping foreign key constraint\n";
}

echo "\nMigration completed successfully!\n";
echo "Data table configurations will now be stored in the database.\n";

// Test the new system
echo "\nTesting data table storage class...\n";

if (class_exists('data_table_storage')) {
    // Test basic functionality
    $test_config = [
        'hidden' => [],
        'structure' => [
            [
                'id' => 'test_col_1',
                'label' => 'Test Column',
                'fields' => ['test_field'],
                'filter' => false,
                'visible' => true
            ]
        ],
        'test' => true
    ];
    
    $save_result = \data_table_storage::save_configuration('test_migration', $test_config, null, 'test_source');
    if ($save_result) {
        echo "✓ Save test passed\n";

        $get_result = \data_table_storage::get_configuration('test_migration', null);
        if ($get_result && $get_result['configuration']['test'] === true) {
            echo "✓ Retrieve test passed\n";

            // Clean up test data
            \data_table_storage::delete_configuration('test_migration', null);
            echo "✓ Delete test passed\n";
        } else {
            echo "✗ Retrieve test failed\n";
        }
    } else {
        echo "✗ Save test failed\n";
    }
} else {
    echo "✗ data_table_storage class not found\n";
}

echo "\n=== Migration Summary ===\n";
echo "• Database table: autobooks_data_table_storage created\n";
echo "• Session preferences migrated: $migrated_count\n";
echo "• System ready for database-based data table storage\n";
echo "• Future enhancements: data_source integration ready\n";
?>
