<?php
namespace edge;

class loop{
    public int $index;       // Current index (0-based)
    public int $iteration;   // Current iteration (1-based)
    public int $remaining;   // Remaining iterations
    public int $count;       // Total items in the array
    public bool $first;       // Is this the first iteration
    public bool $last;        // Is this the last iteration
    public bool $even;        // Is this an even iteration
    public bool $odd;         // Is this an odd iteration
    public int $depth;       // Nesting depth
    public mixed $parent;      // Parent loop

    public function __construct($count, $depth = 1, $parent = null)
    {
        $this->index = 0;
        $this->iteration = 1;
        $this->remaining = $count;
        $this->count = $count;
        $this->first = $count > 0;
        $this->last = $count === 1;
        $this->even = false;
        $this->odd = true;
        $this->depth = $depth;
        $this->parent = $parent;
    }

    public function update(): void
    {
        $this->index = $this->index + 1;
        $this->iteration = $this->index + 1;
        $this->remaining = $this->count - $this->iteration;
        $this->first = $this->index === 0;
        $this->last = $this->index === $this->count - 1;
        $this->even = $this->iteration % 2 === 0;
        $this->odd = !$this->even;
    }
}
?>