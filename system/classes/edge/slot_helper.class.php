<?php
namespace edge;

class slot_helper {
    
    /**
     * Parse slot content from component tag content
     * 
     * @param string $content The content between component tags
     * @return array Array of slots with 'default' and named slots
     */
    public static function parse($content) {
        if (empty($content)) {
            return ['default' => ''];
        }
        
        $slots = ['default' => ''];
        
        // Extract named slots
        $content = preg_replace_callback(
            '/<x-slot\s+name=["\']([^"\']+)["\'][^>]*>(.*?)<\/x-slot>/s',
            function($matches) use (&$slots) {
                $slotName = $matches[1];
                $slotContent = trim($matches[2]);
                $slots[$slotName] = $slotContent;
                return ''; // Remove slot from content
            },
            $content
        );
        
        // Remaining content becomes the default slot
        $slots['default'] = trim($content);
        
        return $slots;
    }
    
    /**
     * Get a specific slot from parsed slots
     * 
     * @param array $slots Parsed slots array
     * @param string $name Slot name
     * @param string $default Default content if slot doesn't exist
     * @return string Slot content
     */
    public static function get($slots, $name, $default = '') {
        return isset($slots[$name]) ? $slots[$name] : $default;
    }
    
    /**
     * Check if a slot exists and has content
     * 
     * @param array $slots Parsed slots array
     * @param string $name Slot name
     * @return bool True if slot exists and has content
     */
    public static function has($slots, $name) {
        return isset($slots[$name]) && !empty(trim($slots[$name]));
    }
}
