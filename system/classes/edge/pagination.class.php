<?php
namespace edge;

class pagination {
    public int $index;       // Current index (0-based)
    public int $current_page_number;   // Current page (1-based)
    public int $current_page;   // Alias for current_page_number for compatibility
    public int $items_per_page;   // Items per page
    public int $item_count;       // Total items in the array
    public int $first_item;       // First item number on current page
    public int $last_item;        // Last item number on current page
    public bool $first;       // Is this the first page
    public bool $last;        // Is this the last page
    public int $total_items;        // Total items (alias for item_count)
    public int $total_pages;         // Total number of pages

    public function __construct($item_count, $first_item = 0, $current_page_number = 1, $items_per_page = 100)
    {
        $this->index = 0;
        $this->current_page_number = $current_page_number;
        $this->current_page = $current_page_number; // Alias for compatibility
        $this->items_per_page = $items_per_page;
        $this->item_count = $item_count;

        // Calculate first and last item numbers for display (1-based)
        $this->first_item = ($current_page_number - 1) * $items_per_page + 1;
        $this->last_item = min($this->first_item + $items_per_page - 1, $item_count);

        $this->total_items = $this->item_count;
        $this->total_pages = ceil($this->item_count / $this->items_per_page);

        // Set first/last page flags
        $this->first = ($current_page_number == 1);
        $this->last = ($current_page_number == $this->total_pages);
    }

    public function page_array(): array
    {
        $pages = [];
        $current = $this->current_page_number;
        $total = $this->total_pages;

        // If we have 7 or fewer pages, show all
        if ($total <= 7) {
            for ($i = 1; $i <= $total; $i++) {
                $pages[] = $i;
            }
            return $pages;
        }

        // Always show first page
        $pages[] = 1;

        // Show pages around current page
        $start = max(2, $current - 2);
        $end = min($total - 1, $current + 2);

        // Add ellipsis if there's a gap after page 1
        if ($start > 2) {
            $pages[] = "...";
        }

        // Add pages around current
        for ($i = $start; $i <= $end; $i++) {
            $pages[] = $i;
        }

        // Add ellipsis if there's a gap before last page
        if ($end < $total - 1) {
            $pages[] = "...";
        }

        // Always show last page (if it's not already included)
        if ($total > 1) {
            $pages[] = $total;
        }

        return $pages;
    }
}

?>