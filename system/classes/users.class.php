<?php
namespace system;
class users {
    private static $db;
    public static int $id;
    public static string $username;
    public static string $email;
    public static string $role;
    public static bool $started;

    public function __construct() {
        global $db;
        self::$db = $db;
    }

    public static function login($username, $password) {
        tcs_log("Login attempt for username: {$username}", 'auth');

        $query = "SELECT * FROM autobooks_users WHERE username = :username AND status = 'active'";
        $result = tep_db_query($query, null, [':username' =>  $username]);

        if ($user = tep_db_fetch_array($result)) {
            tcs_log("User found, verifying password", 'auth');

            if (password_verify($password, $user['password'])) {
                tcs_log("Password verified successfully for user: {$username}", 'auth');

               self::$id = $user['id'];
               self::$username = $user['username'];
               self::$email = $user['email'];
               self::$role = $user['role'];

                // Create session
                $token = bin2hex(random_bytes(32));
                $expires = date('Y-m-d H:i:s', strtotime('+8 hours'));

                tcs_log("Creating new session token for user: {$username}", 'auth');

                tep_db_query(
                    "INSERT INTO autobooks_sessions (user_id, token, expires_at) VALUES (:user_id, :token, :expires)",
                    null,
                    [':user_id' => self::$id, ':token' =>  $token, ':expires' =>  $expires]
                );

                // Update last login
                tep_db_query(
                    "UPDATE autobooks_users SET last_login = NOW() WHERE id = :id",
                    null,
                    [':id' => self::$id]
                );

                // Set both cookie and session
                setcookie('auth_token', $token, strtotime('+24 hours'), '/', '', true, true);
                $_SESSION['auth_token'] = $token;
                $_SESSION['user_id'] = self::$id;
                $_SESSION['user_role'] = self::$role;

                tcs_log("Login successful for user: {$username}, role: {self->role}", 'auth');
                return true;
            }
            tcs_log("Invalid password for user: {$username}", 'auth');
        } else {
            tcs_log("User not found or inactive: {$username}", 'auth');
        }
        return false;
    }

    public static function logout() {
        $token = $_SESSION['auth_token'] ?? $_COOKIE['auth_token'] ?? null;
        $userId = $_SESSION['user_id'] ?? 'unknown';

        tcs_log("Logout initiated for user ID: {$userId}", 'auth');

        if ($token) {
            tcs_log("Removing session token from database", 'auth');
            tep_db_query(
                "DELETE FROM autobooks_sessions WHERE token = :token",
                null,
                [':token' =>  $token]
            );
        }

        // Clear both cookie and session
        setcookie('auth_token', '', time() - 3600, '/');
        session_destroy();
        tcs_log("Session and cookies cleared for user ID: {$userId}", 'auth');
    }

    public static function checkAuth() {
        $token = $_SESSION['auth_token'] ?? $_COOKIE['auth_token'] ?? null;

        if ($token) {
            //tcs_log("Checking authentication token", 'auth');
            $result = tep_db_query(
                "SELECT u.* FROM autobooks_users u JOIN autobooks_sessions s ON u.id = s.user_id WHERE s.token = :token AND s.expires_at > NOW() AND u.status = 'active'",
                null,
                [':token' =>  $token]
            );

            if ($user = tep_db_fetch_array($result)) {
              //  tcs_log("Valid session found for user ID: {$user['id']}", 'auth');
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_role'] = $user['role'];
                $_SESSION['auth_token'] = $token;
                return $user;
            }
            tcs_log("Invalid or expired token found", 'auth');
        } else {
            tcs_log("No authentication token found", 'auth');
        }

        // Only clear session if token was invalid or expired
        if ($token) self::logout();
        return false;
    }

    public static function requireRole($required_role): bool {
        $user = self::checkAuth();
        if (!$user) {
            tcs_log("Access denied: No authenticated user found", 'auth');
            // Fix the double slash issue by using rtrim
            header('Location: ' . rtrim(APP_ROOT, '/') . '/login');
            exit;
        }
        if (is_array($required_role)) {
            if (!in_array($user['role'], $required_role)) {
                tcs_log("Access denied: User ID {$user['id']} with role {$user['role']} attempted to access {$required_role} protected resource", 'auth');
                header('HTTP/1.1 403 Forbidden');
                exit('Access Denied');
            }
            return true;
        }
        $roles = ['dev' =>  3, 'admin' =>  2, 'user' =>  1];
        if ($roles[$user['role']] < $roles[$required_role]) {
            tcs_log("Access denied: User ID {$user['id']} with role {$user['role']} attempted to access {$required_role} protected resource", 'auth');
            header('HTTP/1.1 403 Forbidden');
            exit('Access Denied');
        }
        return true;
       // tcs_log("Role check passed for user ID {$user['id']}: {$user['role']} >= {$required_role}", 'auth');
    }

    /**
     * Get user details by ID
     *
     * @param int $user_id The user ID to retrieve details for
     * @return array|false User details array or false if user not found
     */
    public static function getUserById($user_id) {
        tcs_log("Retrieving user details for ID: {$user_id}", 'auth');

        $query = "SELECT * FROM autobooks_users WHERE id = :user_id AND status = 'active'";
        $result = tep_db_query($query, null, [':user_id' => $user_id]);

        if ($user = tep_db_fetch_array($result)) {
            tcs_log("User found with ID: {$user_id}", 'auth');
            return $user;
        } else {
            tcs_log("User not found or inactive with ID: {$user_id}", 'auth');
            return false;
        }
    }
}