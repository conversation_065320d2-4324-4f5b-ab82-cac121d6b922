<?php
namespace system;

use system\database;

/**
 * Column Analyzer Class
 * 
 * Intelligently analyzes database columns and suggests better names
 * based on column names, data patterns, and context analysis.
 */
class column_analyzer {
    
    private static $mapping_rules;
    private static $data_patterns;
    private static $log_target = 'column_analyzer';
    
    /**
     * Initialize the analyzer with configuration
     */
    private static function init() {
        if (!isset(self::$mapping_rules)) {
            self::$mapping_rules = require_once(FS_SYSTEM . DS . 'config' . DS . 'column_mapping_rules.php');
        }
        if (!isset(self::$data_patterns)) {
            self::$data_patterns = require_once(FS_SYSTEM . DS . 'config' . DS . 'data_patterns.php');
        }
    }
    
    /**
     * Analyze a column and suggest a better name
     * 
     * @param string $table_name Database table name
     * @param string $column_name Original column name
     * @param array $table_columns All columns in the table
     * @return array Analysis result with suggested name and confidence
     */
    public static function analyze_column(string $table_name, string $column_name, array $table_columns = []): array {
        self::init();
        
        try {
            // Skip analysis for certain column types
            if (self::should_skip_analysis($column_name)) {
                return [
                    'original_name' => $column_name,
                    'suggested_name' => $column_name,
                    'confidence' => 100,
                    'reasoning' => 'System column - no analysis needed',
                    'analysis_performed' => false
                ];
            }
            
            // Factor 1: Column Name Analysis (30%)
            $name_analysis = self::analyze_column_name($column_name);
            
            // Factor 2: Data Pattern Analysis (50%)
            $data_analysis = self::analyze_data_patterns($table_name, $column_name);
            
            // Factor 3: Context Analysis (20%)
            $context_analysis = self::analyze_context($column_name, $table_columns, $table_name);
            
            // Combine analyses with weighted scoring
            $final_analysis = self::combine_analyses($name_analysis, $data_analysis, $context_analysis);
            
            // Make final decision
            $suggestion = self::make_suggestion($column_name, $final_analysis, $table_name);
            
            return [
                'original_name' => $column_name,
                'suggested_name' => $suggestion['name'],
                'confidence' => $suggestion['confidence'],
                'reasoning' => $suggestion['reasoning'],
                'analysis_performed' => true,
                'detailed_analysis' => [
                    'name_analysis' => $name_analysis,
                    'data_analysis' => $data_analysis,
                    'context_analysis' => $context_analysis,
                    'final_scores' => $final_analysis
                ]
            ];
            
        } catch (\Exception $e) {
            tcs_log("Error analyzing column {$column_name}: " . $e->getMessage(), self::$log_target);
            return [
                'original_name' => $column_name,
                'suggested_name' => $column_name,
                'confidence' => 0,
                'reasoning' => 'Analysis failed: ' . $e->getMessage(),
                'analysis_performed' => false
            ];
        }
    }
    
    /**
     * Check if column should be skipped from analysis
     */
    private static function should_skip_analysis(string $column_name): bool {
        $skip_patterns = [
            'id', 'created_at', 'updated_at', 'last_modified', 'data_hash',
            'timestamp', 'datetime', 'date_created', 'date_updated'
        ];
        
        return in_array(strtolower($column_name), $skip_patterns);
    }
    
    /**
     * Analyze column name patterns (Factor 1 - 30%)
     */
    private static function analyze_column_name(string $column_name): array {
        $patterns = self::$mapping_rules['column_name_patterns'];
        $clean_name = strtolower(trim($column_name));
        
        // Check exact matches first
        if (isset($patterns['exact_matches'][$clean_name])) {
            return $patterns['exact_matches'][$clean_name];
        }
        
        // Check partial matches
        if (isset($patterns['partial_matches'][$clean_name])) {
            return $patterns['partial_matches'][$clean_name];
        }
        
        // Check generic patterns
        if (isset($patterns['generic_patterns'][$clean_name])) {
            return $patterns['generic_patterns'][$clean_name];
        }
        
        // No pattern match found
        return [
            'type' => 'unknown',
            'confidence' => 0,
            'requires_analysis' => true
        ];
    }
    
    /**
     * Analyze data patterns in the column (Factor 2 - 50%)
     */
    private static function analyze_data_patterns(string $table_name, string $column_name): array {
        try {
            // Get sample data
            $sample_data = self::get_sample_data($table_name, $column_name);
            
            if (empty($sample_data)) {
                return ['type' => 'unknown', 'confidence' => 0, 'reason' => 'No data available'];
            }
            
            // Filter out null/empty values
            $valid_data = array_filter($sample_data, function($value) {
                return !is_null($value) && trim($value) !== '';
            });
            
            if (empty($valid_data)) {
                return ['type' => 'unknown', 'confidence' => 0, 'reason' => 'All values are null/empty'];
            }
            
            // Check null percentage
            $null_percentage = (count($sample_data) - count($valid_data)) / count($sample_data);
            if ($null_percentage > self::$data_patterns['sampling_config']['null_threshold']) {
                return ['type' => 'unknown', 'confidence' => 0, 'reason' => 'Too many null values'];
            }
            
            // Analyze patterns
            $pattern_scores = [];
            
            // Test regex patterns
            foreach (self::$data_patterns['regex_patterns'] as $pattern_name => $pattern_config) {
                $matches = 0;
                foreach ($valid_data as $value) {
                    if (preg_match($pattern_config['pattern'], $value)) {
                        $matches++;
                    }
                }
                
                if ($matches >= $pattern_config['min_matches']) {
                    $match_percentage = $matches / count($valid_data);
                    $score = $pattern_config['confidence'] * $match_percentage;
                    $pattern_scores[$pattern_name] = [
                        'score' => $score,
                        'matches' => $matches,
                        'total' => count($valid_data),
                        'percentage' => $match_percentage
                    ];
                }
            }
            
            // Test keyword lists
            foreach (self::$data_patterns['keyword_lists'] as $list_name => $list_config) {
                $matches = 0;
                foreach ($valid_data as $value) {
                    foreach ($list_config['keywords'] as $keyword) {
                        if (stripos($value, $keyword) !== false) {
                            $matches++;
                            break; // Count each value only once per keyword list
                        }
                    }
                }
                
                if ($matches >= $list_config['min_matches']) {
                    $match_percentage = $matches / count($valid_data);
                    $score = $list_config['confidence'] * $match_percentage;
                    $pattern_scores[$list_name] = [
                        'score' => $score,
                        'matches' => $matches,
                        'total' => count($valid_data),
                        'percentage' => $match_percentage,
                        'type' => $list_config['type']
                    ];
                }
            }
            
            // Return best match
            if (!empty($pattern_scores)) {
                $best_pattern = array_keys($pattern_scores, max($pattern_scores))[0];
                $best_score = $pattern_scores[$best_pattern];
                
                return [
                    'type' => $best_score['type'] ?? $best_pattern,
                    'confidence' => $best_score['score'],
                    'pattern_matches' => $pattern_scores,
                    'sample_size' => count($valid_data)
                ];
            }
            
            return ['type' => 'unknown', 'confidence' => 0, 'reason' => 'No patterns matched'];
            
        } catch (\Exception $e) {
            tcs_log("Error in data pattern analysis: " . $e->getMessage(), self::$log_target);
            return ['type' => 'unknown', 'confidence' => 0, 'reason' => 'Analysis error: ' . $e->getMessage()];
        }
    }
    
    /**
     * Get sample data from the table column
     */
    private static function get_sample_data(string $table_name, string $column_name): array {
        $config = self::$data_patterns['sampling_config'];
        
        try {
            // Get total row count
            $count_result = database::table($table_name)->count();
            $total_rows = $count_result;
            
            if ($total_rows == 0) {
                return [];
            }
            
            // Calculate sample size
            $sample_size = min($config['max_sample_size'], max($config['min_sample_size'], 
                min($config['sample_size'], $total_rows)));
            
            // Get distributed sample
            $samples = [];
            $distribution = $config['sample_distribution'];
            
            // Sample from start
            $start_count = (int)($sample_size * $distribution['start']);
            if ($start_count > 0) {
                $start_samples = database::table($table_name)
                    ->select([$column_name])
                    ->limit($start_count)
                    ->get();
                $samples = array_merge($samples, array_column($start_samples, $column_name));
            }
            
            // Sample from middle
            $middle_count = (int)($sample_size * $distribution['middle']);
            if ($middle_count > 0 && $total_rows > $start_count * 2) {
                $middle_offset = (int)($total_rows / 2) - (int)($middle_count / 2);
                $middle_samples = database::table($table_name)
                    ->select([$column_name])
                    ->offset($middle_offset)
                    ->limit($middle_count)
                    ->get();
                $samples = array_merge($samples, array_column($middle_samples, $column_name));
            }
            
            // Sample from end
            $end_count = $sample_size - count($samples);
            if ($end_count > 0 && $total_rows > $sample_size) {
                $end_offset = $total_rows - $end_count;
                $end_samples = database::table($table_name)
                    ->select([$column_name])
                    ->offset($end_offset)
                    ->limit($end_count)
                    ->get();
                $samples = array_merge($samples, array_column($end_samples, $column_name));
            }
            
            return $samples;
            
        } catch (\Exception $e) {
            tcs_log("Error getting sample data: " . $e->getMessage(), self::$log_target);
            return [];
        }
    }
    
    /**
     * Analyze context based on other columns (Factor 3 - 20%)
     */
    private static function analyze_context(string $column_name, array $table_columns, string $table_name): array {
        $context_rules = self::$mapping_rules['context_rules'];
        $column_names = array_map('strtolower', $table_columns);
        $clean_name = strtolower($column_name);
        
        $context_score = 0;
        $suggested_type = null;
        $reasoning = [];
        
        // Check name disambiguation rules
        if (isset($context_rules['name_disambiguation'])) {
            foreach ($context_rules['name_disambiguation'] as $rule_name => $rule) {
                $condition_met = false;
                foreach ($rule['condition'] as $required_column) {
                    if (in_array(strtolower($required_column), $column_names)) {
                        $condition_met = true;
                        break;
                    }
                }
                
                if ($condition_met && isset($rule['effect'][$clean_name])) {
                    $effect = $rule['effect'][$clean_name];
                    $context_score += $effect['confidence_boost'];
                    $suggested_type = $effect['type'];
                    $reasoning[] = "Rule '{$rule_name}' applied";
                }
            }
        }
        
        // Check table type detection
        if (isset($context_rules['table_type_detection'])) {
            foreach ($context_rules['table_type_detection'] as $table_type => $detection_rule) {
                $indicator_count = 0;
                foreach ($detection_rule['indicators'] as $indicator) {
                    if (in_array(strtolower($indicator), $column_names)) {
                        $indicator_count++;
                    }
                }
                
                if ($indicator_count >= $detection_rule['threshold']) {
                    if (isset($detection_rule['effects'][$clean_name])) {
                        $effect = $detection_rule['effects'][$clean_name];
                        $context_score += $effect['confidence_boost'];
                        $suggested_type = $effect['type'];
                        $reasoning[] = "Detected as {$table_type} table";
                    }
                }
            }
        }
        
        return [
            'type' => $suggested_type,
            'confidence' => min(100, $context_score),
            'reasoning' => $reasoning
        ];
    }

    /**
     * Combine all analyses with weighted scoring
     */
    private static function combine_analyses(array $name_analysis, array $data_analysis, array $context_analysis): array {
        $weights = self::$data_patterns['analysis_weights'];

        $combined_scores = [];

        // Collect all suggested types
        $all_types = [];
        if (isset($name_analysis['type']) && $name_analysis['type'] !== 'unknown') {
            $all_types[] = $name_analysis['type'];
        }
        if (isset($data_analysis['type']) && $data_analysis['type'] !== 'unknown') {
            $all_types[] = $data_analysis['type'];
        }
        if (isset($context_analysis['type']) && $context_analysis['type'] !== 'unknown') {
            $all_types[] = $context_analysis['type'];
        }

        $unique_types = array_unique($all_types);

        // Calculate weighted scores for each type
        foreach ($unique_types as $type) {
            $score = 0;

            // Add name analysis score
            if (isset($name_analysis['type']) && $name_analysis['type'] === $type) {
                $score += ($name_analysis['confidence'] ?? 0) * $weights['column_name_analysis'];
            }

            // Add data analysis score
            if (isset($data_analysis['type']) && $data_analysis['type'] === $type) {
                $score += ($data_analysis['confidence'] ?? 0) * $weights['data_pattern_analysis'];
            }

            // Add context analysis score
            if (isset($context_analysis['type']) && $context_analysis['type'] === $type) {
                $score += ($context_analysis['confidence'] ?? 0) * $weights['context_analysis'];
            }

            $combined_scores[$type] = $score;
        }

        return $combined_scores;
    }

    /**
     * Make final suggestion based on combined analysis
     */
    private static function make_suggestion(string $original_name, array $final_analysis, string $table_name): array {
        $thresholds = self::$data_patterns['decision_thresholds'];

        if (empty($final_analysis)) {
            return [
                'name' => $original_name,
                'confidence' => 0,
                'reasoning' => 'No analysis results available'
            ];
        }

        // Get highest scoring type
        $best_type = array_keys($final_analysis, max($final_analysis))[0];
        $best_score = $final_analysis[$best_type];

        // Apply decision logic
        if ($best_score >= $thresholds['high_confidence']) {
            $suggested_name = self::get_standard_column_name($best_type);
            return [
                'name' => $suggested_name,
                'confidence' => $best_score,
                'reasoning' => "High confidence match for '{$best_type}' (score: {$best_score})"
            ];
        }

        if ($best_score >= $thresholds['medium_confidence']) {
            $suggested_name = self::get_standard_column_name($best_type);
            return [
                'name' => $suggested_name,
                'confidence' => $best_score,
                'reasoning' => "Medium confidence match for '{$best_type}' (score: {$best_score})"
            ];
        }

        if ($best_score >= $thresholds['prefix_threshold']) {
            $prefixed_name = self::add_context_prefix($original_name, $table_name);
            return [
                'name' => $prefixed_name,
                'confidence' => $best_score,
                'reasoning' => "Low confidence - added context prefix (score: {$best_score})"
            ];
        }

        // Keep original name
        return [
            'name' => $original_name,
            'confidence' => $best_score,
            'reasoning' => "Confidence too low to suggest changes (score: {$best_score})"
        ];
    }

    /**
     * Get standard column name for a type
     */
    private static function get_standard_column_name(string $type): string {
        $standard_names = self::$mapping_rules['standard_column_names'];

        if (isset($standard_names[$type])) {
            return $type;
        }

        // Fallback to type name
        return $type;
    }

    /**
     * Add context prefix to column name
     */
    private static function add_context_prefix(string $column_name, string $table_name): string {
        $prefixes = self::$mapping_rules['source_prefixes'];
        $table_lower = strtolower($table_name);

        foreach ($prefixes as $prefix => $keywords) {
            foreach ($keywords as $keyword) {
                if (strpos($table_lower, $keyword) !== false) {
                    return $prefix . $column_name;
                }
            }
        }

        // Default prefix based on table name
        $table_parts = explode('_', $table_name);
        if (count($table_parts) > 1) {
            return $table_parts[0] . '_' . $column_name;
        }

        return $column_name;
    }

    /**
     * Analyze multiple columns in a table
     */
    public static function analyze_table_columns(string $table_name, array $columns = []): array {
        self::init();

        try {
            // Get table columns if not provided
            if (empty($columns)) {
                $schema_result = database::rawQuery("DESCRIBE `{$table_name}`");
                $schema_columns = $schema_result->fetchAll(\PDO::FETCH_ASSOC);
                $columns = array_column($schema_columns, 'Field');
            }

            $results = [];

            foreach ($columns as $column) {
                $results[$column] = self::analyze_column($table_name, $column, $columns);
            }

            return [
                'table_name' => $table_name,
                'total_columns' => count($columns),
                'analyzed_columns' => count(array_filter($results, function($r) { return $r['analysis_performed']; })),
                'suggestions' => array_filter($results, function($r) {
                    return $r['suggested_name'] !== $r['original_name'];
                }),
                'all_results' => $results
            ];

        } catch (\Exception $e) {
            tcs_log("Error analyzing table columns: " . $e->getMessage(), self::$log_target);
            return [
                'error' => 'Failed to analyze table: ' . $e->getMessage(),
                'table_name' => $table_name
            ];
        }
    }

    /**
     * Get analysis summary for reporting
     */
    public static function get_analysis_summary(array $analysis_results): array {
        $summary = [
            'total_columns' => 0,
            'analyzed_columns' => 0,
            'suggestions_made' => 0,
            'high_confidence_suggestions' => 0,
            'medium_confidence_suggestions' => 0,
            'low_confidence_suggestions' => 0,
            'confidence_distribution' => []
        ];

        foreach ($analysis_results as $result) {
            $summary['total_columns']++;

            if ($result['analysis_performed']) {
                $summary['analyzed_columns']++;
            }

            if ($result['suggested_name'] !== $result['original_name']) {
                $summary['suggestions_made']++;

                $confidence = $result['confidence'];
                if ($confidence >= 70) {
                    $summary['high_confidence_suggestions']++;
                } elseif ($confidence >= 50) {
                    $summary['medium_confidence_suggestions']++;
                } else {
                    $summary['low_confidence_suggestions']++;
                }

                $confidence_range = (int)($confidence / 10) * 10;
                $summary['confidence_distribution'][$confidence_range] =
                    ($summary['confidence_distribution'][$confidence_range] ?? 0) + 1;
            }
        }

        return $summary;
    }
}
