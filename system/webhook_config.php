<?php
/**
 * Webhook Configuration
 *
 * This file contains configuration for webhook endpoints that can bypass
 * the normal authentication flow.
 */

return [
    // Webhook secrets for HMAC verification
    'webhook_secrets' => [
        // Autodesk subscription webhook secret
        // This should match the secret you provided when setting up the webhook in Autodesk
        'autodesk_subscription' => 'GWDinOSWlBswFyq5xs3ozbVFT7i6C43MeHbv5bXmxEbdH2WZ', // Replace with your actual webhook secret
    ],

    // Webhook endpoints that are allowed without authentication
    'endpoints' => [
        'autodesk_subscription_webhook' => [
            'handler' => 'AutodeskSubscription::update_from_api',
            'secret_key' => 'autodesk_subscription',
            'allowed_ips' => [], // Optional: Add IP addresses to restrict access
        ],
    ],
];
