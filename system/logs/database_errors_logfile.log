[database_errors] [2025-07-11 08:26:01] [database.class.php:531] array(13) {\n  ["error_code"]: string(5) "42S22"\n  ["error_message"]: string(85) "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'sort_order' in 'order clause'"\n  ["sql_state"]: string(5) "42S22"\n  ["driver_error_code"]: int(1054)\n  ["driver_error_message"]: string(45) "Unknown column 'sort_order' in 'order clause'"\n  ["query"]: string(141) "SELECT id, parent_path, route_key, name, icon, required_roles, show_navbar FROM autobooks_navigation as nav ORDER BY sort_order ASC, name ASC"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(27) "autobooks_navigation as nav"\n  ["user_id"]: string(9) "Anonymous"\n  ["request_uri"]: string(38) "/baffletrain/autocadlt/autobooks/login"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(675) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(476): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(223): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/paths.php(272): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/startup_sequence.class.php(14): build_routes()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(22): startup_sequence::start()\n#5 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 531\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S22","error_message":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'sort_order' in 'order clause'","sql_state":"42S22","driver_error_code":1054,"driver_error_message":"Unknown column 'sort_order' in 'order clause'","query":"SELECT id, parent_path, route_key, name, icon, required_roles, show_navbar FROM autobooks_navigation as nav ORDER BY sort_order ASC, name ASC","parameters":[],"table":"autobooks_navigation as nav","user_id":"Anonymous","request_uri":"\/baffletrain\/autocadlt\/autobooks\/login","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko\/******** Firefox\/140.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(476): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(223): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/paths.php(272): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/startup_sequence.class.php(14): build_routes()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(22): startup_sequence::start()\n#5 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 481\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S22",1054,"Unknown column 'sort_order' in 'order clause'"]}\n         1: "SELECT id, parent_path, route_key, name, icon, required_roles, show_navbar FROM autobooks_navigation as nav ORDER BY sort_order ASC, name ASC"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 223\n         <strong>Arguments:</strong> \n         0: "SELECT id, parent_path, route_key, name, icon, required_roles, show_navbar FROM autobooks_navigation as nav ORDER BY sort_order ASC, name ASC"\n         1: []\n-->\n
[database_errors] [2025-07-14 20:47:25] [database.class.php:531] array(13) {\n  ["error_code"]: string(5) "42S22"\n  ["error_message"]: string(93) "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'endcust.company_name' in 'field list'"\n  ["sql_state"]: string(5) "42S22"\n  ["driver_error_code"]: int(1054)\n  ["driver_error_message"]: string(53) "Unknown column 'endcust.company_name' in 'field list'"\n  ["query"]: string(680) "SELECT quotes.*, endcust.id as endcust_id, endcust.first_name as endcust_first_name, endcust.last_name as endcust_last_name, endcust.company_name as endcust_company_name, quotecontact.id as quotecontact_id, quotecontact.first_name as quotecontact_first_name, quotecontact.last_name as quotecontact_last_name, quotecontact.company_name as quotecontact_company_name, CONCAT(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes_modified_at DESC LIMIT 30"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(25) "autodesk_quotes as quotes"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(48) "/baffletrain/autocadlt/autobooks/autodesk/quotes"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1966) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(476): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(223): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_quotes.class.php(98): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_quotes.class.php(38): autodesk_api\autodesk_quotes->database_get_current_quotes()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/quotes.fn.php(114): autodesk_api\autodesk_quotes->get_all()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/autodesk/quotes/quotes.view.php(2): generate_quote_table()\n#6 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-view.edge.php(20): include('...')\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#9 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-main.edge.php(24): edge\edge::render()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()\n#13 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(26): system\router::route()\n#14 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 531\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S22","error_message":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'endcust.company_name' in 'field list'","sql_state":"42S22","driver_error_code":1054,"driver_error_message":"Unknown column 'endcust.company_name' in 'field list'","query":"SELECT quotes.*, endcust.id as endcust_id, endcust.first_name as endcust_first_name, endcust.last_name as endcust_last_name, endcust.company_name as endcust_company_name, quotecontact.id as quotecontact_id, quotecontact.first_name as quotecontact_first_name, quotecontact.last_name as quotecontact_last_name, quotecontact.company_name as quotecontact_company_name, CONCAT(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes_modified_at DESC LIMIT 30","parameters":[],"table":"autodesk_quotes as quotes","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/autodesk\/quotes","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko\/******** Firefox\/140.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(476): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(223): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/classes\/autodesk_api\/autodesk_quotes.class.php(98): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/classes\/autodesk_api\/autodesk_quotes.class.php(38): autodesk_api\\autodesk_quotes->database_get_current_quotes()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/functions\/quotes.fn.php(114): autodesk_api\\autodesk_quotes->get_all()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/views\/autodesk\/quotes\/quotes.view.php(2): generate_quote_table()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-view.edge.php(20): include('...')\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-main.edge.php(24): edge\\edge::render()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(208): edge\\edge::render()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(26): system\\router::route()\n#14 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 481\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S22",1054,"Unknown column 'endcust.company_name' in 'field list'"]}\n         1: "SELECT quotes.*, endcust.id as endcust_id, endcust.first_name as endcust_first_name, endcust.last_name as endcust_last_name, endcust.company_name as endcust_company_name, quotecontact.id as quotecontact_id, quotecontact.first_name as quotecontact_first_name, quotecontact.last_name as quotecontact_last_name, quotecontact.company_name as quotecontact_company_name, CONCAT(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes_modified_at DESC LIMIT 30"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 223\n         <strong>Arguments:</strong> \n         0: "SELECT quotes.*, endcust.id as endcust_id, endcust.first_name as endcust_first_name, endcust.last_name as endcust_last_name, endcust.company_name as endcust_company_name, quotecontact.id as quotecontact_id, quotecontact.first_name as quotecontact_first_name, quotecontact.last_name as quotecontact_last_name, quotecontact.company_name as quotecontact_company_name, CONCAT(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes_modified_at DESC LIMIT 30"\n         1: []\n-->\n
[database_errors] [2025-07-14 20:47:35] [database.class.php:531] array(13) {\n  ["error_code"]: string(5) "42S22"\n  ["error_message"]: string(93) "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'endcust.company_name' in 'field list'"\n  ["sql_state"]: string(5) "42S22"\n  ["driver_error_code"]: int(1054)\n  ["driver_error_message"]: string(53) "Unknown column 'endcust.company_name' in 'field list'"\n  ["query"]: string(680) "SELECT quotes.*, endcust.id as endcust_id, endcust.first_name as endcust_first_name, endcust.last_name as endcust_last_name, endcust.company_name as endcust_company_name, quotecontact.id as quotecontact_id, quotecontact.first_name as quotecontact_first_name, quotecontact.last_name as quotecontact_last_name, quotecontact.company_name as quotecontact_company_name, CONCAT(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes_modified_at DESC LIMIT 30"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(25) "autodesk_quotes as quotes"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(48) "/baffletrain/autocadlt/autobooks/autodesk/quotes"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1589) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(476): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(223): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_quotes.class.php(98): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_quotes.class.php(38): autodesk_api\autodesk_quotes->database_get_current_quotes()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/quotes.fn.php(114): autodesk_api\autodesk_quotes->get_all()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/autodesk/quotes/quotes.view.php(2): generate_quote_table()\n#6 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-view.edge.php(20): include('...')\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(26): system\router::route()\n#11 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 531\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S22","error_message":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'endcust.company_name' in 'field list'","sql_state":"42S22","driver_error_code":1054,"driver_error_message":"Unknown column 'endcust.company_name' in 'field list'","query":"SELECT quotes.*, endcust.id as endcust_id, endcust.first_name as endcust_first_name, endcust.last_name as endcust_last_name, endcust.company_name as endcust_company_name, quotecontact.id as quotecontact_id, quotecontact.first_name as quotecontact_first_name, quotecontact.last_name as quotecontact_last_name, quotecontact.company_name as quotecontact_company_name, CONCAT(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes_modified_at DESC LIMIT 30","parameters":[],"table":"autodesk_quotes as quotes","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/autodesk\/quotes","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko\/******** Firefox\/140.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(476): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(223): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/classes\/autodesk_api\/autodesk_quotes.class.php(98): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/classes\/autodesk_api\/autodesk_quotes.class.php(38): autodesk_api\\autodesk_quotes->database_get_current_quotes()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/functions\/quotes.fn.php(114): autodesk_api\\autodesk_quotes->get_all()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/views\/autodesk\/quotes\/quotes.view.php(2): generate_quote_table()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-view.edge.php(20): include('...')\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(208): edge\\edge::render()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(26): system\\router::route()\n#11 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 481\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S22",1054,"Unknown column 'endcust.company_name' in 'field list'"]}\n         1: "SELECT quotes.*, endcust.id as endcust_id, endcust.first_name as endcust_first_name, endcust.last_name as endcust_last_name, endcust.company_name as endcust_company_name, quotecontact.id as quotecontact_id, quotecontact.first_name as quotecontact_first_name, quotecontact.last_name as quotecontact_last_name, quotecontact.company_name as quotecontact_company_name, CONCAT(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes_modified_at DESC LIMIT 30"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 223\n         <strong>Arguments:</strong> \n         0: "SELECT quotes.*, endcust.id as endcust_id, endcust.first_name as endcust_first_name, endcust.last_name as endcust_last_name, endcust.company_name as endcust_company_name, quotecontact.id as quotecontact_id, quotecontact.first_name as quotecontact_first_name, quotecontact.last_name as quotecontact_last_name, quotecontact.company_name as quotecontact_company_name, CONCAT(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes_modified_at DESC LIMIT 30"\n         1: []\n-->\n
[database_errors] [2025-07-14 20:47:39] [database.class.php:531] array(13) {\n  ["error_code"]: string(5) "42S22"\n  ["error_message"]: string(93) "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'endcust.company_name' in 'field list'"\n  ["sql_state"]: string(5) "42S22"\n  ["driver_error_code"]: int(1054)\n  ["driver_error_message"]: string(53) "Unknown column 'endcust.company_name' in 'field list'"\n  ["query"]: string(680) "SELECT quotes.*, endcust.id as endcust_id, endcust.first_name as endcust_first_name, endcust.last_name as endcust_last_name, endcust.company_name as endcust_company_name, quotecontact.id as quotecontact_id, quotecontact.first_name as quotecontact_first_name, quotecontact.last_name as quotecontact_last_name, quotecontact.company_name as quotecontact_company_name, CONCAT(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes_modified_at DESC LIMIT 30"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(25) "autodesk_quotes as quotes"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(48) "/baffletrain/autocadlt/autobooks/autodesk/quotes"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1589) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(476): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(223): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_quotes.class.php(98): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_quotes.class.php(38): autodesk_api\autodesk_quotes->database_get_current_quotes()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/quotes.fn.php(114): autodesk_api\autodesk_quotes->get_all()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/autodesk/quotes/quotes.view.php(2): generate_quote_table()\n#6 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-view.edge.php(20): include('...')\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(26): system\router::route()\n#11 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 531\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S22","error_message":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'endcust.company_name' in 'field list'","sql_state":"42S22","driver_error_code":1054,"driver_error_message":"Unknown column 'endcust.company_name' in 'field list'","query":"SELECT quotes.*, endcust.id as endcust_id, endcust.first_name as endcust_first_name, endcust.last_name as endcust_last_name, endcust.company_name as endcust_company_name, quotecontact.id as quotecontact_id, quotecontact.first_name as quotecontact_first_name, quotecontact.last_name as quotecontact_last_name, quotecontact.company_name as quotecontact_company_name, CONCAT(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes_modified_at DESC LIMIT 30","parameters":[],"table":"autodesk_quotes as quotes","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/autodesk\/quotes","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko\/******** Firefox\/140.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(476): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(223): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/classes\/autodesk_api\/autodesk_quotes.class.php(98): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/classes\/autodesk_api\/autodesk_quotes.class.php(38): autodesk_api\\autodesk_quotes->database_get_current_quotes()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/functions\/quotes.fn.php(114): autodesk_api\\autodesk_quotes->get_all()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/views\/autodesk\/quotes\/quotes.view.php(2): generate_quote_table()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-view.edge.php(20): include('...')\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(208): edge\\edge::render()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(26): system\\router::route()\n#11 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 481\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S22",1054,"Unknown column 'endcust.company_name' in 'field list'"]}\n         1: "SELECT quotes.*, endcust.id as endcust_id, endcust.first_name as endcust_first_name, endcust.last_name as endcust_last_name, endcust.company_name as endcust_company_name, quotecontact.id as quotecontact_id, quotecontact.first_name as quotecontact_first_name, quotecontact.last_name as quotecontact_last_name, quotecontact.company_name as quotecontact_company_name, CONCAT(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes_modified_at DESC LIMIT 30"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 223\n         <strong>Arguments:</strong> \n         0: "SELECT quotes.*, endcust.id as endcust_id, endcust.first_name as endcust_first_name, endcust.last_name as endcust_last_name, endcust.company_name as endcust_company_name, quotecontact.id as quotecontact_id, quotecontact.first_name as quotecontact_first_name, quotecontact.last_name as quotecontact_last_name, quotecontact.company_name as quotecontact_company_name, CONCAT(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes_modified_at DESC LIMIT 30"\n         1: []\n-->\n
[database_errors] [2025-07-14 20:57:34] [database.class.php:531] array(13) {\n  ["error_code"]: string(5) "42S22"\n  ["error_message"]: string(93) "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'quotes_modified_at' in 'order clause'"\n  ["sql_state"]: string(5) "42S22"\n  ["driver_error_code"]: int(1054)\n  ["driver_error_message"]: string(53) "Unknown column 'quotes_modified_at' in 'order clause'"\n  ["query"]: string(926) "SELECT quotes.*, endcust.id as endcust_id, endcust.name as endcust_name, endcust.first_name as endcust_first_name, endcust.last_name as endcust_last_name, endcust.email as endcust_email, endcust.address1 as endcust_address1, endcust.city as endcust_city, endcust.state_province as endcust_state_province, endcust.postal_code as endcust_postal_code, endcust.country as endcust_country, quotecontact.id as quotecontact_id, quotecontact.name as quotecontact_name_field, quotecontact.first_name as quotecontact_first_name, quotecontact.last_name as quotecontact_last_name, quotecontact.email as quotecontact_email, CONCAT(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes_modified_at DESC LIMIT 30"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(25) "autodesk_quotes as quotes"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(48) "/baffletrain/autocadlt/autobooks/autodesk/quotes"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1589) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(476): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(223): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_quotes.class.php(98): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_quotes.class.php(38): autodesk_api\autodesk_quotes->database_get_current_quotes()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/quotes.fn.php(114): autodesk_api\autodesk_quotes->get_all()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/autodesk/quotes/quotes.view.php(2): generate_quote_table()\n#6 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-view.edge.php(20): include('...')\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(26): system\router::route()\n#11 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 531\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S22","error_message":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'quotes_modified_at' in 'order clause'","sql_state":"42S22","driver_error_code":1054,"driver_error_message":"Unknown column 'quotes_modified_at' in 'order clause'","query":"SELECT quotes.*, endcust.id as endcust_id, endcust.name as endcust_name, endcust.first_name as endcust_first_name, endcust.last_name as endcust_last_name, endcust.email as endcust_email, endcust.address1 as endcust_address1, endcust.city as endcust_city, endcust.state_province as endcust_state_province, endcust.postal_code as endcust_postal_code, endcust.country as endcust_country, quotecontact.id as quotecontact_id, quotecontact.name as quotecontact_name_field, quotecontact.first_name as quotecontact_first_name, quotecontact.last_name as quotecontact_last_name, quotecontact.email as quotecontact_email, CONCAT(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes_modified_at DESC LIMIT 30","parameters":[],"table":"autodesk_quotes as quotes","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/autodesk\/quotes","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko\/******** Firefox\/140.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(476): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(223): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/classes\/autodesk_api\/autodesk_quotes.class.php(98): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/classes\/autodesk_api\/autodesk_quotes.class.php(38): autodesk_api\\autodesk_quotes->database_get_current_quotes()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/functions\/quotes.fn.php(114): autodesk_api\\autodesk_quotes->get_all()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/views\/autodesk\/quotes\/quotes.view.php(2): generate_quote_table()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-view.edge.php(20): include('...')\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(208): edge\\edge::render()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(26): system\\router::route()\n#11 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 481\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S22",1054,"Unknown column 'quotes_modified_at' in 'order clause'"]}\n         1: "SELECT quotes.*, endcust.id as endcust_id, endcust.name as endcust_name, endcust.first_name as endcust_first_name, endcust.last_name as endcust_last_name, endcust.email as endcust_email, endcust.address1 as endcust_address1, endcust.city as endcust_city, endcust.state_province as endcust_state_province, endcust.postal_code as endcust_postal_code, endcust.country as endcust_country, quotecontact.id as quotecontact_id, quotecontact.name as quotecontact_name_field, quotecontact.first_name as quotecontact_first_name, quotecontact.last_name as quotecontact_last_name, quotecontact.email as quotecontact_email, CONCAT(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes_modified_at DESC LIMIT 30"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 223\n         <strong>Arguments:</strong> \n         0: "SELECT quotes.*, endcust.id as endcust_id, endcust.name as endcust_name, endcust.first_name as endcust_first_name, endcust.last_name as endcust_last_name, endcust.email as endcust_email, endcust.address1 as endcust_address1, endcust.city as endcust_city, endcust.state_province as endcust_state_province, endcust.postal_code as endcust_postal_code, endcust.country as endcust_country, quotecontact.id as quotecontact_id, quotecontact.name as quotecontact_name_field, quotecontact.first_name as quotecontact_first_name, quotecontact.last_name as quotecontact_last_name, quotecontact.email as quotecontact_email, CONCAT(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes_modified_at DESC LIMIT 30"\n         1: []\n-->\n
[database_errors] [2025-07-14 20:57:35] [database.class.php:531] array(13) {\n  ["error_code"]: string(5) "42S22"\n  ["error_message"]: string(93) "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'quotes_modified_at' in 'order clause'"\n  ["sql_state"]: string(5) "42S22"\n  ["driver_error_code"]: int(1054)\n  ["driver_error_message"]: string(53) "Unknown column 'quotes_modified_at' in 'order clause'"\n  ["query"]: string(926) "SELECT quotes.*, endcust.id as endcust_id, endcust.name as endcust_name, endcust.first_name as endcust_first_name, endcust.last_name as endcust_last_name, endcust.email as endcust_email, endcust.address1 as endcust_address1, endcust.city as endcust_city, endcust.state_province as endcust_state_province, endcust.postal_code as endcust_postal_code, endcust.country as endcust_country, quotecontact.id as quotecontact_id, quotecontact.name as quotecontact_name_field, quotecontact.first_name as quotecontact_first_name, quotecontact.last_name as quotecontact_last_name, quotecontact.email as quotecontact_email, CONCAT(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes_modified_at DESC LIMIT 30"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(25) "autodesk_quotes as quotes"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(48) "/baffletrain/autocadlt/autobooks/autodesk/quotes"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1589) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(476): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(223): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_quotes.class.php(98): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_quotes.class.php(38): autodesk_api\autodesk_quotes->database_get_current_quotes()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/quotes.fn.php(114): autodesk_api\autodesk_quotes->get_all()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/autodesk/quotes/quotes.view.php(2): generate_quote_table()\n#6 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-view.edge.php(20): include('...')\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(26): system\router::route()\n#11 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 531\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S22","error_message":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'quotes_modified_at' in 'order clause'","sql_state":"42S22","driver_error_code":1054,"driver_error_message":"Unknown column 'quotes_modified_at' in 'order clause'","query":"SELECT quotes.*, endcust.id as endcust_id, endcust.name as endcust_name, endcust.first_name as endcust_first_name, endcust.last_name as endcust_last_name, endcust.email as endcust_email, endcust.address1 as endcust_address1, endcust.city as endcust_city, endcust.state_province as endcust_state_province, endcust.postal_code as endcust_postal_code, endcust.country as endcust_country, quotecontact.id as quotecontact_id, quotecontact.name as quotecontact_name_field, quotecontact.first_name as quotecontact_first_name, quotecontact.last_name as quotecontact_last_name, quotecontact.email as quotecontact_email, CONCAT(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes_modified_at DESC LIMIT 30","parameters":[],"table":"autodesk_quotes as quotes","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/autodesk\/quotes","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko\/******** Firefox\/140.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(476): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(223): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/classes\/autodesk_api\/autodesk_quotes.class.php(98): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/classes\/autodesk_api\/autodesk_quotes.class.php(38): autodesk_api\\autodesk_quotes->database_get_current_quotes()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/functions\/quotes.fn.php(114): autodesk_api\\autodesk_quotes->get_all()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/views\/autodesk\/quotes\/quotes.view.php(2): generate_quote_table()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-view.edge.php(20): include('...')\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(208): edge\\edge::render()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(26): system\\router::route()\n#11 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 481\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S22",1054,"Unknown column 'quotes_modified_at' in 'order clause'"]}\n         1: "SELECT quotes.*, endcust.id as endcust_id, endcust.name as endcust_name, endcust.first_name as endcust_first_name, endcust.last_name as endcust_last_name, endcust.email as endcust_email, endcust.address1 as endcust_address1, endcust.city as endcust_city, endcust.state_province as endcust_state_province, endcust.postal_code as endcust_postal_code, endcust.country as endcust_country, quotecontact.id as quotecontact_id, quotecontact.name as quotecontact_name_field, quotecontact.first_name as quotecontact_first_name, quotecontact.last_name as quotecontact_last_name, quotecontact.email as quotecontact_email, CONCAT(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes_modified_at DESC LIMIT 30"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 223\n         <strong>Arguments:</strong> \n         0: "SELECT quotes.*, endcust.id as endcust_id, endcust.name as endcust_name, endcust.first_name as endcust_first_name, endcust.last_name as endcust_last_name, endcust.email as endcust_email, endcust.address1 as endcust_address1, endcust.city as endcust_city, endcust.state_province as endcust_state_province, endcust.postal_code as endcust_postal_code, endcust.country as endcust_country, quotecontact.id as quotecontact_id, quotecontact.name as quotecontact_name_field, quotecontact.first_name as quotecontact_first_name, quotecontact.last_name as quotecontact_last_name, quotecontact.email as quotecontact_email, CONCAT(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes_modified_at DESC LIMIT 30"\n         1: []\n-->\n
[database_errors] [2025-07-14 20:59:23] [database.class.php:531] array(13) {\n  ["error_code"]: string(5) "42S22"\n  ["error_message"]: string(94) "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'quotes.quote_number' in 'where clause'"\n  ["sql_state"]: string(5) "42S22"\n  ["driver_error_code"]: int(1054)\n  ["driver_error_message"]: string(54) "Unknown column 'quotes.quote_number' in 'where clause'"\n  ["query"]: string(426) "SELECT quotes.*, endcust.id as endcust_id, quotecontact.id as quotecontact_id, endcust.account_csn as endcust_account_csn, quotecontact.account_csn as quotecontact_account_csn FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact WHERE `quotes.quote_number` = :where_quotes_quote_number_0"\n  ["parameters"]: array(1) {\n    [":where_quotes_quote_number_0"]: string(4) "null"\n  }\n  ["table"]: string(25) "autodesk_quotes as quotes"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/api/view"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1459) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(476): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(223): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_quotes.class.php(98): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_quotes.class.php(38): autodesk_api\autodesk_quotes->database_get_current_quotes()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/autodesk/quotes/quotes.api.php(195): autodesk_api\autodesk_quotes->get_all()\n#5 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-api.edge.php(60): api\autodesk\quotes\view()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(26): system\router::route()\n#10 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 531\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S22","error_message":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'quotes.quote_number' in 'where clause'","sql_state":"42S22","driver_error_code":1054,"driver_error_message":"Unknown column 'quotes.quote_number' in 'where clause'","query":"SELECT quotes.*, endcust.id as endcust_id, quotecontact.id as quotecontact_id, endcust.account_csn as endcust_account_csn, quotecontact.account_csn as quotecontact_account_csn FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact WHERE `quotes.quote_number` = :where_quotes_quote_number_0","parameters":{":where_quotes_quote_number_0":"null"},"table":"autodesk_quotes as quotes","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/view","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko\/******** Firefox\/140.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(476): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(223): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/classes\/autodesk_api\/autodesk_quotes.class.php(98): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/classes\/autodesk_api\/autodesk_quotes.class.php(38): autodesk_api\\autodesk_quotes->database_get_current_quotes()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/views\/autodesk\/quotes\/quotes.api.php(195): autodesk_api\\autodesk_quotes->get_all()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-api.edge.php(60): api\\autodesk\\quotes\\view()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(208): edge\\edge::render()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(26): system\\router::route()\n#10 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 481\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S22",1054,"Unknown column 'quotes.quote_number' in 'where clause'"]}\n         1: "SELECT quotes.*, endcust.id as endcust_id, quotecontact.id as quotecontact_id, endcust.account_csn as endcust_account_csn, quotecontact.account_csn as quotecontact_account_csn FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact WHERE `quotes.quote_number` = :where_quotes_quote_number_0"\n         2: {":where_quotes_quote_number_0":"null"}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 223\n         <strong>Arguments:</strong> \n         0: "SELECT quotes.*, endcust.id as endcust_id, quotecontact.id as quotecontact_id, endcust.account_csn as endcust_account_csn, quotecontact.account_csn as quotecontact_account_csn FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact WHERE `quotes.quote_number` = :where_quotes_quote_number_0"\n         1: {":where_quotes_quote_number_0":"null"}\n-->\n
[database_errors] [2025-07-14 21:00:03] [database.class.php:531] array(13) {\n  ["error_code"]: string(5) "42S22"\n  ["error_message"]: string(94) "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'quotes.quote_number' in 'where clause'"\n  ["sql_state"]: string(5) "42S22"\n  ["driver_error_code"]: int(1054)\n  ["driver_error_message"]: string(54) "Unknown column 'quotes.quote_number' in 'where clause'"\n  ["query"]: string(426) "SELECT quotes.*, endcust.id as endcust_id, quotecontact.id as quotecontact_id, endcust.account_csn as endcust_account_csn, quotecontact.account_csn as quotecontact_account_csn FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact WHERE `quotes.quote_number` = :where_quotes_quote_number_0"\n  ["parameters"]: array(1) {\n    [":where_quotes_quote_number_0"]: string(4) "null"\n  }\n  ["table"]: string(25) "autodesk_quotes as quotes"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/api/view"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1459) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(476): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(223): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_quotes.class.php(98): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_quotes.class.php(38): autodesk_api\autodesk_quotes->database_get_current_quotes()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/autodesk/quotes/quotes.api.php(195): autodesk_api\autodesk_quotes->get_all()\n#5 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-api.edge.php(60): api\autodesk\quotes\view()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(26): system\router::route()\n#10 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 531\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S22","error_message":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'quotes.quote_number' in 'where clause'","sql_state":"42S22","driver_error_code":1054,"driver_error_message":"Unknown column 'quotes.quote_number' in 'where clause'","query":"SELECT quotes.*, endcust.id as endcust_id, quotecontact.id as quotecontact_id, endcust.account_csn as endcust_account_csn, quotecontact.account_csn as quotecontact_account_csn FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact WHERE `quotes.quote_number` = :where_quotes_quote_number_0","parameters":{":where_quotes_quote_number_0":"null"},"table":"autodesk_quotes as quotes","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/view","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko\/******** Firefox\/140.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(476): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(223): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/classes\/autodesk_api\/autodesk_quotes.class.php(98): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/classes\/autodesk_api\/autodesk_quotes.class.php(38): autodesk_api\\autodesk_quotes->database_get_current_quotes()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/views\/autodesk\/quotes\/quotes.api.php(195): autodesk_api\\autodesk_quotes->get_all()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-api.edge.php(60): api\\autodesk\\quotes\\view()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(208): edge\\edge::render()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(26): system\\router::route()\n#10 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 481\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S22",1054,"Unknown column 'quotes.quote_number' in 'where clause'"]}\n         1: "SELECT quotes.*, endcust.id as endcust_id, quotecontact.id as quotecontact_id, endcust.account_csn as endcust_account_csn, quotecontact.account_csn as quotecontact_account_csn FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact WHERE `quotes.quote_number` = :where_quotes_quote_number_0"\n         2: {":where_quotes_quote_number_0":"null"}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 223\n         <strong>Arguments:</strong> \n         0: "SELECT quotes.*, endcust.id as endcust_id, quotecontact.id as quotecontact_id, endcust.account_csn as endcust_account_csn, quotecontact.account_csn as quotecontact_account_csn FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact WHERE `quotes.quote_number` = :where_quotes_quote_number_0"\n         1: {":where_quotes_quote_number_0":"null"}\n-->\n
[database_errors] [2025-07-14 21:00:11] [database.class.php:531] array(13) {\n  ["error_code"]: string(5) "42S22"\n  ["error_message"]: string(94) "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'quotes.quote_number' in 'where clause'"\n  ["sql_state"]: string(5) "42S22"\n  ["driver_error_code"]: int(1054)\n  ["driver_error_message"]: string(54) "Unknown column 'quotes.quote_number' in 'where clause'"\n  ["query"]: string(426) "SELECT quotes.*, endcust.id as endcust_id, quotecontact.id as quotecontact_id, endcust.account_csn as endcust_account_csn, quotecontact.account_csn as quotecontact_account_csn FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact WHERE `quotes.quote_number` = :where_quotes_quote_number_0"\n  ["parameters"]: array(1) {\n    [":where_quotes_quote_number_0"]: string(4) "null"\n  }\n  ["table"]: string(25) "autodesk_quotes as quotes"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/api/view"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1459) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(476): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(223): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_quotes.class.php(98): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_quotes.class.php(38): autodesk_api\autodesk_quotes->database_get_current_quotes()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/autodesk/quotes/quotes.api.php(195): autodesk_api\autodesk_quotes->get_all()\n#5 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-api.edge.php(60): api\autodesk\quotes\view()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(26): system\router::route()\n#10 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 531\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S22","error_message":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'quotes.quote_number' in 'where clause'","sql_state":"42S22","driver_error_code":1054,"driver_error_message":"Unknown column 'quotes.quote_number' in 'where clause'","query":"SELECT quotes.*, endcust.id as endcust_id, quotecontact.id as quotecontact_id, endcust.account_csn as endcust_account_csn, quotecontact.account_csn as quotecontact_account_csn FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact WHERE `quotes.quote_number` = :where_quotes_quote_number_0","parameters":{":where_quotes_quote_number_0":"null"},"table":"autodesk_quotes as quotes","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/view","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko\/******** Firefox\/140.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(476): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(223): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/classes\/autodesk_api\/autodesk_quotes.class.php(98): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/classes\/autodesk_api\/autodesk_quotes.class.php(38): autodesk_api\\autodesk_quotes->database_get_current_quotes()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/views\/autodesk\/quotes\/quotes.api.php(195): autodesk_api\\autodesk_quotes->get_all()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-api.edge.php(60): api\\autodesk\\quotes\\view()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(208): edge\\edge::render()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(26): system\\router::route()\n#10 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 481\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S22",1054,"Unknown column 'quotes.quote_number' in 'where clause'"]}\n         1: "SELECT quotes.*, endcust.id as endcust_id, quotecontact.id as quotecontact_id, endcust.account_csn as endcust_account_csn, quotecontact.account_csn as quotecontact_account_csn FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact WHERE `quotes.quote_number` = :where_quotes_quote_number_0"\n         2: {":where_quotes_quote_number_0":"null"}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 223\n         <strong>Arguments:</strong> \n         0: "SELECT quotes.*, endcust.id as endcust_id, quotecontact.id as quotecontact_id, endcust.account_csn as endcust_account_csn, quotecontact.account_csn as quotecontact_account_csn FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact WHERE `quotes.quote_number` = :where_quotes_quote_number_0"\n         1: {":where_quotes_quote_number_0":"null"}\n-->\n
[database_errors] [2025-07-14 21:39:40] [database.class.php:531] array(13) {\n  ["error_code"]: string(5) "42S22"\n  ["error_message"]: string(85) "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'quotecontact' in 'field list'"\n  ["sql_state"]: string(5) "42S22"\n  ["driver_error_code"]: int(1054)\n  ["driver_error_message"]: string(45) "Unknown column 'quotecontact' in 'field list'"\n  ["query"]: string(483) "SELECT quotes.*, endcust.id, endcust.account_csn, endcust.name, endcust.primary_admin_email, quotecontact, quotecontact.account_csn, quotecontact.first_name, quotecontact.last_name, quotecontact.email, quotecontact.first_name, quotecontact.last_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes.modified_at DESC LIMIT 30"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(25) "autodesk_quotes as quotes"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(48) "/baffletrain/autocadlt/autobooks/autodesk/quotes"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1966) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(476): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(223): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_quotes.class.php(98): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_quotes.class.php(38): autodesk_api\autodesk_quotes->database_get_current_quotes()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/quotes.fn.php(114): autodesk_api\autodesk_quotes->get_all()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/autodesk/quotes/quotes.view.php(2): generate_quote_table()\n#6 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-view.edge.php(20): include('...')\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#9 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-main.edge.php(24): edge\edge::render()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()\n#13 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(26): system\router::route()\n#14 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 531\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S22","error_message":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'quotecontact' in 'field list'","sql_state":"42S22","driver_error_code":1054,"driver_error_message":"Unknown column 'quotecontact' in 'field list'","query":"SELECT quotes.*, endcust.id, endcust.account_csn, endcust.name, endcust.primary_admin_email, quotecontact, quotecontact.account_csn, quotecontact.first_name, quotecontact.last_name, quotecontact.email, quotecontact.first_name, quotecontact.last_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes.modified_at DESC LIMIT 30","parameters":[],"table":"autodesk_quotes as quotes","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/autodesk\/quotes","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko\/******** Firefox\/140.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(476): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(223): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/classes\/autodesk_api\/autodesk_quotes.class.php(98): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/classes\/autodesk_api\/autodesk_quotes.class.php(38): autodesk_api\\autodesk_quotes->database_get_current_quotes()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/functions\/quotes.fn.php(114): autodesk_api\\autodesk_quotes->get_all()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/views\/autodesk\/quotes\/quotes.view.php(2): generate_quote_table()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-view.edge.php(20): include('...')\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-main.edge.php(24): edge\\edge::render()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(208): edge\\edge::render()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(26): system\\router::route()\n#14 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 481\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S22",1054,"Unknown column 'quotecontact' in 'field list'"]}\n         1: "SELECT quotes.*, endcust.id, endcust.account_csn, endcust.name, endcust.primary_admin_email, quotecontact, quotecontact.account_csn, quotecontact.first_name, quotecontact.last_name, quotecontact.email, quotecontact.first_name, quotecontact.last_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes.modified_at DESC LIMIT 30"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 223\n         <strong>Arguments:</strong> \n         0: "SELECT quotes.*, endcust.id, endcust.account_csn, endcust.name, endcust.primary_admin_email, quotecontact, quotecontact.account_csn, quotecontact.first_name, quotecontact.last_name, quotecontact.email, quotecontact.first_name, quotecontact.last_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes.modified_at DESC LIMIT 30"\n         1: []\n-->\n
[database_errors] [2025-07-14 21:39:46] [database.class.php:531] array(13) {\n  ["error_code"]: string(5) "42S22"\n  ["error_message"]: string(85) "SQLSTATE[42S22]: Column not found: 1054 Unknown column 'quotecontact' in 'field list'"\n  ["sql_state"]: string(5) "42S22"\n  ["driver_error_code"]: int(1054)\n  ["driver_error_message"]: string(45) "Unknown column 'quotecontact' in 'field list'"\n  ["query"]: string(483) "SELECT quotes.*, endcust.id, endcust.account_csn, endcust.name, endcust.primary_admin_email, quotecontact, quotecontact.account_csn, quotecontact.first_name, quotecontact.last_name, quotecontact.email, quotecontact.first_name, quotecontact.last_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes.modified_at DESC LIMIT 30"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(25) "autodesk_quotes as quotes"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(48) "/baffletrain/autocadlt/autobooks/autodesk/quotes"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1966) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(476): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(223): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_quotes.class.php(98): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes/autodesk_api/autodesk_quotes.class.php(38): autodesk_api\autodesk_quotes->database_get_current_quotes()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions/quotes.fn.php(114): autodesk_api\autodesk_quotes->get_all()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/autodesk/quotes/quotes.view.php(2): generate_quote_table()\n#6 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-view.edge.php(20): include('...')\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#9 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-main.edge.php(24): edge\edge::render()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()\n#13 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(26): system\router::route()\n#14 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 531\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S22","error_message":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'quotecontact' in 'field list'","sql_state":"42S22","driver_error_code":1054,"driver_error_message":"Unknown column 'quotecontact' in 'field list'","query":"SELECT quotes.*, endcust.id, endcust.account_csn, endcust.name, endcust.primary_admin_email, quotecontact, quotecontact.account_csn, quotecontact.first_name, quotecontact.last_name, quotecontact.email, quotecontact.first_name, quotecontact.last_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes.modified_at DESC LIMIT 30","parameters":[],"table":"autodesk_quotes as quotes","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/autodesk\/quotes","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko\/******** Firefox\/140.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(476): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(223): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/classes\/autodesk_api\/autodesk_quotes.class.php(98): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/classes\/autodesk_api\/autodesk_quotes.class.php(38): autodesk_api\\autodesk_quotes->database_get_current_quotes()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/functions\/quotes.fn.php(114): autodesk_api\\autodesk_quotes->get_all()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/views\/autodesk\/quotes\/quotes.view.php(2): generate_quote_table()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-view.edge.php(20): include('...')\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-main.edge.php(24): edge\\edge::render()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(208): edge\\edge::render()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(26): system\\router::route()\n#14 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 481\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S22",1054,"Unknown column 'quotecontact' in 'field list'"]}\n         1: "SELECT quotes.*, endcust.id, endcust.account_csn, endcust.name, endcust.primary_admin_email, quotecontact, quotecontact.account_csn, quotecontact.first_name, quotecontact.last_name, quotecontact.email, quotecontact.first_name, quotecontact.last_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes.modified_at DESC LIMIT 30"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 223\n         <strong>Arguments:</strong> \n         0: "SELECT quotes.*, endcust.id, endcust.account_csn, endcust.name, endcust.primary_admin_email, quotecontact, quotecontact.account_csn, quotecontact.first_name, quotecontact.last_name, quotecontact.email, quotecontact.first_name, quotecontact.last_name FROM autodesk_quotes as quotes LEFT JOIN autodesk_accounts as endcust ON endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts as quotecontact ON quotecontact.id = quotes.quote_contact ORDER BY quotes.modified_at DESC LIMIT 30"\n         1: []\n-->\n
[database_errors] [2025-07-16 19:53:06] [database.class.php:591] array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(204) "SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 1"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1064)\n  ["driver_error_message"]: string(148) "You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 1"\n  ["query"]: string(42) "DELETE FROM autobooks_bluebeam_data WHERE "\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(23) "autobooks_bluebeam_data"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(95) "/baffletrain/autocadlt/autobooks/api/system/nav_tree/delete_nav_entry?parent_path=&key=bluebeam"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1067) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(476): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(345): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(846): system\database->delete()\n#3 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-api.edge.php(60): api\nav_tree\delete_nav_entry()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(26): system\router::route()\n#8 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 591\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 1","sql_state":"42000","driver_error_code":1064,"driver_error_message":"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 1","query":"DELETE FROM autobooks_bluebeam_data WHERE ","parameters":[],"table":"autobooks_bluebeam_data","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/system\/nav_tree\/delete_nav_entry?parent_path=&key=bluebeam","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko\/******** Firefox\/140.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(476): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(345): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(846): system\\database->delete()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-api.edge.php(60): api\\nav_tree\\delete_nav_entry()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(208): edge\\edge::render()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(26): system\\router::route()\n#8 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 481\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1064,"You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '' at line 1"]}\n         1: "DELETE FROM autobooks_bluebeam_data WHERE "\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 345\n         <strong>Arguments:</strong> \n         0: "DELETE FROM autobooks_bluebeam_data WHERE "\n         1: []\n-->\n
