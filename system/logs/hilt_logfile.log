[hilt] [2025-06-21 12:11:07] [hilt.class.php:32]  Processing hilt template: database for key: testy
[hilt] [2025-06-21 12:11:07] [hilt.class.php:122] Error creating table autobooks_testy_data: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1
[hilt] [2025-06-21 12:11:07] [hilt.class.php:71]  Failed to create table: autobooks_testy_data
[hilt] [2025-06-21 12:22:58] [hilt.class.php:32]  Processing hilt template: database for key: test1322
[hilt] [2025-06-21 12:22:58] [hilt.class.php:122] Error creating table autobooks_test1322_data: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1
[hilt] [2025-06-21 12:22:58] [hilt.class.php:71]  Failed to create table: autobooks_test1322_data
[hilt] [2025-06-30 11:54:00] [hilt.class.php:32]  Processing hilt template: database for key: terp
[hilt] [2025-06-30 11:54:00] [hilt.class.php:122] Error creating table autobooks_terp_data: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1
[hilt] [2025-06-30 11:54:00] [hilt.class.php:71]  Failed to create table: autobooks_terp_data
[hilt] [2025-07-16 15:14:22] [hilt.class.php:33]  Processing hilt template: database for key: Sketchup
[hilt] [2025-07-16 15:14:22] [hilt.class.php:117] Table autobooks_Sketchup_data will be created by enhanced import process
[hilt] [2025-07-16 15:14:22] [hilt.class.php:140] Processing uploaded CSV file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//Sketchup_csv_file_1752678861.csv
[hilt] [2025-07-16 15:14:22] [hilt.class.php:96]  Successfully processed database hilt template for key: Sketchup
[hilt] [2025-07-16 15:43:08] [hilt.class.php:33]  Processing hilt template: database for key: bluebeam
[hilt] [2025-07-16 15:43:08] [hilt.class.php:117] Table autobooks_bluebeam_data will be created by enhanced import process
[hilt] [2025-07-16 15:43:08] [hilt.class.php:140] Processing uploaded CSV file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//bluebeam_csv_file_1752680587.csv
[hilt] [2025-07-16 15:43:09] [hilt.class.php:96]  Successfully processed database hilt template for key: bluebeam
[hilt] [2025-07-16 23:25:49] [hilt.class.php:33]  Processing hilt template: html for key: sketch
[hilt] [2025-07-16 23:25:49] [hilt.class.php:300] Successfully processed HTML hilt template for key: sketch
[hilt] [2025-07-17 09:09:07] [hilt.class.php:33]  Processing hilt template: database for key: sketchup
[hilt] [2025-07-17 09:09:07] [hilt.class.php:117] Table autobooks_sketchup_data will be created by enhanced import process
[hilt] [2025-07-17 09:09:07] [hilt.class.php:140] Processing uploaded CSV file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//sketchup_csv_file_1752743346.csv
[hilt] [2025-07-17 09:09:07] [hilt.class.php:96]  Successfully processed database hilt template for key: sketchup
[hilt] [2025-07-17 23:09:57] [hilt.class.php:33]  Processing hilt template: database for key: Bluebeam
[hilt] [2025-07-17 23:09:57] [hilt.class.php:117] Table autobooks_Bluebeam_data will be created by enhanced import process
[hilt] [2025-07-17 23:09:57] [hilt.class.php:140] Processing uploaded CSV file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//Bluebeam_csv_file_1752793796.csv
[hilt] [2025-07-17 23:09:58] [hilt.class.php:96]  Successfully processed database hilt template for key: Bluebeam
[hilt] [2025-07-18 00:27:47] [hilt.class.php:33]  Processing hilt template: database for key: Bluebeam
[hilt] [2025-07-18 00:27:47] [hilt.class.php:117] Table autobooks_Bluebeam_data will be created by enhanced import process
[hilt] [2025-07-18 00:27:47] [hilt.class.php:140] Processing uploaded CSV file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//Bluebeam_csv_file_1752798466.csv
[hilt] [2025-07-18 00:27:48] [hilt.class.php:96]  Successfully processed database hilt template for key: Bluebeam
[hilt] [2025-07-18 00:40:20] [hilt.class.php:33]  Processing hilt template: database for key: bluebeam
[hilt] [2025-07-18 00:40:20] [hilt.class.php:117] Table autobooks_bluebeam_data will be created by enhanced import process
[hilt] [2025-07-18 00:40:20] [hilt.class.php:140] Processing uploaded CSV file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//bluebeam_csv_file_1752799219.csv
[hilt] [2025-07-18 00:40:20] [hilt.class.php:96]  Successfully processed database hilt template for key: bluebeam
[hilt] [2025-07-18 00:45:14] [hilt.class.php:33]  Processing hilt template: database for key: bluebeamtest2
[hilt] [2025-07-18 00:45:14] [hilt.class.php:117] Table autobooks_bluebeamtest2_data will be created by enhanced import process
[hilt] [2025-07-18 00:45:14] [hilt.class.php:140] Processing uploaded CSV file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//bluebeamtest2_csv_file_1752799513.csv
[hilt] [2025-07-18 00:45:15] [hilt.class.php:96]  Successfully processed database hilt template for key: bluebeamtest2
[hilt] [2025-07-18 00:49:12] [hilt.class.php:33]  Processing hilt template: database for key: bb3
[hilt] [2025-07-18 00:49:12] [hilt.class.php:117] Table autobooks_bb3_data will be created by enhanced import process
[hilt] [2025-07-18 00:49:12] [hilt.class.php:140] Processing uploaded CSV file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//bb3_csv_file_1752799751.csv
[hilt] [2025-07-18 00:49:12] [hilt.class.php:96]  Successfully processed database hilt template for key: bb3
[hilt] [2025-07-18 00:52:43] [hilt.class.php:33]  Processing hilt template: database for key: BB4
[hilt] [2025-07-18 00:52:43] [hilt.class.php:117] Table autobooks_BB4_data will be created by enhanced import process
[hilt] [2025-07-18 00:52:43] [hilt.class.php:140] Processing uploaded CSV file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//BB4_csv_file_1752799962.csv
[hilt] [2025-07-18 00:52:43] [hilt.class.php:96]  Successfully processed database hilt template for key: BB4
[hilt] [2025-07-18 00:55:54] [hilt.class.php:33]  Processing hilt template: database for key: bb5
[hilt] [2025-07-18 00:55:54] [hilt.class.php:117] Table autobooks_bb5_data will be created by enhanced import process
[hilt] [2025-07-18 00:55:54] [hilt.class.php:140] Processing uploaded CSV file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//bb5_csv_file_1752800153.csv
[hilt] [2025-07-18 00:55:55] [hilt.class.php:96]  Successfully processed database hilt template for key: bb5
[hilt] [2025-07-21 23:40:47] [hilt.class.php:33]  Processing hilt template: html for key: test
[hilt] [2025-07-21 23:40:47] [hilt.class.php:300] Successfully processed HTML hilt template for key: test
