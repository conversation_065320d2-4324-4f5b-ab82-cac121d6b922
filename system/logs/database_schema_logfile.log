[database_schema] [2025-07-16 15:14:22] [database.class.php:1225] Array\n(\n    [query] => CREATE TABLE autobooks_Sketchup_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `sold_to_name` VA<PERSON>HA<PERSON>(255) NULL, `sold_to_number` INT NULL, `vendor_name` VA<PERSON>HAR(255) NULL, `reseller_number` INT NULL, `reseller_vendor_id` VARCHAR(255) NULL, `end_customer_vendor_id` INT NULL, `end_customer_name` VARCHAR(255) NULL, `end_customer_address_1` VARCHAR(255) NULL, `end_customer_address_2` VA<PERSON>HAR(255) NULL, `end_customer_address_3` VARCHAR(255) NULL, `end_customer_city` VARCHAR(255) NULL, `end_customer_state` VARCHAR(255) NULL, `end_customer_zip_code` VARCHAR(255) NULL, `end_customer_country` VARCHAR(255) NULL, `end_customer_account_type` VARC<PERSON><PERSON>(255) NULL, `end_customer_contact_name` VA<PERSON>HAR(255) NULL, `end_customer_contact_email` VARC<PERSON>R(255) NULL, `end_customer_contact_phone` VARCHAR(255) NULL, `end_customer_industry_segment` VARCHAR(255) NULL, `agreement_program_name` VARCHAR(255) NULL, `agreement_number` INT NULL, `agreement_start_date` VARCHAR(255) NULL, `agreement_end_date` VARCHAR(255) NULL, `agreement_terms` VARCHAR(255) NULL, `agreement_type` VARCHAR(255) NULL, `agreement_status` VARCHAR(255) NULL, `agreement_support_level` VARCHAR(255) NULL, `agreement_days_due` INT NULL, `agreement_autorenew` INT NULL, `product_name` VARCHAR(255) NULL, `product_family` VARCHAR(255) NULL, `product_market_segment` VARCHAR(255) NULL, `product_release` VARCHAR(255) NULL, `product_type` VARCHAR(255) NULL, `product_deployment` VARCHAR(255) NULL, `product_sku` VARCHAR(255) NULL, `product_sku_description` VARCHAR(255) NULL, `product_part` VARCHAR(255) NULL, `product_list_price` INT NULL, `product_list_price_currency` VARCHAR(255) NULL, `subscription_id` VARCHAR(255) NULL, `subscription_serial_number` VARCHAR(255) NULL, `subscription_status` VARCHAR(255) NULL, `subscription_quantity` INT NULL, `subscription_start_date` VARCHAR(255) NULL, `subscription_end_date` VARCHAR(255) NULL, `subscription_contact_name` VARCHAR(255) NULL, `subscription_contact_email` VARCHAR(255) NULL, `subscription_level` VARCHAR(255) NULL, `subscription_days_due` INT NULL, `quotation_id` VARCHAR(255) NULL, `quotation_type` VARCHAR(255) NULL, `quotation_vendor_id` INT NULL, `quotation_deal_registration_number` VARCHAR(255) NULL, `quotation_status` VARCHAR(255) NULL, `quotation_resellerpo_previous` VARCHAR(255) NULL, `quotation_due_date` VARCHAR(255) NULL, `flaer_phase` VARCHAR(255) NULL, `updated` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_Sketchup_data\n    [type] => CREATE\n    [timestamp] => 2025-07-16 15:14:22\n)\n
[database_schema] [2025-07-16 15:14:22] [database.class.php:1225] Array\n(\n    [query] => CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))\n    [table] => autobooks_table_configs\n    [type] => CREATE\n    [timestamp] => 2025-07-16 15:14:22\n)\n
[database_schema] [2025-07-16 15:43:08] [database.class.php:1225] Array\n(\n    [query] => CREATE TABLE autobooks_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-07-16 15:43:08\n)\n
[database_schema] [2025-07-16 15:43:09] [database.class.php:1225] Array\n(\n    [query] => CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))\n    [table] => autobooks_table_configs\n    [type] => CREATE\n    [timestamp] => 2025-07-16 15:43:09\n)\n
[database_schema] [2025-07-17 09:09:07] [database.class.php:1301] Array\n(\n    [query] => CREATE TABLE autobooks_sketchup_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `sold_to_name` VARCHAR(255) NULL, `sold_to_number` INT NULL, `vendor_name` VARCHAR(255) NULL, `reseller_number` INT NULL, `reseller_vendor_id` VARCHAR(255) NULL, `end_customer_vendor_id` INT NULL, `end_customer_name` VARCHAR(255) NULL, `end_customer_address_1` VARCHAR(255) NULL, `end_customer_address_2` VARCHAR(255) NULL, `end_customer_address_3` VARCHAR(255) NULL, `end_customer_city` VARCHAR(255) NULL, `end_customer_state` VARCHAR(255) NULL, `end_customer_zip_code` VARCHAR(255) NULL, `end_customer_country` VARCHAR(255) NULL, `end_customer_account_type` VARCHAR(255) NULL, `end_customer_contact_name` VARCHAR(255) NULL, `end_customer_contact_email` VARCHAR(255) NULL, `end_customer_contact_phone` VARCHAR(255) NULL, `end_customer_industry_segment` VARCHAR(255) NULL, `agreement_program_name` VARCHAR(255) NULL, `agreement_number` INT NULL, `agreement_start_date` VARCHAR(255) NULL, `agreement_end_date` VARCHAR(255) NULL, `agreement_terms` VARCHAR(255) NULL, `agreement_type` VARCHAR(255) NULL, `agreement_status` VARCHAR(255) NULL, `agreement_support_level` VARCHAR(255) NULL, `agreement_days_due` INT NULL, `agreement_autorenew` INT NULL, `product_name` VARCHAR(255) NULL, `product_family` VARCHAR(255) NULL, `product_market_segment` VARCHAR(255) NULL, `product_release` VARCHAR(255) NULL, `product_type` VARCHAR(255) NULL, `product_deployment` VARCHAR(255) NULL, `product_sku` VARCHAR(255) NULL, `product_sku_description` VARCHAR(255) NULL, `product_part` VARCHAR(255) NULL, `product_list_price` INT NULL, `product_list_price_currency` VARCHAR(255) NULL, `subscription_id` VARCHAR(255) NULL, `subscription_serial_number` VARCHAR(255) NULL, `subscription_status` VARCHAR(255) NULL, `subscription_quantity` INT NULL, `subscription_start_date` VARCHAR(255) NULL, `subscription_end_date` VARCHAR(255) NULL, `subscription_contact_name` VARCHAR(255) NULL, `subscription_contact_email` VARCHAR(255) NULL, `subscription_level` VARCHAR(255) NULL, `subscription_days_due` INT NULL, `quotation_id` VARCHAR(255) NULL, `quotation_type` VARCHAR(255) NULL, `quotation_vendor_id` INT NULL, `quotation_deal_registration_number` VARCHAR(255) NULL, `quotation_status` VARCHAR(255) NULL, `quotation_resellerpo_previous` VARCHAR(255) NULL, `quotation_due_date` VARCHAR(255) NULL, `flaer_phase` VARCHAR(255) NULL, `updated` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_sketchup_data\n    [type] => CREATE\n    [timestamp] => 2025-07-17 09:09:07\n)\n
[database_schema] [2025-07-17 09:09:07] [database.class.php:1301] Array\n(\n    [query] => CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))\n    [table] => autobooks_table_configs\n    [type] => CREATE\n    [timestamp] => 2025-07-17 09:09:07\n)\n
[database_schema] [2025-07-17 23:09:57] [database.class.php:1301] Array\n(\n    [query] => CREATE TABLE autobooks_Bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_Bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-07-17 23:09:57\n)\n
[database_schema] [2025-07-17 23:09:58] [database.class.php:1301] Array\n(\n    [query] => CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))\n    [table] => autobooks_table_configs\n    [type] => CREATE\n    [timestamp] => 2025-07-17 23:09:58\n)\n
[database_schema] [2025-07-18 00:27:47] [database.class.php:1301] Array\n(\n    [query] => CREATE TABLE autobooks_Bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_Bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-07-18 00:27:47\n)\n
[database_schema] [2025-07-18 00:27:48] [database.class.php:1301] Array\n(\n    [query] => CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))\n    [table] => autobooks_table_configs\n    [type] => CREATE\n    [timestamp] => 2025-07-18 00:27:48\n)\n
[database_schema] [2025-07-18 00:40:20] [database.class.php:1301] Array\n(\n    [query] => CREATE TABLE autobooks_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-07-18 00:40:20\n)\n
[database_schema] [2025-07-18 00:40:20] [database.class.php:1301] Array\n(\n    [query] => CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))\n    [table] => autobooks_table_configs\n    [type] => CREATE\n    [timestamp] => 2025-07-18 00:40:20\n)\n
[database_schema] [2025-07-18 00:45:14] [database.class.php:1301] Array\n(\n    [query] => CREATE TABLE autobooks_bluebeamtest2_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_bluebeamtest2_data\n    [type] => CREATE\n    [timestamp] => 2025-07-18 00:45:14\n)\n
[database_schema] [2025-07-18 00:45:15] [database.class.php:1301] Array\n(\n    [query] => CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))\n    [table] => autobooks_table_configs\n    [type] => CREATE\n    [timestamp] => 2025-07-18 00:45:15\n)\n
[database_schema] [2025-07-18 00:49:12] [database.class.php:1301] Array\n(\n    [query] => CREATE TABLE autobooks_bb3_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_bb3_data\n    [type] => CREATE\n    [timestamp] => 2025-07-18 00:49:12\n)\n
[database_schema] [2025-07-18 00:49:12] [database.class.php:1301] Array\n(\n    [query] => CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))\n    [table] => autobooks_table_configs\n    [type] => CREATE\n    [timestamp] => 2025-07-18 00:49:12\n)\n
[database_schema] [2025-07-18 00:52:43] [database.class.php:1301] Array\n(\n    [query] => CREATE TABLE autobooks_BB4_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_BB4_data\n    [type] => CREATE\n    [timestamp] => 2025-07-18 00:52:43\n)\n
[database_schema] [2025-07-18 00:52:43] [database.class.php:1301] Array\n(\n    [query] => CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))\n    [table] => autobooks_table_configs\n    [type] => CREATE\n    [timestamp] => 2025-07-18 00:52:43\n)\n
[database_schema] [2025-07-18 00:55:54] [database.class.php:1301] Array\n(\n    [query] => CREATE TABLE autobooks_bb5_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_bb5_data\n    [type] => CREATE\n    [timestamp] => 2025-07-18 00:55:54\n)\n
[database_schema] [2025-07-18 00:55:55] [database.class.php:1301] Array\n(\n    [query] => CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))\n    [table] => autobooks_table_configs\n    [type] => CREATE\n    [timestamp] => 2025-07-18 00:55:55\n)\n
