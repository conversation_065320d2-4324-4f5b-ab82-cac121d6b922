[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-23 13:10:34
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 8179c096-4b82-4899-b1f6-1f837e51bb3c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68364773573404\n            [status] => Active\n            [quantity] => 2\n            [endDate] => 2026-06-21\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-23T12:50:28.000+0000\n        )\n\n    [publishedAt] => 2025-06-23T13:10:31.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68364773573404', status = 'Active', quantity = 2, endDate = '2026-06-21', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '68364773573404', status = 'Active', quantity = 2, endDate = '2026-06-21', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-23 13:10:34
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 2318c8c1-4385-4be4-a9e7-4e7a82f2f6f6\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62435123126924\n            [status] => Active\n            [quantity] => 3\n            [endDate] => 2026-06-21\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-23T12:50:29.000+0000\n        )\n\n    [publishedAt] => 2025-06-23T13:10:32.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-23 13:10:34] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62435123126924', status = 'Active', quantity = 3, endDate = '2026-06-21', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '62435123126924', status = 'Active', quantity = 3, endDate = '2026-06-21', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-23 13:10:37] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-23 13:10:37
[subscription_update] [2025-06-23 13:10:37] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-23 13:10:37] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => c7e59421-9aab-4216-9344-178bca49f9f8\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71265719119871\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-06-21\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-23T12:50:33.000+0000\n        )\n\n    [publishedAt] => 2025-06-23T13:10:35.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-23 13:10:37] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-23 13:10:37] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-23 13:10:37] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-23 13:10:37] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-23 13:10:37] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-23 13:10:37] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-23 13:10:37] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-23 13:10:37] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-23 13:10:37] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71265719119871', status = 'Active', quantity = 1, endDate = '2026-06-21', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '71265719119871', status = 'Active', quantity = 1, endDate = '2026-06-21', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-23 14:08:04] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-23 14:08:04
[subscription_update] [2025-06-23 14:08:04] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-23 14:08:04] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => f42c56aa-1537-481d-8397-d3b272d89b28\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59316980792360\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-06-29\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-23T13:42:59.000+0000\n        )\n\n    [publishedAt] => 2025-06-23T14:08:01.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-23 14:08:04] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-23 14:08:04] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-23 14:08:04] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-23 14:08:04] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-23 14:08:04] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-23 14:08:04] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-23 14:08:04] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-23 14:08:04] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-23 14:08:04] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59316980792360', status = 'Active', quantity = 1, endDate = '2026-06-29', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '59316980792360', status = 'Active', quantity = 1, endDate = '2026-06-29', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-23 15:37:22] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-23 15:37:22
[subscription_update] [2025-06-23 15:37:22] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-23 15:37:22] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 995ad212-1e9e-4dfa-81f0-2f09e5f69c8d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59316980792360\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-23T15:07:09.000+0000\n        )\n\n    [publishedAt] => 2025-06-23T15:37:20.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-23 15:37:22] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-23 15:37:22] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-23 15:37:22] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-23 15:37:22] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-23 15:37:22] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59316980792360', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '59316980792360', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-23 15:37:45] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-23 15:37:45
[subscription_update] [2025-06-23 15:37:45] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-23 15:37:45] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => c95123f3-f9e0-4a3f-8040-a02ba6f8b7e7\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62435123126924\n            [quantity] => 3\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-23T15:07:30.000+0000\n        )\n\n    [publishedAt] => 2025-06-23T15:37:42.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-23 15:37:45] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-23 15:37:45] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-23 15:37:45] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-23 15:37:45] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-23 15:37:45] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62435123126924', quantity = 3 ON DUPLICATE KEY UPDATE subscriptionId = '62435123126924', quantity = 3;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-23 15:38:10] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-23 15:38:10
[subscription_update] [2025-06-23 15:38:10] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-23 15:38:10] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 6bd00a59-34f5-4e1e-8b1a-1e49b5f9497c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75068468067413\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-23T15:12:58.000+0000\n        )\n\n    [publishedAt] => 2025-06-23T15:38:07.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-23 15:38:10] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-06-23 15:38:18] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75068468067413\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-06-23\n            [endDate] => 2026-06-22\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => LOC\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Right Heat Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Right Heat Ltd\n                            [type] => End Customer\n                            [address1] => Unit 12 Nep Business Park Stratford Road\n                            [address2] => Pattinson Industrial Estate\n                            [address3] => \n                            [city] => Washington\n                            [stateProvince] => TYNE AND WEAR\n                            [postalCode] => NE38 8QP\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Construction Services\n                            [primaryAdminFirstName] => Mark\n                            [primaryAdminLastName] => Theakston\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 8253012\n                            [teamName] => Mark Theakston - 3012\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Mark\n                            [last] => Theakston\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Registered\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => \n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-06-23 15:38:18] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-06-23 15:38:31] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-23 15:38:31
[subscription_update] [2025-06-23 15:38:31] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-23 15:38:31] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => f2a30a35-9a8d-4bda-bafd-ea0781f4d8d1\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75068580316322\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-23T15:13:18.000+0000\n        )\n\n    [publishedAt] => 2025-06-23T15:38:29.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-23 15:38:31] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-06-23 15:38:33] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-23 15:38:33
[subscription_update] [2025-06-23 15:38:33] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-23 15:38:33] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => ce898e1c-9f20-493d-be0c-4815899ae6a9\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75068615514382\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-23T15:13:18.000+0000\n        )\n\n    [publishedAt] => 2025-06-23T15:38:31.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-23 15:38:33] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-06-23 15:38:35] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75068580316322\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-06-23\n            [endDate] => 2026-06-22\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Johnson Design Partnership Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Johnson Design Partnership Ltd\n                            [type] => End Customer\n                            [address1] => Johnson Design Partnership\n                            [address2] => \n                            [address3] => \n                            [city] => Bridgnorth\n                            [stateProvince] => SHROPSHIRE\n                            [postalCode] => WV16 5DP\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Architecture Services\n                            [primaryAdminFirstName] => M\n                            [primaryAdminLastName] => Spinks\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 8059259\n                            [teamName] => M Spinks - 9259\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => M\n                            [last] => Spinks\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Registered\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-06-23 15:38:35] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-06-23 15:38:36] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75068615514382\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2020-06-30\n            [endDate] => 2025-06-29\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => LOC\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => OFF\n            [offeringId] => OD-000234\n            [offeringCode] => PDCOLL\n            [offeringName] => Product Design & MFG Collection\n            [marketingName] => Product Design & Manufacturing Collection\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Denis Welch Motorsport\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Denis Welch Motorsport\n                            [type] => End Customer\n                            [address1] => Yoxall\n                            [address2] => \n                            [address3] => \n                            [city] => Burton On Trent\n                            [stateProvince] => \n                            [postalCode] => DE13 8NA\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => M&E\n                            [parentIndustrySegment] => Games\n                            [primaryAdminFirstName] => Victoria\n                            [primaryAdminLastName] => Kerr\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => ********\n                            [teamName] => Linda Shaw - 2228\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Victoria\n                            [last] => Kerr\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Registered\n                            [doNotCall] => 1\n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-06-23 15:38:36] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-06-23 15:38:47] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-23 15:38:47
[subscription_update] [2025-06-23 15:38:47] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-23 15:38:47] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 4d1397fa-eb19-4e5d-8c4d-09d8d2e5ec26\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68364773573404\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-23T15:08:37.000+0000\n        )\n\n    [publishedAt] => 2025-06-23T15:38:45.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-23 15:38:47] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-23 15:38:47] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-23 15:38:47] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-23 15:38:47] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-23 15:38:47] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68364773573404', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '68364773573404', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-23 15:39:41] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-23 15:39:41
[subscription_update] [2025-06-23 15:39:41] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-23 15:39:41] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 4ab0817a-0238-4c23-83c1-1db124d3012a\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71265719119871\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-23T15:09:33.000+0000\n        )\n\n    [publishedAt] => 2025-06-23T15:39:39.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-23 15:39:41] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-23 15:39:41] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-23 15:39:41] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-23 15:39:41] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-23 15:39:41] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71265719119871', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '71265719119871', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-23 16:36:37] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-23 16:36:37
[subscription_update] [2025-06-23 16:36:37] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-23 16:36:37] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 7f7f7f8e-6fa2-48e1-b3ff-94581c517a88\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65511946667839\n            [status] => Active\n            [quantity] => 2\n            [endDate] => 2026-06-12\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-23T16:11:31.000+0000\n        )\n\n    [publishedAt] => 2025-06-23T16:36:33.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-23 16:36:37] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-23 16:36:37] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-23 16:36:37] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-23 16:36:37] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-23 16:36:37] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-23 16:36:37] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-23 16:36:37] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-23 16:36:37] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-23 16:36:37] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65511946667839', status = 'Active', quantity = 2, endDate = '2026-06-12', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '65511946667839', status = 'Active', quantity = 2, endDate = '2026-06-12', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-23 19:39:33] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-23 19:39:33
[subscription_update] [2025-06-23 19:39:33] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-23 19:39:33] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => e6937c80-98a0-4aeb-8e9a-3b9d6263df83\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65511946667839\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-23T19:09:24.000+0000\n        )\n\n    [publishedAt] => 2025-06-23T19:39:31.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-23 19:39:33] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-23 19:39:33] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-23 19:39:33] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-23 19:39:33] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-23 19:39:33] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65511946667839', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '65511946667839', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-24 08:41:01] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-24 08:41:01
[subscription_update] [2025-06-24 08:41:01] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-24 08:41:01] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => bb8eb979-a1c2-4cc1-a9fb-961e2a0bf537\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72018003980494\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-07-04\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-24T08:23:41.000+0000\n        )\n\n    [publishedAt] => 2025-06-24T08:40:58.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-24 08:41:01] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-24 08:41:01] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-24 08:41:01] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-24 08:41:01] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-24 08:41:01] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-24 08:41:01] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-24 08:41:01] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-24 08:41:01] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-24 08:41:01] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72018003980494', status = 'Active', quantity = 1, endDate = '2026-07-04', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '72018003980494', status = 'Active', quantity = 1, endDate = '2026-07-04', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-24 08:41:05] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-24 08:41:05
[subscription_update] [2025-06-24 08:41:05] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-24 08:41:05] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => d101c93d-feab-45fd-8f85-478fbbca69e9\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72258911056117\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-01\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-24T08:23:43.000+0000\n        )\n\n    [publishedAt] => 2025-06-24T08:41:03.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-24 08:41:05] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-24 08:41:05] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-24 08:41:05] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-24 08:41:05] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-24 08:41:05] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-24 08:41:05] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-24 08:41:05] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-24 08:41:05] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-24 08:41:05] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72258911056117', status = 'Active', quantity = 1, endDate = '2026-08-01', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '72258911056117', status = 'Active', quantity = 1, endDate = '2026-08-01', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-24 10:38:35] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-24 10:38:35
[subscription_update] [2025-06-24 10:38:35] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-24 10:38:35] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 75a614d5-2952-438c-991f-3f56de0c675c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62454685260520\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-06-23\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-24T09:44:52.000+0000\n        )\n\n    [publishedAt] => 2025-06-24T10:38:33.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-24 10:38:35] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-24 10:38:35] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-24 10:38:35] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-24 10:38:35] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-24 10:38:35] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-24 10:38:35] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-24 10:38:35] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-24 10:38:35] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-24 10:38:35] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62454685260520', status = 'Active', quantity = 1, endDate = '2026-06-23', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '62454685260520', status = 'Active', quantity = 1, endDate = '2026-06-23', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-24 12:18:36] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-24 12:18:36
[subscription_update] [2025-06-24 12:18:36] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-24 12:18:36] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 36b494e6-6ba9-4f8c-9440-e13e5fe3053d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62454685260520\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-24T11:13:18.000+0000\n        )\n\n    [publishedAt] => 2025-06-24T12:18:34.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-24 12:18:36] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-24 12:18:36] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-24 12:18:36] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-24 12:18:36] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-24 12:18:36] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62454685260520', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '62454685260520', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-24 12:22:28] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-24 12:22:28
[subscription_update] [2025-06-24 12:22:28] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-24 12:22:28] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 0b5c3128-13df-4039-9d19-9394970b7b43\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72018003980494\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-24T11:15:58.000+0000\n        )\n\n    [publishedAt] => 2025-06-24T12:22:26.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-24 12:22:28] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-24 12:22:28] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-24 12:22:28] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-24 12:22:28] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-24 12:22:28] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72018003980494', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '72018003980494', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-24 12:23:05] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-24 12:23:05
[subscription_update] [2025-06-24 12:23:05] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-24 12:23:05] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => b659c7c1-4843-4670-9a8b-67d357c6e76b\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72258911056117\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-24T11:15:58.000+0000\n        )\n\n    [publishedAt] => 2025-06-24T12:23:02.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-24 12:23:05] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-24 12:23:05] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-24 12:23:05] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-24 12:23:05] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-24 12:23:05] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72258911056117', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '72258911056117', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-24 14:15:14] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-24 14:15:14
[subscription_update] [2025-06-24 14:15:14] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-24 14:15:14] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => e0e54fad-1d28-4b5d-9c34-7b7614db9e9c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68976522429040\n            [status] => Active\n            [quantity] => 2\n            [endDate] => 2026-07-18\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-24T14:00:08.000+0000\n        )\n\n    [publishedAt] => 2025-06-24T14:15:12.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-24 14:15:14] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-24 14:15:14] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-24 14:15:14] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-24 14:15:14] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-24 14:15:14] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-24 14:15:14] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-24 14:15:14] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-24 14:15:14] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-24 14:15:14] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68976522429040', status = 'Active', quantity = 2, endDate = '2026-07-18', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '68976522429040', status = 'Active', quantity = 2, endDate = '2026-07-18', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-24 14:15:27] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-24 14:15:27
[subscription_update] [2025-06-24 14:15:27] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-24 14:15:27] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 5d632c7d-f35a-4f9d-bb09-b27e5ac1eb15\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65789219589573\n            [status] => Active\n            [quantity] => 13\n            [endDate] => 2026-07-24\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-24T14:00:21.000+0000\n        )\n\n    [publishedAt] => 2025-06-24T14:15:24.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-24 14:15:27] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-24 14:15:27] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-24 14:15:27] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-24 14:15:27] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-24 14:15:27] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-24 14:15:27] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-24 14:15:27] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-24 14:15:27] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-24 14:15:27] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65789219589573', status = 'Active', quantity = 13, endDate = '2026-07-24', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '65789219589573', status = 'Active', quantity = 13, endDate = '2026-07-24', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-24 14:15:28] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-24 14:15:28
[subscription_update] [2025-06-24 14:15:28] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-24 14:15:28] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 3039e8a3-d76b-4b60-9271-7c5ac04540f3\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 70480501681875\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-07-24\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-24T14:00:23.000+0000\n        )\n\n    [publishedAt] => 2025-06-24T14:15:26.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-24 14:15:28] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-24 14:15:28] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-24 14:15:28] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-24 14:15:28] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-24 14:15:28] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-24 14:15:28] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-24 14:15:28] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-24 14:15:28] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-24 14:15:28] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '70480501681875', status = 'Active', quantity = 1, endDate = '2026-07-24', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '70480501681875', status = 'Active', quantity = 1, endDate = '2026-07-24', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-24 14:15:30] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-24 14:15:30
[subscription_update] [2025-06-24 14:15:30] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-24 14:15:30] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 25555422-50e2-4a77-a790-fcf6fa0f038d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65789219597474\n            [status] => Active\n            [quantity] => 2\n            [endDate] => 2026-07-24\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-24T14:00:25.000+0000\n        )\n\n    [publishedAt] => 2025-06-24T14:15:28.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-24 14:15:30] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-24 14:15:30] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-24 14:15:30] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-24 14:15:30] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-24 14:15:30] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-24 14:15:30] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-24 14:15:30] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-24 14:15:30] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-24 14:15:30] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65789219597474', status = 'Active', quantity = 2, endDate = '2026-07-24', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '65789219597474', status = 'Active', quantity = 2, endDate = '2026-07-24', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-24 14:15:35] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-24 14:15:35
[subscription_update] [2025-06-24 14:15:35] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-24 14:15:35] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => b48f255d-dc36-4645-b523-ccbd5d666169\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 67949004297855\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-07-24\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-24T14:00:29.000+0000\n        )\n\n    [publishedAt] => 2025-06-24T14:15:32.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-24 14:15:35] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-24 14:15:35] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-24 14:15:35] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-24 14:15:35] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-24 14:15:35] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-24 14:15:35] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-24 14:15:35] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-24 14:15:35] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-24 14:15:35] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '67949004297855', status = 'Active', quantity = 1, endDate = '2026-07-24', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '67949004297855', status = 'Active', quantity = 1, endDate = '2026-07-24', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-24 14:15:39] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-24 14:15:39
[subscription_update] [2025-06-24 14:15:39] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-24 14:15:39] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 98ef60e2-cc7b-4aba-8a56-92e35b1b9e49\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68173774923922\n            [status] => Active\n            [quantity] => 3\n            [endDate] => 2026-07-24\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-24T14:00:34.000+0000\n        )\n\n    [publishedAt] => 2025-06-24T14:15:37.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-24 14:15:39] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-24 14:15:39] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-24 14:15:39] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-24 14:15:39] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-24 14:15:39] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-24 14:15:39] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-24 14:15:39] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-24 14:15:39] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-24 14:15:39] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68173774923922', status = 'Active', quantity = 3, endDate = '2026-07-24', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '68173774923922', status = 'Active', quantity = 3, endDate = '2026-07-24', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-24 15:35:28] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-24 15:35:28
[subscription_update] [2025-06-24 15:35:28] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-24 15:35:28] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 0608264b-d36e-460b-98f9-2181bd93ef30\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75076465560597\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-24T15:10:19.000+0000\n        )\n\n    [publishedAt] => 2025-06-24T15:35:26.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-24 15:35:28] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-06-24 15:35:31] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75076465560597\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-06-24\n            [endDate] => 2026-06-23\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => **********\n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Allwater Technologies\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Allwater Technologies\n                            [type] => End Customer\n                            [address1] => Cheddar Business Park Wedmore Road\n                            [address2] => \n                            [address3] => \n                            [city] => Cheddar\n                            [stateProvince] => SOMERSET\n                            [postalCode] => BS27 3EB\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => OTH\n                            [parentIndustrySegment] => Other\n                            [primaryAdminFirstName] => Susan\n                            [primaryAdminLastName] => Fisher\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 8011048\n                            [teamName] => Susan Fisher - 1048\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Kate\n                            [last] => Wisniewski\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => \n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => \n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-06-24 15:35:31] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-06-24 15:37:41] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-24 15:37:41
[subscription_update] [2025-06-24 15:37:41] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-24 15:37:41] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 0c1c9124-a2dc-4adb-905f-e1069393b09e\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65789219589573\n            [quantity] => 13\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-24T15:07:33.000+0000\n        )\n\n    [publishedAt] => 2025-06-24T15:37:39.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-24 15:37:41] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-24 15:37:41] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-24 15:37:41] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-24 15:37:41] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-24 15:37:41] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65789219589573', quantity = 13 ON DUPLICATE KEY UPDATE subscriptionId = '65789219589573', quantity = 13;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-24 15:37:41] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-24 15:37:41
[subscription_update] [2025-06-24 15:37:41] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-24 15:37:41] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 2d966f34-2e92-43eb-ab12-1baa0447af59\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65789219597474\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-24T15:07:33.000+0000\n        )\n\n    [publishedAt] => 2025-06-24T15:37:39.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-24 15:37:41] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-24 15:37:41] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-24 15:37:41] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-24 15:37:41] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-24 15:37:41] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65789219597474', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '65789219597474', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-24 15:37:56] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-24 15:37:56
[subscription_update] [2025-06-24 15:37:56] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-24 15:37:56] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 2a0018c3-a38d-44dc-91c9-809195088a07\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 67949004297855\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-24T15:07:44.000+0000\n        )\n\n    [publishedAt] => 2025-06-24T15:37:54.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-24 15:37:56] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-24 15:37:56] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-24 15:37:56] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-24 15:37:56] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-24 15:37:56] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '67949004297855', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '67949004297855', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-24 15:38:06] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-24 15:38:06
[subscription_update] [2025-06-24 15:38:06] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-24 15:38:06] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => c8b39ea9-213e-48d0-9d7b-b18bdab71ddc\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68173774923922\n            [quantity] => 3\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-24T15:07:57.000+0000\n        )\n\n    [publishedAt] => 2025-06-24T15:38:04.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-24 15:38:06] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-24 15:38:06] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-24 15:38:06] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-24 15:38:06] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-24 15:38:06] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68173774923922', quantity = 3 ON DUPLICATE KEY UPDATE subscriptionId = '68173774923922', quantity = 3;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-24 15:38:49] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-24 15:38:49
[subscription_update] [2025-06-24 15:38:49] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-24 15:38:49] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 1fb42cde-7c09-4190-ad39-19fea28203d6\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68976522429040\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-24T15:08:40.000+0000\n        )\n\n    [publishedAt] => 2025-06-24T15:38:47.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-24 15:38:49] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-24 15:38:49] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-24 15:38:49] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-24 15:38:49] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-24 15:38:49] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68976522429040', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '68976522429040', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-24 15:38:58] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-24 15:38:58
[subscription_update] [2025-06-24 15:38:58] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-24 15:38:58] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => c45460ce-a9ac-4fb1-af68-d2ce7387bdff\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 70480501681875\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-24T15:08:50.000+0000\n        )\n\n    [publishedAt] => 2025-06-24T15:38:56.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-24 15:38:58] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-24 15:38:58] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-24 15:38:58] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-24 15:38:58] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-24 15:38:58] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '70480501681875', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '70480501681875', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-25 06:39:57] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-25 06:39:57
[subscription_update] [2025-06-25 06:39:57] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-25 06:39:57] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 3ec8bb34-f44a-48db-98ae-d523c96d9a49\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 74721888887917\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-06-25T06:24:53.000+0000\n        )\n\n    [publishedAt] => 2025-06-25T06:39:55.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-25 06:39:57] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-25 06:39:57] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-25 06:39:57] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-25 06:39:57] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-25 06:39:57] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74721888887917', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '74721888887917', status = 'Active';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-25 07:08:41] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-25 07:08:41
[subscription_update] [2025-06-25 07:08:41] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-25 07:08:41] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => ee50ba75-50b1-49be-9265-82c8c952a171\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75068468067413\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-25T06:33:37.000+0000\n        )\n\n    [publishedAt] => 2025-06-25T07:08:39.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-25 07:08:41] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-25 07:08:41] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-25 07:08:41] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-25 07:08:41] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-25 07:08:41] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75068468067413', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '75068468067413', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-25 10:08:57] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-25 10:08:57
[subscription_update] [2025-06-25 10:08:57] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-25 10:08:57] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => c715bed3-d5a4-4466-8c30-cf4359f4c36d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68813806592431\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-06-29\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-25T09:48:48.000+0000\n        )\n\n    [publishedAt] => 2025-06-25T10:08:55.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-25 10:08:57] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-25 10:08:57] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-25 10:08:57] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-25 10:08:57] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-25 10:08:57] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-25 10:08:57] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-25 10:08:57] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-25 10:08:57] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-25 10:08:57] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68813806592431', status = 'Active', quantity = 1, endDate = '2026-06-29', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '68813806592431', status = 'Active', quantity = 1, endDate = '2026-06-29', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-25 10:09:24] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-25 10:09:24
[subscription_update] [2025-06-25 10:09:24] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-25 10:09:24] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 2f288550-abbc-4ae1-9006-bfa90e2b9c47\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71749894321329\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-06-03\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-25T09:44:19.000+0000\n        )\n\n    [publishedAt] => 2025-06-25T10:09:22.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-25 10:09:24] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-25 10:09:24] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-25 10:09:24] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-25 10:09:24] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-25 10:09:24] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-25 10:09:24] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-25 10:09:24] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-25 10:09:24] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-25 10:09:24] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71749894321329', status = 'Active', quantity = 1, endDate = '2026-06-03', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '71749894321329', status = 'Active', quantity = 1, endDate = '2026-06-03', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-25 10:36:32] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-25 10:36:32
[subscription_update] [2025-06-25 10:36:32] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-25 10:36:32] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => f623a164-afe7-4ecf-9693-5a2f4e6a35c5\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72113207696067\n            [status] => Active\n            [quantity] => 2\n            [endDate] => 2026-07-15\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-25T10:01:28.000+0000\n        )\n\n    [publishedAt] => 2025-06-25T10:36:30.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-25 10:36:32] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-25 10:36:32] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-25 10:36:32] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-25 10:36:32] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-25 10:36:32] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-25 10:36:32] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-25 10:36:32] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-25 10:36:32] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-25 10:36:32] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72113207696067', status = 'Active', quantity = 2, endDate = '2026-07-15', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '72113207696067', status = 'Active', quantity = 2, endDate = '2026-07-15', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-25 11:37:55] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-25 11:37:55
[subscription_update] [2025-06-25 11:37:55] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-25 11:37:55] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 1ecc64b7-1f9e-4386-a0bf-8fb53eed3af0\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68813806592431\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-25T11:17:43.000+0000\n        )\n\n    [publishedAt] => 2025-06-25T11:37:53.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-25 11:37:55] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-25 11:37:55] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-25 11:37:55] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-25 11:37:55] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-25 11:37:55] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68813806592431', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '68813806592431', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-25 11:38:37] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-25 11:38:37
[subscription_update] [2025-06-25 11:38:37] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-25 11:38:37] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 70aa6413-e549-4157-8656-8a6cd8a4aa4f\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71749894321329\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-25T11:18:22.000+0000\n        )\n\n    [publishedAt] => 2025-06-25T11:38:35.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-25 11:38:37] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-25 11:38:37] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-25 11:38:37] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-25 11:38:37] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-25 11:38:37] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71749894321329', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '71749894321329', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-25 11:39:01] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-25 11:39:01
[subscription_update] [2025-06-25 11:39:01] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-25 11:39:01] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 01a4d382-0aea-4bc9-957e-09dd0eb23db4\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72113207696067\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-25T11:18:53.000+0000\n        )\n\n    [publishedAt] => 2025-06-25T11:38:59.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-25 11:39:01] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-25 11:39:01] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-25 11:39:01] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-25 11:39:01] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-25 11:39:01] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72113207696067', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '72113207696067', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-25 11:39:07] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-25 11:39:07
[subscription_update] [2025-06-25 11:39:07] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-25 11:39:07] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 406f437d-0ff4-4495-93af-148776feaff8\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56534717291798\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-08\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-25T11:09:03.000+0000\n        )\n\n    [publishedAt] => 2025-06-25T11:39:05.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-25 11:39:07] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-25 11:39:07] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-25 11:39:07] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-25 11:39:07] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-25 11:39:07] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-25 11:39:07] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-25 11:39:07] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-25 11:39:07] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-25 11:39:07] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56534717291798', status = 'Active', quantity = 1, endDate = '2026-08-08', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '56534717291798', status = 'Active', quantity = 1, endDate = '2026-08-08', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-25 11:40:06] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-25 11:40:06
[subscription_update] [2025-06-25 11:40:06] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-25 11:40:06] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => a1322bde-8133-4b32-ab63-b21fb641e921\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75083737136666\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-25T11:19:56.000+0000\n        )\n\n    [publishedAt] => 2025-06-25T11:40:04.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-25 11:40:06] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-06-25 11:40:09] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-25 11:40:09
[subscription_update] [2025-06-25 11:40:09] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-25 11:40:09] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 41042175-10ca-431f-a64f-bbd07acf987a\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56534717291798\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-25T11:14:59.000+0000\n        )\n\n    [publishedAt] => 2025-06-25T11:40:06.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-25 11:40:09] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-25 11:40:09] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-25 11:40:09] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-25 11:40:09] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-25 11:40:09] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56534717291798', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '56534717291798', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-25 11:40:09] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75083737136666\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-06-25\n            [endDate] => 2025-10-25\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => **********\n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Vida Design Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Vida Design Ltd\n                            [type] => End Customer\n                            [address1] => 1 Victoria Street\n                            [address2] => \n                            [address3] => \n                            [city] => Bristol\n                            [stateProvince] => AVON\n                            [postalCode] => BS1 6AA\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Engineering Service Providers\n                            [primaryAdminFirstName] => Joe\n                            [primaryAdminLastName] => Dobson\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => ********\n                            [teamName] => Joe Dobson - 2558\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Joe\n                            [last] => Dobson\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => \n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => \n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-06-25 11:40:09] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-06-25 14:37:17] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-25 14:37:17
[subscription_update] [2025-06-25 14:37:17] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-25 14:37:17] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 0a76ae69-5377-4b3e-94d3-e0dda03c933f\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56389941173065\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-07-22\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-25T14:07:12.000+0000\n        )\n\n    [publishedAt] => 2025-06-25T14:37:14.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-25 14:37:17] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-25 14:37:17] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-25 14:37:17] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-25 14:37:17] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-25 14:37:17] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-25 14:37:17] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-25 14:37:17] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-25 14:37:17] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-25 14:37:17] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56389941173065', status = 'Active', quantity = 1, endDate = '2026-07-22', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '56389941173065', status = 'Active', quantity = 1, endDate = '2026-07-22', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-25 14:39:46] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-25 14:39:46
[subscription_update] [2025-06-25 14:39:46] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-25 14:39:46] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 6d49b5f9-0ff8-458f-adef-8724300b18ff\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59318155228436\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-07-25\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-25T14:14:42.000+0000\n        )\n\n    [publishedAt] => 2025-06-25T14:39:44.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-25 14:39:46] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-25 14:39:46] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-25 14:39:46] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-25 14:39:46] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-25 14:39:46] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-25 14:39:46] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-25 14:39:46] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-25 14:39:46] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-25 14:39:46] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59318155228436', status = 'Active', quantity = 1, endDate = '2026-07-25', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '59318155228436', status = 'Active', quantity = 1, endDate = '2026-07-25', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-25 15:38:05] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-25 15:38:05
[subscription_update] [2025-06-25 15:38:05] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-25 15:38:05] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 61ed64ba-ad7f-4085-8af9-841e2547dfd7\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56389941173065\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-25T15:07:56.000+0000\n        )\n\n    [publishedAt] => 2025-06-25T15:38:03.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-25 15:38:05] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-25 15:38:05] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-25 15:38:05] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-25 15:38:05] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-25 15:38:05] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '56389941173065', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '56389941173065', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-25 15:38:57] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-25 15:38:57
[subscription_update] [2025-06-25 15:38:57] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-25 15:38:57] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 8cbff6be-ae73-409b-99dc-ada71fbc20ea\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59318155228436\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-25T15:08:37.000+0000\n        )\n\n    [publishedAt] => 2025-06-25T15:38:55.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-25 15:38:57] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-25 15:38:57] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-25 15:38:57] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-25 15:38:57] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-25 15:38:57] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59318155228436', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '59318155228436', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-26 06:41:31] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-26 06:41:31
[subscription_update] [2025-06-26 06:41:31] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-26 06:41:31] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 214384ba-b0f8-49d7-9e03-013d08b7568c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62970883476457\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-06-26T06:26:26.000+0000\n        )\n\n    [publishedAt] => 2025-06-26T06:41:28.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-26 06:41:31] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-26 06:41:31] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-26 06:41:31] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-26 06:41:31] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-26 06:41:31] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62970883476457', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '62970883476457', status = 'Active';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-26 09:06:10] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-26 09:06:10
[subscription_update] [2025-06-26 09:06:10] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-26 09:06:10] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => c10bac82-6bb3-46a4-bb9f-74e367f2a03d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68811454343295\n            [autoRenew] => OFF\n            [message] => subscription autoRenew changed.\n            [modifiedAt] => 2025-06-26T08:51:04.000+0000\n        )\n\n    [publishedAt] => 2025-06-26T09:06:07.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-26 09:06:10] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-26 09:06:10] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-26 09:06:10] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-26 09:06:10] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-26 09:06:10] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68811454343295', autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '68811454343295', autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-26 09:08:47] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-26 09:08:47
[subscription_update] [2025-06-26 09:08:47] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-26 09:08:47] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => f7ff3f59-e411-4a3f-a4b8-374af14cfabb\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68811454343295\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-06-29\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,term changed.\n            [modifiedAt] => 2025-06-26T08:48:42.000+0000\n        )\n\n    [publishedAt] => 2025-06-26T09:08:44.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-26 09:08:47] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-26 09:08:47] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-26 09:08:47] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-26 09:08:47] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-26 09:08:47] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-26 09:08:47] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-26 09:08:47] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-26 09:08:47] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68811454343295', status = 'Active', quantity = 1, endDate = '2026-06-29', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '68811454343295', status = 'Active', quantity = 1, endDate = '2026-06-29', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-26 11:40:43] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-26 11:40:43
[subscription_update] [2025-06-26 11:40:43] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-26 11:40:43] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 5d74b9f2-aea6-44ef-9a3a-be5099d132ab\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68811454343295\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-26T11:15:25.000+0000\n        )\n\n    [publishedAt] => 2025-06-26T11:40:41.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-26 11:40:43] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-26 11:40:43] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-26 11:40:43] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-26 11:40:43] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-26 11:40:43] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68811454343295', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '68811454343295', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-26 15:11:28] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-26 15:11:28
[subscription_update] [2025-06-26 15:11:28] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-26 15:11:28] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 1bd6348e-085b-4db4-b8fe-9899910a3ce9\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71957415909353\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-06-27\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-26T14:46:22.000+0000\n        )\n\n    [publishedAt] => 2025-06-26T15:11:24.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-26 15:11:28] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-26 15:11:28] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-26 15:11:28] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-26 15:11:28] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-26 15:11:28] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-26 15:11:28] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-26 15:11:28] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-26 15:11:28] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-26 15:11:28] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71957415909353', status = 'Active', quantity = 1, endDate = '2026-06-27', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '71957415909353', status = 'Active', quantity = 1, endDate = '2026-06-27', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-26 15:36:54] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-26 15:36:54
[subscription_update] [2025-06-26 15:36:54] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-26 15:36:54] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 49b6df10-490f-48cb-b78b-b29329459ccc\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55448144379007\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-06-27\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-26T15:16:50.000+0000\n        )\n\n    [publishedAt] => 2025-06-26T15:36:52.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-26 15:36:54] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-26 15:36:54] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-26 15:36:54] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-26 15:36:54] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-26 15:36:54] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-26 15:36:54] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-26 15:36:54] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-26 15:36:54] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-26 15:36:54] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '55448144379007', status = 'Active', quantity = 1, endDate = '2026-06-27', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '55448144379007', status = 'Active', quantity = 1, endDate = '2026-06-27', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-26 15:37:52] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-26 15:37:52
[subscription_update] [2025-06-26 15:37:52] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-26 15:37:52] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 47fc1a9d-a7e8-4757-8709-81edd68a2b98\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75094100203858\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-26T15:12:43.000+0000\n        )\n\n    [publishedAt] => 2025-06-26T15:37:50.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-26 15:37:52] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-06-26 15:37:56] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75094100203858\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-06-26\n            [endDate] => 2026-06-25\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => Surrey Tech Services Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => Surrey Tech Services Ltd\n                            [type] => End Customer\n                            [address1] => Beasleys Ait Fordbridge Road\n                            [address2] => \n                            [address3] => \n                            [city] => Sunbury-On-Thames\n                            [stateProvince] => \n                            [postalCode] => TW16 6AS\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Construction Services\n                            [primaryAdminFirstName] => Dan\n                            [primaryAdminLastName] => Parsons\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => ********\n                            [teamName] => Dan Parsons - 4755\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Dan\n                            [last] => Parsons\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Active\n                            [doNotCall] => 1\n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-06-26 15:37:56] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-06-26 15:40:52] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-26 15:40:52
[subscription_update] [2025-06-26 15:40:52] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-26 15:40:52] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 8abf0ced-999f-4226-9476-c7f25b351456\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71957415909353\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-26T15:10:43.000+0000\n        )\n\n    [publishedAt] => 2025-06-26T15:40:50.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-26 15:40:52] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-26 15:40:52] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-26 15:40:52] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-26 15:40:52] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-26 15:40:52] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71957415909353', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '71957415909353', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-26 19:40:22] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-26 19:40:22
[subscription_update] [2025-06-26 19:40:22] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-26 19:40:22] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => db99e78b-b044-4eec-a28d-44434c50dfe6\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55448144379007\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-26T19:10:11.000+0000\n        )\n\n    [publishedAt] => 2025-06-26T19:40:20.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-26 19:40:22] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-26 19:40:22] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-26 19:40:22] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-26 19:40:22] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-26 19:40:22] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '55448144379007', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '55448144379007', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-27 06:09:38] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-27 06:09:38
[subscription_update] [2025-06-27 06:09:38] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-27 06:09:38] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 9e15a7a4-1d44-4a3d-a69c-1b7773019a4c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62126034080827\n            [status] => Expired\n            [quantity] => 2\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-06-27T05:44:33.000+0000\n        )\n\n    [publishedAt] => 2025-06-27T06:09:36.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-27 06:09:38] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-27 06:09:38] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-27 06:09:38] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-27 06:09:38] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-27 06:09:38] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-27 06:09:38] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62126034080827', status = 'Expired', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '62126034080827', status = 'Expired', quantity = 2;\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-27 06:09:39] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-27 06:09:39
[subscription_update] [2025-06-27 06:09:39] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-27 06:09:39] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 1a0c5971-f7fa-4377-a09d-8b82a6176b07\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71586989467064\n            [status] => Expired\n            [quantity] => 1\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-06-27T05:44:34.000+0000\n        )\n\n    [publishedAt] => 2025-06-27T06:09:37.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-27 06:09:39] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-27 06:09:39] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-27 06:09:39] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-27 06:09:39] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-27 06:09:39] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-27 06:09:39] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71586989467064', status = 'Expired', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '71586989467064', status = 'Expired', quantity = 1;\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-27 07:10:04] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-27 07:10:04
[subscription_update] [2025-06-27 07:10:04] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-27 07:10:04] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => ed143eab-17e9-4231-b53f-d6797c464df3\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68811454343295\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-27T06:44:59.000+0000\n        )\n\n    [publishedAt] => 2025-06-27T07:10:02.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-27 07:10:04] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-27 07:10:04] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-27 07:10:04] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-27 07:10:04] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-27 07:10:04] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68811454343295', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '68811454343295', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-27 09:44:44] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-27 09:44:44
[subscription_update] [2025-06-27 09:44:44] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-27 09:44:44] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 618241d6-15b3-498f-b6df-4a1c69c39c83\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59411479608927\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-07-06\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-27T09:29:40.000+0000\n        )\n\n    [publishedAt] => 2025-06-27T09:44:42.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-27 09:44:45] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-27 09:44:45] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-27 09:44:45] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-27 09:44:45] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-27 09:44:45] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-27 09:44:45] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-27 09:44:45] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-27 09:44:45] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-27 09:44:45] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59411479608927', status = 'Active', quantity = 1, endDate = '2026-07-06', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '59411479608927', status = 'Active', quantity = 1, endDate = '2026-07-06', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-27 11:08:30] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-27 11:08:30
[subscription_update] [2025-06-27 11:08:30] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-27 11:08:30] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 003435ff-678c-4a04-9073-1b4a9e64f0d5\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62496438093967\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-06-28\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-27T10:48:24.000+0000\n        )\n\n    [publishedAt] => 2025-06-27T11:08:27.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-27 11:08:30] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-27 11:08:30] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-27 11:08:30] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-27 11:08:30] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-27 11:08:30] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-27 11:08:30] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-27 11:08:30] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-27 11:08:30] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-27 11:08:30] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62496438093967', status = 'Active', quantity = 1, endDate = '2026-06-28', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '62496438093967', status = 'Active', quantity = 1, endDate = '2026-06-28', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-27 11:36:53] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-27 11:36:53
[subscription_update] [2025-06-27 11:36:53] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-27 11:36:53] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => a4690ef8-f9b5-4484-a98e-7ed58c37b652\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59411479608927\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-27T11:16:43.000+0000\n        )\n\n    [publishedAt] => 2025-06-27T11:36:51.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-27 11:36:53] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-27 11:36:53] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-27 11:36:53] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-27 11:36:53] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-27 11:36:53] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59411479608927', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '59411479608927', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-27 11:37:27] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-27 11:37:27
[subscription_update] [2025-06-27 11:37:27] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-27 11:37:27] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => ca56cdc5-a9f4-4a9f-ab28-d99f1692ed5e\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62496438093967\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-27T11:17:14.000+0000\n        )\n\n    [publishedAt] => 2025-06-27T11:37:24.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-27 11:37:27] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-27 11:37:27] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-27 11:37:27] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-27 11:37:27] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-27 11:37:27] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62496438093967', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '62496438093967', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-27 11:37:29] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-27 11:37:29
[subscription_update] [2025-06-27 11:37:29] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-27 11:37:29] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => e179849c-f3a6-487d-b72b-ad4183471c7c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55448138769464\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-07-04\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-27T11:22:24.000+0000\n        )\n\n    [publishedAt] => 2025-06-27T11:37:26.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-27 11:37:29] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-27 11:37:29] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-27 11:37:29] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-27 11:37:29] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-27 11:37:29] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-27 11:37:29] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-27 11:37:29] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-27 11:37:29] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-27 11:37:29] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '55448138769464', status = 'Active', quantity = 1, endDate = '2026-07-04', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '55448138769464', status = 'Active', quantity = 1, endDate = '2026-07-04', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-27 14:37:10] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-27 14:37:10
[subscription_update] [2025-06-27 14:37:10] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-27 14:37:10] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => db66f412-93c4-4c50-8120-13c12eb51305\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56899581400448\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-07-02\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-27T14:22:05.000+0000\n        )\n\n    [publishedAt] => 2025-06-27T14:37:08.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-27 14:37:10] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-06-27 14:37:14] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 56899581400448\n            [subscriptionReferenceNumber] => 566-78221538\n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2019-09-22\n            [endDate] => 2026-07-02\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000280\n            [offeringCode] => RVTLTS\n            [offeringName] => AutoCAD Revit LT Suite\n            [marketingName] => AutoCAD Revit LT Suite\n            [currency] => \n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => KETSTONE ARCHITECTURE\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => KETSTONE ARCHITECTURE\n                            [type] => End Customer\n                            [address1] => Normanby Gateway Lysaghts Way\n                            [address2] => \n                            [address3] => \n                            [city] => Scunthorpe\n                            [stateProvince] => \n                            [postalCode] => DN15 9YG\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Architecture Services\n                            [primaryAdminFirstName] => Jon\n                            [primaryAdminLastName] => Bayley\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 8198979\n                            [teamName] => Jon Bayley - 8979\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Jon\n                            [last] => Bayley\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Registered\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-06-27 14:37:14] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-06-27 15:38:34] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-27 15:38:34
[subscription_update] [2025-06-27 15:38:34] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-27 15:38:34] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 2989de8d-ec3b-48a0-9614-df83389d71a0\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75102602641708\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-27T15:08:26.000+0000\n        )\n\n    [publishedAt] => 2025-06-27T15:38:32.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-27 15:38:34] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-06-27 15:38:38] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75102602641708\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-06-27\n            [endDate] => 2026-06-26\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => MICAM Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => MICAM Ltd\n                            [type] => End Customer\n                            [address1] => Aviator Way\n                            [address2] => \n                            [address3] => \n                            [city] => Manchester\n                            [stateProvince] => Greater Manchester\n                            [postalCode] => M22 5TG\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => \n                            [parentIndustryGroup] => \n                            [parentIndustrySegment] => \n                            [primaryAdminFirstName] => Pat\n                            [primaryAdminLastName] => Dunlea\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => ********\n                            [teamName] => Pat Dunlea - 3747\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Pat\n                            [last] => Dunlea\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => \n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => \n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-06-27 15:38:38] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-06-27 15:38:46] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-27 15:38:46
[subscription_update] [2025-06-27 15:38:46] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-27 15:38:46] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 5bf83c51-a355-4510-8230-bd96e950f113\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55448138769464\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-27T15:03:36.000+0000\n        )\n\n    [publishedAt] => 2025-06-27T15:38:44.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-27 15:38:46] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-27 15:38:46] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-27 15:38:46] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-27 15:38:46] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-27 15:38:46] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '55448138769464', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '55448138769464', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-27 15:39:09] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-27 15:39:09
[subscription_update] [2025-06-27 15:39:09] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-27 15:39:09] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => faa8b1d5-ab8a-4824-9676-935f0c0b7d47\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 56899581400448\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-27T15:03:59.000+0000\n        )\n\n    [publishedAt] => 2025-06-27T15:39:07.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-27 15:39:09] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-06-27 15:39:12] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 56899581400448\n            [subscriptionReferenceNumber] => 566-78221538\n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2019-09-22\n            [endDate] => 2026-07-02\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000280\n            [offeringCode] => RVTLTS\n            [offeringName] => AutoCAD Revit LT Suite\n            [marketingName] => AutoCAD Revit LT Suite\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => KETSTONE ARCHITECTURE\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => KETSTONE ARCHITECTURE\n                            [type] => End Customer\n                            [address1] => Normanby Gateway Lysaghts Way\n                            [address2] => \n                            [address3] => \n                            [city] => Scunthorpe\n                            [stateProvince] => \n                            [postalCode] => DN15 9YG\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Architecture Services\n                            [primaryAdminFirstName] => Jon\n                            [primaryAdminLastName] => Bayley\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 8198979\n                            [teamName] => Jon Bayley - 8979\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Jon\n                            [last] => Bayley\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Registered\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-06-27 15:39:12] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-06-27 15:40:50] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-27 15:40:50
[subscription_update] [2025-06-27 15:40:50] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-27 15:40:50] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 6eefe754-770b-4776-9505-9ba5a88b02b7\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75103518754837\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-27T15:10:38.000+0000\n        )\n\n    [publishedAt] => 2025-06-27T15:40:48.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-27 15:40:50] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-06-27 15:40:54] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75103518754837\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                            [0] => 56346356602218\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Inactive\n            [startDate] => 2025-07-18\n            [endDate] => 2028-07-17\n            [term] => 3 Year\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => JAMESDODDRELL:ARCHITECT Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => JAMESDODDRELL:ARCHITECT Ltd\n                            [type] => End Customer\n                            [address1] => 11 Kidborough Down\n                            [address2] => Great Bookham\n                            [address3] => \n                            [city] => Leatherhead\n                            [stateProvince] => SURREY\n                            [postalCode] => KT23 4LG\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Architecture Services\n                            [primaryAdminFirstName] => James\n                            [primaryAdminLastName] => Doddrell\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 7714390\n                            [teamName] => James Doddrell - 4390\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => james\n                            [last] => james\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => \n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => \n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-06-27 15:40:54] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-06-28 06:10:18] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-28 06:10:18
[subscription_update] [2025-06-28 06:10:18] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-28 06:10:18] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 6d1c30c1-93d2-46f6-bdab-3521c5899e32\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68543958470650\n            [status] => Expired\n            [quantity] => 5\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-06-28T05:50:14.000+0000\n        )\n\n    [publishedAt] => 2025-06-28T06:10:16.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-28 06:10:18] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-28 06:10:18] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-28 06:10:18] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-28 06:10:18] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-28 06:10:18] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-28 06:10:18] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68543958470650', status = 'Expired', quantity = 5 ON DUPLICATE KEY UPDATE subscriptionId = '68543958470650', status = 'Expired', quantity = 5;\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-29 19:42:30] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-29 19:42:30
[subscription_update] [2025-06-29 19:42:30] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-29 19:42:30] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => eab7ece2-ca18-47e7-8641-1c1f00a999cd\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55955752615641\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-06-02\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-29T19:27:25.000+0000\n        )\n\n    [publishedAt] => 2025-06-29T19:42:27.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-29 19:42:30] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-29 19:42:30] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-29 19:42:30] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-29 19:42:30] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-29 19:42:30] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-29 19:42:30] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-29 19:42:30] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-29 19:42:30] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-29 19:42:30] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '55955752615641', status = 'Active', quantity = 1, endDate = '2026-06-02', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '55955752615641', status = 'Active', quantity = 1, endDate = '2026-06-02', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-29 23:36:47] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-29 23:36:47
[subscription_update] [2025-06-29 23:36:47] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-29 23:36:47] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 941b5a0f-fa10-41de-b7d9-71ac88c4f193\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55955752615641\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-06-29T23:06:38.000+0000\n        )\n\n    [publishedAt] => 2025-06-29T23:36:44.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-29 23:36:47] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-29 23:36:47] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-29 23:36:47] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-29 23:36:47] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-29 23:36:47] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '55955752615641', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '55955752615641', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-06-30 00:06:59] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-30 00:06:59
[subscription_update] [2025-06-30 00:06:59] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-30 00:06:59] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 4181dbbe-5442-4ec7-8095-31f2f48e6d4f\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75068615514382\n            [status] => Expired\n            [quantity] => 1\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-06-29T23:51:54.000+0000\n        )\n\n    [publishedAt] => 2025-06-30T00:06:56.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-30 00:06:59] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-30 00:06:59] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-30 00:06:59] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-30 00:06:59] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-30 00:06:59] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-30 00:06:59] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75068615514382', status = 'Expired', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '75068615514382', status = 'Expired', quantity = 1;\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-30 06:08:32] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-30 06:08:32
[subscription_update] [2025-06-30 06:08:32] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-30 06:08:32] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => ed2a41cf-d605-4045-aabe-0d283b5903e5\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 74471577906522\n            [status] => Canceled\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-06-30T05:38:28.000+0000\n        )\n\n    [publishedAt] => 2025-06-30T06:08:30.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-30 06:08:32] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-30 06:08:32] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-30 06:08:32] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-30 06:08:32] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-30 06:08:32] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74471577906522', status = 'Canceled' ON DUPLICATE KEY UPDATE subscriptionId = '74471577906522', status = 'Canceled';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-30 06:08:33] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-30 06:08:33
[subscription_update] [2025-06-30 06:08:33] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-30 06:08:33] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 4a121430-efdf-4435-834f-6b13caa30dd4\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55602855255426\n            [status] => Canceled\n            [endDate] => 2025-04-22\n            [autoRenew] => OFF\n            [message] => subscription status,endDate,autoRenew changed.\n            [modifiedAt] => 2025-06-30T05:38:29.000+0000\n        )\n\n    [publishedAt] => 2025-06-30T06:08:31.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-30 06:08:33] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-30 06:08:33] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-30 06:08:33] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-30 06:08:33] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-30 06:08:33] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-30 06:08:33] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-30 06:08:33] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '55602855255426', status = 'Canceled', endDate = '2025-04-22', autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '55602855255426', status = 'Canceled', endDate = '2025-04-22', autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-30 06:08:35] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-30 06:08:35
[subscription_update] [2025-06-30 06:08:35] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-30 06:08:35] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => c121462c-dbd4-468a-b115-c14b2d70a5a7\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 66127147458564\n            [status] => Canceled\n            [endDate] => 2025-04-22\n            [autoRenew] => OFF\n            [message] => subscription status,endDate,autoRenew changed.\n            [modifiedAt] => 2025-06-30T05:38:31.000+0000\n        )\n\n    [publishedAt] => 2025-06-30T06:08:34.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-30 06:08:35] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-30 06:08:35] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-30 06:08:35] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-30 06:08:35] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-30 06:08:35] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-30 06:08:35] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-30 06:08:35] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '66127147458564', status = 'Canceled', endDate = '2025-04-22', autoRenew = 'OFF' ON DUPLICATE KEY UPDATE subscriptionId = '66127147458564', status = 'Canceled', endDate = '2025-04-22', autoRenew = 'OFF';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-06-30 07:41:05] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-06-30 07:41:05
[subscription_update] [2025-06-30 07:41:05] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-06-30 07:41:05] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => b929f47f-1c6f-4c9d-90f4-0d9409897c50\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55986438255949\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-09-27\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-06-30T07:16:01.000+0000\n        )\n\n    [publishedAt] => 2025-06-30T07:41:03.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-06-30 07:41:05] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-06-30 07:41:05] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-06-30 07:41:05] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-06-30 07:41:05] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-06-30 07:41:05] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-06-30 07:41:05] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-06-30 07:41:05] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-06-30 07:41:05] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-06-30 07:41:06] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '55986438255949', status = 'Active', quantity = 1, endDate = '2026-09-27', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '55986438255949', status = 'Active', quantity = 1, endDate = '2026-09-27', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-11 13:49:49] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-11 13:49:49
[subscription_update] [2025-07-11 13:49:49] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-11 13:49:49] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 1e924964-8726-45d3-9dc8-dc7521ffb655\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 74700029815254\n            [endDate] => 2025-08-10\n            [message] => subscription endDate changed.\n            [modifiedAt] => 2025-07-10T23:06:02.000+0000\n        )\n\n    [csn] => **********\n    [publishedAt] => 2025-07-11T13:49:45.000Z\n)\n
[subscription_update] [2025-07-11 13:49:49] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-11 13:49:49] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-07-11 13:49:49] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-11 13:49:49] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-11 13:49:49] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74700029815254', endDate = '2025-08-10' ON DUPLICATE KEY UPDATE subscriptionId = '74700029815254', endDate = '2025-08-10';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-11 13:50:22] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-11 13:50:22
[subscription_update] [2025-07-11 13:50:22] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-11 13:50:22] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 4cf4d0d7-5b72-42f8-a63e-a0a47fedaf4e\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59481765181147\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-07-14\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-07-11T06:52:02.000+0000\n        )\n\n    [csn] => **********\n    [publishedAt] => 2025-07-11T13:50:19.000Z\n)\n
[subscription_update] [2025-07-11 13:50:22] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-11 13:50:22] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-11 13:50:22] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-11 13:50:22] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-07-11 13:50:22] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-07-11 13:50:22] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-07-11 13:50:22] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-11 13:50:22] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-11 13:50:22] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59481765181147', status = 'Active', quantity = 1, endDate = '2026-07-14', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '59481765181147', status = 'Active', quantity = 1, endDate = '2026-07-14', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-11 13:50:25] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-11 13:50:25
[subscription_update] [2025-07-11 13:50:25] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-11 13:50:25] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 5b28d51c-8ca1-4b11-b960-335a949945f5\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71861567707131\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-07-11T06:44:53.000+0000\n        )\n\n    [csn] => **********\n    [publishedAt] => 2025-07-11T13:50:21.000Z\n)\n
[subscription_update] [2025-07-11 13:50:25] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-11 13:50:25] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-11 13:50:25] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-11 13:50:25] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-11 13:50:25] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71861567707131', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '71861567707131', status = 'Active';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-11 13:50:26] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-11 13:50:26
[subscription_update] [2025-07-11 13:50:26] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-11 13:50:26] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 5a68c80f-5eb9-4482-bd0e-620d46f19557\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59481765181147\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-11T07:11:18.000+0000\n        )\n\n    [csn] => **********\n    [publishedAt] => 2025-07-11T13:50:23.000Z\n)\n
[subscription_update] [2025-07-11 13:50:26] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-11 13:50:26] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-11 13:50:26] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-11 13:50:26] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-11 13:50:26] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59481765181147', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '59481765181147', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-11 13:50:27] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-11 13:50:27
[subscription_update] [2025-07-11 13:50:27] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-11 13:50:27] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 9c7b09b2-249f-4a63-a2e2-9bd854c9bac9\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71861567707131\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-07-11T06:44:53.000+0000\n        )\n\n    [csn] => **********\n    [publishedAt] => 2025-07-11T13:50:24.000Z\n)\n
[subscription_update] [2025-07-11 13:50:27] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-11 13:50:27] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-11 13:50:27] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-11 13:50:27] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-11 13:50:27] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71861567707131', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '71861567707131', status = 'Active';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-11 13:50:41] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-11 13:50:41
[subscription_update] [2025-07-11 13:50:41] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-11 13:50:41] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 661928fc-d64e-4b64-b9f1-49204a990322\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71861567707131\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-07-11T06:44:53.000+0000\n        )\n\n    [csn] => **********\n    [publishedAt] => 2025-07-11T13:50:37.000Z\n)\n
[subscription_update] [2025-07-11 13:50:41] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-11 13:50:41] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-11 13:50:41] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-11 13:50:41] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-11 13:50:41] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71861567707131', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '71861567707131', status = 'Active';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-11 13:50:42] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-11 13:50:42
[subscription_update] [2025-07-11 13:50:42] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-11 13:50:42] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => a261e849-2f3d-4343-b2cb-8f7bf414b45b\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71861567707131\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-07-11T06:44:53.000+0000\n        )\n\n    [csn] => **********\n    [publishedAt] => 2025-07-11T13:50:38.000Z\n)\n
[subscription_update] [2025-07-11 13:50:42] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-11 13:50:42] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-11 13:50:42] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-11 13:50:42] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-11 13:50:42] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71861567707131', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '71861567707131', status = 'Active';\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-11 15:07:07] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-11 15:07:07
[subscription_update] [2025-07-11 15:07:07] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-11 15:07:07] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => e6c96f6a-5fdb-42fe-b7f5-62b98af33692\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62826288139585\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-08-05\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-07-11T14:42:02.000+0000\n        )\n\n    [publishedAt] => 2025-07-11T15:07:04.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-11 15:07:07] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-11 15:07:07] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-11 15:07:07] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-11 15:07:07] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-07-11 15:07:07] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-07-11 15:07:07] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-07-11 15:07:07] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-11 15:07:07] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-11 15:07:07] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62826288139585', status = 'Active', quantity = 1, endDate = '2026-08-05', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '62826288139585', status = 'Active', quantity = 1, endDate = '2026-08-05', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-11 15:38:45] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-11 15:38:45
[subscription_update] [2025-07-11 15:38:45] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-11 15:38:45] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 8d6b97a7-7813-4bfa-ac6a-fd67978447c9\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62826288139585\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-11T15:08:13.000+0000\n        )\n\n    [publishedAt] => 2025-07-11T15:38:43.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-11 15:38:45] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-11 15:38:45] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-11 15:38:45] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-11 15:38:45] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-11 15:38:45] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62826288139585', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '62826288139585', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-11 16:36:46] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-11 16:36:46
[subscription_update] [2025-07-11 16:36:46] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-11 16:36:46] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 5e9a9a1b-0790-4fc5-9538-b88bc165d340\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75206444731711\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-11T16:11:41.000+0000\n        )\n\n    [publishedAt] => 2025-07-11T16:36:43.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-11 16:36:46] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-11 16:36:46] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-11 16:36:46] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-11 16:36:46] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-11 16:36:46] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75206444731711', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '75206444731711', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-12 06:05:51] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-12 06:05:51
[subscription_update] [2025-07-12 06:05:51] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-12 06:05:51] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 2da19119-4c14-43c7-ba3c-a45b545812e0\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62340821508147\n            [status] => Expired\n            [quantity] => 4\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-07-12T05:35:46.000+0000\n        )\n\n    [publishedAt] => 2025-07-12T06:05:49.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-12 06:05:51] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-12 06:05:51] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-12 06:05:51] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-12 06:05:51] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-12 06:05:51] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-12 06:05:51] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62340821508147', status = 'Expired', quantity = 4 ON DUPLICATE KEY UPDATE subscriptionId = '62340821508147', status = 'Expired', quantity = 4;\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-12 06:05:51] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-12 06:05:51
[subscription_update] [2025-07-12 06:05:51] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-12 06:05:51] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => a242f2c1-9845-42b5-8293-37a0fba198ba\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68683034038427\n            [status] => Expired\n            [quantity] => 2\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-07-12T05:35:47.000+0000\n        )\n\n    [publishedAt] => 2025-07-12T06:05:49.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-12 06:05:51] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-12 06:05:51] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-12 06:05:51] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-12 06:05:51] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-12 06:05:51] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-12 06:05:51] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68683034038427', status = 'Expired', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '68683034038427', status = 'Expired', quantity = 2;\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-12 07:07:27] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-12 07:07:27
[subscription_update] [2025-07-12 07:07:27] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-12 07:07:27] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => ba748a01-40bf-4f0f-9f6b-ad718a4c359b\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75153960451088\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-12T06:47:23.000+0000\n        )\n\n    [publishedAt] => 2025-07-12T07:07:25.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-12 07:07:27] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-12 07:07:27] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-12 07:07:27] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-12 07:07:27] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-12 07:07:27] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75153960451088', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '75153960451088', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-12 07:07:28] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-12 07:07:28
[subscription_update] [2025-07-12 07:07:28] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-12 07:07:28] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => d7391b63-6c1c-4f55-908a-2198418b1ccd\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75153960447587\n            [quantity] => 6\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-12T06:47:24.000+0000\n        )\n\n    [publishedAt] => 2025-07-12T07:07:26.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-12 07:07:28] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-12 07:07:28] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-12 07:07:28] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-12 07:07:28] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-12 07:07:28] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75153960447587', quantity = 6 ON DUPLICATE KEY UPDATE subscriptionId = '75153960447587', quantity = 6;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-12 07:08:37] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-12 07:08:37
[subscription_update] [2025-07-12 07:08:37] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-12 07:08:37] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 828c553b-24af-4921-ba8e-1fcb5e090f4c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75153960443986\n            [quantity] => 5\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-12T06:48:33.000+0000\n        )\n\n    [publishedAt] => 2025-07-12T07:08:35.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-12 07:08:37] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-12 07:08:37] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-12 07:08:37] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-12 07:08:37] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-12 07:08:37] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75153960443986', quantity = 5 ON DUPLICATE KEY UPDATE subscriptionId = '75153960443986', quantity = 5;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-12 07:09:25] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-12 07:09:25
[subscription_update] [2025-07-12 07:09:25] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-12 07:09:25] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 0ec56046-f802-48b1-8724-6f304db8f39e\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75153960440885\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-12T06:44:20.000+0000\n        )\n\n    [publishedAt] => 2025-07-12T07:09:23.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-12 07:09:25] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-12 07:09:25] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-12 07:09:25] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-12 07:09:25] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-12 07:09:25] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75153960440885', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '75153960440885', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-12 07:09:59] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-12 07:09:59
[subscription_update] [2025-07-12 07:09:59] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-12 07:09:59] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => ac66cd00-b4ce-4ed9-8010-97b2b16b815d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68683034038427\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-07-12T06:44:56.000+0000\n        )\n\n    [publishedAt] => 2025-07-12T07:09:58.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-12 07:09:59] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-12 07:09:59] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-12 07:09:59] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-12 07:09:59] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-12 07:09:59] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68683034038427', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '68683034038427', status = 'Active';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-12 07:10:27] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-12 07:10:27
[subscription_update] [2025-07-12 07:10:27] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-12 07:10:27] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 3e41cccd-825c-42b1-9ca2-c1200f658a29\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62340821508147\n            [status] => Active\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-07-12T06:45:22.000+0000\n        )\n\n    [publishedAt] => 2025-07-12T07:10:25.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-12 07:10:27] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-12 07:10:27] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-12 07:10:27] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-12 07:10:27] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-12 07:10:27] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62340821508147', status = 'Active' ON DUPLICATE KEY UPDATE subscriptionId = '62340821508147', status = 'Active';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-13 06:09:01] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-13 06:09:01
[subscription_update] [2025-07-13 06:09:01] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-13 06:09:01] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 4a21126c-055d-43b9-b2dc-10c3d68cca6e\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 74973535400236\n            [status] => Expired\n            [quantity] => 1\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-07-13T05:43:57.000+0000\n        )\n\n    [publishedAt] => 2025-07-13T06:08:59.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-13 06:09:01] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-13 06:09:01] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-13 06:09:01] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-13 06:09:01] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-13 06:09:01] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-13 06:09:01] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74973535400236', status = 'Expired', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '74973535400236', status = 'Expired', quantity = 1;\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-14 09:07:20] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-14 09:07:20
[subscription_update] [2025-07-14 09:07:20] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-14 09:07:20] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => a865d085-4c38-45f7-bfa4-cccd9bff470a\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62634114267554\n            [status] => Active\n            [quantity] => 2\n            [endDate] => 2026-07-14\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-07-14T08:37:08.000+0000\n        )\n\n    [publishedAt] => 2025-07-14T09:07:18.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-14 09:07:20] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-14 09:07:20] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-14 09:07:20] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-14 09:07:20] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-07-14 09:07:20] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-07-14 09:07:20] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-07-14 09:07:20] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-14 09:07:20] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-14 09:07:20] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62634114267554', status = 'Active', quantity = 2, endDate = '2026-07-14', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '62634114267554', status = 'Active', quantity = 2, endDate = '2026-07-14', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-14 09:07:21] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-14 09:07:21
[subscription_update] [2025-07-14 09:07:21] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-14 09:07:21] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => a113f36b-b8f7-4eea-a282-0b5b1e51fc30\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65780769869262\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-07-13\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-07-14T08:37:09.000+0000\n        )\n\n    [publishedAt] => 2025-07-14T09:07:19.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-14 09:07:21] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-14 09:07:21] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-14 09:07:21] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-14 09:07:21] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-07-14 09:07:21] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-07-14 09:07:21] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-07-14 09:07:21] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-14 09:07:21] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-14 09:07:21] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65780769869262', status = 'Active', quantity = 1, endDate = '2026-07-13', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '65780769869262', status = 'Active', quantity = 1, endDate = '2026-07-13', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-14 09:07:23] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-14 09:07:23
[subscription_update] [2025-07-14 09:07:23] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-14 09:07:23] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 5772c3da-caae-4f70-b453-f5e273dd55fc\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62634114260253\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-07-14\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-07-14T08:37:13.000+0000\n        )\n\n    [publishedAt] => 2025-07-14T09:07:21.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-14 09:07:23] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-14 09:07:23] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-14 09:07:23] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-14 09:07:23] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-07-14 09:07:23] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-07-14 09:07:23] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-07-14 09:07:23] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-14 09:07:23] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-14 09:07:23] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62634114260253', status = 'Active', quantity = 1, endDate = '2026-07-14', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '62634114260253', status = 'Active', quantity = 1, endDate = '2026-07-14', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-14 09:07:27] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-14 09:07:27
[subscription_update] [2025-07-14 09:07:27] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-14 09:07:27] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 76e49c62-5157-4f96-8cc7-82f72cf25785\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 63051040836776\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-07-14\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-07-14T08:37:12.000+0000\n        )\n\n    [publishedAt] => 2025-07-14T09:07:24.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-14 09:07:27] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-14 09:07:27] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-14 09:07:27] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-14 09:07:27] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-07-14 09:07:27] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-07-14 09:07:27] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-07-14 09:07:27] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-14 09:07:27] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-14 09:07:27] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '63051040836776', status = 'Active', quantity = 1, endDate = '2026-07-14', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '63051040836776', status = 'Active', quantity = 1, endDate = '2026-07-14', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-14 11:36:08] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-14 11:36:08
[subscription_update] [2025-07-14 11:36:08] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-14 11:36:08] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 9fa0890b-3b49-413d-bd8a-0caaad594593\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62634114260253\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-14T11:20:59.000+0000\n        )\n\n    [publishedAt] => 2025-07-14T11:36:06.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-14 11:36:08] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-14 11:36:08] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-14 11:36:08] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-14 11:36:08] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-14 11:36:08] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62634114260253', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '62634114260253', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-14 11:36:16] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-14 11:36:16
[subscription_update] [2025-07-14 11:36:16] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-14 11:36:16] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => aae1e77c-9e09-49d7-98de-941eb9eaa6ef\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62634114267554\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-14T11:21:08.000+0000\n        )\n\n    [publishedAt] => 2025-07-14T11:36:14.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-14 11:36:16] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-14 11:36:16] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-14 11:36:16] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-14 11:36:16] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-14 11:36:16] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62634114267554', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '62634114267554', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-14 11:36:26] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-14 11:36:26
[subscription_update] [2025-07-14 11:36:26] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-14 11:36:26] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 3509e55c-ab75-46e0-a101-3f289e05a904\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 63051040836776\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-14T11:21:18.000+0000\n        )\n\n    [publishedAt] => 2025-07-14T11:36:23.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-14 11:36:26] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-14 11:36:26] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-14 11:36:26] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-14 11:36:26] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-14 11:36:26] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '63051040836776', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '63051040836776', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-14 11:36:49] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-14 11:36:49
[subscription_update] [2025-07-14 11:36:49] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-14 11:36:49] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => d4958a19-f7f8-4a09-8840-a3491a383200\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65780769869262\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-14T11:21:40.000+0000\n        )\n\n    [publishedAt] => 2025-07-14T11:36:47.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-14 11:36:49] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-14 11:36:49] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-14 11:36:49] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-14 11:36:49] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-14 11:36:49] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65780769869262', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '65780769869262', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-14 11:42:31] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-14 11:42:31
[subscription_update] [2025-07-14 11:42:31] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-14 11:42:31] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 6103f8e1-3db9-4e10-a418-5718f35e2105\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75249093216411\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-14T11:27:24.000+0000\n        )\n\n    [publishedAt] => 2025-07-14T11:42:29.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-14 11:42:31] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-07-14 11:42:35] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75249093216411\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-07-14\n            [endDate] => 2026-07-13\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => A-32282555\n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => LOC\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000125\n            [offeringCode] => COLLRP\n            [offeringName] => BIM Collaborate Pro\n            [marketingName] => BIM Collaborate Pro\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => ADF London Ltd\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => ADF London Ltd\n                            [type] => End Customer\n                            [address1] => 27 Kelso Place\n                            [address2] => \n                            [address3] => \n                            [city] => London\n                            [stateProvince] => LONDON\n                            [postalCode] => W8 5QG\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => \n                            [namedAccountGroup] => Midmarket\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Construction Services\n                            [primaryAdminFirstName] => \n                            [primaryAdminLastName] => \n                            [primaryAdminEmail] => \n                            [teamId] => \n                            [teamName] => \n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => ADF\n                            [last] => london\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Active\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-07-14 11:42:35] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, Record updated, New record inserted, New record inserted, <br>
[subscription_update] [2025-07-14 12:07:28] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-14 12:07:28
[subscription_update] [2025-07-14 12:07:28] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-14 12:07:28] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 0fb8b365-6aa2-4371-b027-6f9906ae9bea\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65882885159298\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-07-25\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-07-14T11:47:23.000+0000\n        )\n\n    [publishedAt] => 2025-07-14T12:07:26.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-14 12:07:28] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-14 12:07:28] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-14 12:07:28] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-14 12:07:28] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-07-14 12:07:28] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-07-14 12:07:28] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-07-14 12:07:28] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-14 12:07:28] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-14 12:07:28] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65882885159298', status = 'Active', quantity = 1, endDate = '2026-07-25', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '65882885159298', status = 'Active', quantity = 1, endDate = '2026-07-25', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-14 15:36:45] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-14 15:36:45
[subscription_update] [2025-07-14 15:36:45] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-14 15:36:45] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => d20e0240-a5a2-4336-b74e-78f8567ae336\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65882885159298\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-14T15:11:36.000+0000\n        )\n\n    [publishedAt] => 2025-07-14T15:36:43.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-14 15:36:45] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-14 15:36:45] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-14 15:36:45] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-14 15:36:45] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-14 15:36:45] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65882885159298', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '65882885159298', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-15 00:11:13] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-15 00:11:13
[subscription_update] [2025-07-15 00:11:13] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-15 00:11:13] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 28593f34-4d32-45cc-8308-576f4c17a499\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75068615514382\n            [status] => Suspended\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-07-14T23:41:06.000+0000\n        )\n\n    [publishedAt] => 2025-07-15T00:11:11.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-15 00:11:13] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-15 00:11:13] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-15 00:11:13] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-15 00:11:13] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-15 00:11:13] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75068615514382', status = 'Suspended' ON DUPLICATE KEY UPDATE subscriptionId = '75068615514382', status = 'Suspended';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-15 07:05:35] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-15 07:05:35
[subscription_update] [2025-07-15 07:05:35] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-15 07:05:35] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => eb784f8b-0b97-49df-a59a-93a689311349\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 63051040836776\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-15T06:45:30.000+0000\n        )\n\n    [publishedAt] => 2025-07-15T07:05:32.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-15 07:05:35] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-15 07:05:35] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-15 07:05:35] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-15 07:05:35] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-15 07:05:35] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '63051040836776', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '63051040836776', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-15 07:05:41] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-15 07:05:41
[subscription_update] [2025-07-15 07:05:41] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-15 07:05:41] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 576e7a14-8615-4f2d-95ad-4b13c91e356e\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62851504013355\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-15T06:45:36.000+0000\n        )\n\n    [publishedAt] => 2025-07-15T07:05:38.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-15 07:05:41] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-15 07:05:41] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-15 07:05:41] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-15 07:05:41] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-15 07:05:41] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62851504013355', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '62851504013355', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-15 07:05:55] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-15 07:05:55
[subscription_update] [2025-07-15 07:05:55] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-15 07:05:55] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => da1068fa-c4c7-469d-b3fc-0f4a9dae7b4a\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62634114260253\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-15T06:45:50.000+0000\n        )\n\n    [publishedAt] => 2025-07-15T07:05:53.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-15 07:05:55] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-15 07:05:55] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-15 07:05:55] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-15 07:05:55] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-15 07:05:55] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62634114260253', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '62634114260253', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-15 07:06:12] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-15 07:06:12
[subscription_update] [2025-07-15 07:06:12] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-15 07:06:12] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => a64cb336-dc0a-4dfa-bc3e-354d3aa4e40d\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62851791696292\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-15T06:46:07.000+0000\n        )\n\n    [publishedAt] => 2025-07-15T07:06:10.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-15 07:06:12] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-15 07:06:12] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-15 07:06:12] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-15 07:06:12] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-15 07:06:12] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62851791696292', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '62851791696292', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-15 07:07:24] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-15 07:07:24
[subscription_update] [2025-07-15 07:07:24] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-15 07:07:24] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => c22a7faf-5231-4d13-816c-107c510b050c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62634114267554\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-15T06:47:19.000+0000\n        )\n\n    [publishedAt] => 2025-07-15T07:07:22.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-15 07:07:24] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-15 07:07:24] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-15 07:07:24] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-15 07:07:24] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-15 07:07:24] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62634114267554', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '62634114267554', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-15 07:08:37] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-15 07:08:37
[subscription_update] [2025-07-15 07:08:37] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-15 07:08:37] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => ff794f76-1644-4998-8e2d-267d1445a55a\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75008173297281\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-15T06:43:33.000+0000\n        )\n\n    [publishedAt] => 2025-07-15T07:08:35.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-15 07:08:37] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-15 07:08:37] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-15 07:08:37] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-15 07:08:37] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-15 07:08:37] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75008173297281', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '75008173297281', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-15 07:10:29] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-15 07:10:29
[subscription_update] [2025-07-15 07:10:29] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-15 07:10:29] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 89479f26-0942-4df8-b0b1-2466c952bfc0\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65780769869262\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-15T06:45:24.000+0000\n        )\n\n    [publishedAt] => 2025-07-15T07:10:27.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-15 07:10:29] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-15 07:10:29] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-15 07:10:29] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-15 07:10:29] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-15 07:10:29] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65780769869262', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '65780769869262', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-15 12:10:06] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-15 12:10:06
[subscription_update] [2025-07-15 12:10:06] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-15 12:10:06] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 45c23cf9-b8ca-44dc-9229-433b41960ead\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62625497916827\n            [quantity] => 5\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-15T11:40:00.000+0000\n        )\n\n    [publishedAt] => 2025-07-15T12:10:03.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-15 12:10:06] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-15 12:10:06] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-15 12:10:06] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-15 12:10:06] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-15 12:10:06] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62625497916827', quantity = 5 ON DUPLICATE KEY UPDATE subscriptionId = '62625497916827', quantity = 5;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-15 15:40:38] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-15 15:40:38
[subscription_update] [2025-07-15 15:40:38] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-15 15:40:38] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 4e90d52d-96b6-473d-bcb6-f500183c3f84\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72114670752653\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-07-15\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-07-15T15:25:32.000+0000\n        )\n\n    [publishedAt] => 2025-07-15T15:40:36.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-15 15:40:38] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-15 15:40:38] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-15 15:40:38] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-15 15:40:38] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-07-15 15:40:38] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-07-15 15:40:38] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-07-15 15:40:38] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-15 15:40:38] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-15 15:40:38] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72114670752653', status = 'Active', quantity = 1, endDate = '2026-07-15', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '72114670752653', status = 'Active', quantity = 1, endDate = '2026-07-15', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-15 19:38:08] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-15 19:38:08
[subscription_update] [2025-07-15 19:38:08] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-15 19:38:08] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 4fbcad0c-0f96-497c-b02a-467abfd08847\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72114670752653\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-15T19:17:59.000+0000\n        )\n\n    [publishedAt] => 2025-07-15T19:38:06.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-15 19:38:08] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-15 19:38:08] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-15 19:38:08] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-15 19:38:08] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-15 19:38:08] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72114670752653', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '72114670752653', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-16 07:07:04] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-16 07:07:04
[subscription_update] [2025-07-16 07:07:04] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-16 07:07:04] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 713a3ed3-7733-47eb-81b0-0260eda88978\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 67821030871088\n            [quantity] => 3\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-16T06:51:59.000+0000\n        )\n\n    [publishedAt] => 2025-07-16T07:07:01.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-16 07:07:04] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-16 07:07:04] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-16 07:07:04] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-16 07:07:04] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-16 07:07:04] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '67821030871088', quantity = 3 ON DUPLICATE KEY UPDATE subscriptionId = '67821030871088', quantity = 3;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-16 07:08:14] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-16 07:08:14
[subscription_update] [2025-07-16 07:08:14] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-16 07:08:14] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => d0e85fcc-15c7-4cbc-b018-54c34102b395\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 63715840917913\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-16T06:43:10.000+0000\n        )\n\n    [publishedAt] => 2025-07-16T07:08:12.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-16 07:08:14] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-16 07:08:14] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-16 07:08:14] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-16 07:08:14] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-16 07:08:14] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '63715840917913', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '63715840917913', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-16 07:08:57] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-16 07:08:57
[subscription_update] [2025-07-16 07:08:57] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-16 07:08:57] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => ff3de270-e104-4654-81b2-342963720718\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71033223428191\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-16T06:43:52.000+0000\n        )\n\n    [publishedAt] => 2025-07-16T07:08:54.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-16 07:08:57] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-16 07:08:57] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-16 07:08:57] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-16 07:08:57] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-16 07:08:57] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71033223428191', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '71033223428191', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-16 07:09:56] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-16 07:09:56
[subscription_update] [2025-07-16 07:09:56] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-16 07:09:56] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 30cdbb63-563d-4cbe-86b7-cebfaa72f52c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71222734770085\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-16T06:44:52.000+0000\n        )\n\n    [publishedAt] => 2025-07-16T07:09:54.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-16 07:09:56] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-16 07:09:56] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-16 07:09:56] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-16 07:09:56] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-16 07:09:56] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71222734770085', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '71222734770085', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-16 07:10:10] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-16 07:10:10
[subscription_update] [2025-07-16 07:10:10] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-16 07:10:10] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => ade9f275-603b-4297-89a3-8f72f660fb6e\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68250644732072\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-16T06:45:05.000+0000\n        )\n\n    [publishedAt] => 2025-07-16T07:10:08.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-16 07:10:10] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-16 07:10:10] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-16 07:10:10] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-16 07:10:10] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-16 07:10:10] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68250644732072', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '68250644732072', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-16 07:10:58] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-16 07:10:58
[subscription_update] [2025-07-16 07:10:58] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-16 07:10:58] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 46d1779f-a01e-4320-afca-a92ec4058b02\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 66090323171022\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-16T06:50:53.000+0000\n        )\n\n    [publishedAt] => 2025-07-16T07:10:55.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-16 07:10:58] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-16 07:10:58] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-16 07:10:58] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-16 07:10:58] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-16 07:10:58] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '66090323171022', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '66090323171022', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-16 07:11:09] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-16 07:11:09
[subscription_update] [2025-07-16 07:11:09] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-16 07:11:09] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => c2a348d5-4f4c-46ea-8733-1177cc14d435\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71034082592755\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-16T06:46:05.000+0000\n        )\n\n    [publishedAt] => 2025-07-16T07:11:07.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-16 07:11:09] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-16 07:11:09] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-16 07:11:09] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-16 07:11:09] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-16 07:11:09] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71034082592755', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '71034082592755', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-16 07:11:21] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-16 07:11:21
[subscription_update] [2025-07-16 07:11:21] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-16 07:11:21] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => c7c4fa8f-e6de-4b72-9b28-7cf77150a66c\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 70066499073484\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-16T06:51:17.000+0000\n        )\n\n    [publishedAt] => 2025-07-16T07:11:19.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-16 07:11:21] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-16 07:11:21] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-16 07:11:21] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-16 07:11:21] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-16 07:11:21] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '70066499073484', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '70066499073484', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-16 07:11:23] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-16 07:11:23
[subscription_update] [2025-07-16 07:11:23] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-16 07:11:23] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => eccbf558-f12e-455d-904b-6f393baabc82\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71759633649727\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-16T06:51:19.000+0000\n        )\n\n    [publishedAt] => 2025-07-16T07:11:21.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-16 07:11:23] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-16 07:11:23] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-16 07:11:23] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-16 07:11:23] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-16 07:11:23] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71759633649727', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '71759633649727', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-16 07:11:29] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-16 07:11:29
[subscription_update] [2025-07-16 07:11:29] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-16 07:11:29] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 863a90cc-a2ec-4356-b729-cc256cbd683b\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65712164075515\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-16T06:56:25.000+0000\n        )\n\n    [publishedAt] => 2025-07-16T07:11:27.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-16 07:11:29] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-16 07:11:29] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-16 07:11:29] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-16 07:11:29] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-16 07:11:29] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65712164075515', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '65712164075515', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-16 07:12:17] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-16 07:12:17
[subscription_update] [2025-07-16 07:12:17] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-16 07:12:17] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 14e52b19-61a6-4a92-8647-ba18d79ac566\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72769517582067\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-16T06:57:12.000+0000\n        )\n\n    [publishedAt] => 2025-07-16T07:12:15.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-16 07:12:17] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-16 07:12:17] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-16 07:12:17] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-16 07:12:17] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-16 07:12:17] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72769517582067', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '72769517582067', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-16 07:16:24] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-16 07:16:24
[subscription_update] [2025-07-16 07:16:24] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-16 07:16:24] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 81acab0d-5e2a-46a4-93f2-22fd05975f46\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 72120951665721\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-16T07:01:18.000+0000\n        )\n\n    [publishedAt] => 2025-07-16T07:16:22.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-16 07:16:24] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-16 07:16:24] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-16 07:16:24] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-16 07:16:24] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-16 07:16:24] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '72120951665721', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '72120951665721', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-16 10:07:29] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-16 10:07:29
[subscription_update] [2025-07-16 10:07:29] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-16 10:07:29] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 77581eae-af7d-41c2-9ad0-a5c8ba4eeda1\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65594554281446\n            [status] => Active\n            [quantity] => 2\n            [endDate] => 2026-07-21\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-07-16T09:42:24.000+0000\n        )\n\n    [publishedAt] => 2025-07-16T10:07:26.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-16 10:07:29] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-16 10:07:29] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-16 10:07:29] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-16 10:07:29] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-07-16 10:07:29] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-07-16 10:07:29] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-07-16 10:07:29] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-16 10:07:29] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-16 10:07:29] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65594554281446', status = 'Active', quantity = 2, endDate = '2026-07-21', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '65594554281446', status = 'Active', quantity = 2, endDate = '2026-07-21', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-16 15:37:23] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-16 15:37:23
[subscription_update] [2025-07-16 15:37:23] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-16 15:37:23] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 7ba7f739-5a8a-4392-9a7c-5015783c7b6f\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 65594554281446\n            [quantity] => 2\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-16T15:16:50.000+0000\n        )\n\n    [publishedAt] => 2025-07-16T15:37:21.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-16 15:37:23] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-16 15:37:23] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-16 15:37:23] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-16 15:37:23] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-16 15:37:23] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '65594554281446', quantity = 2 ON DUPLICATE KEY UPDATE subscriptionId = '65594554281446', quantity = 2;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-16 15:38:37] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-16 15:38:37
[subscription_update] [2025-07-16 15:38:37] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-16 15:38:37] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 3ce04fb6-1bd2-4720-922e-69790d20baf3\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 74895877192874\n            [quantity] => 3\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-16T15:23:29.000+0000\n        )\n\n    [publishedAt] => 2025-07-16T15:38:35.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-16 15:38:37] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-16 15:38:37] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-16 15:38:37] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-16 15:38:37] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-16 15:38:37] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74895877192874', quantity = 3 ON DUPLICATE KEY UPDATE subscriptionId = '74895877192874', quantity = 3;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-16 15:38:37] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-16 15:38:37
[subscription_update] [2025-07-16 15:38:37] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-16 15:38:37] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 1a52bbe5-8247-4c3e-9a28-884bc504a1b5\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 74920602259458\n            [quantity] => 3\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-16T15:23:29.000+0000\n        )\n\n    [publishedAt] => 2025-07-16T15:38:35.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-16 15:38:37] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-16 15:38:37] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-16 15:38:37] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-16 15:38:37] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-16 15:38:37] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '74920602259458', quantity = 3 ON DUPLICATE KEY UPDATE subscriptionId = '74920602259458', quantity = 3;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-16 15:39:53] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-16 15:39:53
[subscription_update] [2025-07-16 15:39:53] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-16 15:39:53] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => decec1c7-30ad-4fdf-92d9-6b83b19260cc\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75265394172391\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-16T15:24:45.000+0000\n        )\n\n    [publishedAt] => 2025-07-16T15:39:50.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-16 15:39:53] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-07-16 15:39:53] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-16 15:39:53
[subscription_update] [2025-07-16 15:39:53] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-16 15:39:53] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => cb9eb2ef-096d-4547-8c09-037160d1eaa6\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75265394168690\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-16T15:24:45.000+0000\n        )\n\n    [publishedAt] => 2025-07-16T15:39:51.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-16 15:39:53] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-07-16 15:39:57] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75265394172391\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Inactive\n            [startDate] => 2025-07-21\n            [endDate] => 2026-07-05\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => **********\n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => LOC\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => ETCH ASSOCIATES\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => ETCH ASSOCIATES\n                            [type] => End Customer\n                            [address1] => 1 Union Way\n                            [address2] => \n                            [address3] => \n                            [city] => Witney\n                            [stateProvince] => OXFORDSHIRE\n                            [postalCode] => OX28 6HD\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Engineering Service Providers\n                            [primaryAdminFirstName] => \n                            [primaryAdminLastName] => \n                            [primaryAdminEmail] => \n                            [teamId] => \n                            [teamName] => \n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Etch\n                            [last] => Associates\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Active\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-07-16 15:39:57] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-07-16 15:39:58] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75265394168690\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Inactive\n            [startDate] => 2025-07-21\n            [endDate] => 2026-04-30\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => A-30567902\n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => LOC\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000105\n            [offeringCode] => BM36DP\n            [offeringName] => Docs\n            [marketingName] => Docs\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => ETCH ASSOCIATES\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => ETCH ASSOCIATES\n                            [type] => End Customer\n                            [address1] => 1 Union Way\n                            [address2] => \n                            [address3] => \n                            [city] => Witney\n                            [stateProvince] => OXFORDSHIRE\n                            [postalCode] => OX28 6HD\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Engineering Service Providers\n                            [primaryAdminFirstName] => \n                            [primaryAdminLastName] => \n                            [primaryAdminEmail] => \n                            [teamId] => \n                            [teamName] => \n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Etch\n                            [last] => Associates\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => Active\n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => 1\n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-07-16 15:39:58] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-07-16 15:42:08] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-16 15:42:08
[subscription_update] [2025-07-16 15:42:08] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-16 15:42:08] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => ad5c6b8f-c0d0-4cad-8455-5040300c7252\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75266597996116\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-16T15:27:00.000+0000\n        )\n\n    [publishedAt] => 2025-07-16T15:42:06.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-16 15:42:08] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-07-16 15:42:11] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 75266597996116\n            [subscriptionReferenceNumber] => \n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Active\n            [startDate] => 2025-07-15\n            [endDate] => 2025-08-24\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => \n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => Credit Card\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => POTTER COWAN\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => POTTER COWAN\n                            [type] => End Customer\n                            [address1] => Phoenix House 20 Duncrue Crescent\n                            [address2] => \n                            [address3] => \n                            [city] => Belfast\n                            [stateProvince] => \n                            [postalCode] => BT3 9BW\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Midmarket\n                            [parentIndustryGroup] => AEC\n                            [parentIndustrySegment] => Construction Services\n                            [primaryAdminFirstName] => \n                            [primaryAdminLastName] => \n                            [primaryAdminEmail] => \n                            [teamId] => \n                            [teamName] => \n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => David\n                            [last] => Nelson\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => \n                            [doNotCall] => \n                            [doNotEmail] => \n                            [doNotMail] => \n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-07-16 15:42:11] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, New record inserted, <br>
[subscription_update] [2025-07-17 00:06:37] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-17 00:06:37
[subscription_update] [2025-07-17 00:06:37] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-17 00:06:37] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 871d2f71-6a38-4834-b5fb-825b8118df71\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75014667058610\n            [status] => Expired\n            [quantity] => 1\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-07-16T23:51:31.000+0000\n        )\n\n    [publishedAt] => 2025-07-17T00:06:34.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-17 00:06:37] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-17 00:06:37] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-17 00:06:37] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-17 00:06:37] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-17 00:06:37] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-17 00:06:37] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75014667058610', status = 'Expired', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '75014667058610', status = 'Expired', quantity = 1;\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-17 06:09:39] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-17 06:09:39
[subscription_update] [2025-07-17 06:09:39] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-17 06:09:39] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 59b0b8c7-bfa2-43f5-b76a-bc2674543d1f\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75006472719688\n            [status] => Expired\n            [quantity] => 1\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-07-17T05:39:34.000+0000\n        )\n\n    [publishedAt] => 2025-07-17T06:09:37.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-17 06:09:39] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-17 06:09:39] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-17 06:09:39] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-17 06:09:39] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-17 06:09:39] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-17 06:09:39] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75006472719688', status = 'Expired', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '75006472719688', status = 'Expired', quantity = 1;\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-17 06:09:40] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-17 06:09:40
[subscription_update] [2025-07-17 06:09:40] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-17 06:09:40] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => c2032630-0fe4-4d2a-9ee2-dacca2be426f\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 55508693518307\n            [status] => Expired\n            [quantity] => 1\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-07-17T05:39:36.000+0000\n        )\n\n    [publishedAt] => 2025-07-17T06:09:37.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-17 06:09:40] [autodesk_subscription.class.php:435] Subscription not found in database, getting from API: 
[subscription_update] [2025-07-17 06:09:41] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-17 06:09:41
[subscription_update] [2025-07-17 06:09:41] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-17 06:09:41] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => af03f9a1-8950-4326-8778-9a59894e5cbb\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 71887756008038\n            [status] => Expired\n            [quantity] => 1\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-07-17T05:39:37.000+0000\n        )\n\n    [publishedAt] => 2025-07-17T06:09:39.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-17 06:09:41] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-17 06:09:41] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-17 06:09:41] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-17 06:09:41] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-17 06:09:41] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-17 06:09:41] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '71887756008038', status = 'Expired', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '71887756008038', status = 'Expired', quantity = 1;\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-17 06:09:42] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-17 06:09:42
[subscription_update] [2025-07-17 06:09:42] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-17 06:09:42] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => f69f1876-39a0-4159-9365-0513f9950cb7\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68915687453814\n            [status] => Expired\n            [quantity] => 1\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-07-17T05:39:38.000+0000\n        )\n\n    [publishedAt] => 2025-07-17T06:09:40.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-17 06:09:42] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-17 06:09:42] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-17 06:09:42] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-17 06:09:42] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-17 06:09:42] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-17 06:09:42] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68915687453814', status = 'Expired', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '68915687453814', status = 'Expired', quantity = 1;\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-17 06:09:44] [autodesk_subscription.class.php:445] Got subscription from api: Array\n(\n    [0] => Array\n        (\n            [subscriptionId] => 55508693518307\n            [subscriptionReferenceNumber] => 562-96510504\n            [switch] => Array\n                (\n                    [fromSubscriptions] => Array\n                        (\n                        )\n\n                    [toSubscription] => \n                )\n\n            [quantity] => 1\n            [status] => Expired\n            [startDate] => 2019-04-14\n            [endDate] => 2026-07-03\n            [term] => Annual\n            [billingBehavior] => Recurring\n            [billingFrequency] => Annual\n            [intendedUsage] => COM\n            [connectivity] => Online\n            [connectivityInterval] => 30 Days\n            [opportunityNumber] => **********\n            [servicePlan] => Standard\n            [accessModel] => Single User\n            [paymentMethod] => LOC\n            [recordType] => Attribute based\n            [renewalCounter] => \n            [autoRenew] => ON\n            [offeringId] => OD-000031\n            [offeringCode] => ACDLT\n            [offeringName] => AutoCAD LT\n            [marketingName] => AutoCAD LT\n            [currency] => GBP\n            [annualDeclaredValue] => \n            [pricingMethod] => Array\n                (\n                    [code] => QTY\n                    [description] => Quantity based\n                )\n\n            [accounts] => Array\n                (\n                    [soldTo] => Array\n                        (\n                            [csn] => **********\n                            [name] => CHEMOXY\n                        )\n\n                    [solutionProvider] => Array\n                        (\n                            [csn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [localLanguageName] => \n                            [type] => Reseller\n                            [address1] => Unit F, Yorkway\n                            [address2] => Mandale Ind Est\n                            [address3] => \n                            [city] => Stockton On Tees\n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [country] => United Kingdom\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [nurtureReseller] => Array\n                        (\n                            [csn] => \n                            [name] => \n                            [lockDate] => \n                            [nurtureDiscountEligibility] => \n                        )\n\n                )\n\n            [endCustomer] => Array\n                (\n                    [account] => Array\n                        (\n                            [csn] => **********\n                            [name] => CHEMOXY\n                            [type] => End Customer\n                            [address1] => Haverton Hill Road\n                            [address2] => \n                            [address3] => \n                            [city] => Billingham\n                            [stateProvince] => CLEVELAND\n                            [postalCode] => TS23 1PY\n                            [country] => United Kingdom\n                            [individualFlag] => \n                            [namedAccountFlag] => 1\n                            [namedAccountGroup] => Territory\n                            [parentIndustryGroup] => MFG\n                            [parentIndustrySegment] => Process Manufacturing\n                            [primaryAdminFirstName] => Peter\n                            [primaryAdminLastName] => McArthur\n                            [primaryAdminEmail] => <EMAIL>\n                            [teamId] => 7050254\n                            [teamName] => David Pearson - 0254\n                            [stateProvinceCode] => \n                            [countryCode] => GB\n                        )\n\n                    [purchaser] => Array\n                        (\n                            [first] => Peter\n                            [last] => McArthur\n                            [email] => <EMAIL>\n                            [status] => Active\n                            [portalRegistration] => \n                            [doNotCall] => \n                            [doNotEmail] => 1\n                            [doNotMail] => \n                        )\n\n                )\n\n        )\n\n)\n
[subscription_update] [2025-07-17 06:09:44] [autodesk_subscription.class.php:455] Subscription import successful: Line 1: No changes needed, No changes needed, New record inserted, Record updated, <br>
[subscription_update] [2025-07-17 12:38:57] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-17 12:38:57
[subscription_update] [2025-07-17 12:38:57] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-17 12:38:57] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 008e2178-3139-4c22-a330-9f2a63513dbb\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62496911986311\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-06-28\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-07-17T12:03:52.000+0000\n        )\n\n    [publishedAt] => 2025-07-17T12:38:55.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-17 12:38:57] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-17 12:38:57] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-17 12:38:57] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-17 12:38:57] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-07-17 12:38:57] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-07-17 12:38:57] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-07-17 12:38:57] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-17 12:38:57] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-17 12:38:57] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62496911986311', status = 'Active', quantity = 1, endDate = '2026-06-28', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '62496911986311', status = 'Active', quantity = 1, endDate = '2026-06-28', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-17 14:09:11] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-17 14:09:11
[subscription_update] [2025-07-17 14:09:11] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-17 14:09:11] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => ccbface3-5b7f-4b30-bcf6-f478afe4b7fa\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69054087093103\n            [status] => Active\n            [quantity] => 1\n            [endDate] => 2026-07-27\n            [autoRenew] => ON\n            [term] => Annual\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-07-17T13:39:06.000+0000\n        )\n\n    [publishedAt] => 2025-07-17T14:09:09.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-17 14:09:11] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-17 14:09:11] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-17 14:09:11] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-17 14:09:11] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-07-17 14:09:11] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-07-17 14:09:11] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-07-17 14:09:11] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-17 14:09:11] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-17 14:09:11] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69054087093103', status = 'Active', quantity = 1, endDate = '2026-07-27', autoRenew = 'ON', term = 'Annual' ON DUPLICATE KEY UPDATE subscriptionId = '69054087093103', status = 'Active', quantity = 1, endDate = '2026-07-27', autoRenew = 'ON', term = 'Annual';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-17 15:10:01] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-17 15:10:01
[subscription_update] [2025-07-17 15:10:01] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-17 15:10:01] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => dd72c8d8-38fb-47ce-8ece-8f4345746565\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59680488697967\n            [status] => Active\n            [quantity] => 5\n            [endDate] => 2028-08-06\n            [autoRenew] => ON\n            [term] => 3 Year\n            [message] => subscription status,quantity,endDate,autoRenew,term changed.\n            [modifiedAt] => 2025-07-17T14:49:55.000+0000\n        )\n\n    [publishedAt] => 2025-07-17T15:09:57.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-17 15:10:01] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-17 15:10:01] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-17 15:10:01] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-17 15:10:01] [autodesk_subscription.class.php:483] found key: endDate and mapped to autodesk_subscriptions.endDate
[subscription_update] [2025-07-17 15:10:01] [autodesk_subscription.class.php:483] found key: autoRenew and mapped to autodesk_subscriptions.autoRenew
[subscription_update] [2025-07-17 15:10:01] [autodesk_subscription.class.php:483] found key: term and mapped to autodesk_subscriptions.term
[subscription_update] [2025-07-17 15:10:01] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-17 15:10:01] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-17 15:10:01] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59680488697967', status = 'Active', quantity = 5, endDate = '2028-08-06', autoRenew = 'ON', term = '3 Year' ON DUPLICATE KEY UPDATE subscriptionId = '59680488697967', status = 'Active', quantity = 5, endDate = '2028-08-06', autoRenew = 'ON', term = '3 Year';\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-17 15:37:59] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-17 15:37:59
[subscription_update] [2025-07-17 15:37:59] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-17 15:37:59] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 0c98dfed-9ff8-4693-8bc5-904bd89a7fde\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 69054087093103\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-17T15:17:48.000+0000\n        )\n\n    [publishedAt] => 2025-07-17T15:37:57.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-17 15:37:59] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-17 15:37:59] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-17 15:37:59] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-17 15:37:59] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-17 15:37:59] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '69054087093103', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '69054087093103', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-17 15:39:29] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-17 15:39:29
[subscription_update] [2025-07-17 15:39:29] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-17 15:39:29] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 80aa07e9-e0fa-4a69-8be8-1dbc67b36e15\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 59680488697967\n            [quantity] => 5\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-17T15:14:11.000+0000\n        )\n\n    [publishedAt] => 2025-07-17T15:39:27.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-17 15:39:29] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-17 15:39:29] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-17 15:39:29] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-17 15:39:29] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-17 15:39:29] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '59680488697967', quantity = 5 ON DUPLICATE KEY UPDATE subscriptionId = '59680488697967', quantity = 5;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-17 15:39:57] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-17 15:39:57
[subscription_update] [2025-07-17 15:39:57] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-17 15:39:57] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 9836632f-fa12-4f3c-88cb-aab75b67597a\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 62496911986311\n            [quantity] => 1\n            [message] => subscription quantity changed.\n            [modifiedAt] => 2025-07-17T15:14:43.000+0000\n        )\n\n    [publishedAt] => 2025-07-17T15:39:55.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-17 15:39:57] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-17 15:39:57] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-17 15:39:57] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-17 15:39:57] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-17 15:39:57] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '62496911986311', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '62496911986311', quantity = 1;\n",\n        "affected_rows": 0\n    }\n]
[subscription_update] [2025-07-17 20:35:32] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-17 20:35:32
[subscription_update] [2025-07-17 20:35:32] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-17 20:35:32] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => f07bb43d-7eac-4105-b680-ac35d0cb8bb1\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 75103518754837\n            [status] => Active\n            [quantity] => 1\n            [message] => subscription status,quantity changed.\n            [modifiedAt] => 2025-07-17T20:15:26.000+0000\n        )\n\n    [publishedAt] => 2025-07-17T20:35:29.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-17 20:35:32] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-17 20:35:32] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-17 20:35:32] [autodesk_subscription.class.php:483] found key: quantity and mapped to autodesk_subscriptions.quantity
[subscription_update] [2025-07-17 20:35:32] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-17 20:35:32] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-17 20:35:32] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '75103518754837', status = 'Active', quantity = 1 ON DUPLICATE KEY UPDATE subscriptionId = '75103518754837', status = 'Active', quantity = 1;\n",\n        "affected_rows": 2\n    }\n]
[subscription_update] [2025-07-18 00:10:57] [autodesk_subscription.class.php:401] Starting subscription change processing at 2025-07-18 00:10:57
[subscription_update] [2025-07-18 00:10:57] [autodesk_subscription.class.php:418] Subscription column mapping retrieved
[subscription_update] [2025-07-18 00:10:57] [autodesk_subscription.class.php:424] Incoming payload: Array\n(\n    [id] => 686a5e62-3311-444f-9074-f40f55f8554a\n    [topic] => subscription-change\n    [event] => changed\n    [sender] => PWS Subscription Service\n    [environment] => prd\n    [payload] => Array\n        (\n            [subscriptionId] => 68561662888668\n            [status] => Suspended\n            [message] => subscription status changed.\n            [modifiedAt] => 2025-07-17T23:40:52.000+0000\n        )\n\n    [publishedAt] => 2025-07-18T00:10:55.000Z\n    [csn] => **********\n)\n
[subscription_update] [2025-07-18 00:10:57] [autodesk_subscription.class.php:483] found key: subscriptionId and mapped to autodesk_subscriptions.subscriptionId
[subscription_update] [2025-07-18 00:10:57] [autodesk_subscription.class.php:483] found key: status and mapped to autodesk_subscriptions.status
[subscription_update] [2025-07-18 00:10:57] [autodesk_subscription.class.php:495] Skipped unmapped key: message
[subscription_update] [2025-07-18 00:10:57] [autodesk_subscription.class.php:495] Skipped unmapped key: modifiedAt
[subscription_update] [2025-07-18 00:10:57] [autodesk_subscription.class.php:520] [\n    {\n        "status": "success",\n        "message": "autodesk_subscriptions updated successfull",\n        "query_sql": "INSERT INTO autodesk_subscriptions SET subscriptionId = '68561662888668', status = 'Suspended' ON DUPLICATE KEY UPDATE subscriptionId = '68561662888668', status = 'Suspended';\n",\n        "affected_rows": 2\n    }\n]
