[api_errors] [2025-06-06 22:45:47] [layout-api.comp.php:53]  Error executing API function api\nav_tree\save_nav_entry: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1
[api_errors] [2025-06-06 22:49:04] [layout-api.comp.php:53]  <!-- functions.php > tcs_log() 115: Error executing API function api\nav_tree\save_nav_entry: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 -->

[api_errors] [2025-06-06 22:50:19] [layout-api.comp.php:53]  
<!--
********************************************************************************************************************************************************
functions.php > tcs_log() 115
string(263) "Error executing API function api\nav_tree\save_nav_entry: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1"

    ---------------------------------------------------------------------------- 
      Function: tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/components/compiled/layout-api.comp.php, Line: 53
        Arguments: 
         0: "Error executing API function api\\nav_tree\\save_nav_entry: SQLSTATE[42000]: Syntax error or access...
         1: "api_errors"
         2: true
      Function: include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 171
        Arguments: 
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/resources\/comp...
      Function: phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 164
        Arguments: 
         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/api\/na...
         1: "resources\/components\/compiled\/layout-api.comp.php"
      Function: render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 201
        Arguments: 
         0: "layout-api"
         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/api\/na...
      Function: route, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 22
        Arguments: 

----------------------------------------------------------------------------
-->

