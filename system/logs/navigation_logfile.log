[navigation] [2025-06-21 12:11:07] [nav_tree.api.php:225] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n            [html] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => product_catalog.csv\n                    [full_path] => product_catalog.csv\n                    [type] => text/csv\n                    [tmp_name] => /tmp/phpiCMQ5E\n                    [error] => 0\n                    [size] => 243406\n                )\n\n        )\n\n    [key] => testy\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-06-21 12:11:07] [nav_tree.api.php:86]  View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/testy//testy.edge.php created successfully
[navigation] [2025-06-21 12:11:07] [nav_tree.api.php:110] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n            [html] => \n        )\n\n    [parent_path] => root\n    [route_key] => testy\n    [name] => testy\n    [icon] => autodesk\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-06-21 12:22:58] [nav_tree.api.php:226] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n            [html] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => product_catalog.csv\n                    [full_path] => product_catalog.csv\n                    [type] => text/csv\n                    [tmp_name] => /tmp/phpcZaXVG\n                    [error] => 0\n                    [size] => 243406\n                )\n\n        )\n\n    [key] => test1322\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-06-21 12:22:58] [nav_tree.api.php:86]  View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/test1322//test1322.edge.php created successfully
[navigation] [2025-06-21 12:22:58] [nav_tree.api.php:110] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n            [html] => \n        )\n\n    [parent_path] => root\n    [route_key] => test1322\n    [name] => test1322\n    [icon] => autodesk\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles)\n        )\n\n)\n
[navigation] [2025-06-30 11:54:00] [nav_tree.api.php:226] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/baffletrain/autocadlt/autobooks/system/templates/data_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n            [html] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => products_autodesk_catalog.csv\n                    [full_path] => products_autodesk_catalog.csv\n                    [type] => text/csv\n                    [tmp_name] => /tmp/php1mP2h6\n                    [error] => 0\n                    [size] => 804887\n                )\n\n        )\n\n    [key] => terp\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-06-30 11:54:00] [nav_tree.api.php:86]  View file /var/www/vhosts/cadservices.co.uk/test.cadservices.co.uk/baffletrain/autocadlt/autobooks/resources/views/terp//terp.edge.php created successfully
[navigation] [2025-06-30 11:54:00] [nav_tree.api.php:110] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n            [html] => \n        )\n\n    [parent_path] => root\n    [route_key] => terp\n    [name] => terp\n    [icon] => autodesk\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles)\n        )\n\n)\n
[navigation] [2025-07-16 15:14:21] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//Sketchup_csv_file_1752678861.csv
[navigation] [2025-07-16 15:14:22] [nav_tree.api.php:545] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => text/csv\n                    [size] => 57389\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//Sketchup_csv_file_1752678861.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => Sketchup\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-07-16 15:14:22] [nav_tree.api.php:311] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/Sketchup/Sketchup.edge.php created successfully
[navigation] [2025-07-16 15:14:22] [nav_tree.api.php:387] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => Sketchup\n    [name] => Sketchup\n    [icon] => cube\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system)\n        )\n\n)\n
[navigation] [2025-07-16 15:43:07] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//bluebeam_csv_file_1752680587.csv
[navigation] [2025-07-16 15:43:08] [nav_tree.api.php:545] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 62916\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//bluebeam_csv_file_1752680587.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-07-16 15:43:09] [nav_tree.api.php:311] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-07-16 15:43:09] [nav_tree.api.php:387] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => BlueBeam\n    [icon] => bold\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system)\n        )\n\n)\n
[navigation] [2025-07-16 18:59:35] [nav_tree.api.php:850] Array\n(\n    [action] => navigation_entry_deleted\n    [parent_path] => root\n    [route_key] => Sketchup\n    [name] => Sketchup\n    [user_id] => 2\n)\n
[navigation] [2025-07-16 23:25:49] [nav_tree.api.php:545] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatescustom_html_template.hilt.php\n    [template_data] => Array\n        (\n            [html] => \n        )\n\n    [files] => Array\n        (\n        )\n\n    [key] => sketch\n    [template_type] => custom_html\n    [hilt_template_type] => html\n)\n
[navigation] [2025-07-16 23:25:49] [nav_tree.api.php:311] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/sketch/sketch.edge.php created successfully
[navigation] [2025-07-16 23:25:49] [nav_tree.api.php:387] Array\n(\n    [action] => navigation_entry_created\n    [template] => custom_html_template\n    [template_data] => Array\n        (\n            [html] => \n        )\n\n    [parent_path] => root\n    [route_key] => sketch\n    [name] => sketch\n    [icon] => sketchup\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system)\n        )\n\n)\n
[navigation] [2025-07-17 09:09:06] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//sketchup_csv_file_1752743346.csv
[navigation] [2025-07-17 09:09:07] [nav_tree.api.php:545] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => application/vnd.ms-excel\n                    [size] => 57389\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//sketchup_csv_file_1752743346.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => sketchup\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-07-17 09:09:07] [nav_tree.api.php:311] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/sketchup/sketchup.edge.php created successfully
[navigation] [2025-07-17 09:09:07] [nav_tree.api.php:387] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => Sketchup\n    [icon] => sketchup\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system)\n        )\n\n)\n
[navigation] [2025-07-17 23:09:56] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//Bluebeam_csv_file_1752793796.csv
[navigation] [2025-07-17 23:09:57] [nav_tree.api.php:545] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 62916\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//Bluebeam_csv_file_1752793796.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => Bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-07-17 23:09:58] [nav_tree.api.php:311] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/Bluebeam/Bluebeam.edge.php created successfully
[navigation] [2025-07-17 23:09:58] [nav_tree.api.php:387] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => Bluebeam\n    [name] => Bluebeam\n    [icon] => bold\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system)\n        )\n\n)\n
[navigation] [2025-07-18 00:27:46] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//Bluebeam_csv_file_1752798466.csv
[navigation] [2025-07-18 00:27:47] [nav_tree.api.php:545] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 62916\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//Bluebeam_csv_file_1752798466.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => Bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-07-18 00:27:48] [nav_tree.api.php:311] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/Bluebeam/Bluebeam.edge.php created successfully
[navigation] [2025-07-18 00:27:48] [nav_tree.api.php:387] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => Bluebeam\n    [name] => Bluebeam\n    [icon] => bold\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system)\n        )\n\n)\n
[navigation] [2025-07-18 00:40:19] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//bluebeam_csv_file_1752799219.csv
[navigation] [2025-07-18 00:40:20] [nav_tree.api.php:545] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 62916\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//bluebeam_csv_file_1752799219.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-07-18 00:40:20] [nav_tree.api.php:311] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-07-18 00:40:20] [nav_tree.api.php:387] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => bluebeam\n    [icon] => sketchup\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system)\n        )\n\n)\n
[navigation] [2025-07-18 00:45:13] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//bluebeamtest2_csv_file_1752799513.csv
[navigation] [2025-07-18 00:45:14] [nav_tree.api.php:545] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 62916\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//bluebeamtest2_csv_file_1752799513.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeamtest2\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-07-18 00:45:15] [nav_tree.api.php:311] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeamtest2/bluebeamtest2.edge.php created successfully
[navigation] [2025-07-18 00:45:15] [nav_tree.api.php:387] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeamtest2\n    [name] => bluebeamtest2\n    [icon] => sketchup\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system)\n        )\n\n)\n
[navigation] [2025-07-18 00:49:11] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//bb3_csv_file_1752799751.csv
[navigation] [2025-07-18 00:49:12] [nav_tree.api.php:545] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 62916\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//bb3_csv_file_1752799751.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bb3\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-07-18 00:49:12] [nav_tree.api.php:311] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bb3/bb3.edge.php created successfully
[navigation] [2025-07-18 00:49:12] [nav_tree.api.php:387] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bb3\n    [name] => bb3\n    [icon] => sketchup\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system)\n        )\n\n)\n
[navigation] [2025-07-18 00:52:42] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//BB4_csv_file_1752799962.csv
[navigation] [2025-07-18 00:52:43] [nav_tree.api.php:545] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 62916\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//BB4_csv_file_1752799962.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => BB4\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-07-18 00:52:43] [nav_tree.api.php:311] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/BB4/BB4.edge.php created successfully
[navigation] [2025-07-18 00:52:43] [nav_tree.api.php:387] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => BB4\n    [name] => BB4\n    [icon] => sketchup\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system)\n        )\n\n)\n
[navigation] [2025-07-18 00:55:53] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//bb5_csv_file_1752800153.csv
[navigation] [2025-07-18 00:55:54] [nav_tree.api.php:545] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 62916\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads//bb5_csv_file_1752800153.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bb5\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-07-18 00:55:55] [nav_tree.api.php:311] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bb5/bb5.edge.php created successfully
[navigation] [2025-07-18 00:55:55] [nav_tree.api.php:387] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bb5\n    [name] => bb5\n    [icon] => sketchup\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system)\n        )\n\n)\n
[navigation] [2025-07-21 23:40:47] [nav_tree.api.php:545] Array\n(\n    [template_file] => E:\Build\httpdocs\baffletrain\autocadlt\autobooks\system\templatescustom_html_template.hilt.php\n    [template_data] => Array\n        (\n            [html] => <div>This is a test</div>\n        )\n\n    [files] => Array\n        (\n        )\n\n    [key] => test\n    [template_type] => custom_html\n    [hilt_template_type] => html\n)\n
