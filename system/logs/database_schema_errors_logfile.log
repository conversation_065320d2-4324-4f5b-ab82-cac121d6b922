[database_schema_errors] [2025-07-16 15:14:22] [database.class.php:1011] array(13) {\n  ["error_code"]: string(5) "42S21"\n  ["error_message"]: string(79) "SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'table_name'"\n  ["sql_state"]: string(5) "42S21"\n  ["driver_error_code"]: int(1060)\n  ["driver_error_message"]: string(34) "Duplicate column name 'table_name'"\n  ["query"]: string(488) "CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))"\n  ["parameters"]: array(0) {\n  }\n  ["operation_type"]: string(6) "SCHEMA"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(76) "/baffletrain/autocadlt/autobooks/api/system/nav_tree/save_nav_entry_progress"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(2727) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(1205): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(1044): system\Blueprint->execute()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/table_config_manager.class.php(24): system\Schema::create()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/table_config_manager.class.php(63): system\table_config_manager::ensure_config_table_exists()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1586): system\table_config_manager::store_table_config()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1414): system\data_importer::generate_and_store_table_config()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1343): system\data_importer::import_csv_with_auto_schema()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(142): system\data_importer::import_csv_to_hilt_table()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(77): system\hilt::import_csv_data()\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(38): system\hilt::process_database_template()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(554): system\hilt::process_template()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(280): api\nav_tree\generate_view_content()\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(150): api\nav_tree\process_nav_entry_async()\n#13 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-api.edge.php(60): api\nav_tree\save_nav_entry_progress()\n#14 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#15 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#16 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()\n#17 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(26): system\router::route()\n#18 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1011\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S21","error_message":"SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'table_name'","sql_state":"42S21","driver_error_code":1060,"driver_error_message":"Duplicate column name 'table_name'","query":"CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))","parameters":[],"operation_type":"SCHEMA","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/system\/nav_tree\/save_nav_entry_progress","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko\/20100101 Firefox\/140.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(1205): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(1044): system\\Blueprint->execute()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/table_config_manager.class.php(24): system\\Schema::create()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/table_config_manager.class.php(63): system\\table_config_manager::ensure_config_table_exists()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1586): system\\table_config_manager::store_table_config()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1414): system\\data_importer::generate_and_store_table_config()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1343): system\\data_importer::import_csv_with_auto_schema()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(142): system\\data_importer::import_csv_to_hilt_table()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(77): system\\hilt::import_csv_data()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(38): system\\hilt::process_database_template()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(554): system\\hilt::process_template()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(280): api\\nav_tree\\generate_view_content()\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(150): api\\nav_tree\\process_nav_entry_async()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-api.edge.php(60): api\\nav_tree\\save_nav_entry_progress()\n#14 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#15 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#16 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(208): edge\\edge::render()\n#17 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(26): system\\router::route()\n#18 {main}"}\n         1: "database_schema_errors"\n         2: true\n      <strong>Function:</strong> handleSchemaError, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1209\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S21",1060,"Duplicate column name 'table_name'"]}\n         1: "CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))"\n         2: []\n      <strong>Function:</strong> execute, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1044\n         <strong>Arguments:</strong> \n-->\n
[database_schema_errors] [2025-07-16 15:43:09] [database.class.php:1011] array(13) {\n  ["error_code"]: string(5) "42S21"\n  ["error_message"]: string(79) "SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'table_name'"\n  ["sql_state"]: string(5) "42S21"\n  ["driver_error_code"]: int(1060)\n  ["driver_error_message"]: string(34) "Duplicate column name 'table_name'"\n  ["query"]: string(488) "CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))"\n  ["parameters"]: array(0) {\n  }\n  ["operation_type"]: string(6) "SCHEMA"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(76) "/baffletrain/autocadlt/autobooks/api/system/nav_tree/save_nav_entry_progress"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(2727) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(1205): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(1044): system\Blueprint->execute()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/table_config_manager.class.php(24): system\Schema::create()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/table_config_manager.class.php(63): system\table_config_manager::ensure_config_table_exists()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1586): system\table_config_manager::store_table_config()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1414): system\data_importer::generate_and_store_table_config()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1343): system\data_importer::import_csv_with_auto_schema()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(142): system\data_importer::import_csv_to_hilt_table()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(77): system\hilt::import_csv_data()\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(38): system\hilt::process_database_template()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(554): system\hilt::process_template()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(280): api\nav_tree\generate_view_content()\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(150): api\nav_tree\process_nav_entry_async()\n#13 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-api.edge.php(60): api\nav_tree\save_nav_entry_progress()\n#14 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#15 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#16 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()\n#17 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(26): system\router::route()\n#18 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1011\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S21","error_message":"SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'table_name'","sql_state":"42S21","driver_error_code":1060,"driver_error_message":"Duplicate column name 'table_name'","query":"CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))","parameters":[],"operation_type":"SCHEMA","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/system\/nav_tree\/save_nav_entry_progress","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko\/20100101 Firefox\/140.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(1205): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(1044): system\\Blueprint->execute()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/table_config_manager.class.php(24): system\\Schema::create()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/table_config_manager.class.php(63): system\\table_config_manager::ensure_config_table_exists()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1586): system\\table_config_manager::store_table_config()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1414): system\\data_importer::generate_and_store_table_config()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1343): system\\data_importer::import_csv_with_auto_schema()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(142): system\\data_importer::import_csv_to_hilt_table()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(77): system\\hilt::import_csv_data()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(38): system\\hilt::process_database_template()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(554): system\\hilt::process_template()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(280): api\\nav_tree\\generate_view_content()\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(150): api\\nav_tree\\process_nav_entry_async()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-api.edge.php(60): api\\nav_tree\\save_nav_entry_progress()\n#14 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#15 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#16 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(208): edge\\edge::render()\n#17 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(26): system\\router::route()\n#18 {main}"}\n         1: "database_schema_errors"\n         2: true\n      <strong>Function:</strong> handleSchemaError, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1209\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S21",1060,"Duplicate column name 'table_name'"]}\n         1: "CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))"\n         2: []\n      <strong>Function:</strong> execute, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1044\n         <strong>Arguments:</strong> \n-->\n
[database_schema_errors] [2025-07-17 09:09:07] [database.class.php:1087] 
[database_schema_errors] [2025-07-17 23:09:58] [database.class.php:1087] array(13) {\n  ["error_code"]: string(5) "42S21"\n  ["error_message"]: string(79) "SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'table_name'"\n  ["sql_state"]: string(5) "42S21"\n  ["driver_error_code"]: int(1060)\n  ["driver_error_message"]: string(34) "Duplicate column name 'table_name'"\n  ["query"]: string(488) "CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))"\n  ["parameters"]: array(0) {\n  }\n  ["operation_type"]: string(6) "SCHEMA"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(76) "/baffletrain/autocadlt/autobooks/api/system/nav_tree/save_nav_entry_progress"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(2727) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(1281): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(1120): system\Blueprint->execute()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/table_config_manager.class.php(24): system\Schema::create()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/table_config_manager.class.php(63): system\table_config_manager::ensure_config_table_exists()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1586): system\table_config_manager::store_table_config()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1414): system\data_importer::generate_and_store_table_config()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1343): system\data_importer::import_csv_with_auto_schema()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(142): system\data_importer::import_csv_to_hilt_table()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(77): system\hilt::import_csv_data()\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(38): system\hilt::process_database_template()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(554): system\hilt::process_template()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(280): api\nav_tree\generate_view_content()\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(150): api\nav_tree\process_nav_entry_async()\n#13 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-api.edge.php(60): api\nav_tree\save_nav_entry_progress()\n#14 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#15 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#16 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()\n#17 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(30): system\router::route()\n#18 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1087\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S21","error_message":"SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'table_name'","sql_state":"42S21","driver_error_code":1060,"driver_error_message":"Duplicate column name 'table_name'","query":"CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))","parameters":[],"operation_type":"SCHEMA","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/system\/nav_tree\/save_nav_entry_progress","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko\/20100101 Firefox\/140.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(1281): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(1120): system\\Blueprint->execute()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/table_config_manager.class.php(24): system\\Schema::create()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/table_config_manager.class.php(63): system\\table_config_manager::ensure_config_table_exists()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1586): system\\table_config_manager::store_table_config()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1414): system\\data_importer::generate_and_store_table_config()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1343): system\\data_importer::import_csv_with_auto_schema()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(142): system\\data_importer::import_csv_to_hilt_table()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(77): system\\hilt::import_csv_data()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(38): system\\hilt::process_database_template()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(554): system\\hilt::process_template()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(280): api\\nav_tree\\generate_view_content()\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(150): api\\nav_tree\\process_nav_entry_async()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-api.edge.php(60): api\\nav_tree\\save_nav_entry_progress()\n#14 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#15 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#16 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(208): edge\\edge::render()\n#17 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(30): system\\router::route()\n#18 {main}"}\n         1: "database_schema_errors"\n         2: true\n      <strong>Function:</strong> handleSchemaError, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1285\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S21",1060,"Duplicate column name 'table_name'"]}\n         1: "CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))"\n         2: []\n      <strong>Function:</strong> execute, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1120\n         <strong>Arguments:</strong> \n-->\n
[database_schema_errors] [2025-07-18 00:27:48] [database.class.php:1087] array(13) {\n  ["error_code"]: string(5) "42S21"\n  ["error_message"]: string(79) "SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'table_name'"\n  ["sql_state"]: string(5) "42S21"\n  ["driver_error_code"]: int(1060)\n  ["driver_error_message"]: string(34) "Duplicate column name 'table_name'"\n  ["query"]: string(488) "CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))"\n  ["parameters"]: array(0) {\n  }\n  ["operation_type"]: string(6) "SCHEMA"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(76) "/baffletrain/autocadlt/autobooks/api/system/nav_tree/save_nav_entry_progress"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(2727) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(1281): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(1120): system\Blueprint->execute()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/table_config_manager.class.php(24): system\Schema::create()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/table_config_manager.class.php(63): system\table_config_manager::ensure_config_table_exists()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1586): system\table_config_manager::store_table_config()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1414): system\data_importer::generate_and_store_table_config()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1343): system\data_importer::import_csv_with_auto_schema()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(142): system\data_importer::import_csv_to_hilt_table()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(77): system\hilt::import_csv_data()\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(38): system\hilt::process_database_template()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(554): system\hilt::process_template()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(280): api\nav_tree\generate_view_content()\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(150): api\nav_tree\process_nav_entry_async()\n#13 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-api.edge.php(60): api\nav_tree\save_nav_entry_progress()\n#14 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#15 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#16 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()\n#17 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(30): system\router::route()\n#18 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1087\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S21","error_message":"SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'table_name'","sql_state":"42S21","driver_error_code":1060,"driver_error_message":"Duplicate column name 'table_name'","query":"CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))","parameters":[],"operation_type":"SCHEMA","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/system\/nav_tree\/save_nav_entry_progress","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko\/20100101 Firefox\/140.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(1281): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(1120): system\\Blueprint->execute()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/table_config_manager.class.php(24): system\\Schema::create()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/table_config_manager.class.php(63): system\\table_config_manager::ensure_config_table_exists()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1586): system\\table_config_manager::store_table_config()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1414): system\\data_importer::generate_and_store_table_config()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1343): system\\data_importer::import_csv_with_auto_schema()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(142): system\\data_importer::import_csv_to_hilt_table()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(77): system\\hilt::import_csv_data()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(38): system\\hilt::process_database_template()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(554): system\\hilt::process_template()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(280): api\\nav_tree\\generate_view_content()\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(150): api\\nav_tree\\process_nav_entry_async()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-api.edge.php(60): api\\nav_tree\\save_nav_entry_progress()\n#14 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#15 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#16 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(208): edge\\edge::render()\n#17 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(30): system\\router::route()\n#18 {main}"}\n         1: "database_schema_errors"\n         2: true\n      <strong>Function:</strong> handleSchemaError, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1285\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S21",1060,"Duplicate column name 'table_name'"]}\n         1: "CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))"\n         2: []\n      <strong>Function:</strong> execute, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1120\n         <strong>Arguments:</strong> \n-->\n
[database_schema_errors] [2025-07-18 00:40:20] [database.class.php:1087] array(13) {\n  ["error_code"]: string(5) "42S21"\n  ["error_message"]: string(79) "SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'table_name'"\n  ["sql_state"]: string(5) "42S21"\n  ["driver_error_code"]: int(1060)\n  ["driver_error_message"]: string(34) "Duplicate column name 'table_name'"\n  ["query"]: string(488) "CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))"\n  ["parameters"]: array(0) {\n  }\n  ["operation_type"]: string(6) "SCHEMA"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(76) "/baffletrain/autocadlt/autobooks/api/system/nav_tree/save_nav_entry_progress"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(2727) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(1281): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(1120): system\Blueprint->execute()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/table_config_manager.class.php(24): system\Schema::create()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/table_config_manager.class.php(63): system\table_config_manager::ensure_config_table_exists()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1586): system\table_config_manager::store_table_config()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1414): system\data_importer::generate_and_store_table_config()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1343): system\data_importer::import_csv_with_auto_schema()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(142): system\data_importer::import_csv_to_hilt_table()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(77): system\hilt::import_csv_data()\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(38): system\hilt::process_database_template()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(554): system\hilt::process_template()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(280): api\nav_tree\generate_view_content()\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(150): api\nav_tree\process_nav_entry_async()\n#13 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-api.edge.php(60): api\nav_tree\save_nav_entry_progress()\n#14 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#15 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#16 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()\n#17 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(30): system\router::route()\n#18 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1087\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S21","error_message":"SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'table_name'","sql_state":"42S21","driver_error_code":1060,"driver_error_message":"Duplicate column name 'table_name'","query":"CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))","parameters":[],"operation_type":"SCHEMA","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/system\/nav_tree\/save_nav_entry_progress","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko\/20100101 Firefox\/140.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(1281): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(1120): system\\Blueprint->execute()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/table_config_manager.class.php(24): system\\Schema::create()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/table_config_manager.class.php(63): system\\table_config_manager::ensure_config_table_exists()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1586): system\\table_config_manager::store_table_config()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1414): system\\data_importer::generate_and_store_table_config()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1343): system\\data_importer::import_csv_with_auto_schema()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(142): system\\data_importer::import_csv_to_hilt_table()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(77): system\\hilt::import_csv_data()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(38): system\\hilt::process_database_template()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(554): system\\hilt::process_template()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(280): api\\nav_tree\\generate_view_content()\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(150): api\\nav_tree\\process_nav_entry_async()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-api.edge.php(60): api\\nav_tree\\save_nav_entry_progress()\n#14 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#15 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#16 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(208): edge\\edge::render()\n#17 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(30): system\\router::route()\n#18 {main}"}\n         1: "database_schema_errors"\n         2: true\n      <strong>Function:</strong> handleSchemaError, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1285\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S21",1060,"Duplicate column name 'table_name'"]}\n         1: "CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))"\n         2: []\n      <strong>Function:</strong> execute, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1120\n         <strong>Arguments:</strong> \n-->\n
[database_schema_errors] [2025-07-18 00:45:15] [database.class.php:1087] array(13) {\n  ["error_code"]: string(5) "42S21"\n  ["error_message"]: string(79) "SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'table_name'"\n  ["sql_state"]: string(5) "42S21"\n  ["driver_error_code"]: int(1060)\n  ["driver_error_message"]: string(34) "Duplicate column name 'table_name'"\n  ["query"]: string(488) "CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))"\n  ["parameters"]: array(0) {\n  }\n  ["operation_type"]: string(6) "SCHEMA"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(76) "/baffletrain/autocadlt/autobooks/api/system/nav_tree/save_nav_entry_progress"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(2727) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(1281): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(1120): system\Blueprint->execute()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/table_config_manager.class.php(24): system\Schema::create()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/table_config_manager.class.php(63): system\table_config_manager::ensure_config_table_exists()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1586): system\table_config_manager::store_table_config()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1414): system\data_importer::generate_and_store_table_config()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1343): system\data_importer::import_csv_with_auto_schema()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(142): system\data_importer::import_csv_to_hilt_table()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(77): system\hilt::import_csv_data()\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(38): system\hilt::process_database_template()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(554): system\hilt::process_template()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(280): api\nav_tree\generate_view_content()\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(150): api\nav_tree\process_nav_entry_async()\n#13 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-api.edge.php(60): api\nav_tree\save_nav_entry_progress()\n#14 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#15 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#16 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()\n#17 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(30): system\router::route()\n#18 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1087\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S21","error_message":"SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'table_name'","sql_state":"42S21","driver_error_code":1060,"driver_error_message":"Duplicate column name 'table_name'","query":"CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))","parameters":[],"operation_type":"SCHEMA","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/system\/nav_tree\/save_nav_entry_progress","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko\/20100101 Firefox\/140.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(1281): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(1120): system\\Blueprint->execute()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/table_config_manager.class.php(24): system\\Schema::create()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/table_config_manager.class.php(63): system\\table_config_manager::ensure_config_table_exists()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1586): system\\table_config_manager::store_table_config()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1414): system\\data_importer::generate_and_store_table_config()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1343): system\\data_importer::import_csv_with_auto_schema()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(142): system\\data_importer::import_csv_to_hilt_table()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(77): system\\hilt::import_csv_data()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(38): system\\hilt::process_database_template()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(554): system\\hilt::process_template()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(280): api\\nav_tree\\generate_view_content()\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(150): api\\nav_tree\\process_nav_entry_async()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-api.edge.php(60): api\\nav_tree\\save_nav_entry_progress()\n#14 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#15 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#16 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(208): edge\\edge::render()\n#17 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(30): system\\router::route()\n#18 {main}"}\n         1: "database_schema_errors"\n         2: true\n      <strong>Function:</strong> handleSchemaError, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1285\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S21",1060,"Duplicate column name 'table_name'"]}\n         1: "CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))"\n         2: []\n      <strong>Function:</strong> execute, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1120\n         <strong>Arguments:</strong> \n-->\n
[database_schema_errors] [2025-07-18 00:49:12] [database.class.php:1087] array(13) {\n  ["error_code"]: string(5) "42S21"\n  ["error_message"]: string(79) "SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'table_name'"\n  ["sql_state"]: string(5) "42S21"\n  ["driver_error_code"]: int(1060)\n  ["driver_error_message"]: string(34) "Duplicate column name 'table_name'"\n  ["query"]: string(488) "CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))"\n  ["parameters"]: array(0) {\n  }\n  ["operation_type"]: string(6) "SCHEMA"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(76) "/baffletrain/autocadlt/autobooks/api/system/nav_tree/save_nav_entry_progress"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(2727) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(1281): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(1120): system\Blueprint->execute()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/table_config_manager.class.php(24): system\Schema::create()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/table_config_manager.class.php(63): system\table_config_manager::ensure_config_table_exists()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1586): system\table_config_manager::store_table_config()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1414): system\data_importer::generate_and_store_table_config()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1343): system\data_importer::import_csv_with_auto_schema()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(142): system\data_importer::import_csv_to_hilt_table()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(77): system\hilt::import_csv_data()\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(38): system\hilt::process_database_template()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(554): system\hilt::process_template()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(280): api\nav_tree\generate_view_content()\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(150): api\nav_tree\process_nav_entry_async()\n#13 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-api.edge.php(60): api\nav_tree\save_nav_entry_progress()\n#14 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#15 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#16 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()\n#17 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(30): system\router::route()\n#18 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1087\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S21","error_message":"SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'table_name'","sql_state":"42S21","driver_error_code":1060,"driver_error_message":"Duplicate column name 'table_name'","query":"CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))","parameters":[],"operation_type":"SCHEMA","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/system\/nav_tree\/save_nav_entry_progress","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko\/20100101 Firefox\/140.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(1281): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(1120): system\\Blueprint->execute()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/table_config_manager.class.php(24): system\\Schema::create()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/table_config_manager.class.php(63): system\\table_config_manager::ensure_config_table_exists()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1586): system\\table_config_manager::store_table_config()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1414): system\\data_importer::generate_and_store_table_config()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1343): system\\data_importer::import_csv_with_auto_schema()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(142): system\\data_importer::import_csv_to_hilt_table()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(77): system\\hilt::import_csv_data()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(38): system\\hilt::process_database_template()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(554): system\\hilt::process_template()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(280): api\\nav_tree\\generate_view_content()\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(150): api\\nav_tree\\process_nav_entry_async()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-api.edge.php(60): api\\nav_tree\\save_nav_entry_progress()\n#14 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#15 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#16 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(208): edge\\edge::render()\n#17 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(30): system\\router::route()\n#18 {main}"}\n         1: "database_schema_errors"\n         2: true\n      <strong>Function:</strong> handleSchemaError, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1285\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S21",1060,"Duplicate column name 'table_name'"]}\n         1: "CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))"\n         2: []\n      <strong>Function:</strong> execute, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1120\n         <strong>Arguments:</strong> \n-->\n
[database_schema_errors] [2025-07-18 00:52:43] [database.class.php:1087] array(13) {\n  ["error_code"]: string(5) "42S21"\n  ["error_message"]: string(79) "SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'table_name'"\n  ["sql_state"]: string(5) "42S21"\n  ["driver_error_code"]: int(1060)\n  ["driver_error_message"]: string(34) "Duplicate column name 'table_name'"\n  ["query"]: string(488) "CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))"\n  ["parameters"]: array(0) {\n  }\n  ["operation_type"]: string(6) "SCHEMA"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(76) "/baffletrain/autocadlt/autobooks/api/system/nav_tree/save_nav_entry_progress"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(2727) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(1281): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(1120): system\Blueprint->execute()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/table_config_manager.class.php(24): system\Schema::create()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/table_config_manager.class.php(63): system\table_config_manager::ensure_config_table_exists()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1586): system\table_config_manager::store_table_config()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1414): system\data_importer::generate_and_store_table_config()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1343): system\data_importer::import_csv_with_auto_schema()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(142): system\data_importer::import_csv_to_hilt_table()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(77): system\hilt::import_csv_data()\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(38): system\hilt::process_database_template()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(554): system\hilt::process_template()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(280): api\nav_tree\generate_view_content()\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(150): api\nav_tree\process_nav_entry_async()\n#13 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-api.edge.php(60): api\nav_tree\save_nav_entry_progress()\n#14 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#15 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#16 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()\n#17 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(30): system\router::route()\n#18 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1087\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S21","error_message":"SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'table_name'","sql_state":"42S21","driver_error_code":1060,"driver_error_message":"Duplicate column name 'table_name'","query":"CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))","parameters":[],"operation_type":"SCHEMA","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/system\/nav_tree\/save_nav_entry_progress","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko\/20100101 Firefox\/140.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(1281): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(1120): system\\Blueprint->execute()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/table_config_manager.class.php(24): system\\Schema::create()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/table_config_manager.class.php(63): system\\table_config_manager::ensure_config_table_exists()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1586): system\\table_config_manager::store_table_config()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1414): system\\data_importer::generate_and_store_table_config()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1343): system\\data_importer::import_csv_with_auto_schema()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(142): system\\data_importer::import_csv_to_hilt_table()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(77): system\\hilt::import_csv_data()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(38): system\\hilt::process_database_template()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(554): system\\hilt::process_template()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(280): api\\nav_tree\\generate_view_content()\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(150): api\\nav_tree\\process_nav_entry_async()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-api.edge.php(60): api\\nav_tree\\save_nav_entry_progress()\n#14 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#15 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#16 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(208): edge\\edge::render()\n#17 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(30): system\\router::route()\n#18 {main}"}\n         1: "database_schema_errors"\n         2: true\n      <strong>Function:</strong> handleSchemaError, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1285\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S21",1060,"Duplicate column name 'table_name'"]}\n         1: "CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))"\n         2: []\n      <strong>Function:</strong> execute, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1120\n         <strong>Arguments:</strong> \n-->\n
[database_schema_errors] [2025-07-18 00:55:55] [database.class.php:1087] array(13) {\n  ["error_code"]: string(5) "42S21"\n  ["error_message"]: string(79) "SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'table_name'"\n  ["sql_state"]: string(5) "42S21"\n  ["driver_error_code"]: int(1060)\n  ["driver_error_message"]: string(34) "Duplicate column name 'table_name'"\n  ["query"]: string(488) "CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))"\n  ["parameters"]: array(0) {\n  }\n  ["operation_type"]: string(6) "SCHEMA"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(76) "/baffletrain/autocadlt/autobooks/api/system/nav_tree/save_nav_entry_progress"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(2727) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(1281): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(1120): system\Blueprint->execute()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/table_config_manager.class.php(24): system\Schema::create()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/table_config_manager.class.php(63): system\table_config_manager::ensure_config_table_exists()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1586): system\table_config_manager::store_table_config()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1414): system\data_importer::generate_and_store_table_config()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_importer.class.php(1343): system\data_importer::import_csv_with_auto_schema()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(142): system\data_importer::import_csv_to_hilt_table()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(77): system\hilt::import_csv_data()\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/hilt.class.php(38): system\hilt::process_database_template()\n#10 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(554): system\hilt::process_template()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(280): api\nav_tree\generate_view_content()\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/nav_tree.api.php(150): api\nav_tree\process_nav_entry_async()\n#13 /var/www/vhosts/cadservices.co.uk/temp/autobooks/layout-api.edge.php(60): api\nav_tree\save_nav_entry_progress()\n#14 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(134): include('...')\n#15 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(127): edge\edge::phprender()\n#16 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(208): edge\edge::render()\n#17 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(30): system\router::route()\n#18 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1087\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S21","error_message":"SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'table_name'","sql_state":"42S21","driver_error_code":1060,"driver_error_message":"Duplicate column name 'table_name'","query":"CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))","parameters":[],"operation_type":"SCHEMA","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/api\/system\/nav_tree\/save_nav_entry_progress","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko\/20100101 Firefox\/140.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(1281): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(1120): system\\Blueprint->execute()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/table_config_manager.class.php(24): system\\Schema::create()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/table_config_manager.class.php(63): system\\table_config_manager::ensure_config_table_exists()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1586): system\\table_config_manager::store_table_config()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1414): system\\data_importer::generate_and_store_table_config()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_importer.class.php(1343): system\\data_importer::import_csv_with_auto_schema()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(142): system\\data_importer::import_csv_to_hilt_table()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(77): system\\hilt::import_csv_data()\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/hilt.class.php(38): system\\hilt::process_database_template()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(554): system\\hilt::process_template()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(280): api\\nav_tree\\generate_view_content()\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/api\/nav_tree.api.php(150): api\\nav_tree\\process_nav_entry_async()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/layout-api.edge.php(60): api\\nav_tree\\save_nav_entry_progress()\n#14 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(134): include('...')\n#15 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(127): edge\\edge::phprender()\n#16 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(208): edge\\edge::render()\n#17 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(30): system\\router::route()\n#18 {main}"}\n         1: "database_schema_errors"\n         2: true\n      <strong>Function:</strong> handleSchemaError, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1285\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S21",1060,"Duplicate column name 'table_name'"]}\n         1: "CREATE TABLE autobooks_table_configs (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `table_name` VARCHAR(255), `route_key` VARCHAR(255), `table_schema` JSON, `table_config` JSON, `column_mappings` JSON, `description` TEXT NULL, `data_source` VARCHAR(100) DEFAULT 'csv', `is_active` BOOLEAN DEFAULT 1, `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `table_name` VARCHAR(255) UNIQUE, `route_key` VARCHAR(255))"\n         2: []\n      <strong>Function:</strong> execute, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 1120\n         <strong>Arguments:</strong> \n-->\n
