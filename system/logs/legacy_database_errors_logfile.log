[legacy_database_errors] [2025-07-11 09:22:16] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'subscription_reference_number' in 'field list'\n    [query] => INSERT INTO `autodesk_subscription_history`(\n                `id`,\n                `subscription_id`,\n                `subscription_reference_number`,\n                `media`,\n                `message`,\n                `user_id`,\n                `date`,\n                `triggered_by`,\n                `result`\n            )\n            VALUES(\n                NULL,\n                '15',\n                '573-98887491',\n                'E-Mail',\n                'Automatic E-Mail sent by Autobooks',\n                1,\n                NOW(),\n                '60',\n                '')\n        \n    [parameters] => Array\n        (\n        )\n\n    [user_id] => Anonymous\n    [request_uri] => /baffletrain/autocadlt/autobooks/send_email.php\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-11 09:22:16\n)\n
[legacy_database_errors] [2025-07-11 09:25:35] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'subscription_reference_number' in 'field list'\n    [query] => INSERT INTO `autodesk_subscription_history`(\n                `id`,\n                `subscription_id`,\n                `subscription_reference_number`,\n                `media`,\n                `message`,\n                `user_id`,\n                `date`,\n                `triggered_by`,\n                `result`\n            )\n            VALUES(\n                NULL,\n                '15',\n                '573-98887491',\n                'E-Mail',\n                'Automatic E-Mail sent by Autobooks',\n                1,\n                NOW(),\n                '60',\n                '')\n        \n    [parameters] => Array\n        (\n        )\n\n    [user_id] => Anonymous\n    [request_uri] => /baffletrain/autocadlt/autobooks/send_email.php\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-11 09:25:35\n)\n
[legacy_database_errors] [2025-07-11 09:39:12] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'subscription_id' in 'field list'\n    [query] => INSERT INTO `autodesk_history`(\n                `id`,\n                `subscription_id`,\n                `subscription_reference_number`,\n                `media`,\n                `message`,\n                `user_id`,\n                `date`,\n                `triggered_by`,\n                `result`\n            )\n            VALUES(\n                NULL,\n                '15',\n                '573-98887491',\n                'E-Mail',\n                'Automatic E-Mail sent by Autobooks',\n                1,\n                NOW(),\n                '60',\n                '')\n        \n    [parameters] => Array\n        (\n        )\n\n    [user_id] => Anonymous\n    [request_uri] => /baffletrain/autocadlt/autobooks/send_email.php\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-11 09:39:12\n)\n
[legacy_database_errors] [2025-07-11 09:42:02] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'subscription_id' in 'field list'\n    [query] => INSERT INTO `autodesk_history`(\n                `id`,\n                `subscription_id`,\n                `subscription_reference_number`,\n                `media`,\n                `message`,\n                `user_id`,\n                `date`,\n                `triggered_by`,\n                `result`\n            )\n            VALUES(\n                NULL,\n                '15',\n                '573-98887491',\n                'E-Mail',\n                'Automatic E-Mail sent by Autobooks',\n                1,\n                NOW(),\n                '60',\n                '')\n        \n    [parameters] => Array\n        (\n        )\n\n    [user_id] => Anonymous\n    [request_uri] => /baffletrain/autocadlt/autobooks/send_email.php\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-11 09:42:02\n)\n
[legacy_database_errors] [2025-07-11 09:46:41] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'subscription_id' in 'field list'\n    [query] => INSERT INTO `autodesk_history`(\n                `id`,\n                `subscription_id`,\n                `subscription_reference_number`,\n                `media`,\n                `message`,\n                `user_id`,\n                `date`,\n                `triggered_by`,\n                `result`\n            )\n            VALUES(\n                NULL,\n                '15',\n                '573-98887491',\n                'E-Mail',\n                'Automatic E-Mail sent by Autobooks',\n                1,\n                NOW(),\n                '60',\n                '')\n        \n    [parameters] => Array\n        (\n        )\n\n    [user_id] => Anonymous\n    [request_uri] => /baffletrain/autocadlt/autobooks/send_email.php\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-11 09:46:41\n)\n
[legacy_database_errors] [2025-07-11 09:58:15] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'subscription_id' in 'field list'\n    [query] => INSERT INTO `autodesk_history`(\n                `id`,\n                `subscription_id`,\n                `subscription_reference_number`,\n                `media`,\n                `message`,\n                `user_id`,\n                `date`,\n                `triggered_by`,\n                `result`\n            )\n            VALUES(\n                NULL,\n                '15',\n                '573-98887491',\n                'E-Mail',\n                'Automatic E-Mail sent by Autobooks',\n                1,\n                NOW(),\n                '60',\n                '')\n        \n    [parameters] => Array\n        (\n        )\n\n    [user_id] => Anonymous\n    [request_uri] => /baffletrain/autocadlt/autobooks/send_email.php\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-11 09:58:15\n)\n
[legacy_database_errors] [2025-07-11 09:59:16] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'subscription_id' in 'field list'\n    [query] => INSERT INTO `autodesk_history`(\n                `id`,\n                `subscription_id`,\n                `subscription_reference_number`,\n                `media`,\n                `message`,\n                `user_id`,\n                `date`,\n                `triggered_by`,\n                `result`\n            )\n            VALUES(\n                NULL,\n                '15',\n                '573-98887491',\n                'E-Mail',\n                'Automatic E-Mail sent by Autobooks',\n                1,\n                NOW(),\n                '60',\n                '')\n        \n    [parameters] => Array\n        (\n        )\n\n    [user_id] => Anonymous\n    [request_uri] => /baffletrain/autocadlt/autobooks/send_email.php\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-11 09:59:16\n)\n
[legacy_database_errors] [2025-07-11 09:59:22] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'subscription_id' in 'field list'\n    [query] => INSERT INTO `autodesk_history`(\n                `id`,\n                `subscription_id`,\n                `subscription_reference_number`,\n                `media`,\n                `message`,\n                `user_id`,\n                `date`,\n                `triggered_by`,\n                `result`\n            )\n            VALUES(\n                NULL,\n                '15',\n                '573-98887491',\n                'E-Mail',\n                'Automatic E-Mail sent by Autobooks',\n                1,\n                NOW(),\n                '60',\n                '')\n        \n    [parameters] => Array\n        (\n        )\n\n    [user_id] => Anonymous\n    [request_uri] => /baffletrain/autocadlt/autobooks/send_email.php\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-11 09:59:22\n)\n
[legacy_database_errors] [2025-07-13 00:50:30] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'subs.enddatediff' in 'field list'\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, subs.id AS subs_id FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 30\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/autodesk/subscriptions\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-13 00:50:30\n)\n
[legacy_database_errors] [2025-07-13 22:04:38] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _lastquote_quote_status, .lastquote_quote_number AS _lastquote_quote_number, ' at line 1\n    [query] => SELECT CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END AS subs_enddatediff, endcust.name AS endcust_name, endcust.primary_admin_email AS endcust_primary_admin_email, endcust.account_csn AS endcust_account_csn, subs.offeringName AS subs_offeringName, subs.status AS subs_status, subs.subscriptionId AS subs_subscriptionId, subs.opportunityNumber AS subs_opportunityNumber, subs.subscriptionReferenceNumber AS subs_subscriptionReferenceNumber, subs.quantity AS subs_quantity, subs.term AS subs_term, subs.endDate AS subs_endDate, .lastquote_quote_status AS _lastquote_quote_status, .lastquote_quote_number AS _lastquote_quote_number, subs.last_modified AS subs_last_modified, subs.id AS subs_id FROM autodesk_subscriptions subs LEFT JOIN autodesk_accounts endcust ON subs.endCustomer_id = endcust.id ORDER BY subs_enddatediff LIMIT 30\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/autodesk/subscriptions\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-13 22:04:38\n)\n
[legacy_database_errors] [2025-07-13 22:06:40] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _lastquote_quote_status, .lastquote_quote_number AS _lastquote_quote_number, ' at line 1\n    [query] => SELECT CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END AS subs_enddatediff, endcust.name AS endcust_name, endcust.primary_admin_email AS endcust_primary_admin_email, endcust.account_csn AS endcust_account_csn, subs.offeringName AS subs_offeringName, subs.status AS subs_status, subs.subscriptionId AS subs_subscriptionId, subs.opportunityNumber AS subs_opportunityNumber, subs.subscriptionReferenceNumber AS subs_subscriptionReferenceNumber, subs.quantity AS subs_quantity, subs.term AS subs_term, subs.endDate AS subs_endDate, .lastquote_quote_status AS _lastquote_quote_status, .lastquote_quote_number AS _lastquote_quote_number, subs.last_modified AS subs_last_modified, subs.id AS subs_id FROM autodesk_subscriptions subs LEFT JOIN autodesk_accounts endcust ON subs.endCustomer_id = endcust.id ORDER BY subs_enddatediff LIMIT 30\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/autodesk/subscriptions\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-13 22:06:40\n)\n
[legacy_database_errors] [2025-07-13 22:06:42] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _lastquote_quote_status, .lastquote_quote_number AS _lastquote_quote_number, ' at line 1\n    [query] => SELECT CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END AS subs_enddatediff, endcust.name AS endcust_name, endcust.primary_admin_email AS endcust_primary_admin_email, endcust.account_csn AS endcust_account_csn, subs.offeringName AS subs_offeringName, subs.status AS subs_status, subs.subscriptionId AS subs_subscriptionId, subs.opportunityNumber AS subs_opportunityNumber, subs.subscriptionReferenceNumber AS subs_subscriptionReferenceNumber, subs.quantity AS subs_quantity, subs.term AS subs_term, subs.endDate AS subs_endDate, .lastquote_quote_status AS _lastquote_quote_status, .lastquote_quote_number AS _lastquote_quote_number, subs.last_modified AS subs_last_modified, subs.id AS subs_id FROM autodesk_subscriptions subs LEFT JOIN autodesk_accounts endcust ON subs.endCustomer_id = endcust.id ORDER BY subs_enddatediff LIMIT 30\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/autodesk/subscriptions\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-13 22:06:42\n)\n
[legacy_database_errors] [2025-07-13 22:07:09] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _lastquote_quote_status, .lastquote_quote_number AS _lastquote_quote_number, ' at line 1\n    [query] => SELECT CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END AS subs_enddatediff, endcust.name AS endcust_name, endcust.primary_admin_email AS endcust_primary_admin_email, endcust.account_csn AS endcust_account_csn, subs.offeringName AS subs_offeringName, subs.status AS subs_status, subs.subscriptionId AS subs_subscriptionId, subs.opportunityNumber AS subs_opportunityNumber, subs.subscriptionReferenceNumber AS subs_subscriptionReferenceNumber, subs.quantity AS subs_quantity, subs.term AS subs_term, subs.endDate AS subs_endDate, .lastquote_quote_status AS _lastquote_quote_status, .lastquote_quote_number AS _lastquote_quote_number, subs.last_modified AS subs_last_modified, subs.id AS subs_id FROM autodesk_subscriptions subs LEFT JOIN autodesk_accounts endcust ON subs.endCustomer_id = endcust.id ORDER BY subs_enddatediff LIMIT 30\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/autodesk/subscriptions\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-13 22:07:09\n)\n
[legacy_database_errors] [2025-07-13 22:07:13] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _lastquote_quote_status, .lastquote_quote_number AS _lastquote_quote_number, ' at line 1\n    [query] => SELECT CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END AS subs_enddatediff, endcust.name AS endcust_name, endcust.primary_admin_email AS endcust_primary_admin_email, endcust.account_csn AS endcust_account_csn, subs.offeringName AS subs_offeringName, subs.status AS subs_status, subs.subscriptionId AS subs_subscriptionId, subs.opportunityNumber AS subs_opportunityNumber, subs.subscriptionReferenceNumber AS subs_subscriptionReferenceNumber, subs.quantity AS subs_quantity, subs.term AS subs_term, subs.endDate AS subs_endDate, .lastquote_quote_status AS _lastquote_quote_status, .lastquote_quote_number AS _lastquote_quote_number, subs.last_modified AS subs_last_modified, subs.id AS subs_id FROM autodesk_subscriptions subs LEFT JOIN autodesk_accounts endcust ON subs.endCustomer_id = endcust.id ORDER BY subs_enddatediff LIMIT 30\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/autodesk/subscriptions\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-13 22:07:13\n)\n
[legacy_database_errors] [2025-07-13 22:08:22] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _lastquote_quote_status, .lastquote_quote_number AS _lastquote_quote_number, ' at line 1\n    [query] => SELECT CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END AS subs_enddatediff, endcust.name AS endcust_name, endcust.primary_admin_email AS endcust_primary_admin_email, endcust.account_csn AS endcust_account_csn, subs.offeringName AS subs_offeringName, subs.status AS subs_status, subs.subscriptionId AS subs_subscriptionId, subs.opportunityNumber AS subs_opportunityNumber, subs.subscriptionReferenceNumber AS subs_subscriptionReferenceNumber, subs.quantity AS subs_quantity, subs.term AS subs_term, subs.endDate AS subs_endDate, .lastquote_quote_status AS _lastquote_quote_status, .lastquote_quote_number AS _lastquote_quote_number, subs.last_modified AS subs_last_modified, subs.id AS subs_id FROM autodesk_subscriptions subs LEFT JOIN autodesk_accounts endcust ON subs.endCustomer_id = endcust.id ORDER BY subs_enddatediff LIMIT 30\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/autodesk/subscriptions\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-13 22:08:22\n)\n
[legacy_database_errors] [2025-07-13 22:08:24] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _lastquote_quote_status, .lastquote_quote_number AS _lastquote_quote_number, ' at line 1\n    [query] => SELECT CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END AS subs_enddatediff, endcust.name AS endcust_name, endcust.primary_admin_email AS endcust_primary_admin_email, endcust.account_csn AS endcust_account_csn, subs.offeringName AS subs_offeringName, subs.status AS subs_status, subs.subscriptionId AS subs_subscriptionId, subs.opportunityNumber AS subs_opportunityNumber, subs.subscriptionReferenceNumber AS subs_subscriptionReferenceNumber, subs.quantity AS subs_quantity, subs.term AS subs_term, subs.endDate AS subs_endDate, .lastquote_quote_status AS _lastquote_quote_status, .lastquote_quote_number AS _lastquote_quote_number, subs.last_modified AS subs_last_modified, subs.id AS subs_id FROM autodesk_subscriptions subs LEFT JOIN autodesk_accounts endcust ON subs.endCustomer_id = endcust.id ORDER BY subs_enddatediff LIMIT 30\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/autodesk/subscriptions\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-13 22:08:24\n)\n
[legacy_database_errors] [2025-07-13 22:08:40] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _lastquote_quote_status, .lastquote_quote_number AS _lastquote_quote_number, ' at line 1\n    [query] => SELECT CASE WHEN DATEDIFF(subs.endDate, NOW()) >= -31 THEN DATEDIFF(subs.endDate, NOW()) ELSE 9999 END AS subs_enddatediff, endcust.name AS endcust_name, endcust.primary_admin_email AS endcust_primary_admin_email, endcust.account_csn AS endcust_account_csn, subs.offeringName AS subs_offeringName, subs.status AS subs_status, subs.subscriptionId AS subs_subscriptionId, subs.opportunityNumber AS subs_opportunityNumber, subs.subscriptionReferenceNumber AS subs_subscriptionReferenceNumber, subs.quantity AS subs_quantity, subs.term AS subs_term, subs.endDate AS subs_endDate, .lastquote_quote_status AS _lastquote_quote_status, .lastquote_quote_number AS _lastquote_quote_number, subs.last_modified AS subs_last_modified, subs.id AS subs_id FROM autodesk_subscriptions subs LEFT JOIN autodesk_accounts endcust ON subs.endCustomer_id = endcust.id ORDER BY subs_enddatediff LIMIT 30\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/autodesk/subscriptions\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-13 22:08:40\n)\n
[legacy_database_errors] [2025-07-14 08:25:06] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'subs.enddatediff' in 'field list'\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, subs.id AS subs_id FROM autodesk_subscriptions subs ORDER BY subs_enddatediff\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/data_table/pagination?search_terms=&callback=generate_subscription_table&column%5Bsubs_offeringName%5D%5Bmulti%5D=&column%5Bsubs_status%5D%5Bmulti%5D=&column%5Bsubs_quantity%5D%5Bmulti%5D=&column%5Bsubs_term%5D%5Bmulti%5D=&page=2\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-14 08:25:06\n)\n
[legacy_database_errors] [2025-07-14 08:25:35] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'subs.enddatediff' in 'field list'\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, subs.id AS subs_id FROM autodesk_subscriptions subs ORDER BY subs_enddatediff\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/data_table/pagination?search_terms=&callback=generate_subscription_table&column%5Bsubs_offeringName%5D%5Bmulti%5D=&column%5Bsubs_status%5D%5Bmulti%5D=&column%5Bsubs_quantity%5D%5Bmulti%5D=&column%5Bsubs_term%5D%5Bmulti%5D=&page=2\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-14 08:25:35\n)\n
[legacy_database_errors] [2025-07-14 14:21:30] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'null'' at line 1\n    [query] => SELECT quotes.quote_number AS quotes_quote_number, . AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'null'\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/view\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-14 14:21:30\n)\n
[legacy_database_errors] [2025-07-14 14:32:56] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'subs.enddatediff' in 'field list'\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, subs.id AS subs_id FROM autodesk_subscriptions subs ORDER BY subs_enddatediff\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/data_table/pagination?search_terms=&callback=generate_subscription_table&column%5Bsubs_offeringName%5D%5Bmulti%5D=&column%5Bsubs_status%5D%5Bmulti%5D=&column%5Bsubs_quantity%5D%5Bmulti%5D=&column%5Bsubs_term%5D%5Bmulti%5D=&page=2\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-14 14:32:56\n)\n
[legacy_database_errors] [2025-07-14 14:46:29] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'null'' at line 1\n    [query] => SELECT quotes.quote_number AS quotes_quote_number, . AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'null'\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/view\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-14 14:46:29\n)\n
[legacy_database_errors] [2025-07-14 14:47:16] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'null'' at line 1\n    [query] => SELECT quotes.quote_number AS quotes_quote_number, . AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'null'\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/view\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-14 14:47:16\n)\n
[legacy_database_errors] [2025-07-14 15:22:02] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-928366'' at line 1\n    [query] => SELECT quotes.quote_number AS quotes_quote_number, . AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-928366'\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/view\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-14 15:22:02\n)\n
[legacy_database_errors] [2025-07-14 20:18:13] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-927963'' at line 1\n    [query] => SELECT quotes.quote_number AS quotes_quote_number, . AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-927963'\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/view\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-14 20:18:13\n)\n
[legacy_database_errors] [2025-07-14 21:13:07] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-929045'' at line 1\n    [query] => SELECT quotes.quote_number AS quotes_quote_number, . AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-929045'\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/view\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-14 21:13:07\n)\n
[legacy_database_errors] [2025-07-14 22:03:12] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-929045'' at line 1\n    [query] => SELECT quotes.quote_number AS quotes_quote_number, . AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-929045'\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/view\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-14 22:03:12\n)\n
[legacy_database_errors] [2025-07-14 22:10:25] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-928366'' at line 1\n    [query] => SELECT quotes.quote_number AS quotes_quote_number, . AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-928366'\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/view\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-14 22:10:25\n)\n
[legacy_database_errors] [2025-07-14 23:09:24] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-927963'' at line 1\n    [query] => SELECT quotes.quote_number AS quotes_quote_number, . AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-927963'\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/view\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-14 23:09:24\n)\n
[legacy_database_errors] [2025-07-14 23:09:58] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-928366'' at line 1\n    [query] => SELECT quotes.quote_number AS quotes_quote_number, . AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-928366'\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/view\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-14 23:09:58\n)\n
[legacy_database_errors] [2025-07-14 23:12:08] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-928520'' at line 1\n    [query] => SELECT quotes.quote_number AS quotes_quote_number, . AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-928520'\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/view\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-14 23:12:08\n)\n
[legacy_database_errors] [2025-07-14 23:12:41] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-927963'' at line 1\n    [query] => SELECT quotes.quote_number AS quotes_quote_number, . AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-927963'\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/view\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-14 23:12:41\n)\n
[legacy_database_errors] [2025-07-14 23:15:12] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-927963'' at line 1\n    [query] => SELECT quotes.quote_number AS quotes_quote_number, . AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-927963'\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/view\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-14 23:15:12\n)\n
[legacy_database_errors] [2025-07-14 23:15:22] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-128727'' at line 1\n    [query] => SELECT quotes.quote_number AS quotes_quote_number, . AS _ FROM autodesk_quotes quotes WHERE quotes.quote_number = 'Q-128727'\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/view\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-14 23:15:22\n)\n
[legacy_database_errors] [2025-07-14 23:19:44] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_quotes quotes LEFT JOIN autodesk_accounts endcust on endcust.' at line 1\n    [query] => SELECT quotes.quote_number AS quotes_quote_number, quotes.quote_status AS quotes_quote_status, quotes.quote_created_time AS quotes_quote_created_time, quotes.quote_expiration_date AS quotes_quote_expiration_date, quotes.quoted_date AS quotes_quoted_date, quotes.quote_opportunity_number AS quotes_quote_opportunity_number, quotes.quote_language AS quotes_quote_language, endcust.name AS endcust_name, endcust.primary_admin_email AS endcust_primary_admin_email, Concat(quotecontact.first_name, ' ', quotecontact.last_name) AS quotecontact_name, quotecontact.email AS quotecontact_email, quotes.quote_currency AS quotes_quote_currency, quotes.total_list_amount AS quotes_total_list_amount, quotes.total_net_amount AS quotes_total_net_amount, quotes.total_amount AS quotes_total_amount, quotes.total_discount AS quotes_total_discount, quotes.estimated_tax AS quotes_estimated_tax, quotes.payment_terms_code AS quotes_payment_terms_code, quotes.payment_terms_description AS quotes_payment_terms_description, quotes.id AS quotes_id, . AS _ FROM autodesk_quotes quotes LEFT JOIN autodesk_accounts endcust on endcust.id = quotes.end_customer LEFT JOIN autodesk_accounts quotecontact on quotecontact.id = quotes.quote_contact WHERE quotes.quote_number = 'Q-928520'\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/view\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-14 23:19:44\n)\n
[legacy_database_errors] [2025-07-15 09:54:08] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'quote_id' in 'where clause'\n    [query] => SELECT * FROM autodesk_history WHERE (quote_id = :quote_id OR quote_reference = :quote_number) ORDER BY date DESC\n    [parameters] => Array\n        (\n            [:quote_id] => 1590\n            [:quote_number] => Q-928520\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/view\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-15 09:54:08\n)\n
[legacy_database_errors] [2025-07-15 09:54:25] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'quote_id' in 'where clause'\n    [query] => SELECT * FROM autodesk_history WHERE (quote_id = :quote_id OR quote_reference = :quote_number) ORDER BY date DESC\n    [parameters] => Array\n        (\n            [:quote_id] => 1558\n            [:quote_number] => Q-128692\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/view\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-15 09:54:25\n)\n
[legacy_database_errors] [2025-07-15 10:01:13] [database.php:64]  Array\n(\n    [sql_state] => 42S22\n    [driver_error_code] => 1054\n    [driver_error_message] => Unknown column 'quote_id' in 'where clause'\n    [query] => SELECT * FROM autodesk_history WHERE (quote_id = :quote_id OR quote_reference = :quote_number) ORDER BY date DESC\n    [parameters] => Array\n        (\n            [:quote_id] => 1558\n            [:quote_number] => Q-128692\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/view\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-15 10:01:13\n)\n
[legacy_database_errors] [2025-07-15 10:09:21] [database.php:64]  Array\n(\n    [sql_state] => 42S02\n    [driver_error_code] => 1146\n    [driver_error_message] => Table 'wwwcadservicescouk.users' doesn't exist\n    [query] => SELECT h.*, u.name as user_name FROM autodesk_history h LEFT JOIN users u ON h.user_id = u.id WHERE h.target = 'quote' AND h.target_reference = :quote_number ORDER BY h.date DESC, h.id DESC\n    [parameters] => Array\n        (\n            [:quote_number] => Q-128682\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/view\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-15 10:09:21\n)\n
[legacy_database_errors] [2025-07-15 20:55:25] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'LIMIT 1' at line 1\n    [query] => SELECT subs.endDate AS subs_endDate, subs.id AS subs_id FROM autodesk_subscriptions subs WHERE subs.endDate BETWEEN 'Array' LIMIT 1\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/dashboard_stats\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-15 20:55:25\n)\n
[legacy_database_errors] [2025-07-15 20:55:36] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'LIMIT 1' at line 1\n    [query] => SELECT subs.endDate AS subs_endDate, subs.id AS subs_id FROM autodesk_subscriptions subs WHERE subs.endDate BETWEEN 'Array' LIMIT 1\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/dashboard_stats\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-15 20:55:36\n)\n
[legacy_database_errors] [2025-07-15 20:56:29] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'LIMIT 1' at line 1\n    [query] => SELECT subs.endDate AS subs_endDate, subs.id AS subs_id FROM autodesk_subscriptions subs WHERE subs.endDate BETWEEN 'Array' LIMIT 1\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/dashboard_stats\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-15 20:56:29\n)\n
[legacy_database_errors] [2025-07-15 20:57:48] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'LIMIT 1' at line 1\n    [query] => SELECT subs.endDate AS subs_endDate, subs.id AS subs_id FROM autodesk_subscriptions subs WHERE subs.endDate BETWEEN 'Array' LIMIT 1\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/dashboard_stats\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-15 20:57:48\n)\n
[legacy_database_errors] [2025-07-15 20:58:27] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'LIMIT 1' at line 1\n    [query] => SELECT subs.endDate AS subs_endDate, subs.id AS subs_id FROM autodesk_subscriptions subs WHERE subs.endDate BETWEEN 'Array' LIMIT 1\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/dashboard_stats\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-15 20:58:27\n)\n
[legacy_database_errors] [2025-07-15 21:41:26] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'LIMIT 1' at line 1\n    [query] => SELECT subs.endDate AS subs_endDate, subs.id AS subs_id FROM autodesk_subscriptions subs WHERE subs.endDate BETWEEN 'Array' LIMIT 1\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/dashboard_stats\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-15 21:41:26\n)\n
[legacy_database_errors] [2025-07-15 21:45:58] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'LIMIT 1' at line 1\n    [query] => SELECT subs.endDate AS subs_endDate, subs.id AS subs_id FROM autodesk_subscriptions subs WHERE subs.endDate BETWEEN 'Array' LIMIT 1\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/dashboard_stats\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-15 21:45:58\n)\n
[legacy_database_errors] [2025-07-15 23:17:00] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-15 23:17:00\n)\n
[legacy_database_errors] [2025-07-15 23:17:01] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-15 23:17:01\n)\n
[legacy_database_errors] [2025-07-16 07:57:44] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => *************\n    [timestamp] => 2025-07-16 07:57:44\n)\n
[legacy_database_errors] [2025-07-16 08:08:20] [database.php:64]  Array\n(\n    [sql_state] => 42000\n    [driver_error_code] => 1064\n    [driver_error_message] => You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100' at line 1\n    [query] => SELECT subs.enddatediff AS subs_enddatediff, . AS _ FROM autodesk_subscriptions subs ORDER BY subs_enddatediff LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => ************\n    [timestamp] => 2025-07-16 08:08:20\n)\n
[legacy_database_errors] [2025-07-21 23:08:44] [database.php:64]  Array\n(\n    [sql_state] => 42S02\n    [driver_error_code] => 1146\n    [driver_error_message] => Table 'wwwcadservicescouk.manual_subscription_entries' doesn't exist\n    [query] => SELECT *, 'manual_entries' as source_table FROM manual_subscription_entries ORDER BY created_at DESC LIMIT 1\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/dashboard_stats\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => 127.0.0.1\n    [timestamp] => 2025-07-21 23:08:44\n)\n
[legacy_database_errors] [2025-07-21 23:08:44] [database.php:64]  Array\n(\n    [sql_state] => 42S02\n    [driver_error_code] => 1146\n    [driver_error_message] => Table 'wwwcadservicescouk.manual_subscription_entries' doesn't exist\n    [query] => SELECT *, 'manual_entries' as source_table FROM manual_subscription_entries ORDER BY created_at DESC LIMIT 100\n    [parameters] => Array\n        (\n        )\n\n    [user_id] => 2\n    [request_uri] => /baffletrain/autocadlt/autobooks/api/unified/unified_subscriptions_table\n    [user_agent] => Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/******** Firefox/140.0\n    [ip_address] => 127.0.0.1\n    [timestamp] => 2025-07-21 23:08:44\n)\n
