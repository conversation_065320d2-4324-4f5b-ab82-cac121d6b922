<?php
/**
 * Autoload wrapper for compatibility with test files
 * This file provides a simple interface to load the system
 */

// Define constants if not already defined
if (!defined('DS')) define('DS', DIRECTORY_SEPARATOR);
if (!defined('DEBUG_MODE')) define('DEBUG_MODE', true);
if (!defined('API_RUN')) define('API_RUN', false);

// Get the application root path
$app_root = dirname(__DIR__);

// Load the path schema
$schema = require_once $app_root . DS . 'system' . DS . 'config' . DS . 'path_schema.php';

// Build paths
$path = ['fs_app_root' => $app_root];
require_once $app_root . DS . 'system' . DS . 'paths.php';
$path = build_paths($path, $schema);

// Build constants
build_constants($path);

// Load functions
require_once $path['fs_system'] . DS . 'functions' . DS . 'functions.php';

// Load database functions
require_once $path['fs_system'] . DS . 'functions' . DS . 'database.php';

// Load vendor autoloader if it exists
$vendor_autoload = $path['fs_doc_root'] . 'vendor' . DS . 'autoload.php';
if (file_exists($vendor_autoload)) {
    require_once $vendor_autoload;
}

// Load custom autoloader
require_once $path['fs_system'] . DS . 'autoloader.php';

// Set up database connection
$db_server = 'localhost';
$db_username = 'wwwcadservicescouk';
$db_password = 'S96#1kvYuCGE';
$db_database = 'wwwcadservicescouk';

$db = tep_db_connect($db_server, $db_username, $db_password, $db_database) or die('Unable to connect to database server!');

// Set error reporting
error_reporting(E_ALL & ~E_NOTICE);
?>
