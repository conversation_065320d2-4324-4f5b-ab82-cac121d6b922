<?php

/**
 * Add Column Analyzer Demo to Navigation
 *
 * This script adds the column analyzer demo to the navigation system
 */

// Bootstrap the application
const DS = DIRECTORY_SEPARATOR;
$path['fs_app_root'] = dirname(__DIR__) . DS;
$schema = require_once $path['fs_app_root'] . DS . 'system'. DS . 'config'. DS . 'path_schema.php';
$path['fs_system'] = "{$path['fs_app_root']}{$schema['system']['root']}" ;

define('DEBUG_MODE', true);
define('API_RUN', false);
require_once $path['fs_system'] . DS . 'classes'. DS . 'startup_sequence.class.php';

// Initialize the startup sequence
startup_sequence::start($path, $schema);

try {
    // Check if the navigation entry already exists
    $existing_query = "SELECT id FROM autobooks_navigation WHERE parent_path = 'system' AND route_key = 'column_analyzer_demo'";
    $existing = tcs_db_query($existing_query);

    if (!empty($existing)) {
        echo "✅ Column Analyzer Demo navigation entry already exists (ID: {$existing[0]['id']})\n";
        exit(0);
    }

    // Check if 'system' parent exists, if not create it
    $system_parent_query = "SELECT id FROM autobooks_navigation WHERE parent_path = 'root' AND route_key = 'system'";
    $system_parent = tcs_db_query($system_parent_query);

    if (empty($system_parent)) {
        echo "📁 Creating 'System' parent navigation entry...\n";
        $insert_parent_query = "INSERT INTO autobooks_navigation
            (parent_path, route_key, name, icon, file_path, required_roles, sort_order, show_navbar, can_delete, is_system)
            VALUES
            ('root', 'system', 'System', 'cog', '', ?, 100, 1, 0, 1)";
        tep_db_query($insert_parent_query, [json_encode(['admin', 'dev'])]);
        echo "✅ System parent navigation entry created\n";
    }

    // Add the column analyzer demo entry
    echo "🧠 Adding Column Analyzer Demo navigation entry...\n";
    $insert_demo_query = "INSERT INTO autobooks_navigation
        (parent_path, route_key, name, icon, file_path, required_roles, sort_order, show_navbar, can_delete, is_system)
        VALUES
        ('system', 'column_analyzer_demo', 'Column Analyzer Demo', 'brain', 'system', ?, 1, 1, 1, 0)";
    tep_db_query($insert_demo_query, [json_encode(['admin', 'dev'])]);

    echo "✅ Column Analyzer Demo navigation entry added successfully!\n";
    echo "🌐 You can now access it at: /system/column_analyzer_demo\n";
    echo "👤 Required roles: admin, dev\n";

} catch (Exception $e) {
    echo "❌ Error adding navigation entry: " . $e->getMessage() . "\n";
    exit(1);
}
