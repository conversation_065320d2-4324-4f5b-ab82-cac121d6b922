@props([
    'name' => 'basic-template',
    'description' => 'A basic hilt template',
    'type' => 'hilt-basic',
    'route_key' => '{{ $route_key }}'
])

@php
// Basic hilt template
// This template provides a starting point for custom content
$route_key = $route_key ?? 'default';
@endphp

<div class="hilt-basic-template">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            
            <!-- Header Section -->
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">
                    Welcome to {{ ucwords(str_replace('_', ' ', $route_key)) }}
                </h1>
                <p class="text-xl text-gray-600">
                    This is a basic hilt template. Edit this file to customize the content.
                </p>
            </div>
            
            <!-- Main Content Section -->
            <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <h2 class="text-2xl font-semibold text-gray-800 mb-4">Getting Started</h2>
                        <p class="text-gray-600 mb-4">
                            This template uses the Hilt system, which provides a flexible foundation for creating 
                            dynamic content. You can customize this template by editing the .hilt.php file.
                        </p>
                        <ul class="list-disc list-inside text-gray-600 space-y-2">
                            <li>Use Edge template syntax for dynamic content</li>
                            <li>Access database functionality with @table and @get directives</li>
                            <li>Include custom PHP logic in @php blocks</li>
                            <li>Style with Tailwind CSS classes</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h2 class="text-2xl font-semibold text-gray-800 mb-4">Template Information</h2>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Route Key:</span>
                                    <span class="text-gray-900">{{ $route_key }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Template Type:</span>
                                    <span class="text-gray-900">Hilt Basic</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700">Created:</span>
                                    <span class="text-gray-900">{{ date('Y-m-d H:i:s') }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Features Section -->
            <div class="grid md:grid-cols-3 gap-6 mb-8">
                <div class="bg-blue-50 rounded-lg p-6 text-center">
                    <div class="w-12 h-12 bg-blue-500 rounded-lg mx-auto mb-4 flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Easy to Use</h3>
                    <p class="text-gray-600">Simple Edge template syntax makes content creation straightforward.</p>
                </div>
                
                <div class="bg-green-50 rounded-lg p-6 text-center">
                    <div class="w-12 h-12 bg-green-500 rounded-lg mx-auto mb-4 flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Powerful</h3>
                    <p class="text-gray-600">Built-in database integration and dynamic content capabilities.</p>
                </div>
                
                <div class="bg-purple-50 rounded-lg p-6 text-center">
                    <div class="w-12 h-12 bg-purple-500 rounded-lg mx-auto mb-4 flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2h-4"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Flexible</h3>
                    <p class="text-gray-600">Customize and extend to meet your specific requirements.</p>
                </div>
            </div>
            
            <!-- Action Section -->
            <div class="text-center">
                <div class="bg-gray-50 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Ready to Customize?</h2>
                    <p class="text-gray-600 mb-6">
                        Edit this template file to create your custom content. You can add database queries, 
                        forms, dynamic content, and more.
                    </p>
                    <div class="space-x-4">
                        <a href="{{ APP_ROOT }}/navigation" 
                           class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                            Manage Navigation
                        </a>
                        <a href="{{ APP_ROOT }}{{ $route_key }}.settings" 
                           class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Template Settings
                        </a>
                    </div>
                </div>
            </div>
            
        </div>
    </div>
</div>

<style>
.hilt-basic-template {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.container {
    position: relative;
    z-index: 1;
}

.hilt-basic-template::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}
</style>
