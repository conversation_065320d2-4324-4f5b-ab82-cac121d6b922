<?php
namespace api\templates;

use data_table\data_table;

/**
 * Handle data table filtering for template data
 * This function will be called when the callback is 'generate_template_data_table'
 *
 * @param array $p Parameters from the request
 * @return string HTML content of filtered template data
 */
function data_table_filter($p) {
    // Check if this is a template callback
    if (isset($p['callback']) && $p['callback'] === 'generate_template_data_table') {
        return handle_template_callback($p);
    }

    // For other callbacks, delegate to the main data_table API
    $criteria = data_table::api_process_criteria($p);
    print_rr("calling " . $p['callback'] . " criteria: " . $criteria);
    return $p['callback'](criteria: $criteria);
}

/**
 * Handle template-specific callback
 *
 * @param array $p Parameters from the request
 * @return string HTML content of filtered template data
 */
function handle_template_callback($p) {
    // Include the template functions if not already loaded
    if (!function_exists('generate_template_data_table')) {
        require_once __DIR__ . '/data_table_template.php';
    }

    // Process the criteria using the data_table class
    $criteria = data_table::api_process_criteria($p);

    // Add any additional processing for template-specific criteria
    if (isset($p['search_terms']) && !empty($p['search_terms'])) {
        $criteria['search'] = $p['search_terms'];
    }

    if (isset($p['order_by']) && !empty($p['order_by'])) {
        $criteria['order_by'] = $p['order_by'];
        $criteria['order_direction'] = $p['order_direction'] ?? 'ASC';
    }

    // Call the template data table function
    return generate_template_data_table($criteria, false);
}

/**
 * Handle search functionality for template data
 *
 * @param array $p Parameters from the request
 * @return string HTML content of filtered template data
 */
function search($p) {
    if (!function_exists('generate_template_data_table')) {
        require_once __DIR__ . '/data_table_template.php';
    }

    $criteria = [];
    if (isset($p['search_terms']) && !empty($p['search_terms'])) {
        $criteria['search'] = $p['search_terms'];
    }

    return generate_template_data_table($criteria, true);
}
