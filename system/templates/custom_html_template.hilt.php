@props([
    'name' => 'custom-html',
    'description' => 'A custom HTML hilt template',
    'type' => 'hilt-html',
    'route_key' => '{{ $route_key }}'
])

@php
// Custom HTML hilt template
// The HTML content will be replaced with actual content during processing
$route_key = $route_key ?? 'default';
@endphp

<div class="hilt-html-template">
    <div class="template-header">
        <h1 class="text-2xl font-bold mb-4">{{ ucwords(str_replace('_', ' ', $route_key)) }}</h1>
    </div>
    
    <div class="custom-content">
        <!-- HTML_CONTENT -->
        /* HTML_CONTENT */
    </div>
    
    <div class="template-footer mt-6">
        <div class="text-sm text-gray-500 border-t pt-4">
            <p>Hilt HTML Template</p>
            <p>Route: {{ $route_key }}</p>
            <p>Last updated: {{ date('Y-m-d H:i:s') }}</p>
        </div>
    </div>
</div>

<style>
.hilt-html-template {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.custom-content {
    min-height: 200px;
    background: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.template-footer {
    margin-top: 2rem;
}
</style>
