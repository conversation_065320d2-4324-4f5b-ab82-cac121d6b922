@props([
    'name' => 'file-upload',
    'description' => 'A file upload hilt template',
    'type' => 'hilt-file-upload',
    'route_key' => '{{ $route_key }}'
])

@php
// File upload hilt template
// Content from uploaded files will be processed and inserted
$route_key = $route_key ?? 'default';
@endphp

<div class="hilt-file-upload-template">
    <div class="template-header">
        <h1 class="text-2xl font-bold mb-4">{{ ucwords(str_replace('_', ' ', $route_key)) }}</h1>
        <p class="text-gray-600 mb-6">Content loaded from uploaded file</p>
    </div>
    
    <div class="upload-content">
        <div class="content-wrapper">
            <!-- FILE_CONTENT -->
            <div class="default-content p-6 bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg text-center">
                <div class="max-w-md mx-auto">
                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No file uploaded</h3>
                    <p class="mt-1 text-sm text-gray-500">Upload a file to replace this content.</p>
                    <div class="mt-4">
                        <a href="{{ APP_ROOT }}/navigation" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                            Manage Templates
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="template-info mt-6">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="text-sm font-medium text-blue-800 mb-2">File Upload Template Information</h3>
            <div class="text-sm text-blue-700">
                <p><strong>Route:</strong> {{ $route_key }}</p>
                <p><strong>Template Type:</strong> Hilt File Upload</p>
                <p><strong>Supported Files:</strong> HTML, PHP, TXT, and other text-based files</p>
                <p><strong>Processing:</strong> Files are automatically processed based on their extension</p>
            </div>
        </div>
    </div>
    
    <div class="template-footer mt-6">
        <div class="text-sm text-gray-500 border-t pt-4">
            <p>Hilt File Upload Template</p>
            <p>Last updated: {{ date('Y-m-d H:i:s') }}</p>
        </div>
    </div>
</div>

<style>
.hilt-file-upload-template {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.content-wrapper {
    min-height: 300px;
}

.upload-content {
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

.default-content {
    min-height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.template-info {
    margin-top: 2rem;
}

.template-footer {
    margin-top: 2rem;
}
</style>
