<?php
/**
 * Path utility functions
 */

/**
 * Normalize a path by removing double slashes and trimming
 * 
 * @param string $path Path to normalize
 * @return string Normalized path
 */
function normalize_path(string $path): string {
    // Replace multiple slashes with a single slash
    while (strpos($path, '//') !== false) {
        $path = str_replace('//', '/', $path);
    }
    $out = trim(trim($path), '/');
    // Trim slashes from beginning and end
    return str_replace(['\\','/'], DIRECTORY_SEPARATOR, $out);
}

/**
 * Join path segments with proper slash handling
 *
 * @param string ...$segments Path segments to join
 * @return string Joined path
 */
function join_paths(string ...$segments): string {
    // Check if the first segment is an absolute path
    $is_absolute = !empty($segments) && str_starts_with($segments[0], '/');

    $path = implode('/', array_map(function($segment) {
        return trim(trim($segment), '/');
    }, $segments));

    $normalized = normalize_path($path);

    // Preserve absolute path if the first segment was absolute
    return $is_absolute ? '/' . $normalized : $normalized;
}

/**
 * Build a filesystem path with leading slash
 *
 * @param string $path Path to format
 * @return string Formatted filesystem path
 */
function fs_path(string $path): string {
    // Don't add leading slash for Windows absolute paths (e.g., C:\ or E:\)
    if (preg_match('/^[A-Za-z]:/', $path)) {
        return normalize_path($path);
    }
    return '/' . normalize_path($path);
}

/**
 * Build a web path with leading slash
 *
 * @param string $path Path to format
 * @return string Formatted web path
 */
function web_path(string $path): string {
    // Replace multiple slashes with a single slash
    while (strpos($path, '//') !== false) {
        $path = str_replace('//', '/', $path);
    }
    // Convert backslashes to forward slashes for web paths
    $path = str_replace('\\', '/', $path);
    // Trim slashes from beginning and end
    $path = trim(trim($path), '/');
    return $path;
}

/**
 * Replace placeholders in a path with actual values
 * 
 * @param string $path Path with placeholders
 * @param array $replacements Key-value pairs for replacements
 * @return string Path with replacements
 */
function replace_path_placeholders(string $path, array $replacements): string {
    foreach ($replacements as $key => $value) {
        $path = str_replace('{' . $key . '}', $value, $path);
    }
    return $path;
}