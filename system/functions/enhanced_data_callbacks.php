<?php
/**
 * Global callback functions for enhanced data table functionality
 * These functions are called by the data_table API callback system
 */

if (!function_exists('enhanced_data_table_filter')) {
    function enhanced_data_table_filter($criteria = []) {
        // The criteria parameter contains the processed criteria from data_table::api_process_criteria
        // We need to get the original parameters to extract search_terms and table_name
        $original_params = array_merge($_GET, $_POST);
        $table_name = $original_params['table_name'] ?? 'autobooks_enhanced_data';

        // Convert criteria to the format expected by enhanced_data API
        $p = [
            'table_name' => $table_name,
            'just_body' => true
        ];

        // Add search terms if provided (data_table API uses 'search_terms')
        if (isset($original_params['search_terms']) && !empty($original_params['search_terms'])) {
            $p['search'] = $original_params['search_terms'];
        } elseif (isset($criteria['search'])) {
            $p['search'] = $criteria['search'];
        }

        // Add pagination if provided
        if (isset($criteria['limit'])) {
            $p['per_page'] = $criteria['limit'];
        }
        if (isset($criteria['offset'])) {
            $p['page'] = floor($criteria['offset'] / ($criteria['limit'] ?? 50)) + 1;
        }

        // Add sorting if provided
        if (isset($criteria['order_by'])) {
            $p['sort_column'] = $criteria['order_by'];
            $p['sort_direction'] = $criteria['order_direction'] ?? 'asc';
        }

        // Add column filters if provided
        if (isset($criteria['where'])) {
            $p['filters'] = $criteria['where'];
        }

        // Include the enhanced_data API if not already loaded
        if (!function_exists('api\\enhanced_data\\data_table_filter')) {
            require_once FS_SYS_API . DIRECTORY_SEPARATOR . 'enhanced_data.api.php';
        }

        // Call the enhanced_data API function
        return \api\enhanced_data\data_table_filter($p);
    }
}
