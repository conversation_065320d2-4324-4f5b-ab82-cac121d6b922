<?php
/**
 * Helper functions for working with notifications
 */

use system\notification;
use system\notifications;

/**
 * Add a notification for a user
 *
 * @param int $user_id User ID to send notification to
 * @param string $type Notification type
 * @param string $title Notification title
 * @param string $message Notification message
 * @param string|null $link Optional link to include with notification
 * @return int|bool The notification ID if successful, false otherwise
 */
function add_notification($user_id, $type, $title, $message, $link = null) {
    $notification = new notification();
    return $notification->create($user_id, $type, $title, $message, $link);
}

/**
 * Add a notification for the current user
 *
 * @param string $type Notification type
 * @param string $title Notification title
 * @param string $message Notification message
 * @param string|null $link Optional link to include with notification
 * @return int|bool The notification ID if successful, false otherwise
 */
function add_notification_for_current_user($type, $title, $message, $link = null) {
    $user = system\users::checkAuth();
    if (!$user) {
        return false;
    }

    return add_notification($user['id'], $type, $title, $message, $link);
}

/**
 * Add a system notification for all users with a specific role
 *
 * @param string $role Role to send notification to
 * @param string $title Notification title
 * @param string $message Notification message
 * @param string|null $link Optional link to include with notification
 * @return array Array of notification IDs created
 */
function add_notification_for_role($role, $title, $message, $link = null) {
    global $db;

    // Get all users with the specified role
    $query = "SELECT id FROM autobooks_users WHERE role = :role";
    $result = tep_db_query($query, null, [':role' => $role]);

    $notification_ids = [];
    while ($user = tep_db_fetch_array($result)) {
        $notification_id = add_notification($user['id'], 'system', $title, $message, $link);
        if ($notification_id) {
            $notification_ids[] = $notification_id;
        }
    }

    return $notification_ids;
}

/**
 * Add a system notification for all users
 *
 * @param string $title Notification title
 * @param string $message Notification message
 * @param string|null $link Optional link to include with notification
 * @return array Array of notification IDs created
 */
function add_notification_for_all_users($title, $message, $link = null) {
    global $db;

    // Get all users
    $query = "SELECT id FROM autobooks_users";
    $result = tep_db_query($query);

    $notification_ids = [];
    while ($user = tep_db_fetch_array($result)) {
        $notification_id = add_notification($user['id'], 'system', $title, $message, $link);
        if ($notification_id) {
            $notification_ids[] = $notification_id;
        }
    }

    return $notification_ids;
}
