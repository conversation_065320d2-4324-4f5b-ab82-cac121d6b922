<?php

// $props = [
//     'title' => 'table',
//     'description' => 'A table with grouping',
//     'addButtonText' => 'Add',
//     'groupedData' => [
//         [
//             'name' => 'Admin Users',
//             'items' => [
//                 ['name' => '<PERSON>', 'title' => 'CEO', 'email' => '<EMAIL>', 'role' => 'Administrator'],
//                 ['name' => '<PERSON>', 'title' => 'CFO', 'email' => '<EMAIL>', 'role' => 'Administrator'],
//             ]
//         ],
//         [
//             'name' => 'Regular Users',
//             'items' => [
//                 ['name' => '<PERSON>', 'title' => 'Sales Rep', 'email' => '<EMAIL>', 'role' => 'User'],
//             ]
//         ],
//     ],
//     'columns' => [
//         ['label' => 'Name', 'field' => 'name'],
//         ['label' => 'Title', 'field' => 'title'],
//         ['label' => 'Email', 'field' => 'email'],
//         ['label' => 'Role', 'field' => 'role'],
//     ]
// ];


function compile_component($uncompiled_string):string {
    // Handle custom component tags <x-component>
    $patterns = [
        'variable' => '/(?>\{\{|\{!!)\s*(.*?)\s*(?>!!\}|\}\})/'
    ];
    $compiled = preg_replace_callback(
        '/<x-([\w-]+)([^>]*)(?>\/>|>([^<]*)<\/x-[\w-]+)>/',
        function ($matches) use (&$propsArray, &$patterns) {
            $componentName = $matches[1]; // Component name (e.g., select)
            $attributes = $matches[2]; // Attributes inside the tag
            $tag_content = trim($matches[3]); // contents between start end tag
            // Parse attributes into PHP array
            $props = [];
            preg_match_all('/([\w\-:]+)=(?>"([^"]*)"|\'([^\']*)\')/', $attributes, $attrMatches, PREG_SET_ORDER);
            foreach ($attrMatches as $attr) {
                $key = $attr[1];

                // Handle bound values (e.g., :options="...")
                if (str_starts_with($key, ':')) {
                    $key = substr($key, 1); // Remove the ":" prefix
                    $value = preg_replace($patterns['variable'], '$1', $attr[2].$attr[3]);
                    $props[] = "'$key' => $value";
                } else {
                    $value = preg_replace($patterns['variable'], '\' . $1 . \'', $attr[2].$attr[3]);
                    $props[] = "'$key' => '$value'";
                }
                if ($tag_content != "") {
                    $props["tag_content"] = "'$tag_content'";
                }
            }



            $propsString = implode(', ', $props);
            //print_rr($propsString);
            // Replace <x-component> with a call to render the component
            return "<?= component('$componentName', [ $propsString ]) ?>";
        },
        $compiled
    );

    //echo "<br>cached version doesn't exist, compiling... from '" . FS_APP_ROOT . "resources/components/{$component_name}.blade.php'<br>\n";
    $class_inject = '';
    if (strpos($uncompiled_string, '$loop')) {
        $class_inject .= 'requires_once(FS_CLASSES . \'/components_loop.class.php\');$loop = new loop(0);';
    }
    if (strpos($uncompiled_string,'$pagination')) {//item_count, $first_item, $current_page = 1, $items_per_page = 100
        $class_inject .= 'requires_once(FS_CLASSES . \'/components_pagination.class.php\'); $pagination = new pagination($item_count,$first_item,$current_page,$items_per_page);';
    }
    // Handle @props([...])

    if (preg_match('/@props\s*\(\s*\[(.*?)\]\s*\)/s', $uncompiled_string, $m)) {
        $propsArray = trim($m[1]);
        $propsArray = preg_replace('/\/\/.*/', '', $propsArray); // Remove line comments
        $propsArray = "[" . $propsArray . PHP_EOL . "]";
        $compiled = str_replace($m[0], '<?php extract(process_props(' . $propsArray . ',$component_data));' . $class_inject . ' ?>', $uncompiled_string);
    }
    //remove html comments
    $compiled = preg_replace('/<!--.*?-->/s', '', $compiled);
    //minify html

    // Handle custom component tags <x-component>
    $compiled = preg_replace_callback(
        '/<x-([\w-]+)([^>]*)(?>\/>|>([^<]*)<\/x-[\w-]+)>/',
        function ($matches) use (&$propsArray, &$patterns) {
            $componentName = $matches[1]; // Component name (e.g., select)
            $attributes = $matches[2]; // Attributes inside the tag
            $tag_content = trim($matches[3]); // contents between start end tag
            // Parse attributes into PHP array
            $props = [];
            preg_match_all('/([\w\-:]+)=(?>"([^"]*)"|\'([^\']*)\')/', $attributes, $attrMatches, PREG_SET_ORDER);
            foreach ($attrMatches as $attr) {
                $key = $attr[1];

                // Handle bound values (e.g., :options="...")
                if (str_starts_with($key, ':')) {
                    $key = substr($key, 1); // Remove the ":" prefix
                    $value = preg_replace($patterns['variable'], '$1', $attr[2].$attr[3]);
                    $props[] = "'$key' => $value";
                } else {
                    $value = preg_replace($patterns['variable'], '\' . $1 . \'', $attr[2].$attr[3]);
                    $props[] = "'$key' => '$value'";
                }
                if ($tag_content != "") {
                    $props["tag_content"] = "'$tag_content'";
                }
            }



            $propsString = implode(', ', $props);
            //print_rr($propsString);
            // Replace <x-component> with a call to render the component
            return "<?= component('$componentName', [ $propsString ]) ?>";
        },
        $compiled
    );

    $compiled = preg_replace($patterns['variable'], '<?= $1 ?>', $compiled);


    // Convert Blade-like directives to PHP
    //  $compiled = preg_replace('/@if\s*\(((?:[^()]+|(?R))*)\)/', '<?php if($1): ?/>', $compiled);
    // $compiled = preg_replace('/@elseif\s*\(((?:[^()]+|(?R))*)\)/', '<?php elseif($1): ?/>', $compiled);
    $compiled = compilecustomdirectives($compiled);
    $compiled = str_replace('@else', '<?php else: ?>', $compiled);
    $compiled = str_replace('@endif', '<?php endif ?>', $compiled);
    $compiled = str_replace('@break', '<?php break; ?>', $compiled);


    $compiled = preg_replace('/@foreach\s*\((\$.+?)( as [^)]+)\)/','<?php \$loop = new loop(count($1),$loop->depth+1,$loop); foreach($1$2): ?>', $compiled);
    $compiled = str_replace('@endforeach', '<?php $loop->update(); endforeach; $loop = $loop->parent;  ?>', $compiled);
    // Save compiled version

    $compiled = str_replace('@include', '<?php include', $compiled);
    $compiled = str_replace('@include_once', '<?php include_once', $compiled);
    $compiled = str_replace('@php', '<?php', $compiled);
    $compiled = str_replace('@endphp', '?>', $compiled);


    return $compiled;
}

function extractCondition($content, $start):string{
    $stack = [];
    $result = '';
    $inside = false;
    //print_rr($content,'extracting',true,true);
    for ($i = $start; $i < strlen($content); $i++) {
        $char = $content[$i];
        if ($char === '(') {
            if ($inside) {
                $stack[] = $char;
            } else {
                $inside = true;
            }
            $result .= $char;
        } elseif ($char === ')') {
            if (!empty($stack)) {
                array_pop($stack);
                $result .= $char;
            } elseif ($inside) {
                $result .= $char;
                break;
            }
        } elseif ($inside) {
            $result .= $char;
        }
    }
    return $result;
}

function compileCustomDirectives($compiled):string {
    return preg_replace_callback('/@elseif\s*\((.*)\)/', function ($matches) {
        $startPos = strpos($matches[0], '(');
        $condition = extractCondition($matches[0], $startPos);
        return "<?php elseif{$condition}: ?>";
    }, preg_replace_callback('/@if\s*\((.*)\)/', function ($matches) {
        $startPos = strpos($matches[0], '(');
        $condition = extractCondition($matches[0], $startPos);
        return "<?php if{$condition}: ?>";
    }, $compiled));
}

/**
 * @throws Exception
 */
function process_props($defaultProps, $props = [], $debug = false){
    $extra_attributes = '';
    if ($debug)  print_rr($defaultProps, "defaultProps");
    foreach ($props as $key => $value) {
        if ($debug)  print_rr("is $key in defaultProps", "default props");
        if (isset($defaultProps[$key])) continue;
        if ($debug) {
            //print_rr($defaultProps, "props");
           // print_rr("no" );
        }
        if (is_array($value)) {
            $extra_attributes .= " {$key}='" . json_encode($value) . "'";
        } else {
            if (is_int($key) || is_float($key) || is_bool($key)) {
                $extra_attributes .= " {$key}=$value";
            } else {
                $extra_attributes .= " {$key}='{$value}'";
            }
        }
    }
    $props = array_merge($defaultProps, $props);
    $props['extra_attributes'] = $extra_attributes;
    if ($debug) print_rr($props,"template props");
    return $props;
}

function filter_db_schema($table_data): array{
    $db = [];
    foreach ( $table_data['columns'] as $column ) {
        if (is_array($column['field'])) {
            foreach ($column['field'] as $key => $sub_field) $db[] = $sub_field;
            continue;
        }
        $db[] = $column['field'];
    }
    return $db;
}


/**
 * @param string $component_name
 * @param array $data
 * @return false|string
 */
function component(string $component_name, array $data = []){
    Edge::render($component_name, $data);
}

function get_template($component_data,$compiled_path): false|string {
    ob_start();
    include($compiled_path);
    return ob_get_clean();
}

