-- Migration: Add data_source_id to email_campaigns table
-- Date: 2025-07-24
-- Description: Links email campaigns to specific data sources for better organization

-- Check if column already exists before adding it
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'email_campaigns'
    AND COLUMN_NAME = 'data_source_id'
);

-- Add the column if it doesn't exist
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE `email_campaigns` ADD COLUMN `data_source_id` int(11) DEFAULT NULL COMMENT "Reference to autobooks_data_sources" AFTER `email_template`',
    'SELECT "Column data_source_id already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add index if column was added
SET @index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'email_campaigns'
    AND INDEX_NAME = 'idx_data_source_id'
);

SET @sql = IF(@index_exists = 0 AND @column_exists = 0,
    'ALTER TABLE `email_campaigns` ADD KEY `idx_data_source_id` (`data_source_id`)',
    'SELECT "Index idx_data_source_id already exists or column was not added" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Show final table structure
DESCRIBE email_campaigns;
