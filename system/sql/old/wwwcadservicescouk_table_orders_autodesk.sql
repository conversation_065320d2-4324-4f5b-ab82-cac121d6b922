
-- --------------------------------------------------------

--
-- Table structure for table `orders_autodesk`
--

CREATE TABLE `orders_autodesk` (
  `orders_autodesk_id` int(11) NOT NULL,
  `orders_id` int(11) NOT NULL,
  `customers_id` int(11) NOT NULL DEFAULT 0,
  `opportunityNumber` varchar(64) COLLATE utf8_unicode_ci NOT NULL,
  `customers_name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `customers_company` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `customers_street_address` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `customers_suburb` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `customers_city` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `customers_postcode` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `customers_state` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `customers_country` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `customers_telephone` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `customers_email_address` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `customers_address_format_id` int(5) NOT NULL DEFAULT 0,
  `delivery_name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `delivery_company` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `delivery_street_address` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `delivery_suburb` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `delivery_city` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `delivery_postcode` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `delivery_state` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `delivery_country` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `delivery_address_format_id` int(5) NOT NULL DEFAULT 0,
  `billing_name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `billing_company` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `billing_street_address` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `billing_suburb` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `billing_city` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `billing_postcode` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `billing_state` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `billing_country` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `billing_address_format_id` int(5) NOT NULL DEFAULT 0,
  `payment_method` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `cc_type` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `cc_owner` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `cc_number` varchar(64) COLLATE utf8_unicode_ci DEFAULT NULL,
  `cc_expires` varchar(4) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ip_order_no` varchar(30) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ip_requested_by` varchar(30) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ip_contact_person` varchar(30) COLLATE utf8_unicode_ci DEFAULT NULL,
  `cc_start` varchar(4) COLLATE utf8_unicode_ci DEFAULT NULL,
  `cc_issue` char(3) COLLATE utf8_unicode_ci DEFAULT NULL,
  `cc_cvv` varchar(4) COLLATE utf8_unicode_ci DEFAULT NULL,
  `cc_po_no` varchar(30) COLLATE utf8_unicode_ci DEFAULT NULL,
  `cc_po_contact_name` varchar(30) COLLATE utf8_unicode_ci DEFAULT NULL,
  `cc_po_department` varchar(30) COLLATE utf8_unicode_ci DEFAULT NULL,
  `last_modified` datetime DEFAULT NULL,
  `date_purchased` datetime DEFAULT NULL,
  `orders_status` int(5) NOT NULL DEFAULT 0,
  `orders_date_finished` datetime DEFAULT NULL,
  `currency` char(3) COLLATE utf8_unicode_ci DEFAULT NULL,
  `currency_value` decimal(14,6) DEFAULT NULL,
  `account_name` varchar(32) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `account_number` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `po_number` varchar(12) COLLATE utf8_unicode_ci DEFAULT NULL,
  `customers_street_address2` varchar(64) COLLATE utf8_unicode_ci DEFAULT NULL,
  `delivery_street_address2` varchar(64) COLLATE utf8_unicode_ci DEFAULT NULL,
  `billing_street_address2` varchar(64) COLLATE utf8_unicode_ci DEFAULT NULL,
  `block_review_request` int(1) NOT NULL DEFAULT 0
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
