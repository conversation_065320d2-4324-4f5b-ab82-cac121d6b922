-- --------------------------------------------------------
--
-- Table structure for table `autobooks_data_sources`
-- Data Source Management System
--

CREATE TABLE `autobooks_data_sources` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT 'Display name for the data source',
  `table_name` varchar(255) NOT NULL COMMENT 'Database table name',
  `description` text DEFAULT NULL COMMENT 'Optional description',
  `category` enum('data_table','email','users','system','autodesk','other') NOT NULL DEFAULT 'other' COMMENT 'Data source category',
  `column_mapping` longtext DEFAULT NULL COMMENT 'JSON column mapping configuration',
  `filters` longtext DEFAULT NULL COMMENT 'JSON filter configuration',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT 'Data source status',
  `created_by` int(11) DEFAULT NULL COMMENT 'User ID who created this data source',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_status_category` (`status`,`category`),
  KEY `idx_table_name` (`table_name`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Data source configurations for tables and email campaigns';

-- --------------------------------------------------------
--
-- Sample data for `autobooks_data_sources`
--

INSERT INTO `autobooks_data_sources` (`name`, `table_name`, `description`, `category`, `status`, `created_by`) VALUES
('User Management Data', 'autobooks_users', 'System users and their information', 'users', 'active', 1),
('Navigation Data', 'autobooks_navigation', 'System navigation menu items', 'system', 'active', 1),
('Email Campaign Data', 'autobooks_email_campaigns', 'Email campaign configurations', 'email', 'active', 1);

-- Add foreign key constraint if autobooks_users table exists
-- ALTER TABLE `autobooks_data_sources`
-- ADD CONSTRAINT `fk_data_sources_created_by`
-- FOREIGN KEY (`created_by`) REFERENCES `autobooks_users` (`id`) ON DELETE SET NULL;

-- --------------------------------------------------------
--
-- Navigation entry for data sources
--

INSERT INTO `autobooks_navigation` (`parent_path`, `route_key`, `name`, `icon`, `file_path`, `required_roles`, `sort_order`, `show_navbar`, `can_delete`, `is_system`, `created_at`, `updated_at`) VALUES
('root', 'data_sources', 'Data Sources', 'database', 'system', '[]', 15, 1, 0, 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE
`name` = VALUES(`name`),
`icon` = VALUES(`icon`),
`file_path` = VALUES(`file_path`),
`is_system` = VALUES(`is_system`),
`updated_at` = NOW();
