-- Email Campaign Management System Database Schema

-- Main campaigns table
CREATE TABLE IF NOT EXISTS `email_campaigns` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT 'Campaign name/title',
  `description` text DEFAULT NULL COMMENT 'Campaign description',
  `type` enum('subscription_renewal','general','promotional','notification') NOT NULL DEFAULT 'general',
  `status` enum('draft','active','paused','completed','archived') NOT NULL DEFAULT 'draft',
  `template_id` int(11) DEFAULT NULL COMMENT 'Reference to email_campaign_templates',
  `target_audience` json DEFAULT NULL COMMENT 'JSON criteria for target selection',
  `send_rules` json DEFAULT NULL COMMENT 'JSON rules for when to send',
  `send_schedule` json DEFAULT NULL COMMENT 'JSON schedule configuration',
  `from_email` varchar(255) DEFAULT NULL,
  `from_name` varchar(255) DEFAULT NULL,
  `reply_to` varchar(255) DEFAULT NULL,
  `subject_template` varchar(500) DEFAULT NULL,
  `email_template` varchar(255) DEFAULT NULL COMMENT 'Template file name',
  `data_source_id` int(11) DEFAULT NULL COMMENT 'Reference to autobooks_data_sources',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_run_at` timestamp NULL DEFAULT NULL,
  `next_run_at` timestamp NULL DEFAULT NULL,
  `total_sent` int(11) DEFAULT 0,
  `total_delivered` int(11) DEFAULT 0,
  `total_failed` int(11) DEFAULT 0,
  `is_system` tinyint(1) DEFAULT 0 COMMENT 'System-generated campaign (like existing subscription renewal)',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_type` (`type`),
  KEY `idx_next_run` (`next_run_at`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_data_source_id` (`data_source_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Email campaign definitions and settings';

-- Campaign templates table
CREATE TABLE IF NOT EXISTS `email_campaign_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `campaign_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `version` int(11) NOT NULL DEFAULT 1,
  `subject_template` varchar(500) DEFAULT NULL,
  `body_template` longtext NOT NULL,
  `template_type` enum('html','text','mixed') NOT NULL DEFAULT 'html',
  `placeholders` json DEFAULT NULL COMMENT 'Available placeholders for this template',
  `is_active` tinyint(1) DEFAULT 1,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_campaign_id` (`campaign_id`),
  KEY `idx_version` (`version`),
  KEY `idx_is_active` (`is_active`),
  FOREIGN KEY (`campaign_id`) REFERENCES `email_campaigns` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Email template storage and versioning';

-- Campaign rules table for complex rule definitions
CREATE TABLE IF NOT EXISTS `email_campaign_rules` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `campaign_id` int(11) NOT NULL,
  `rule_name` varchar(255) NOT NULL,
  `rule_type` enum('time_based','event_based','condition_based') NOT NULL,
  `rule_config` json NOT NULL COMMENT 'Rule configuration parameters',
  `priority` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_campaign_id` (`campaign_id`),
  KEY `idx_rule_type` (`rule_type`),
  KEY `idx_priority` (`priority`),
  FOREIGN KEY (`campaign_id`) REFERENCES `email_campaigns` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Campaign rule definitions';

-- Campaign send history table
CREATE TABLE IF NOT EXISTS `email_campaign_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `campaign_id` int(11) NOT NULL,
  `template_id` int(11) DEFAULT NULL,
  `recipient_email` varchar(255) NOT NULL,
  `recipient_name` varchar(255) DEFAULT NULL,
  `recipient_type` enum('customer','subscription','manual') DEFAULT 'manual',
  `recipient_reference_id` varchar(100) DEFAULT NULL COMMENT 'Reference to customer/subscription ID',
  `subject` varchar(500) DEFAULT NULL,
  `body_preview` text DEFAULT NULL COMMENT 'First 500 chars of email body',
  `send_status` enum('pending','sent','delivered','failed','bounced','unsubscribed') NOT NULL DEFAULT 'pending',
  `send_result` text DEFAULT NULL COMMENT 'Detailed send result/error message',
  `triggered_by_rule` varchar(255) DEFAULT NULL,
  `triggered_by_user` int(11) DEFAULT NULL,
  `sent_at` timestamp NULL DEFAULT NULL,
  `delivered_at` timestamp NULL DEFAULT NULL,
  `opened_at` timestamp NULL DEFAULT NULL,
  `clicked_at` timestamp NULL DEFAULT NULL,
  `unsubscribed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_campaign_id` (`campaign_id`),
  KEY `idx_recipient_email` (`recipient_email`),
  KEY `idx_send_status` (`send_status`),
  KEY `idx_sent_at` (`sent_at`),
  KEY `idx_recipient_reference` (`recipient_reference_id`),
  FOREIGN KEY (`campaign_id`) REFERENCES `email_campaigns` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Email campaign send history and tracking';

-- Campaign recipients/audience table
CREATE TABLE IF NOT EXISTS `email_campaign_recipients` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `campaign_id` int(11) NOT NULL,
  `email` varchar(255) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `recipient_type` enum('customer','subscription','manual','imported') DEFAULT 'manual',
  `reference_id` varchar(100) DEFAULT NULL COMMENT 'Reference to customer/subscription ID',
  `metadata` json DEFAULT NULL COMMENT 'Additional recipient data for personalization',
  `is_active` tinyint(1) DEFAULT 1,
  `unsubscribed` tinyint(1) DEFAULT 0,
  `unsubscribed_at` timestamp NULL DEFAULT NULL,
  `added_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_campaign_id` (`campaign_id`),
  KEY `idx_email` (`email`),
  KEY `idx_recipient_type` (`recipient_type`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_unsubscribed` (`unsubscribed`),
  UNIQUE KEY `unique_campaign_email` (`campaign_id`, `email`),
  FOREIGN KEY (`campaign_id`) REFERENCES `email_campaigns` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Campaign recipient management';

-- Unsubscribe tokens table for secure unsubscribe links
CREATE TABLE IF NOT EXISTS `email_unsubscribe_tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `token` varchar(64) NOT NULL,
  `email` varchar(255) NOT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `expires_at` timestamp NOT NULL,
  `used_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_token` (`token`),
  KEY `idx_email` (`email`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_campaign_id` (`campaign_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Secure unsubscribe token management';

-- Insert default Autodesk subscription renewal campaign (migration from existing system)
INSERT INTO `email_campaigns` (
  `name`, 
  `description`, 
  `type`, 
  `status`, 
  `from_email`, 
  `from_name`, 
  `subject_template`, 
  `is_system`,
  `created_by`
) VALUES (
  'Autodesk Subscription Renewal Reminders',
  'Automated reminders for Autodesk subscription renewals based on expiration dates',
  'subscription_renewal',
  'active',
  '<EMAIL>',
  'TCS CAD & BIM Solutions Limited',
  'Your {{subs_product_name}} subscription {{subs_status,"active":"is ending soon", "expired": "has expired"}}',
  1,
  1
) ON DUPLICATE KEY UPDATE `name` = `name`;
