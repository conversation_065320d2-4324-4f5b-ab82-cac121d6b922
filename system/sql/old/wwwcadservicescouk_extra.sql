
--
-- Indexes for dumped tables
--

--
-- Indexes for table `autobooks_navigation`
--
ALTER TABLE `autobooks_navigation`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_route` (`parent_path`,`route_key`) COMMENT 'Ensure unique route within parent path',
  ADD KEY `idx_parent_path` (`parent_path`),
  ADD KEY `idx_route_key` (`route_key`),
  ADD KEY `idx_sort_order` (`sort_order`);

--
-- Indexes for table `autobooks_notifications`
--
ALTER TABLE `autobooks_notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `is_read` (`is_read`),
  ADD KEY `created_at` (`created_at`);

--
-- Indexes for table `autobooks_notification_preferences`
--
ALTER TABLE `autobooks_notification_preferences`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_type` (`user_id`,`type`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `autobooks_sessions`
--
<PERSON><PERSON>R TABLE `autobooks_sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `autobooks_users`
--
ALTER TABLE `autobooks_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `autodesk_accounts`
--
ALTER TABLE `autodesk_accounts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `account_csn` (`account_csn`) USING BTREE,
  ADD KEY `idx_endcust_id` (`id`);

--
-- Indexes for table `autodesk_email_history`
--
ALTER TABLE `autodesk_email_history`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `autodesk_history`
--
ALTER TABLE `autodesk_history`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `autodesk_orders`
--
ALTER TABLE `autodesk_orders`
  ADD PRIMARY KEY (`quote_id`);

--
-- Indexes for table `autodesk_quotes`
--
ALTER TABLE `autodesk_quotes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `quote_number` (`quote_number`),
  ADD KEY `idx_quote_id` (`quote_number`) USING BTREE;

--
-- Indexes for table `autodesk_quote_line_items`
--
ALTER TABLE `autodesk_quote_line_items`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `line_number` (`line_number`),
  ADD KEY `idx_qitem_subscription_id` (`subscription_id`),
  ADD KEY `idx_qitem_quote_id` (`quote_id`);

--
-- Indexes for table `autodesk_quote_line_items_reference_subscriptions`
--
ALTER TABLE `autodesk_quote_line_items_reference_subscriptions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `line_item_id` (`line_item_id`),
  ADD KEY `idx_line_item_id` (`line_item_id`);

--
-- Indexes for table `autodesk_quote_line_item_offers`
--
ALTER TABLE `autodesk_quote_line_item_offers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `line_item_id` (`line_item_id`);

--
-- Indexes for table `autodesk_storage`
--
ALTER TABLE `autodesk_storage`
  ADD PRIMARY KEY (`autodesk_storage_id`),
  ADD UNIQUE KEY `autodesk_storage_key` (`autodesk_storage_key`);

--
-- Indexes for table `autodesk_subscriptions`
--
ALTER TABLE `autodesk_subscriptions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `subscriptionReferenceNumber` (`subscriptionReferenceNumber`),
  ADD UNIQUE KEY `subscriptionId` (`subscriptionId`),
  ADD KEY `last_email_history_id` (`last_email_history_id`),
  ADD KEY `soldTo_id` (`soldTo_id`),
  ADD KEY `solutionProvider_id` (`solutionProvider_id`),
  ADD KEY `nurtureReseller_id` (`nurtureReseller_id`),
  ADD KEY `endCustomer_id` (`endCustomer_id`),
  ADD KEY `idx_subs_endDate` (`endDate`),
  ADD KEY `idx_subs_endCustomer_id` (`endCustomer_id`),
  ADD KEY `idx_subs_subscriptionId` (`subscriptionId`),
  ADD KEY `idx_subs_status` (`status`);

--
-- Indexes for table `autodesk_subscriptions_unsub`
--
ALTER TABLE `autodesk_subscriptions_unsub`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `subscriptionId` (`subscriptionId`,`subscriptionReferenceNumber`);

--
-- Indexes for table `orders`
--
ALTER TABLE `orders`
  ADD PRIMARY KEY (`orders_id`),
  ADD KEY `idx1_orders` (`customers_id`),
  ADD KEY `idx_orders_customers_id` (`customers_id`);

--
-- Indexes for table `orders_autodesk`
--
ALTER TABLE `orders_autodesk`
  ADD PRIMARY KEY (`orders_autodesk_id`),
  ADD KEY `idx1_orders` (`customers_id`),
  ADD KEY `idx_orders_customers_id` (`customers_id`),
  ADD KEY `orders_id` (`orders_id`);

--
-- Indexes for table `products_to_autodesk_catalog`
--
ALTER TABLE `products_to_autodesk_catalog`
  ADD PRIMARY KEY (`products_to_autodesk_catalog_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `autobooks_navigation`
--
ALTER TABLE `autobooks_navigation`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `autobooks_notifications`
--
ALTER TABLE `autobooks_notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `autobooks_notification_preferences`
--
ALTER TABLE `autobooks_notification_preferences`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `autobooks_sessions`
--
ALTER TABLE `autobooks_sessions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=477;

--
-- AUTO_INCREMENT for table `autobooks_users`
--
ALTER TABLE `autobooks_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `autodesk_accounts`
--
ALTER TABLE `autodesk_accounts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=170440;

--
-- AUTO_INCREMENT for table `autodesk_email_history`
--
ALTER TABLE `autodesk_email_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=401;

--
-- AUTO_INCREMENT for table `autodesk_history`
--
ALTER TABLE `autodesk_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1502;

--
-- AUTO_INCREMENT for table `autodesk_orders`
--
ALTER TABLE `autodesk_orders`
  MODIFY `quote_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=122;

--
-- AUTO_INCREMENT for table `autodesk_quotes`
--
ALTER TABLE `autodesk_quotes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1599;

--
-- AUTO_INCREMENT for table `autodesk_quote_line_items`
--
ALTER TABLE `autodesk_quote_line_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1916;

--
-- AUTO_INCREMENT for table `autodesk_quote_line_items_reference_subscriptions`
--
ALTER TABLE `autodesk_quote_line_items_reference_subscriptions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `autodesk_quote_line_item_offers`
--
ALTER TABLE `autodesk_quote_line_item_offers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `autodesk_storage`
--
ALTER TABLE `autodesk_storage`
  MODIFY `autodesk_storage_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=121;

--
-- AUTO_INCREMENT for table `autodesk_subscriptions`
--
ALTER TABLE `autodesk_subscriptions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=33494;

--
-- AUTO_INCREMENT for table `autodesk_subscriptions_unsub`
--
ALTER TABLE `autodesk_subscriptions_unsub`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `orders`
--
ALTER TABLE `orders`
  MODIFY `orders_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31064;

--
-- AUTO_INCREMENT for table `orders_autodesk`
--
ALTER TABLE `orders_autodesk`
  MODIFY `orders_autodesk_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `products_to_autodesk_catalog`
--
ALTER TABLE `products_to_autodesk_catalog`
  MODIFY `products_to_autodesk_catalog_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `autobooks_sessions`
--
ALTER TABLE `autobooks_sessions`
  ADD CONSTRAINT `autobooks_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `autobooks_users` (`id`) ON DELETE CASCADE;
