
-- --------------------------------------------------------

--
-- Table structure for table `autodesk_quotes_tcs`
--

CREATE TABLE `autodesk_quotes_tcs` (
  `id` int(11) NOT NULL,
  `quote_status` varchar(30) DEFAULT NULL,
  `customers_id` int(11) DEFAULT NULL,
  `additional_recipients` varchar(60) DEFAULT NULL,
  `orders_id` int(11) DEFAULT NULL,
  `transaction_id` varchar(60) DEFAULT NULL,
  `quote_number` varchar(60) DEFAULT NULL,
  `quote_opportunity_number` varchar(16) DEFAULT NULL,
  `quote_message` text DEFAULT NULL,
  `quote_created_time` date DEFAULT NULL,
  `quote_expiration_date` date DEFAULT NULL,
  `modified_at` datetime DEFAULT NULL,
  `skip_dda_check` tinyint(1) DEFAULT NULL,
  `agent_account` int(11) DEFAULT NULL,
  `agent_contact` int(11) DEFAULT NULL,
  `end_customer` int(11) DEFAULT NULL,
  `admin` varchar(255) DEFAULT NULL,
  `quote_language` varchar(5) DEFAULT NULL,
  `quoted_date` date DEFAULT NULL,
  `quote_currency` varchar(3) DEFAULT NULL,
  `total_list_amount` decimal(10,2) DEFAULT NULL,
  `total_discount` decimal(10,2) DEFAULT NULL,
  `total_net_amount` decimal(10,2) DEFAULT NULL,
  `total_amount` decimal(10,2) DEFAULT NULL,
  `estimated_tax` decimal(10,2) DEFAULT NULL,
  `payment_terms_code` varchar(16) DEFAULT NULL,
  `payment_terms_description` int(60) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
