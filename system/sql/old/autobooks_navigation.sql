-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jul 20, 2025 at 10:04 PM
-- Server version: 10.2.9-MariaDB
-- PHP Version: 8.4.7

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `wwwcadservicescouk`
--

-- --------------------------------------------------------

--
-- Table structure for table `autobooks_navigation`
--

CREATE TABLE `autobooks_navigation` (
  `id` int(10) UNSIGNED NOT NULL,
  `parent_path` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'Parent path for hierarchical navigation',
  `route_key` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Unique route identifier within parent path',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Display name for the navigation item',
  `icon` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Icon identifier for the navigation item',
  `file_path` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '\\' COMMENT 'location to look for the view file, relative to view directory',
  `required_roles` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'JSON array of roles required to access this route',
  `sort_order` int(11) DEFAULT 0 COMMENT 'Sort order for navigation items within the same parent',
  `show_navbar` tinyint(1) DEFAULT 1 COMMENT 'Whether to show this item in the navigation bar',
  `can_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Can be deleted in the UI, reserved for user added views',
  `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'If true file_path is relative to FS_SYS_VIEWS otherwise relative to FS_VIEWS',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Navigation menu structure and routing';

--
-- Dumping data for table `autobooks_navigation`
--

INSERT INTO `autobooks_navigation` (`id`, `parent_path`, `route_key`, `name`, `icon`, `file_path`, `required_roles`, `sort_order`, `show_navbar`, `can_delete`, `is_system`, `created_at`, `updated_at`) VALUES
(2, 'root', 'dashboard', 'Dashboard', 'chart', 'dashboard', '[]', 1, 1, 0, 1, '2025-05-18 22:10:38', '2025-07-20 21:02:48'),
(3, 'autodesk', 'orders', 'Orders', 'shopping-cart', 'autodesk', '[]', 3, 1, 0, 0, '2025-05-18 22:10:38', '2025-07-17 23:48:33'),
(4, 'autodesk', 'quotes', 'Quotes', 'speech_bubble', 'autodesk', '[]', 4, 1, 0, 0, '2025-05-18 22:10:38', '2025-07-12 12:41:33'),
(5, 'autodesk', 'subscriptions', 'Subscriptions', 'ticket', 'autodesk', '[]', 6, 1, 0, 0, '2025-05-18 22:10:38', '2025-07-12 12:41:33'),
(6, 'autodesk', 'customers', 'Customers', 'user', 'autodesk', '[]', 2, 1, 0, 0, '2025-05-18 22:10:38', '2025-07-17 23:48:26'),
(7, 'autodesk', 'products', 'Products', 'computer_desktop', 'autodesk', '[]', 5, 1, 0, 0, '2025-05-18 22:10:38', '2025-07-17 23:48:43'),
(8, 'root', 'system', 'System', 'code', 'system', '[\"dev\"]', 11, 0, 0, 1, '2025-05-18 22:10:38', '2025-07-20 21:02:49'),
(9, 'system', 'logs', 'logs', 'code', 'system', '[]', 9, 1, 0, 1, '2025-05-18 22:10:38', '2025-07-12 10:38:37'),
(10, 'root', 'autodesk', 'Autodesk', 'autodesk', '', '[]', 5, 1, 0, 0, '2025-07-05 16:06:39', '2025-07-17 12:29:03'),
(35, 'root', 'email_campaigns', 'Email Campaigns', 'envelope', 'email_campaigns', '[\"admin\", \"dev\", \"manager\"]', 50, 1, 0, 1, '2025-07-19 23:56:30', '2025-07-20 00:08:07');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `autobooks_navigation`
--
ALTER TABLE `autobooks_navigation`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_route` (`parent_path`,`route_key`) COMMENT 'Ensure unique route within parent path',
  ADD KEY `idx_parent_path` (`parent_path`),
  ADD KEY `idx_route_key` (`route_key`),
  ADD KEY `idx_sort_order` (`sort_order`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `autobooks_navigation`
--
ALTER TABLE `autobooks_navigation`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=38;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
