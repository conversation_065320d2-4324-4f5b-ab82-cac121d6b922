
-- --------------------------------------------------------

--
-- Table structure for table `autobooks_cloudflow_data`
--

CREATE TABLE `autobooks_cloudflow_data` (
  `id` int(10) UNSIGNED NOT NULL,
  `subscription_id` varchar(255) DEFAULT NULL,
  `customer_name` varchar(255) DEFAULT NULL,
  `software_product_name` varchar(255) DEFAULT NULL,
  `license_type` varchar(255) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` varchar(255) DEFAULT NULL,
  `number_of_licenses_seats` int(11) DEFAULT NULL,
  `price_annual_fee` int(11) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `autobooks_cloudflow_data`
--

INSERT INTO `autobooks_cloudflow_data` (`id`, `subscription_id`, `customer_name`, `software_product_name`, `license_type`, `start_date`, `end_date`, `number_of_licenses_seats`, `price_annual_fee`, `status`, `created_at`, `updated_at`) VALUES
(1, 'SUB-001', 'Alpha Corp', 'CloudFlow Basic Edition', 'Subscription', '2023-01-15', '2024-01-14', 50, 2500, 'Active', '2025-07-09 06:21:40', '2025-07-09 06:21:40'),
(2, 'SUB-002', 'Beta Solutions', 'CloudFlow Professional Edition', 'Subscription', '2023-03-01', '2024-03-01', 25, 5000, 'Active', '2025-07-09 06:21:40', '2025-07-09 06:21:40'),
(3, 'SUB-003', 'Gamma Ltd', 'CloudFlow Enterprise Edition', 'Perpetual', '2022-06-20', 'N/A', 100, 15000, 'Active', '2025-07-09 06:21:40', '2025-07-09 06:21:40'),
(4, 'SUB-004', 'Delta Inc', 'CloudFlow Basic Edition', 'Subscription', '2023-05-10', '2024-05-09', 10, 500, 'Active', '2025-07-09 06:21:40', '2025-07-09 06:21:40'),
(5, 'SUB-005', 'Epsilon Systems', 'CloudFlow Professional Edition', 'Subscription', '2022-11-01', '2023-10-31', 30, 6000, 'Expired', '2025-07-09 06:21:40', '2025-07-09 06:21:40'),
(6, 'SUB-006', 'Zeta Holdings', 'CloudFlow Enterprise Edition', 'Site License', '2023-07-01', '2024-06-30', NULL, 25000, 'Active', '2025-07-09 06:21:40', '2025-07-09 06:21:40'),
(7, 'SUB-007', 'Eta Company', 'CloudFlow Basic Edition', 'Subscription', '2023-09-20', '2024-09-19', 15, 750, 'Pending Renewal', '2025-07-09 06:21:40', '2025-07-09 06:21:40'),
(8, 'SUB-008', 'Theta Group', 'CloudFlow Professional Edition', 'Subscription', '2023-02-10', '2024-02-09', 40, 8000, 'Active', '2025-07-09 06:21:40', '2025-07-09 06:21:40'),
(9, 'SUB-009', 'Iota Services', 'CloudFlow Enterprise Edition', 'Subscription', '2023-04-05', '2024-04-04', 75, 12000, 'Active', '2025-07-09 06:21:40', '2025-07-09 06:21:40'),
(10, 'SUB-010', 'Kappa Corp', 'CloudFlow Basic Edition', 'Perpetual', '2021-12-01', 'N/A', 20, 1000, 'Active', '2025-07-09 06:21:40', '2025-07-09 06:21:40'),
(11, 'SUB-011', 'Lambda Solutions', 'CloudFlow Professional Edition', 'Subscription', '2024-01-01', '2025-01-01', 18, 3600, 'Active', '2025-07-09 06:21:40', '2025-07-09 06:21:40'),
(12, 'SUB-012', 'Mu Enterprises', 'CloudFlow Enterprise Edition', 'Subscription', '2022-08-15', '2023-08-14', 60, 9600, 'Expired', '2025-07-09 06:21:40', '2025-07-09 06:21:40'),
(13, 'SUB-013', 'Nu Innovations', 'CloudFlow Basic Edition', 'Subscription', '2023-06-25', '2024-06-24', 12, 600, 'Pending Renewal', '2025-07-09 06:21:40', '2025-07-09 06:21:40'),
(14, 'SUB-014', 'Xi Solutions', 'CloudFlow Professional Edition', 'Site License', '2023-03-15', '2024-03-14', NULL, 18000, 'Active', '2025-07-09 06:21:40', '2025-07-09 06:21:40'),
(15, 'SUB-015', 'Omicron Tech', 'CloudFlow Enterprise Edition', 'Perpetual', '2022-02-01', 'N/A', 80, 13000, 'Active', '2025-07-09 06:21:40', '2025-07-09 06:21:40'),
(16, 'SUB-016', 'BSBA TEES Ltd', 'Cloud View Core', 'Subscription', '2025-07-09', '2025-07-09', 2, 325, 'Active', '2025-07-09 07:24:16', '2025-07-09 07:24:16');
