
-- --------------------------------------------------------

--
-- Table structure for table `autobooks_navigation`
--

CREATE TABLE `autobooks_navigation` (
  `id` int(10) UNSIGNED NOT NULL,
  `parent_path` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'Parent path for hierarchical navigation',
  `route_key` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Unique route identifier within parent path',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Display name for the navigation item',
  `icon` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Icon identifier for the navigation item',
  `file_path` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '\\' COMMENT 'location to look for the view file, relative to view directory',
  `required_roles` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'JSON array of roles required to access this route',
  `sort_order` int(11) DEFAULT 0 COMMENT 'Sort order for navigation items within the same parent',
  `show_navbar` tinyint(1) DEFAULT 1 COMMENT 'Whether to show this item in the navigation bar',
  `can_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Can be deleted in the UI, reserved for user added views',
  `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'If true file_path is relative to FS_SYS_VIEWS otherwise relative to FS_VIEWS',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Navigation menu structure and routing';

--
-- Dumping data for table `autobooks_navigation`
--

INSERT INTO `autobooks_navigation` (`id`, `parent_path`, `route_key`, `name`, `icon`, `file_path`, `required_roles`, `sort_order`, `show_navbar`, `can_delete`, `is_system`, `created_at`, `updated_at`) VALUES
(2, 'root', 'dashboard', 'Dashboard', 'chart', 'dashboard', '[]', 1, 1, 0, 1, '2025-05-18 22:10:38', '2025-07-22 13:50:31'),
(3, 'autodesk', 'orders', 'Orders', 'shopping-cart', 'autodesk', '[]', 3, 1, 0, 0, '2025-05-18 22:10:38', '2025-07-17 23:48:33'),
(4, 'autodesk', 'quotes', 'Quotes', 'speech_bubble', 'autodesk', '[]', 4, 1, 0, 0, '2025-05-18 22:10:38', '2025-07-12 12:41:33'),
(5, 'autodesk', 'subscriptions', 'Subscriptions', 'ticket', 'autodesk', '[]', 6, 1, 0, 0, '2025-05-18 22:10:38', '2025-07-12 12:41:33'),
(6, 'autodesk', 'customers', 'Customers', 'user', 'autodesk', '[]', 2, 1, 0, 0, '2025-05-18 22:10:38', '2025-07-17 23:48:26'),
(7, 'autodesk', 'products', 'Products', 'computer_desktop', 'autodesk', '[]', 5, 1, 0, 0, '2025-05-18 22:10:38', '2025-07-17 23:48:43'),
(8, 'root', 'system', 'System', 'code', 'system', '[\"dev\"]', 9, 0, 0, 1, '2025-05-18 22:10:38', '2025-07-23 13:30:53'),
(9, 'system', 'logs', 'logs', 'code', 'system', '[]', 9, 1, 0, 1, '2025-05-18 22:10:38', '2025-07-12 10:38:37'),
(10, 'root', 'autodesk', 'Autodesk', 'autodesk', '', '[]', 2, 1, 0, 0, '2025-07-05 16:06:39', '2025-07-23 13:30:53'),
(35, 'root', 'email_campaigns', 'Email Campaigns', 'envelope', 'email_campaigns', '[\"admin\", \"dev\", \"manager\"]', 8, 1, 0, 1, '2025-07-19 23:56:30', '2025-07-23 13:30:53'),
(38, 'system', 'data_sources', 'Data Sources', 'circle-stack', 'data_sources', '[]', 10, 1, 0, 1, '2025-07-22 13:42:20', '2025-07-22 13:47:06'),
(39, 'root', 'data_sources', 'Data Sources', 'database', 'data_sources', '[]', 12, 1, 0, 1, '2025-07-22 14:09:49', '2025-07-23 13:30:53');
