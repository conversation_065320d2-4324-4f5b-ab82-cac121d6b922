
-- --------------------------------------------------------

--
-- Table structure for table `autobooks_data_sources`
--

CREATE TABLE `autobooks_data_sources` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Display name for the data source',
  `description` text COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Optional description of what this data source provides',
  `category` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'other' COMMENT 'Data source category (data_table, email, users, system, autodesk, other)',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT 'Data source status (active, inactive, draft)',
  `table_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Primary database table name (for backward compatibility)',
  `tables` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of all tables used in this data source',
  `table_aliases` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON object mapping table names to their aliases (e.g., {"users": "u", "posts": "p"})',
  `joins` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of join configurations with type, tables, columns, and aliases',
  `selected_columns` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of selected columns from all tables',
  `column_aliases` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON object mapping column keys to their custom aliases (e.g., {"users.first_name": "full_name"})',
  `custom_columns` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of custom SQL columns with expressions and aliases',
  `filters` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of filter configurations for WHERE clauses',
  `sorting` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of sorting rules for ORDER BY clauses',
  `limits` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON object with limit and offset settings for pagination',
  `column_mapping` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON column mapping configuration for advanced use cases',
  `created_by` int(11) DEFAULT NULL COMMENT 'User ID who created this data source',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'When the data source was created',
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT 'When the data source was last updated'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Data source configurations for tables and email campaigns with full multi-table support, aliases, custom columns, sorting, and limits';

--
-- Dumping data for table `autobooks_data_sources`
--

INSERT INTO `autobooks_data_sources` (`id`, `name`, `description`, `category`, `status`, `table_name`, `tables`, `table_aliases`, `joins`, `selected_columns`, `column_aliases`, `custom_columns`, `filters`, `sorting`, `limits`, `column_mapping`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 'Autodesk_autorenew', '', 'other', 'active', 'autodesk_subscriptions', '[\"autodesk_subscriptions\",\"autodesk_accounts\"]', '{\"autodesk_subscriptions\":\"subs\"}', '[{\"type\":\"LEFT\",\"left_table\":\"autodesk_subscriptions\",\"left_column\":\"autodesk_subscriptions.endCustomer_csn\",\"right_table\":\"autodesk_accounts\",\"right_column\":\"autodesk_accounts.account_csn\",\"left_alias\":\"\",\"right_alias\":\"endcust\"}]', '{\"autodesk_subscriptions\":[\"subscriptionId\",\"subscriptionReferenceNumber\",\"quantity\",\"status\",\"startDate\",\"endDate\",\"paymentMethod\",\"offeringId\",\"offeringCode\",\"offeringName\",\"autoRenew\"],\"autodesk_accounts\":[\"name\",\"first_name\",\"last_name\",\"email\",\"city\",\"postal_code\"]}', '[]', '[]', '[{\"column\":\"autoRenew\",\"operator\":\"=\",\"value\":\"ON\"},{\"column\":\"endDate\",\"operator\":\">\",\"value\":\"now()\"}]', '[]', '{\"enabled\":false,\"limit\":\"\",\"offset\":\"\"}', '[]', 2, '2025-07-25 14:52:00', '2025-07-25 14:52:00');
