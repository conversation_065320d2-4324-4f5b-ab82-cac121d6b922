-- Create table for storing data table configurations
CREATE TABLE IF NOT EXISTS `autobooks_data_table_storage` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `table_name` varchar(255) NOT NULL,
    `user_id` int(11) DEFAULT NULL,
    `configuration` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`configuration`)),
    `data_source` varchar(255) DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_table_user` (`table_name`, `user_id`),
    KEY `idx_table_name` (`table_name`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_data_source` (`data_source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add foreign key constraint if users table exists
-- ALTER TABLE `autobooks_data_table_storage` 
-- ADD CONSTRAINT `fk_data_table_storage_user` 
-- FOREIGN KEY (`user_id`) REFERENCES `autobooks_users` (`id`) ON DELETE CASCADE;
