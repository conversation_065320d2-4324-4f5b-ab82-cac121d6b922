
-- --------------------------------------------------------

--
-- Table structure for table `autodesk_quote_line_items_reference_subscriptions`
--

CREATE TABLE `autodesk_quote_line_items_reference_subscriptions` (
  `id` int(11) NOT NULL,
  `line_item_id` int(11) NOT NULL,
  `quantity` int(11) DEFAULT NULL,
  `endDate` date DEFAULT NULL,
  `term` varchar(50) DEFAULT NULL,
  `term_code` varchar(20) DEFAULT NULL,
  `term_description` varchar(255) DEFAULT NULL,
  `offeringCode` varchar(50) DEFAULT NULL,
  `offeringName` varchar(255) DEFAULT NULL,
  `marketingName` varchar(255) DEFAULT NULL,
  `annualDeclaredValue` decimal(10,2) DEFAULT NULL,
  `pricingMethod` varchar(50) DEFAULT NULL,
  `pricingMethod_code` varchar(20) DEFAULT NULL,
  `pricingMethod_description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
