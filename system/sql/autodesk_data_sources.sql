-- ========================================
-- Autodesk Data Sources Migration
-- ========================================
-- This file creates data sources for all Autodesk tables
-- to be used with the data table system

-- 1. Autodesk Subscriptions Data Source
INSERT INTO `autobooks_data_sources` (
    `name`, 
    `description`, 
    `category`, 
    `table_name`, 
    `joins`, 
    `filters`, 
    `columns`, 
    `status`, 
    `created_by`, 
    `created_at`
) VALUES (
    'Autodesk Subscriptions',
    'Complete subscription data with account relationships and calculated fields',
    'autodesk',
    'autodesk_subscriptions',
    '[
        {
            "type": "LEFT JOIN",
            "table": "autodesk_accounts",
            "alias": "soldto",
            "condition": "autodesk_subscriptions.soldTo_id = soldto.id"
        },
        {
            "type": "LEFT JOIN", 
            "table": "autodesk_accounts",
            "alias": "endcust",
            "condition": "autodesk_subscriptions.endCustomer_id = endcust.id"
        },
        {
            "type": "LEFT JOIN",
            "table": "autodesk_accounts", 
            "alias": "solpro",
            "condition": "autodesk_subscriptions.solutionProvider_id = solpro.id"
        },
        {
            "type": "LEFT JOIN",
            "table": "autodesk_accounts",
            "alias": "resell", 
            "condition": "autodesk_subscriptions.nurtureReseller_id = resell.id"
        }
    ]',
    '[
        {
            "field": "autodesk_subscriptions.status",
            "operator": "IN",
            "values": ["Active", "Expired", "Cancelled"],
            "label": "Subscription Status"
        },
        {
            "field": "autodesk_subscriptions.endDate",
            "operator": "BETWEEN",
            "label": "End Date Range"
        },
        {
            "field": "autodesk_subscriptions.offeringCode",
            "operator": "LIKE",
            "label": "Product Code"
        },
        {
            "field": "soldto.name",
            "operator": "LIKE", 
            "label": "Sold To Company"
        },
        {
            "field": "endcust.name",
            "operator": "LIKE",
            "label": "End Customer Company"
        }
    ]',
    '[
        {
            "field": "autodesk_subscriptions.id",
            "alias": "subscription_id",
            "label": "ID"
        },
        {
            "field": "autodesk_subscriptions.subscriptionId",
            "alias": "subscription_number",
            "label": "Subscription ID"
        },
        {
            "field": "autodesk_subscriptions.subscriptionReferenceNumber", 
            "alias": "reference_number",
            "label": "Reference Number"
        },
        {
            "field": "autodesk_subscriptions.status",
            "alias": "status",
            "label": "Status"
        },
        {
            "field": "autodesk_subscriptions.startDate",
            "alias": "start_date", 
            "label": "Start Date"
        },
        {
            "field": "autodesk_subscriptions.endDate",
            "alias": "end_date",
            "label": "End Date"
        },
        {
            "field": "CASE WHEN DATEDIFF(autodesk_subscriptions.endDate, NOW()) >= -31 THEN DATEDIFF(autodesk_subscriptions.endDate, NOW()) ELSE 9999 END",
            "alias": "days_to_expiry",
            "label": "Days to Expiry"
        },
        {
            "field": "autodesk_subscriptions.quantity",
            "alias": "quantity",
            "label": "Quantity"
        },
        {
            "field": "autodesk_subscriptions.offeringCode",
            "alias": "product_code",
            "label": "Product Code"
        },
        {
            "field": "autodesk_subscriptions.offeringName",
            "alias": "product_name", 
            "label": "Product Name"
        },
        {
            "field": "soldto.name",
            "alias": "sold_to_company",
            "label": "Sold To"
        },
        {
            "field": "endcust.name",
            "alias": "end_customer_company",
            "label": "End Customer"
        },
        {
            "field": "solpro.name",
            "alias": "solution_provider",
            "label": "Solution Provider"
        },
        {
            "field": "resell.name",
            "alias": "reseller",
            "label": "Reseller"
        },
        {
            "field": "autodesk_subscriptions.autoRenew",
            "alias": "auto_renew",
            "label": "Auto Renew"
        },
        {
            "field": "autodesk_subscriptions.renewalCounter",
            "alias": "renewal_count",
            "label": "Renewal Count"
        },
        {
            "field": "autodesk_subscriptions.tcs_unsubscribe",
            "alias": "unsubscribed",
            "label": "Unsubscribed"
        },
        {
            "field": "autodesk_subscriptions.last_modified",
            "alias": "last_updated",
            "label": "Last Updated"
        }
    ]',
    'active',
    1,
    NOW()
);

-- 2. Autodesk Accounts Data Source
INSERT INTO `autobooks_data_sources` (
    `name`,
    `description`,
    `category`,
    `table_name`,
    `joins`,
    `filters`,
    `columns`,
    `status`,
    `created_by`,
    `created_at`
) VALUES (
    'Autodesk Accounts',
    'Customer account information with contact details and preferences',
    'autodesk',
    'autodesk_accounts',
    '[]',
    '[
        {
            "field": "account_type",
            "operator": "IN",
            "values": ["Customer", "Partner", "Reseller"],
            "label": "Account Type"
        },
        {
            "field": "country",
            "operator": "=",
            "label": "Country"
        },
        {
            "field": "status",
            "operator": "=",
            "label": "Account Status"
        },
        {
            "field": "name",
            "operator": "LIKE",
            "label": "Company Name"
        },
        {
            "field": "email",
            "operator": "LIKE",
            "label": "Email"
        }
    ]',
    '[
        {
            "field": "id",
            "alias": "account_id",
            "label": "Account ID"
        },
        {
            "field": "account_csn",
            "alias": "csn",
            "label": "CSN"
        },
        {
            "field": "name",
            "alias": "company_name",
            "label": "Company Name"
        },
        {
            "field": "CONCAT(first_name, \" \", last_name)",
            "alias": "contact_name",
            "label": "Contact Name"
        },
        {
            "field": "email",
            "alias": "email",
            "label": "Email"
        },
        {
            "field": "phone",
            "alias": "phone",
            "label": "Phone"
        },
        {
            "field": "account_type",
            "alias": "account_type",
            "label": "Account Type"
        },
        {
            "field": "CONCAT(address1, \", \", city, \", \", state_province, \" \", postal_code)",
            "alias": "full_address",
            "label": "Address"
        },
        {
            "field": "country",
            "alias": "country",
            "label": "Country"
        },
        {
            "field": "status",
            "alias": "status",
            "label": "Status"
        },
        {
            "field": "team_name",
            "alias": "team",
            "label": "Team"
        },
        {
            "field": "CASE WHEN do_not_email = 1 THEN \"Yes\" ELSE \"No\" END",
            "alias": "do_not_email",
            "label": "Do Not Email"
        },
        {
            "field": "primary_admin_email",
            "alias": "admin_email",
            "label": "Admin Email"
        },
        {
            "field": "last_modified",
            "alias": "last_updated",
            "label": "Last Updated"
        }
    ]',
    'active',
    1,
    NOW()
);

-- 3. Autodesk Quotes Data Source
INSERT INTO `autobooks_data_sources` (
    `name`,
    `description`,
    `category`,
    `table_name`,
    `joins`,
    `filters`,
    `columns`,
    `status`,
    `created_by`,
    `created_at`
) VALUES (
    'Autodesk Quotes',
    'Quote information with line items and customer details',
    'autodesk',
    'autodesk_quotes',
    '[
        {
            "type": "LEFT JOIN",
            "table": "autodesk_accounts",
            "alias": "customer",
            "condition": "autodesk_quotes.soldTo_id = customer.id"
        },
        {
            "type": "LEFT JOIN",
            "table": "autodesk_quote_line_items",
            "alias": "items",
            "condition": "autodesk_quotes.id = items.quote_id"
        }
    ]',
    '[
        {
            "field": "autodesk_quotes.status",
            "operator": "IN",
            "values": ["Draft", "Sent", "Accepted", "Expired"],
            "label": "Quote Status"
        },
        {
            "field": "autodesk_quotes.expirationDate",
            "operator": "BETWEEN",
            "label": "Expiration Date Range"
        },
        {
            "field": "customer.name",
            "operator": "LIKE",
            "label": "Customer Name"
        },
        {
            "field": "autodesk_quotes.totalAmount",
            "operator": ">=",
            "label": "Minimum Amount"
        }
    ]',
    '[
        {
            "field": "autodesk_quotes.id",
            "alias": "quote_id",
            "label": "Quote ID"
        },
        {
            "field": "autodesk_quotes.quoteNumber",
            "alias": "quote_number",
            "label": "Quote Number"
        },
        {
            "field": "autodesk_quotes.status",
            "alias": "status",
            "label": "Status"
        },
        {
            "field": "customer.name",
            "alias": "customer_name",
            "label": "Customer"
        },
        {
            "field": "autodesk_quotes.totalAmount",
            "alias": "total_amount",
            "label": "Total Amount"
        },
        {
            "field": "autodesk_quotes.currency",
            "alias": "currency",
            "label": "Currency"
        },
        {
            "field": "autodesk_quotes.createdDate",
            "alias": "created_date",
            "label": "Created Date"
        },
        {
            "field": "autodesk_quotes.expirationDate",
            "alias": "expiration_date",
            "label": "Expiration Date"
        },
        {
            "field": "DATEDIFF(autodesk_quotes.expirationDate, NOW())",
            "alias": "days_to_expiry",
            "label": "Days to Expiry"
        },
        {
            "field": "COUNT(items.id)",
            "alias": "line_item_count",
            "label": "Line Items"
        },
        {
            "field": "autodesk_quotes.last_modified",
            "alias": "last_updated",
            "label": "Last Updated"
        }
    ]',
    'active',
    1,
    NOW()
);

-- 4. Autodesk Orders Data Source
INSERT INTO `autobooks_data_sources` (
    `name`,
    `description`,
    `category`,
    `table_name`,
    `joins`,
    `filters`,
    `columns`,
    `status`,
    `created_by`,
    `created_at`
) VALUES (
    'Autodesk Orders',
    'Order information with customer and fulfillment details',
    'autodesk',
    'autodesk_orders',
    '[
        {
            "type": "LEFT JOIN",
            "table": "autodesk_accounts",
            "alias": "customer",
            "condition": "autodesk_orders.soldTo_id = customer.id"
        }
    ]',
    '[
        {
            "field": "autodesk_orders.status",
            "operator": "IN",
            "values": ["Pending", "Processing", "Fulfilled", "Cancelled"],
            "label": "Order Status"
        },
        {
            "field": "autodesk_orders.orderDate",
            "operator": "BETWEEN",
            "label": "Order Date Range"
        },
        {
            "field": "customer.name",
            "operator": "LIKE",
            "label": "Customer Name"
        }
    ]',
    '[
        {
            "field": "autodesk_orders.id",
            "alias": "order_id",
            "label": "Order ID"
        },
        {
            "field": "autodesk_orders.orderNumber",
            "alias": "order_number",
            "label": "Order Number"
        },
        {
            "field": "autodesk_orders.status",
            "alias": "status",
            "label": "Status"
        },
        {
            "field": "customer.name",
            "alias": "customer_name",
            "label": "Customer"
        },
        {
            "field": "autodesk_orders.totalAmount",
            "alias": "total_amount",
            "label": "Total Amount"
        },
        {
            "field": "autodesk_orders.currency",
            "alias": "currency",
            "label": "Currency"
        },
        {
            "field": "autodesk_orders.orderDate",
            "alias": "order_date",
            "label": "Order Date"
        },
        {
            "field": "autodesk_orders.fulfillmentDate",
            "alias": "fulfillment_date",
            "label": "Fulfillment Date"
        },
        {
            "field": "autodesk_orders.last_modified",
            "alias": "last_updated",
            "label": "Last Updated"
        }
    ]',
    'active',
    1,
    NOW()
);

-- 5. Autodesk Email History Data Source
INSERT INTO `autobooks_data_sources` (
    `name`,
    `description`,
    `category`,
    `table_name`,
    `joins`,
    `filters`,
    `columns`,
    `status`,
    `created_by`,
    `created_at`
) VALUES (
    'Autodesk Email History',
    'Email communication history with subscriptions and accounts',
    'autodesk',
    'autodesk_email_history',
    '[
        {
            "type": "LEFT JOIN",
            "table": "autodesk_subscriptions",
            "alias": "sub",
            "condition": "autodesk_email_history.subscription_id = sub.id"
        },
        {
            "type": "LEFT JOIN",
            "table": "autodesk_accounts",
            "alias": "acc",
            "condition": "sub.soldTo_id = acc.id"
        }
    ]',
    '[
        {
            "field": "autodesk_email_history.email_type",
            "operator": "IN",
            "values": ["renewal", "expiry", "welcome", "notification"],
            "label": "Email Type"
        },
        {
            "field": "autodesk_email_history.sent_date",
            "operator": "BETWEEN",
            "label": "Sent Date Range"
        },
        {
            "field": "autodesk_email_history.status",
            "operator": "=",
            "label": "Email Status"
        },
        {
            "field": "acc.name",
            "operator": "LIKE",
            "label": "Customer Name"
        }
    ]',
    '[
        {
            "field": "autodesk_email_history.id",
            "alias": "email_id",
            "label": "Email ID"
        },
        {
            "field": "autodesk_email_history.email_type",
            "alias": "email_type",
            "label": "Email Type"
        },
        {
            "field": "autodesk_email_history.recipient_email",
            "alias": "recipient",
            "label": "Recipient"
        },
        {
            "field": "acc.name",
            "alias": "customer_name",
            "label": "Customer"
        },
        {
            "field": "sub.subscriptionId",
            "alias": "subscription_id",
            "label": "Subscription ID"
        },
        {
            "field": "sub.offeringName",
            "alias": "product_name",
            "label": "Product"
        },
        {
            "field": "autodesk_email_history.subject",
            "alias": "subject",
            "label": "Subject"
        },
        {
            "field": "autodesk_email_history.sent_date",
            "alias": "sent_date",
            "label": "Sent Date"
        },
        {
            "field": "autodesk_email_history.status",
            "alias": "status",
            "label": "Status"
        },
        {
            "field": "autodesk_email_history.opened",
            "alias": "opened",
            "label": "Opened"
        },
        {
            "field": "autodesk_email_history.clicked",
            "alias": "clicked",
            "label": "Clicked"
        }
    ]',
    'active',
    1,
    NOW()
);

-- 6. Expiring Subscriptions Data Source (Specialized View)
INSERT INTO `autobooks_data_sources` (
    `name`,
    `description`,
    `category`,
    `table_name`,
    `joins`,
    `filters`,
    `columns`,
    `status`,
    `created_by`,
    `created_at`
) VALUES (
    'Expiring Subscriptions',
    'Subscriptions expiring within the next 90 days with customer details',
    'autodesk',
    'autodesk_subscriptions',
    '[
        {
            "type": "LEFT JOIN",
            "table": "autodesk_accounts",
            "alias": "soldto",
            "condition": "autodesk_subscriptions.soldTo_id = soldto.id"
        },
        {
            "type": "LEFT JOIN",
            "table": "autodesk_accounts",
            "alias": "endcust",
            "condition": "autodesk_subscriptions.endCustomer_id = endcust.id"
        }
    ]',
    '[
        {
            "field": "autodesk_subscriptions.endDate",
            "operator": "BETWEEN",
            "values": ["NOW()", "DATE_ADD(NOW(), INTERVAL 90 DAY)"],
            "label": "Expiring Within 90 Days",
            "default": true
        },
        {
            "field": "autodesk_subscriptions.status",
            "operator": "=",
            "values": ["Active"],
            "label": "Active Only",
            "default": true
        },
        {
            "field": "autodesk_subscriptions.tcs_unsubscribe",
            "operator": "=",
            "values": ["0"],
            "label": "Not Unsubscribed",
            "default": true
        }
    ]',
    '[
        {
            "field": "autodesk_subscriptions.subscriptionId",
            "alias": "subscription_id",
            "label": "Subscription ID"
        },
        {
            "field": "soldto.name",
            "alias": "customer_name",
            "label": "Customer"
        },
        {
            "field": "soldto.email",
            "alias": "customer_email",
            "label": "Email"
        },
        {
            "field": "autodesk_subscriptions.offeringName",
            "alias": "product_name",
            "label": "Product"
        },
        {
            "field": "autodesk_subscriptions.quantity",
            "alias": "quantity",
            "label": "Quantity"
        },
        {
            "field": "autodesk_subscriptions.endDate",
            "alias": "expiry_date",
            "label": "Expiry Date"
        },
        {
            "field": "DATEDIFF(autodesk_subscriptions.endDate, NOW())",
            "alias": "days_remaining",
            "label": "Days Remaining"
        },
        {
            "field": "autodesk_subscriptions.autoRenew",
            "alias": "auto_renew",
            "label": "Auto Renew"
        },
        {
            "field": "CASE WHEN autodesk_subscriptions.last_email_history_id IS NOT NULL THEN \"Yes\" ELSE \"No\" END",
            "alias": "email_sent",
            "label": "Email Sent"
        }
    ]',
    'active',
    1,
    NOW()
);

-- 7. Subscription Revenue Summary Data Source
INSERT INTO `autobooks_data_sources` (
    `name`,
    `description`,
    `category`,
    `table_name`,
    `joins`,
    `filters`,
    `columns`,
    `status`,
    `created_by`,
    `created_at`
) VALUES (
    'Subscription Revenue Summary',
    'Revenue analysis by product, customer, and time period',
    'autodesk',
    'autodesk_subscriptions',
    '[
        {
            "type": "LEFT JOIN",
            "table": "autodesk_accounts",
            "alias": "soldto",
            "condition": "autodesk_subscriptions.soldTo_id = soldto.id"
        }
    ]',
    '[
        {
            "field": "autodesk_subscriptions.startDate",
            "operator": "BETWEEN",
            "label": "Start Date Range"
        },
        {
            "field": "autodesk_subscriptions.status",
            "operator": "IN",
            "values": ["Active", "Expired"],
            "label": "Status"
        },
        {
            "field": "autodesk_subscriptions.offeringCode",
            "operator": "LIKE",
            "label": "Product Code"
        },
        {
            "field": "soldto.country",
            "operator": "=",
            "label": "Country"
        }
    ]',
    '[
        {
            "field": "autodesk_subscriptions.offeringCode",
            "alias": "product_code",
            "label": "Product Code"
        },
        {
            "field": "autodesk_subscriptions.offeringName",
            "alias": "product_name",
            "label": "Product Name"
        },
        {
            "field": "soldto.country",
            "alias": "country",
            "label": "Country"
        },
        {
            "field": "COUNT(*)",
            "alias": "subscription_count",
            "label": "Subscriptions"
        },
        {
            "field": "SUM(autodesk_subscriptions.quantity)",
            "alias": "total_quantity",
            "label": "Total Quantity"
        },
        {
            "field": "YEAR(autodesk_subscriptions.startDate)",
            "alias": "start_year",
            "label": "Start Year"
        },
        {
            "field": "MONTH(autodesk_subscriptions.startDate)",
            "alias": "start_month",
            "label": "Start Month"
        },
        {
            "field": "COUNT(CASE WHEN autodesk_subscriptions.status = \"Active\" THEN 1 END)",
            "alias": "active_count",
            "label": "Active Subscriptions"
        },
        {
            "field": "COUNT(CASE WHEN autodesk_subscriptions.autoRenew = \"true\" THEN 1 END)",
            "alias": "auto_renew_count",
            "label": "Auto Renew Count"
        }
    ]',
    'active',
    1,
    NOW()
);

-- ========================================
-- End of Autodesk Data Sources Migration
-- ========================================
