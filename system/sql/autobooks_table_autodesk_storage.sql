
-- --------------------------------------------------------

--
-- Table structure for table `autodesk_storage`
--

CREATE TABLE `autodesk_storage` (
  `autodesk_storage_id` int(11) NOT NULL,
  `autodesk_storage_key` varchar(64) NOT NULL,
  `autodesk_storage_data` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `autodesk_storage`
--

INSERT INTO `autodesk_storage` (`autodesk_storage_id`, `autodesk_storage_key`, `autodesk_storage_data`) VALUES
(1, 'subscription_renew_email_template', '  <h1 style=\"color: #0078D7;\">RENEW NOW</h1>\r\n    <p><strong>IT’S TIME TO RENEW YOUR AUTODESK SUBSCRIPTION CONTRACT</strong></p>\r\n    <p style=\"color: #666;\">Please disregard this email if you have already renewed.</p>\r\n    \r\n    <p>Dear <strong>{{endCustomerName}}</strong>,</p>\r\n    <p>Your Autodesk subscription expires soon. Please check the contract details below and the renewal procedures.</p>\r\n    \r\n    <table style=\"width: 100%; border-collapse: collapse; margin-bottom: 20px;\">\r\n        <tr>\r\n            <td style=\"padding: 8px; border: 1px solid #ddd;\"><strong>Product:</strong></td>\r\n            <td style=\"padding: 8px; border: 1px solid #ddd;\">{{product_name}}</td>\r\n        </tr>\r\n        <tr>\r\n            <td style=\"padding: 8px; border: 1px solid #ddd;\"><strong>Terms:</strong></td>\r\n            <td style=\"padding: 8px; border: 1px solid #ddd;\">{{term}}</td>\r\n        </tr>\r\n        <tr>\r\n            <td style=\"padding: 8px; border: 1px solid #ddd;\"><strong>Seats:</strong></td>\r\n            <td style=\"padding: 8px; border: 1px solid #ddd;\">{{seats}}</td>\r\n        </tr>\r\n        <tr>\r\n            <td style=\"padding: 8px; border: 1px solid #ddd;\"><strong>Serial Number:</strong></td>\r\n            <td style=\"padding: 8px; border: 1px solid #ddd;\">{{subscriptionReferenceNumber}}</td>\r\n        </tr>\r\n        <tr>\r\n            <td style=\"padding: 8px; border: 1px solid #ddd;\"><strong>Renewal Number:</strong></td>\r\n            <td style=\"padding: 8px; border: 1px solid #ddd;\">{{$opportunityNumber}}</td>\r\n        </tr>\r\n    </table>\r\n    \r\n    <p>Please reply to this email to receive a quotation for this renewal.</p>\r\n    \r\n    <h2>Why buy from Autodesk Partner?</h2>\r\n    <p>As a partner, we understand your unique business and industry needs. Autodesk Authorized partners develop solutions, implement and provide specialist support services and much more. See below:</p>\r\n    <ul style=\"list-style: disc; margin-left: 20px;\">\r\n        <li>Competitive Pricing</li>\r\n        <li>Experienced Sales Staff</li>\r\n        <li>Dedicated sales contact with phone number</li>\r\n        <li>Access to value-added services and products</li>\r\n        <li>Product support and training</li>\r\n    </ul>\r\n    \r\n    <p>Regards,</p>\r\n    <p><strong>Aurangzaib Mahmood</strong><br>\r\n    TCS CAD & BIM Solutions Limited<br>\r\n    Unit F, Yorkway<br>\r\n    Mandale Industrial Estate<br>\r\n    Thornaby<br>\r\n    Stockton-on-Tees<br>\r\n    TS17 6BX<br>\r\n    Tel: ************</p>\r\n    \r\n    <p style=\"font-size: 0.9em; color: #666;\">You have received this email because your administrator has set up an automated email notification.</p>\r\n\r\n                '),
(3, 'subscription_renew_email_send_rules', '-20,-15,-10,0,1,3,5,10,15,30,60,90'),
(4, 'subscription_renew_email_from', '<EMAIL>'),
(5, 'subscription_renew_email_subject', 'Your Autodesk contract is due for Renewal shortly '),
(49, 'subscription_renew_email_send_days', ',true,true,true,true,true'),
(56, 'subscription_renew_email_send_time', '11'),
(63, 'subscription_export_data', '{\"id\":\"7afa32e2c8a0f9813a472c38433dd709\",\"status\":\"processing\",\"password\":\"Y2RlMmY4NDItNjhlMC00OTVlLWI2YWYtMjA0YjE0MWZmM2Y3\"}'),
(87, 'quote_export_data', '\"{\\\"id\\\":\\\"922347a4-712e-405b-bf98-bda0b1e4d62d\\\",\\\"status\\\":\\\"processing\\\",\\\"password\\\":\\\"ODVmMjA2NjAtYzEwOS00N2E4LWJiYzQtMDBmNDMwMjhhM2Nm\\\"}\"'),
(105, 'subscription_table_config', '{\r\n  \"table_id\": \"subscriptions\",\r\n  \"db_table\": \"subs\",\r\n  \"columns\": {\r\n    \"endcust_name\": {\r\n      \"label\": \"Name\",\r\n      \"field\": [\r\n        \"endcust_name\",\r\n        \"endcust_primary_admin_email\",\r\n        \"endcust_account_csn\"\r\n      ]\r\n    },\r\n    \"subs_offeringName\": {\r\n      \"label\": \"OfferingName\",\r\n      \"field\": \"subs_offeringName\",\r\n      \"auto_filter\": true,\r\n      \"filter_limit\": 100\r\n    },\r\n    \"subs_status\": {\r\n      \"label\": \"Status\",\r\n      \"field\": \"subs_status\",\r\n      \"filters\": {\r\n        \"Active\": \"Active\",\r\n        \"Canceled\": \"Canceled\",\r\n        \"Expired\": \"Expired\",\r\n        \"Suspended\": \"Suspended\"\r\n      }\r\n    },\r\n    \"subs_subscriptionId\": {\r\n      \"label\": \"Id\",\r\n      \"field\": [\r\n        \"subs_subscriptionId\",\r\n        \"subs_opportunityNumber\",\r\n        \"subs_subscriptionReferenceNumber\"\r\n      ]\r\n    },\r\n    \"subs_quantity\": {\r\n      \"label\": \"Seats\",\r\n      \"field\": \"subs_quantity\",\r\n      \"filters\": {\r\n        \"1\": \"1\",\r\n        \"2\": \"2\",\r\n        \"3\": \"3\",\r\n        \"4\": \"4\",\r\n        \"5\": \"5\",\r\n        \"6\": \"6\",\r\n        \"7\": \"7\",\r\n        \"8\": \"8\",\r\n        \"9\": \"9\",\r\n        \"10\": \"10\"\r\n      }\r\n    },\r\n    \"subs_term\": {\r\n      \"label\": \"Term\",\r\n      \"field\": \"subs_term\",\r\n      \"filters\": {\r\n        \"Annual\": \"Annual\",\r\n        \"2 Year\": \"2-Year\",\r\n        \"3 Year\": \"3-Year\"\r\n      }\r\n    },\r\n    \"subs_endDate\": {\r\n      \"label\": \"endDate\",\r\n      \"field\": [\r\n        \"subs_enddatediff\",\r\n        \"subs_endDate\"\r\n      ]\r\n    },\r\n    \"quoteStatus\":{\r\n       \"label\":\"quoteStatus\",\r\n       \"field\":[\"lastquote_quote_status\",\"lastquote_quote_number\"]\r\n    },\r\n    \"lastmod\": {\r\n      \"label\": \"lastmod\",\r\n      \"field\": \"subs_last_modified\"\r\n    },\r\n    \"actions\": {\r\n      \"label\": \"Actions\",\r\n      \"field\": \"subs_id\"\r\n    }\r\n  }\r\n}'),
(106, 'subscription_replacements', '[]');
