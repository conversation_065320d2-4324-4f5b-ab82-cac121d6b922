
--
-- Indexes for dumped tables
--

--
-- Indexes for table `api_cache`
--
ALTER TABLE `api_cache`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `autobooks_data_sources`
--
ALTER TABLE `autobooks_data_sources`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_status_category` (`status`,`category`) COMMENT 'Index for filtering by status and category',
  ADD KEY `idx_table_name` (`table_name`) COMMENT 'Index for backward compatibility queries',
  ADD KEY `idx_created_by` (`created_by`) COMMENT 'Index for user-specific queries',
  ADD KEY `idx_created_at` (`created_at`) COMMENT 'Index for chronological queries',
  ADD KEY `idx_name` (`name`),
  ADD KEY `idx_category_status` (`category`,`status`);

--
-- Indexes for table `autobooks_navigation`
--
ALTER TABLE `autobooks_navigation`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_route` (`parent_path`,`route_key`) COMMENT 'Ensure unique route within parent path',
  ADD KEY `idx_parent_path` (`parent_path`),
  ADD KEY `idx_route_key` (`route_key`),
  ADD KEY `idx_sort_order` (`sort_order`);

--
-- Indexes for table `autobooks_notifications`
--
ALTER TABLE `autobooks_notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `is_read` (`is_read`),
  ADD KEY `created_at` (`created_at`);

--
-- Indexes for table `autobooks_notification_preferences`
--
ALTER TABLE `autobooks_notification_preferences`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_type` (`user_id`,`type`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `autobooks_sessions`
--
ALTER TABLE `autobooks_sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `autobooks_users`
--
ALTER TABLE `autobooks_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `autodesk_accounts`
--
ALTER TABLE `autodesk_accounts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `account_csn` (`account_csn`) USING BTREE,
  ADD KEY `idx_endcust_id` (`id`);

--
-- Indexes for table `autodesk_email_history`
--
ALTER TABLE `autodesk_email_history`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `autodesk_history`
--
ALTER TABLE `autodesk_history`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `autodesk_orders`
--
ALTER TABLE `autodesk_orders`
  ADD PRIMARY KEY (`quote_id`);

--
-- Indexes for table `autodesk_quotes`
--
ALTER TABLE `autodesk_quotes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `quote_number` (`quote_number`),
  ADD KEY `idx_quote_id` (`quote_number`) USING BTREE;

--
-- Indexes for table `autodesk_quote_line_items`
--
ALTER TABLE `autodesk_quote_line_items`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `line_number` (`line_number`),
  ADD KEY `idx_qitem_subscription_id` (`subscription_id`),
  ADD KEY `idx_qitem_quote_id` (`quote_id`);

--
-- Indexes for table `autodesk_quote_line_items_reference_subscriptions`
--
ALTER TABLE `autodesk_quote_line_items_reference_subscriptions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `line_item_id` (`line_item_id`),
  ADD KEY `idx_line_item_id` (`line_item_id`);

--
-- Indexes for table `autodesk_quote_line_item_offers`
--
ALTER TABLE `autodesk_quote_line_item_offers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `line_item_id` (`line_item_id`);

--
-- Indexes for table `autodesk_storage`
--
ALTER TABLE `autodesk_storage`
  ADD PRIMARY KEY (`autodesk_storage_id`),
  ADD UNIQUE KEY `autodesk_storage_key` (`autodesk_storage_key`);

--
-- Indexes for table `autodesk_subscriptions`
--
ALTER TABLE `autodesk_subscriptions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `subscriptionReferenceNumber` (`subscriptionReferenceNumber`),
  ADD UNIQUE KEY `subscriptionId` (`subscriptionId`),
  ADD KEY `last_email_history_id` (`last_email_history_id`),
  ADD KEY `soldTo_id` (`soldTo_id`),
  ADD KEY `solutionProvider_id` (`solutionProvider_id`),
  ADD KEY `nurtureReseller_id` (`nurtureReseller_id`),
  ADD KEY `endCustomer_id` (`endCustomer_id`),
  ADD KEY `idx_subs_endDate` (`endDate`),
  ADD KEY `idx_subs_endCustomer_id` (`endCustomer_id`),
  ADD KEY `idx_subs_subscriptionId` (`subscriptionId`),
  ADD KEY `idx_subs_status` (`status`);

--
-- Indexes for table `autodesk_subscriptions_unsub`
--
ALTER TABLE `autodesk_subscriptions_unsub`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `subscriptionId` (`subscriptionId`,`subscriptionReferenceNumber`);

--
-- Indexes for table `autodesk_subscription_history`
--
ALTER TABLE `autodesk_subscription_history`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `email_campaigns`
--
ALTER TABLE `email_campaigns`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_type` (`type`),
  ADD KEY `idx_next_run` (`next_run_at`),
  ADD KEY `idx_created_by` (`created_by`),
  ADD KEY `idx_template_id` (`template_id`),
  ADD KEY `idx_data_source_id` (`data_source_id`);

--
-- Indexes for table `email_campaign_history`
--
ALTER TABLE `email_campaign_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_campaign_id` (`campaign_id`),
  ADD KEY `idx_recipient_email` (`recipient_email`),
  ADD KEY `idx_send_status` (`send_status`),
  ADD KEY `idx_sent_at` (`sent_at`),
  ADD KEY `idx_recipient_reference` (`recipient_reference_id`);

--
-- Indexes for table `email_campaign_recipients`
--
ALTER TABLE `email_campaign_recipients`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_campaign_email` (`campaign_id`,`email`),
  ADD KEY `idx_campaign_id` (`campaign_id`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_recipient_type` (`recipient_type`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_unsubscribed` (`unsubscribed`);

--
-- Indexes for table `email_campaign_rules`
--
ALTER TABLE `email_campaign_rules`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_campaign_id` (`campaign_id`),
  ADD KEY `idx_rule_type` (`rule_type`),
  ADD KEY `idx_priority` (`priority`);

--
-- Indexes for table `email_campaign_templates`
--
ALTER TABLE `email_campaign_templates`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_campaign_id` (`campaign_id`),
  ADD KEY `idx_version` (`version`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indexes for table `email_unsubscribe_tokens`
--
ALTER TABLE `email_unsubscribe_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_token` (`token`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_expires_at` (`expires_at`),
  ADD KEY `idx_campaign_id` (`campaign_id`);

--
-- Indexes for table `manual_subscription_entries`
--
ALTER TABLE `manual_subscription_entries`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_company_name` (`company_name`),
  ADD KEY `idx_email_address` (`email_address`),
  ADD KEY `idx_contact_email` (`contact_email`),
  ADD KEY `idx_admin_email` (`admin_email`),
  ADD KEY `idx_subscription_reference` (`subscription_reference`),
  ADD KEY `idx_subscription_number` (`subscription_number`),
  ADD KEY `idx_reference_number` (`reference_number`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_end_date` (`end_date`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `api_cache`
--
ALTER TABLE `api_cache`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `autobooks_data_sources`
--
ALTER TABLE `autobooks_data_sources`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `autobooks_navigation`
--
ALTER TABLE `autobooks_navigation`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=40;

--
-- AUTO_INCREMENT for table `autobooks_notifications`
--
ALTER TABLE `autobooks_notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `autobooks_notification_preferences`
--
ALTER TABLE `autobooks_notification_preferences`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `autobooks_sessions`
--
ALTER TABLE `autobooks_sessions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=505;

--
-- AUTO_INCREMENT for table `autobooks_users`
--
ALTER TABLE `autobooks_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `autodesk_accounts`
--
ALTER TABLE `autodesk_accounts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=275274;

--
-- AUTO_INCREMENT for table `autodesk_email_history`
--
ALTER TABLE `autodesk_email_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=401;

--
-- AUTO_INCREMENT for table `autodesk_history`
--
ALTER TABLE `autodesk_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2042;

--
-- AUTO_INCREMENT for table `autodesk_orders`
--
ALTER TABLE `autodesk_orders`
  MODIFY `quote_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=125;

--
-- AUTO_INCREMENT for table `autodesk_quotes`
--
ALTER TABLE `autodesk_quotes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1773;

--
-- AUTO_INCREMENT for table `autodesk_quote_line_items`
--
ALTER TABLE `autodesk_quote_line_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1952;

--
-- AUTO_INCREMENT for table `autodesk_quote_line_items_reference_subscriptions`
--
ALTER TABLE `autodesk_quote_line_items_reference_subscriptions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `autodesk_quote_line_item_offers`
--
ALTER TABLE `autodesk_quote_line_item_offers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `autodesk_storage`
--
ALTER TABLE `autodesk_storage`
  MODIFY `autodesk_storage_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=123;

--
-- AUTO_INCREMENT for table `autodesk_subscriptions`
--
ALTER TABLE `autodesk_subscriptions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=62780;

--
-- AUTO_INCREMENT for table `autodesk_subscriptions_unsub`
--
ALTER TABLE `autodesk_subscriptions_unsub`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `autodesk_subscription_history`
--
ALTER TABLE `autodesk_subscription_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `email_campaigns`
--
ALTER TABLE `email_campaigns`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `email_campaign_history`
--
ALTER TABLE `email_campaign_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=803;

--
-- AUTO_INCREMENT for table `email_campaign_recipients`
--
ALTER TABLE `email_campaign_recipients`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `email_campaign_rules`
--
ALTER TABLE `email_campaign_rules`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `email_campaign_templates`
--
ALTER TABLE `email_campaign_templates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `email_unsubscribe_tokens`
--
ALTER TABLE `email_unsubscribe_tokens`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `manual_subscription_entries`
--
ALTER TABLE `manual_subscription_entries`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `autobooks_sessions`
--
ALTER TABLE `autobooks_sessions`
  ADD CONSTRAINT `autobooks_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `autobooks_users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `email_campaign_history`
--
ALTER TABLE `email_campaign_history`
  ADD CONSTRAINT `email_campaign_history_ibfk_1` FOREIGN KEY (`campaign_id`) REFERENCES `email_campaigns` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `email_campaign_recipients`
--
ALTER TABLE `email_campaign_recipients`
  ADD CONSTRAINT `email_campaign_recipients_ibfk_1` FOREIGN KEY (`campaign_id`) REFERENCES `email_campaigns` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `email_campaign_rules`
--
ALTER TABLE `email_campaign_rules`
  ADD CONSTRAINT `email_campaign_rules_ibfk_1` FOREIGN KEY (`campaign_id`) REFERENCES `email_campaigns` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `email_campaign_templates`
--
ALTER TABLE `email_campaign_templates`
  ADD CONSTRAINT `email_campaign_templates_ibfk_1` FOREIGN KEY (`campaign_id`) REFERENCES `email_campaigns` (`id`) ON DELETE CASCADE;
