-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jul 31, 2025 at 08:06 PM
-- Server version: 10.2.9-MariaDB
-- PHP Version: 8.4.8

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

--
-- Database: `wwwcadservicescouk`
--

-- --------------------------------------------------------

--
-- Table structure for table `autobooks_data_sources`
--

CREATE TABLE `autobooks_data_sources` (
                                          `id` int(10) UNSIGNED NOT NULL,
                                          `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Display name for the data source',
                                          `description` text COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Optional description of what this data source provides',
                                          `category` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'other' COMMENT 'Data source category (data_table, email, users, system, autodesk, other)',
                                          `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT 'Data source status (active, inactive, draft)',
                                          `table_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Primary database table name (for backward compatibility)',
                                          `tables` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of all tables used in this data source',
                                          `table_aliases` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON object mapping table names to their aliases (e.g., {"users": "u", "posts": "p"})',
                                          `joins` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of join configurations with type, tables, columns, and aliases',
                                          `selected_columns` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of selected columns from all tables',
                                          `column_aliases` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON object mapping column keys to their custom aliases (e.g., {"users.first_name": "full_name"})',
                                          `custom_columns` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of custom SQL columns with expressions and aliases',
                                          `filters` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of filter configurations for WHERE clauses',
                                          `sorting` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of sorting rules for ORDER BY clauses',
                                          `limits` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON object with limit and offset settings for pagination',
                                          `column_mapping` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON column mapping configuration for advanced use cases',
                                          `created_by` int(11) DEFAULT NULL COMMENT 'User ID who created this data source',
                                          `created_at` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'When the data source was created',
                                          `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT 'When the data source was last updated',
                                          `grouping` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'JSON array of grouping rules (GROUP BY)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Data source configurations for tables and email campaigns with full multi-table support, aliases, custom columns, sorting, and limits';

--
-- Dumping data for table `autobooks_data_sources`
--

INSERT INTO `autobooks_data_sources` (`id`, `name`, `description`, `category`, `status`, `table_name`, `tables`, `table_aliases`, `joins`, `selected_columns`, `column_aliases`, `custom_columns`, `filters`, `sorting`, `limits`, `column_mapping`, `created_by`, `created_at`, `updated_at`, `grouping`) VALUES
(1, 'Autodesk_autorenew', '', 'other', 'active', 'autodesk_subscriptions', '["autodesk_subscriptions","autodesk_accounts"]', '{"autodesk_subscriptions":"subs"}', '[{"type":"LEFT","left_table":"autodesk_subscriptions","left_column":"autodesk_subscriptions.endCustomer_csn","right_table":"autodesk_accounts","right_column":"autodesk_accounts.account_csn","left_alias":"","right_alias":"endcust"}]', '{"autodesk_subscriptions":["id","subscriptionId","subscriptionReferenceNumber","quantity","status","startDate","endDate","paymentMethod","offeringId","offeringCode","offeringName","autoRenew"],"autodesk_accounts":["id","account_csn","name","first_name","last_name","email","city","postal_code"]}', '[]', '[]', '[{"column":"autoRenew","operator":"=","value":"ON"},{"column":"status","operator":"=","value":"Active"}]', '[]', '{"enabled":false,"limit":"","offset":""}', '[]', 2, '2025-07-25 14:52:00', '2025-07-28 09:56:21', '[{"column":"autodesk_accounts.account_csn"}]'),
(2, 'test_send', '', 'other', 'active', 'test_mail', '["test_mail"]', '[]', '[]', '{"test_mail":["id","email","name","misc"]}', '[]', '[]', '[{"column":"misc","operator":"NOT LIKE","value":"nooo"}]', '[]', '{"enabled":false,"limit":"","offset":""}', '[]', 2, '2025-07-28 11:35:42', '2025-07-28 14:05:42', '[{"column":"test_mail.name"}]'),
(26, 'Autodesk Subscriptions', 'Complete subscription data with customer relationships', 'autodesk', 'active', 'autodesk_subscriptions', NULL, NULL, '[{"type":"LEFT","left_table":"autodesk_subscriptions","left_column":"autodesk_subscriptions.soldTo_id","right_table":"autodesk_accounts","right_column":"autodesk_accounts.id","left_alias":"","right_alias":"soldto"}]', '{"autodesk_subscriptions":["id","subscriptionId","subscriptionReferenceNumber","quantity","status","startDate","endDate","offeringCode","offeringName","autoRenew","soldTo_id"],"autodesk_accounts":["id","account_csn","name","first_name","last_name","email","city","postal_code"]}', '{"subscriptionId":"subscription_id","status":"status","offeringName":"product_name","endDate":"end_date","quantity":"quantity","soldTo_id":"customer_id"}', NULL, '[{"column":"status","operator":"=","value":"Active"},{"column":"offeringName","operator":"LIKE","value":""}]', NULL, NULL, NULL, 1, '2025-07-31 18:04:38', '2025-07-31 19:04:38', NULL),
(27, 'Autodesk Accounts', 'Customer account information', 'autodesk', 'active', 'autodesk_accounts', NULL, NULL, '[]', '{"autodesk_accounts":["id","account_csn","name","first_name","last_name","email","phone","city","postal_code","country","account_type"]}', '{"id":"account_id","name":"company_name","email":"email","phone":"phone","country":"country","account_type":"account_type"}', NULL, '[{"column":"name","operator":"LIKE","value":""},{"column":"country","operator":"=","value":""}]', NULL, NULL, NULL, 1, '2025-07-31 18:04:38', '2025-07-31 19:04:38', NULL),
(28, 'Expiring Subscriptions', 'Active subscriptions expiring within 90 days', 'autodesk', 'active', 'autodesk_subscriptions', NULL, NULL, '[{"type":"LEFT","left_table":"autodesk_subscriptions","left_column":"autodesk_subscriptions.soldTo_id","right_table":"autodesk_accounts","right_column":"autodesk_accounts.id","left_alias":"","right_alias":"soldto"}]', '{"autodesk_subscriptions":["id","subscriptionId","subscriptionReferenceNumber","quantity","status","startDate","endDate","offeringCode","offeringName","autoRenew","soldTo_id"],"autodesk_accounts":["id","account_csn","name","first_name","last_name","email","city","postal_code"]}', '{"subscriptionId":"subscription_id","offeringName":"product_name","endDate":"expiry_date","quantity":"quantity","soldTo_id":"customer_id"}', NULL, '[{"column":"status","operator":"=","value":"Active"},{"column":"endDate","operator":"BETWEEN","value":""}]', NULL, NULL, NULL, 1, '2025-07-31 18:04:38', '2025-07-31 19:04:38', NULL),
(29, 'Autodesk Email History', 'Email communication history', 'autodesk', 'active', 'autodesk_email_history', NULL, NULL, '[{"type":"LEFT","left_table":"autodesk_email_history","left_column":"autodesk_email_history.subscription_id","right_table":"autodesk_subscriptions","right_column":"autodesk_subscriptions.id","left_alias":"","right_alias":"sub"}]', '{"autodesk_email_history":["id","email_type","recipient_email","subject","sent_date","status","subscription_id"],"autodesk_subscriptions":["id","subscriptionId","offeringName"]}', '{"id":"email_id","email_type":"type","recipient_email":"recipient","subject":"subject","sent_date":"sent_date","status":"status","subscription_id":"subscription_id"}', NULL, '[{"column":"email_type","operator":"=","value":""},{"column":"sent_date","operator":"BETWEEN","value":""}]', NULL, NULL, NULL, 1, '2025-07-31 18:04:38', '2025-07-31 19:04:38', NULL);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `autobooks_data_sources`
--
ALTER TABLE `autobooks_data_sources`
    ADD PRIMARY KEY (`id`),
    ADD KEY `idx_status_category` (`status`,`category`) COMMENT 'Index for filtering by status and category',
    ADD KEY `idx_table_name` (`table_name`) COMMENT 'Index for backward compatibility queries',
    ADD KEY `idx_created_by` (`created_by`) COMMENT 'Index for user-specific queries',
    ADD KEY `idx_created_at` (`created_at`) COMMENT 'Index for chronological queries',
    ADD KEY `idx_name` (`name`),
    ADD KEY `idx_category_status` (`category`,`status`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `autobooks_data_sources`
--
ALTER TABLE `autobooks_data_sources`
    MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=30;
COMMIT;
