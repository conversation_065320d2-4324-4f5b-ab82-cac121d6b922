-- ========================================
-- Autodesk Data Sources Migration (Simple)
-- ========================================
-- Simplified version with basic data sources

-- 1. Autodesk Subscriptions Data Source
INSERT INTO autobooks_data_sources (name, description, category, table_name, status, created_by, created_at) 
VALUES ('Autodesk Subscriptions', 'Complete subscription data with account relationships', 'autodesk', 'autodesk_subscriptions', 'active', 1, NOW());

-- 2. Autodesk Accounts Data Source  
INSERT INTO autobooks_data_sources (name, description, category, table_name, status, created_by, created_at)
VALUES ('Autodesk Accounts', 'Customer account information with contact details', 'autodesk', 'autodesk_accounts', 'active', 1, NOW());

-- 3. Autodesk Quotes Data Source
INSERT INTO autobooks_data_sources (name, description, category, table_name, status, created_by, created_at)
VALUES ('Autodesk Quotes', 'Quote information with line items', 'autodesk', 'autodesk_quotes', 'active', 1, NOW());

-- 4. Autodesk Orders Data Source
INSERT INTO autobooks_data_sources (name, description, category, table_name, status, created_by, created_at)
VALUES ('Autodesk Orders', 'Order information with customer details', 'autodesk', 'autodesk_orders', 'active', 1, NOW());

-- 5. Autodesk Email History Data Source
INSERT INTO autobooks_data_sources (name, description, category, table_name, status, created_by, created_at)
VALUES ('Autodesk Email History', 'Email communication history', 'autodesk', 'autodesk_email_history', 'active', 1, NOW());

-- 6. Expiring Subscriptions Data Source
INSERT INTO autobooks_data_sources (name, description, category, table_name, status, created_by, created_at)
VALUES ('Expiring Subscriptions', 'Subscriptions expiring within 90 days', 'autodesk', 'autodesk_subscriptions', 'active', 1, NOW());

-- 7. Subscription Revenue Summary Data Source
INSERT INTO autobooks_data_sources (name, description, category, table_name, status, created_by, created_at)
VALUES ('Subscription Revenue Summary', 'Revenue analysis by product and customer', 'autodesk', 'autodesk_subscriptions', 'active', 1, NOW());
