<?php
namespace api\notifications;

use system\notifications;
use system\users;

/**
 * Get notifications for the current user
 *
 * @param array $p Request parameters
 * @return string HTML for the notifications list
 */
function get_notifications($p) {
    $notifications_manager = new notifications();
    $unread_only = isset($p['unread_only']) && $p['unread_only'] === 'true';
    $limit = isset($p['limit']) ? (int)$p['limit'] : 10;
    $offset = isset($p['offset']) ? (int)$p['offset'] : 0;

    $notification_list = $notifications_manager->getForCurrentUser($unread_only, $limit, $offset);
    $unread_count = $notifications_manager->getUnreadCount();

    // Render the notifications list
    ob_start();
    include FS_SYS_VIEWS . DS . 'notifications' . DS . 'notification-list.php';
    return ob_get_clean();
}

/**
 * Get the count of unread notifications
 *
 * @return string HTML for the notification count badge
 */
function get_unread_count() {
    $notification = new notifications();
    $count = $notification->getUnreadCount();

    // Return the count as a badge
    ob_start();
    include FS_VIEWS . DS . 'notifications'. DS .'notification-badge.php';
    return ob_get_clean();
}

/**
 * Mark a notification as read
 *
 * @param array $p Request parameters
 * @return string Success message
 */
function mark_as_read($p) {
    if (!isset($p['notification_id'])) {
        return json_encode(['error' => 'Notification ID is required']);
    }

    $notifications = new notifications();
    $result = $notifications->markAsRead($p['notification_id']);

    if ($result) {
        return json_encode(['success' => true]);
    } else {
        return json_encode(['error' => 'Failed to mark notification as read']);
    }
}

/**
 * Mark all notifications as read
 *
 * @return string Success message
 */
function mark_all_as_read() {
    $notifications = new notifications();
    $result = $notifications->markAllAsRead();

    if ($result) {
        return json_encode(['success' => true]);
    } else {
        return json_encode(['error' => 'Failed to mark all notifications as read']);
    }
}

/**
 * Get notification preferences
 *
 * @return string HTML for the notification preferences form
 */
function get_preferences() {
    $notifications = new notifications();
    $preferences = $notifications->getPreferences();

    // Render the preferences form
    ob_start();
    include FS_VIEWS . DS . 'notifications/notification-preferences.php';
    return ob_get_clean();
}

/**
 * Update notification preferences
 *
 * @param array $p Request parameters
 * @return string Success message
 */
function update_preference($p) {
    if (!isset($p['type']) || !isset($p['enabled'])) {
        return json_encode(['error' => 'Type and enabled status are required']);
    }

    $notifications = new notifications();
    $result = $notifications->updatePreference($p['type'], $p['enabled'] === 'true');

    if ($result) {
        return json_encode(['success' => true]);
    } else {
        return json_encode(['error' => 'Failed to update preference']);
    }
}

/**
 * SSE endpoint for real-time notifications
 *
 * @return void Sends SSE events
 */
function sse() {
    // Ensure user is authenticated
    $user = users::checkAuth();
    if (!$user) {
        header('HTTP/1.1 401 Unauthorized');
        exit;
    }

    // Set headers for SSE
    header('Content-Type: text/event-stream');
    header('Cache-Control: no-cache');
    header('Connection: keep-alive');
    header('X-Accel-Buffering: no'); // Disable nginx buffering

    // Send initial message
    echo "event: connected\n";
    echo "data: " . json_encode(['message' => 'Connected to notification stream']) . "\n\n";
    flush();

    // Keep connection alive and check for new notificationss
    $notifications = new notifications();
    $lastCount = $notifications->getUnreadCount();

    // Send initial count
    echo "event: count\n";
    echo "data: " . json_encode(['count' => $lastCount]) . "\n\n";
    flush();

    // Keep checking for new notificationss
    $timeout = 60; // 1 minute timeout
    $start = time();

    while (time() - $start < $timeout) {
        // Check for new notificationss
        $currentCount = $notifications->getUnreadCount();

        if ($currentCount != $lastCount) {
            // Send updated count
            echo "event: count\n";
            echo "data: " . json_encode(['count' => $currentCount]) . "\n\n";

            // If there are new notificationss, send them
            if ($currentCount > $lastCount) {
                $notification_list = $notifications->getForCurrentUser(true, $currentCount - $lastCount);
                echo "event: notifications\n";
                echo "data: " . json_encode(['notifications' => $notification_list]) . "\n\n";
            }

            $lastCount = $currentCount;
            flush();
        }

        // Send a ping every 15 seconds to keep the connection alive
        if (time() % 15 == 0) {
            echo "event: ping\n";
            echo "data: " . json_encode(['time' => time()]) . "\n\n";
            flush();
        }

        // Sleep for a short time to avoid high CPU usage
        sleep(2);
    }

    // Send a reconnect message before closing
    echo "event: reconnect\n";
    echo "data: " . json_encode(['message' => 'Reconnecting...']) . "\n\n";
    flush();
    exit;
}
