<?php

namespace api\data_table\column_preferences;

use function api\data_table\data_table_filter;
use edge\edge;
use data_table\data_table;
/**
 * Get current column preferences from database
 */
function get_column_preferences($table_name){
    $user_id = \data_table_storage::get_current_user_id();
    $config = \data_table_storage::get_configuration($table_name, $user_id);

    if ($config) {
        return $config['configuration'];
    }

    return ['hidden' => [], 'structure' => []];
}

/**
 * Save column preferences to database
 */
function save_column_preferences($table_name, $preferences, $data_source = null){
    $user_id = \data_table_storage::get_current_user_id();
    return \data_table_storage::save_configuration($table_name, $preferences, $user_id, $data_source);
}

/**
 * Initialize column structure if it doesn't exist
 */
function initialize_column_structure($table_name, $original_columns) {
    $preferences = get_column_preferences($table_name);

    // If structure doesn't exist, create it from original columns
    if (empty($preferences['structure'])) {
        $structure = [];
        $hidden = $preferences['hidden'] ?? [];

        foreach ($original_columns as $index => $col) {
            $column_id = 'col_' . $index . '_' . md5($col['label']);
            $structure[] = [
                'id' => $column_id,
                'label' => $col['label'],
                'fields' => is_array($col['field']) ? $col['field'] : [$col['field']],
                'filter' => $col['filter'] ?? false,
                'visible' => !in_array($column_id, $hidden)
            ];
        }

        $preferences['structure'] = $structure;
        save_column_preferences($table_name, $preferences);
    }

    return $preferences;
}

/**
 * Regenerate table with updated preferences
 */
function regenerate_table($params){
    // Remove column preference parameters from the callback params
    unset($params['hidden_columns'], $params['column_structure'], $params['action'], $params['column_id'], $params['field_name'], $params['source_column_id'], $params['target_column_id'], $params['column_order'], $params['column_name']);
    $criteria = data_table::api_process_criteria($params);

    $table_name = $params['table_name'] ?? '';
    $callback = $params['callback'] ?? '';

    // For data source tables, render directly with fresh column preferences
    $user_id = \data_table_storage::get_current_user_id();
    $config = \data_table_storage::get_configuration($table_name, $user_id);

    if ($config && ($config['configuration']['data_source_type'] ?? '') === 'data_source') {
        // This is a data source table - render it with OOB swap for column manager
        $fresh_preferences = get_column_preferences($table_name);

        // Render the main table
        $table_html = edge::render('data-table', [
            'table_name' => $table_name,
            'criteria' => $criteria,
            'column_preferences' => $fresh_preferences,
            'show_column_manager' => true
        ]);

        // Render the column manager with fresh preferences for OOB swap
        $column_manager_html = edge::render('data-table-column-manager', [
            'columns' => $criteria['columns'] ?? [],
            'table_name' => $table_name,
            'callback' => $callback,
            'column_preferences' => $fresh_preferences,
            'db_table' => $params['db_table'] ?? '',
            'available_fields' => $params['available_fields'] ?? '',
            'current_data_source_type' => 'data_source',
            'current_data_source_id' => $config['data_source_id'] ?? null,
            'keep_open' => true  // Keep column manager open after OOB swap
        ]);

        // Add OOB swap attribute to column manager
        $column_manager_oob = str_replace(
            'id="column-manager-' . $table_name . '"',
            'id="column-manager-' . $table_name . '" hx-swap-oob="true"',
            $column_manager_html
        );

        // Return table + OOB column manager
        return $table_html . $column_manager_oob;
    }

    // If we have a callback, use it (for legacy hardcoded tables)
    if (!empty($callback) && function_exists($callback)) {
        return $callback($criteria);
    }

    // Fallback to data_table API
    require_once FS_SYS_API . DS . 'data_table.api.php';
    return \api\data_table\data_table_filter($params);
}


function save($p) {
    $table_name = $p['table_name'] ?? '';

    $preferences = get_column_preferences($table_name);
    // Original save functionality
    $hidden_columns = json_decode($p['hidden_columns'] ?? '[]', true);
    $column_structure = json_decode($p['column_structure'] ?? '[]', true);

    $updated_preferences = array_merge($preferences, [
        'hidden' => $hidden_columns,
        'structure' => $column_structure,
        'updated_at' => date('Y-m-d H:i:s')
    ]);

    save_column_preferences($table_name, $updated_preferences, $p['data_source'] ?? null);
    return regenerate_table($p);
}

function toggle_column($p) {
    $table_name = $p['table_name'] ?? '';

    $preferences = get_column_preferences($table_name);
    $column_id = $p['column_id'] ?? '';
    if (empty($column_id)) {
        throw new Exception('Column ID is required');
    }

    $hidden = $preferences['hidden'] ?? [];
    $structure = $preferences['structure'] ?? [];
    $index = array_search($column_id, $hidden);

    if ($index !== false) {
        // Column is hidden, show it
        unset($hidden[$index]);
        $hidden = array_values($hidden); // Re-index array
        $is_visible = true;
    } else {
        // Column is visible, hide it
        $hidden[] = $column_id;
        $is_visible = false;
    }

    // Update the structure array to sync visible property
    foreach ($structure as &$column) {
        if ($column['id'] === $column_id) {
            $column['visible'] = $is_visible;
            break;
        }
    }

    $updated_preferences = array_merge($preferences, [
        'hidden' => $hidden,
        'structure' => $structure,
        'updated_at' => date('Y-m-d H:i:s')
    ]);

    // Debug removed for production

    save_column_preferences($table_name, $updated_preferences, $p['data_source'] ?? null);

    return regenerate_table($p);
}

function show_all_columns($p) {
    $table_name = $p['table_name'] ?? '';
    $preferences = get_column_preferences($table_name);
    $structure = $preferences['structure'] ?? [];

    // Update all columns in structure to be visible
    foreach ($structure as &$column) {
        $column['visible'] = true;
    }

    $updated_preferences = array_merge($preferences, [
        'hidden' => [],
        'structure' => $structure,
        'updated_at' => date('Y-m-d H:i:s')
    ]);

    save_column_preferences($table_name, $updated_preferences, $p['data_source'] ?? null);
    return regenerate_table($p);
}

function hide_all_columns($p) {
    $table_name = $p['table_name'] ?? '';

    $preferences = get_column_preferences($table_name);
    // Get all column IDs from structure or generate from current columns
    $structure = $preferences['structure'] ?? [];
    $all_column_ids = [];

    if (!empty($structure)) {
        $all_column_ids = array_column($structure, 'id');

        // Update all columns in structure to be hidden
        foreach ($structure as &$column) {
            $column['visible'] = false;
        }
    } else {
        // If no structure, we can't hide all columns
        throw new Exception('No column structure available');
    }

    $updated_preferences = array_merge($preferences, [
        'hidden' => $all_column_ids,
        'structure' => $structure,
        'updated_at' => date('Y-m-d H:i:s')
    ]);

    save_column_preferences($table_name, $updated_preferences, $p['data_source'] ?? null);
    return regenerate_table($p);
}

function add_column($p) {
    $table_name = $p['table_name'] ?? '';

    $preferences = get_column_preferences($table_name);
    $column_name = trim($p['column_name'] ?? '');
    if (empty($column_name)) {
        throw new Exception('Column name is required');
    }

    $structure = $preferences['structure'] ?? [];
    $new_id = "col_new_" . time();
    $new_column = [
        'id' => $new_id,
        'label' => $column_name,
        'fields' => [],
        'filter' => false,
        'visible' => true
    ];

    $structure[] = $new_column;
    $preferences['structure'] = $structure;
    save_column_preferences($table_name, $preferences);
    return regenerate_table($p);
}

function remove_column($p) {
    $table_name = $p['table_name'] ?? '';

    $preferences = get_column_preferences($table_name);
    $column_id = $p['column_id'] ?? '';
    if (empty($column_id)) {
        throw new Exception('Column ID is required');
    }

    $structure = $preferences['structure'] ?? [];
    $structure = array_filter($structure, function ($col) use ($column_id) {
        return $col['id'] !== $column_id;
    });
    $structure = array_values($structure); // Re-index array

    // Also remove from hidden columns if present
    $hidden = $preferences['hidden'] ?? [];
    $hidden = array_filter($hidden, function ($id) use ($column_id) {
        return $id !== $column_id;
    });
    $hidden = array_values($hidden);

    $preferences['structure'] = $structure;
    $preferences['hidden'] = $hidden;
    save_column_preferences($table_name, $preferences);
    return regenerate_table($p);
}

function add_field_to_column($p) {
    $table_name = $p['table_name'] ?? '';
    $preferences = get_column_preferences($table_name);
    $field_name = $p['field_name'] ?? '';
    $column_id = $p['column_id'] ?? '';

    if (empty($field_name) || empty($column_id)) {
        throw new Exception('Field name and column ID are required');
    }

    $structure = $preferences['structure'] ?? [];
    $hidden = $preferences['hidden'] ?? [];

    foreach ($structure as &$column) {
        if ($column['id'] === $column_id) {
            if (!in_array($field_name, $column['fields'])) {
                $column['fields'][] = $field_name;
            }
            // Ensure visible property is set
            $column['visible'] = !in_array($column['id'], $hidden);
            break;
        }
    }
    $preferences['structure'] = $structure;
    save_column_preferences($table_name, $preferences);
    return regenerate_table($p);
}

function remove_field_from_column($p) {
    $table_name = $p['table_name'] ?? '';

    $preferences = get_column_preferences($table_name);
    $field_name = $p['field_name'] ?? '';
    $column_id = $p['column_id'] ?? '';

    if (empty($field_name) || empty($column_id)) {
        throw new Exception('Field name and column ID are required');
    }

    $structure = $preferences['structure'] ?? [];
    $hidden = $preferences['hidden'] ?? [];

    foreach ($structure as &$column) {
        if ($column['id'] === $column_id) {
            $column['fields'] = array_filter($column['fields'], function ($field) use ($field_name) {
                return $field !== $field_name;
            });
            $column['fields'] = array_values($column['fields']); // Re-index
            // Ensure visible property is set
            $column['visible'] = !in_array($column['id'], $hidden);
            break;
        }
    }
    $preferences['structure'] = $structure;
    save_column_preferences($table_name, $preferences);
    return regenerate_table($p);
}

function move_field($p) {
    $table_name = $p['table_name'] ?? '';

    $preferences = get_column_preferences($table_name);
    $field_name = $p['field_name'] ?? '';
    $source_column_id = $p['source_column_id'] ?? '';
    $target_column_id = $p['target_column_id'] ?? '';

    if (empty($field_name) || empty($target_column_id)) {
        throw new Exception('Field name and target column ID are required');
    }

    $structure = $preferences['structure'] ?? [];
    $hidden = $preferences['hidden'] ?? [];

    // Remove from source column if specified
    if (!empty($source_column_id)) {
        foreach ($structure as &$column) {
            if ($column['id'] === $source_column_id) {
                $column['fields'] = array_filter($column['fields'], function ($field) use ($field_name) {
                    return $field !== $field_name;
                });
                $column['fields'] = array_values($column['fields']);
                // Ensure visible property is set
                $column['visible'] = !in_array($column['id'], $hidden);
                break;
            }
        }
    }

    // Add to target column
    foreach ($structure as &$column) {
        if ($column['id'] === $target_column_id) {
            if (!in_array($field_name, $column['fields'])) {
                $column['fields'][] = $field_name;
            }
            // Ensure visible property is set
            $column['visible'] = !in_array($column['id'], $hidden);
            break;
        }
    }

    $preferences['structure'] = $structure;
    save_column_preferences($table_name, $preferences);
    return regenerate_table($p);
}

function reorder_columns($p) {
    $table_name = $p['table_name'] ?? '';
    $column_order = json_decode($p['column_order'] ?? '[]', true);

    if (empty($column_order)) {
        throw new Exception('Column order is required');
    }

    $preferences = get_column_preferences($table_name);
    $structure = $preferences['structure'] ?? [];
    $hidden = $preferences['hidden'] ?? [];

    // Create a lookup array for existing columns
    $columns_by_id = [];
    foreach ($structure as $column) {
        $columns_by_id[$column['id']] = $column;
    }

    // Reorder structure based on the provided order
    $reordered_structure = [];
    foreach ($column_order as $column_id) {
        if (isset($columns_by_id[$column_id])) {
            $column = $columns_by_id[$column_id];
            // Ensure visible property is set correctly
            $column['visible'] = !in_array($column_id, $hidden);
            $reordered_structure[] = $column;
        }
    }

    $preferences['structure'] = $reordered_structure;
    save_column_preferences($table_name, $preferences);
    return regenerate_table($p);
}
