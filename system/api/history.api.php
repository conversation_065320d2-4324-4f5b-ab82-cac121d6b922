<?php
namespace api\history;
use autodesk_api\autodesk_api;

function add_flag($p):void {
    $data = [
        'target' => $p['target'],
        'target_reference' => $p['target_reference'],
        'media' => $p['label'],
        'user_id' => $p['user_id'],
        'message' => $p['message'],
        'customer_csn' => $p['customer_csn'],
        'email_address' => $p['email_address'] ?? '',

    ];
    $history = autodesk_api::database_update_history($data);
}
/*
':target' => $attribs['target'] ?? null,
            ':target_reference' => $attribs['target_reference'] ?? null,
            ':customer_csn' => $attribs['customer_csn'] ?? null,
            ':media' => $attribs['media'] ?? null,
            ':message' => $attribs['message'] ?? null,
            ':user_id' => $attribs['user_id'] ?? null,
            ':email_address' => $attribs['email_address'] ?? null,
            ':triggered_by' => $attribs['triggered_by'] ?? null,
            ':result' => $attribs['result'] ?? null
    */