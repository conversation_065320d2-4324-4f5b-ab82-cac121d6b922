<?php
namespace api\hilt_settings;

use system\users;
use system\hilt;
use system\database;
use system\data_importer;
use edge\Edge;

/**
 * Hilt Settings API
 * 
 * Provides functionality for managing hilt template settings including:
 * - Column mapping configuration
 * - CSV upload and update operations
 * - Data display customization
 */

/**
 * Display settings page for a hilt template
 */
function show_settings($p) {
    // Ensure only admin/dev can access settings
    users::requireRole(['admin', 'dev']);
    
    $route_key = $p['route_key'] ?? '';
    if (empty($route_key)) {
        return '<div class="p-4 bg-red-100 text-red-700">Route key is required</div>';
    }
    
    $table_name = "autobooks_{$route_key}_data";
    
    // Get current table structure and data sample
    $table_info = get_table_info($table_name);
    
    return Edge::render('hilt-settings', [
        'route_key' => $route_key,
        'table_name' => $table_name,
        'table_info' => $table_info
    ]);
}

/**
 * Upload and process CSV data
 */
function upload_csv($p) {
    users::requireRole(['admin', 'dev']);
    
    $route_key = $p['route_key'] ?? '';
    $replace_all = isset($p['replace_all']) && $p['replace_all'] === 'true';
    
    if (empty($route_key)) {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "Route key is required"}}');
        return;
    }
    
    $table_name = "autobooks_{$route_key}_data";
    
    try {
        // Ensure table exists
        if (!hilt::ensure_table_exists($table_name)) {
            header('HX-Trigger: {"showNotification": {"type": "error", "message": "Failed to create database table"}}');
            return;
        }
        
        // Process CSV upload
        $result = hilt::update_csv_data($table_name, $p, $_FILES, $replace_all);
        
        if (isset($result['error'])) {
            header('HX-Trigger: {"showNotification": {"type": "error", "message": "' . htmlspecialchars($result['error']) . '"}}');
            return;
        }
        
        // Log successful upload
        tcs_log([
            'action' => 'csv_upload_successful',
            'route_key' => $route_key,
            'table_name' => $table_name,
            'replace_all' => $replace_all,
            'user_id' => $_SESSION['user_id'] ?? 'Unknown'
        ], 'hilt_settings');
        
        header('HX-Trigger: {"showNotification": {"type": "success", "message": "CSV data uploaded successfully"}}');
        
        // Return updated table info
        return get_table_summary($table_name);
        
    } catch (\Exception $e) {
        tcs_log([
            'action' => 'csv_upload_failed',
            'route_key' => $route_key,
            'error' => $e->getMessage(),
            'user_id' => $_SESSION['user_id'] ?? 'Unknown'
        ], 'hilt_settings');
        
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "Upload failed: ' . htmlspecialchars($e->getMessage()) . '"}}');
        return;
    }
}

/**
 * Clear all data from table
 */
function clear_data($p) {
    users::requireRole(['admin', 'dev']);
    
    $route_key = $p['route_key'] ?? '';
    if (empty($route_key)) {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "Route key is required"}}');
        return;
    }
    
    $table_name = "autobooks_{$route_key}_data";
    
    try {
        if (database::schema()::hasTable($table_name)) {
            database::table($table_name)->truncate();
            
            tcs_log([
                'action' => 'table_data_cleared',
                'route_key' => $route_key,
                'table_name' => $table_name,
                'user_id' => $_SESSION['user_id'] ?? 'Unknown'
            ], 'hilt_settings');
            
            header('HX-Trigger: {"showNotification": {"type": "success", "message": "All data cleared successfully"}}');
        } else {
            header('HX-Trigger: {"showNotification": {"type": "error", "message": "Table does not exist"}}');
        }
        
        return get_table_summary($table_name);
        
    } catch (\Exception $e) {
        tcs_log([
            'action' => 'clear_data_failed',
            'route_key' => $route_key,
            'error' => $e->getMessage(),
            'user_id' => $_SESSION['user_id'] ?? 'Unknown'
        ], 'hilt_settings');
        
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "Failed to clear data: ' . htmlspecialchars($e->getMessage()) . '"}}');
        return;
    }
}

/**
 * Get table information including structure and sample data
 */
function get_table_info(string $table_name): array {
    $info = [
        'exists' => false,
        'row_count' => 0,
        'columns' => [],
        'sample_data' => [],
        'created_at' => null
    ];
    
    try {
        if (!database::schema()::hasTable($table_name)) {
            return $info;
        }
        
        $info['exists'] = true;
        
        // Get row count
        $info['row_count'] = database::table($table_name)->count();
        
        // Get sample data to determine columns
        $sample_rows = database::table($table_name)->limit(5)->get();
        
        if (!empty($sample_rows)) {
            $first_row = $sample_rows[0];
            if (isset($first_row['data_json'])) {
                $decoded_data = json_decode($first_row['data_json'], true);
                if (is_array($decoded_data)) {
                    $info['columns'] = array_keys($decoded_data);
                }
            }
            
            // Process sample data
            foreach ($sample_rows as $row) {
                $decoded_row = json_decode($row['data_json'], true);
                if (is_array($decoded_row)) {
                    $decoded_row['id'] = $row['id'];
                    $decoded_row['created_at'] = $row['created_at'];
                    $info['sample_data'][] = $decoded_row;
                }
            }
            
            $info['created_at'] = $first_row['created_at'] ?? null;
        }
        
    } catch (\Exception $e) {
        tcs_log("Error getting table info for {$table_name}: " . $e->getMessage(), 'hilt_settings');
    }
    
    return $info;
}

/**
 * Get table summary for HTMX updates
 */
function get_table_summary(string $table_name): string {
    $info = get_table_info($table_name);
    
    if (!$info['exists']) {
        return '<div class="p-4 bg-gray-100 text-gray-600">Table does not exist yet</div>';
    }
    
    $html = '<div class="p-4 bg-green-50 border border-green-200 rounded">';
    $html .= '<h3 class="font-bold text-green-800">Table Summary</h3>';
    $html .= '<p><strong>Records:</strong> ' . number_format($info['row_count']) . '</p>';
    
    if (!empty($info['columns'])) {
        $html .= '<p><strong>Columns:</strong> ' . implode(', ', $info['columns']) . '</p>';
    }
    
    if ($info['created_at']) {
        $html .= '<p><strong>Last Updated:</strong> ' . date('Y-m-d H:i:s', strtotime($info['created_at'])) . '</p>';
    }
    
    $html .= '</div>';
    
    return $html;
}

/**
 * Export table data as CSV
 */
function export_csv($p) {
    users::requireRole(['admin', 'dev']);
    
    $route_key = $p['route_key'] ?? '';
    if (empty($route_key)) {
        header('HTTP/1.1 400 Bad Request');
        echo 'Route key is required';
        return;
    }
    
    $table_name = "autobooks_{$route_key}_data";
    
    try {
        if (!database::schema()::hasTable($table_name)) {
            header('HTTP/1.1 404 Not Found');
            echo 'Table does not exist';
            return;
        }
        
        $data = database::table($table_name)->get();
        
        if (empty($data)) {
            header('HTTP/1.1 404 Not Found');
            echo 'No data to export';
            return;
        }
        
        // Set CSV headers
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $route_key . '_data_' . date('Y-m-d_H-i-s') . '.csv"');
        
        $output = fopen('php://output', 'w');
        
        // Get headers from first row
        $first_row = json_decode($data[0]['data_json'], true);
        if (is_array($first_row)) {
            $headers = array_keys($first_row);
            fputcsv($output, $headers);
            
            // Output data rows
            foreach ($data as $row) {
                $decoded_row = json_decode($row['data_json'], true);
                if (is_array($decoded_row)) {
                    fputcsv($output, array_values($decoded_row));
                }
            }
        }
        
        fclose($output);
        
    } catch (\Exception $e) {
        header('HTTP/1.1 500 Internal Server Error');
        echo 'Export failed: ' . $e->getMessage();
    }
}
