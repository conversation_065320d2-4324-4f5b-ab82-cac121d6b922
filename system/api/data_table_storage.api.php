<?php

namespace api\data_table_storage;
use system\database;

/**
 * List all data table configurations for current user
 */
function list_configurations($p) {
    $user_id = \data_table_storage::get_current_user_id();
    $configurations = \data_table_storage::list_configurations($user_id);

    return json_encode([
        'success' => true,
        'configurations' => $configurations,
        'user_id' => $user_id
    ]);
}

/**
 * Get specific configuration
 */
function get_configuration($p) {
    $table_name = $p['table_name'] ?? '';
    if (empty($table_name)) {
        return json_encode(['error' => 'Table name is required']);
    }
    
    $user_id = \data_table_storage::get_current_user_id();
    $config = \data_table_storage::get_configuration($table_name, $user_id);
    
    if ($config) {
        return json_encode([
            'success' => true,
            'configuration' => $config['configuration'],
            'data_source' => $config['data_source'],
            'updated_at' => $config['updated_at']
        ]);
    } else {
        return json_encode([
            'success' => false,
            'message' => 'Configuration not found'
        ]);
    }
}

/**
 * Save configuration
 */
function save_configuration($p) {
    $table_name = $p['table_name'] ?? '';
    $configuration = $p['configuration'] ?? [];
    $data_source = $p['data_source'] ?? null;

    if (empty($table_name)) {
        return json_encode(['error' => 'Table name is required']);
    }

    // Parse configuration if it's JSON string
    if (is_string($configuration)) {
        $configuration = json_decode($configuration, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return json_encode(['error' => 'Invalid configuration JSON']);
        }
    }

    $user_id = \data_table_storage::get_current_user_id();
    $result = \data_table_storage::save_configuration($table_name, $configuration, $user_id, $data_source);

    if ($result) {
        return json_encode([
            'success' => true,
            'message' => 'Configuration saved successfully'
        ]);
    } else {
        return json_encode([
            'success' => false,
            'error' => 'Failed to save configuration'
        ]);
    }
}

/**
 * Delete configuration
 */
function delete_configuration($p) {
    $table_name = $p['table_name'] ?? '';
    if (empty($table_name)) {
        return json_encode(['error' => 'Table name is required']);
    }

    $user_id = \data_table_storage::get_current_user_id();
    $result = \data_table_storage::delete_configuration($table_name, $user_id);

    if ($result) {
        return json_encode([
            'success' => true,
            'message' => 'Configuration deleted successfully'
        ]);
    } else {
        return json_encode([
            'success' => false,
            'error' => 'Failed to delete configuration'
        ]);
    }
}

/**
 * Reset configuration to default
 */
function reset_to_default($p) {
    $table_name = $p['table_name'] ?? '';
    $columns = $p['columns'] ?? [];
    $data_source = $p['data_source'] ?? null;

    if (empty($table_name)) {
        return json_encode(['error' => 'Table name is required']);
    }

    // Parse columns if it's JSON string
    if (is_string($columns)) {
        $columns = json_decode($columns, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return json_encode(['error' => 'Invalid columns JSON']);
        }
    }

    $user_id = \data_table_storage::get_current_user_id();

    // Delete existing configuration
    \data_table_storage::delete_configuration($table_name, $user_id);

    // Create new default configuration
    if (!empty($columns)) {
        $configuration = \data_table_storage::initialize_default_configuration($table_name, $columns, $user_id, $data_source);
        return json_encode([
            'success' => true,
            'message' => 'Configuration reset to default',
            'configuration' => $configuration
        ]);
    } else {
        return json_encode([
            'success' => true,
            'message' => 'Configuration deleted (no default columns provided)'
        ]);
    }
}

/**
 * Update data source configuration for a table
 */
function update_data_source($p) {
    $table_name = $p['table_name'] ?? '';
    $data_source_selection = $p['data_source_selection'] ?? 'hardcoded';
    $callback = $p['callback'] ?? '';

    if (empty($table_name)) {
        return json_encode(['error' => 'Table name is required']);
    }

    $user_id = \data_table_storage::get_current_user_id();

    // Get existing configuration
    $config = \data_table_storage::get_configuration($table_name, $user_id);
    $configuration = $config ? $config['configuration'] : [];

    // Parse the selection value
    if ($data_source_selection === 'hardcoded') {
        $data_source_type = 'hardcoded';
        $data_source_id = null;
    } else {
        // It's a data source ID
        $data_source_type = 'data_source';
        $data_source_id = (int)$data_source_selection;
    }

    // Update data source settings
    $configuration['data_source_type'] = $data_source_type;
    $configuration['data_source_id'] = $data_source_id;
    $configuration['updated_at'] = date('Y-m-d H:i:s');

    // Save updated configuration
    $result = \data_table_storage::save_configuration($table_name, $configuration, $user_id);

    if ($result) {
        // Regenerate the table with new data source
        if (!empty($callback)) {
            require_once FS_SYS_API . DS . 'data_table.api.php';

            $regenerate_params = [
                'table_name' => $table_name,
                'callback' => $callback
            ];

            $table_result = \api\data_table\data_table_filter($regenerate_params);
            return $table_result;
        }

        return json_encode([
            'success' => true,
            'message' => 'Data source updated successfully'
        ]);
    } else {
        return json_encode([
            'success' => false,
            'error' => 'Failed to update data source'
        ]);
    }
}

/**
 * Preview data from a data source
 */
function preview_data_source($p) {
    $data_source_id = (int)($p['data_source_id'] ?? 0);
    $limit = (int)($p['limit'] ?? 5);

    if (empty($data_source_id)) {
        return '<p class="text-red-600 text-sm">No data source selected</p>';
    }

    try {
        $result = \system\data_source_manager::get_data_source_data($data_source_id, ['limit' => $limit]);

        if (!$result['success']) {
            return '<p class="text-red-600 text-sm">Error: ' . htmlspecialchars($result['error']) . '</p>';
        }

        $data = $result['data'];
        if (empty($data)) {
            return '<p class="text-gray-600 text-sm">No data available from this data source</p>';
        }

        // Generate preview table
        $html = '<div class="overflow-x-auto">';
        $html .= '<table class="min-w-full divide-y divide-gray-200">';

        // Header
        $html .= '<thead class="bg-gray-50">';
        $html .= '<tr>';
        foreach (array_keys($data[0]) as $column) {
            $html .= '<th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">';
            $html .= htmlspecialchars($column);
            $html .= '</th>';
        }
        $html .= '</tr>';
        $html .= '</thead>';

        // Body
        $html .= '<tbody class="bg-white divide-y divide-gray-200">';
        foreach ($data as $row) {
            $html .= '<tr>';
            foreach ($row as $value) {
                $html .= '<td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">';
                $html .= htmlspecialchars($value ?? '');
                $html .= '</td>';
            }
            $html .= '</tr>';
        }
        $html .= '</tbody>';
        $html .= '</table>';
        $html .= '</div>';

        $html .= '<p class="text-xs text-gray-500 mt-2">Showing ' . count($data) . ' sample records</p>';

        return $html;

    } catch (Exception $e) {
        return '<p class="text-red-600 text-sm">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
    }
}

/**
 * Update data source and refresh columns
 */
function update_data_source_and_columns($p) {
    $table_name = $p['table_name'] ?? '';
    $data_source_selection = $p['data_source_selection'] ?? 'hardcoded';
    $callback = $p['callback'] ?? '';

    if (empty($table_name)) {
        return json_encode(['error' => 'Table name is required']);
    }

    $user_id = \data_table_storage::get_current_user_id();

    // Get existing configuration
    $config = \data_table_storage::get_configuration($table_name, $user_id);
    $configuration = $config ? $config['configuration'] : [];

    // Parse the selection value
    if ($data_source_selection === 'hardcoded') {
        $data_source_type = 'hardcoded';
        $data_source_id = null;
    } else {
        // It's a data source ID
        $data_source_type = 'data_source';
        $data_source_id = (int)$data_source_selection;
    }

    // Update data source settings
    $configuration['data_source_type'] = $data_source_type;
    $configuration['data_source_id'] = $data_source_id;
    $configuration['updated_at'] = date('Y-m-d H:i:s');

    // Debug output
    tcs_log("Data source update: table=$table_name, type=$data_source_type, id=$data_source_id", 'data_table_saga');

    // If switching to a data source, update available columns
    if ($data_source_type === 'data_source' && $data_source_id) {
        try {
            // Get data source configuration
            $data_source = database::table('autobooks_data_sources')
                ->where('id', $data_source_id)
                ->first();

            if ($data_source) {
                // Reset column structure to match data source
                $selected_columns = json_decode($data_source['selected_columns'], true);
                $column_aliases = json_decode($data_source['column_aliases'], true);

                // Build new column structure from data source
                $new_structure = [];
                $column_index = 0;

                if (is_array($selected_columns)) {
                    foreach ($selected_columns as $table => $columns) {
                        if (is_array($columns)) {
                            foreach ($columns as $column) {
                                $column_id = 'col_' . $column_index . '_' . md5($table . '.' . $column);
                                $display_name = $column_aliases[$column] ?? $column;

                                $new_structure[] = [
                                    'id' => $column_id,
                                    'label' => ucwords(str_replace('_', ' ', $display_name)),
                                    'field' => $table . '_' . $column,
                                    'filter' => true,
                                    'fields' => [$table . '_' . $column],
                                    'visible' => true
                                ];
                                $column_index++;
                            }
                        }
                    }
                }

                // Update configuration with new structure
                $configuration['structure'] = $new_structure;
                $configuration['hidden'] = []; // Reset hidden columns
                $configuration['columns'] = $new_structure; // Store for reference
            }
        } catch (Exception $e) {
            // If data source loading fails, continue with existing structure
        }
    }

    // Save updated configuration
    $result = \data_table_storage::save_configuration($table_name, $configuration, $user_id, $data_source_id);

    if ($result) {
        // Regenerate the table with new data source and columns
        if (!empty($callback)) {
            require_once FS_SYS_API . DS . 'data_table.api.php';

            $regenerate_params = [
                'table_name' => $table_name,
                'callback' => $callback
            ];

            $table_result = \api\data_table\data_table_filter($regenerate_params);
            return $table_result;
        }
        require_once FS_SYS_API . DS . 'data_table.api.php';
        $regenerate_params = [
            'table_name' => $table_name
        ];
        $table_result = \api\data_table\data_table_filter($regenerate_params);
        return $table_result;
    } else {
        return json_encode([
            'success' => false,
            'error' => 'Failed to update data source and columns'
        ]);
    }
}

/**
 * Hide all columns for a table
 */
function hide_all_columns($p) {
    $table_name = $p['table_name'] ?? '';
    $callback = $p['callback'] ?? '';

    if (empty($table_name)) {
        return json_encode(['error' => 'Table name is required']);
    }

    $user_id = \data_table_storage::get_current_user_id();

    // Get existing configuration
    $config = \data_table_storage::get_configuration($table_name, $user_id);
    $configuration = $config ? $config['configuration'] : [];

    // Get current structure
    $structure = $configuration['structure'] ?? [];

    if (!empty($structure)) {
        // Hide all columns
        $hidden_columns = [];
        foreach ($structure as &$column) {
            $column['visible'] = false;
            $hidden_columns[] = $column['id'];
        }

        // Update configuration
        $configuration['structure'] = $structure;
        $configuration['hidden'] = $hidden_columns;
        $configuration['updated_at'] = date('Y-m-d H:i:s');

        // Save configuration
        $result = \data_table_storage::save_configuration($table_name, $configuration, $user_id);

        if ($result) {
            // Regenerate the table
            if (!empty($callback)) {
                require_once FS_SYS_API . DS . 'data_table.api.php';

                $regenerate_params = [
                    'table_name' => $table_name,
                    'callback' => $callback
                ];

                $table_result = \api\data_table\data_table_filter($regenerate_params);
                return $table_result;
            }

            return json_encode([
                'success' => true,
                'message' => 'All columns hidden successfully'
            ]);
        } else {
            return json_encode([
                'success' => false,
                'error' => 'Failed to hide columns'
            ]);
        }
    } else {
        return json_encode([
            'success' => true,
            'message' => 'No columns to hide'
        ]);
    }
}
