<?php
require_once '../system/startup_sequence.php';
use system\users;


function toggle_debug_mode() {


// Ensure only dev users can access this endpoint
if (!in_array(users::checkAuth()['role'], ['dev'])) {
    http_response_code(403);
    exit('Unauthorized');
}

// Get POST data
$data = json_decode(file_get_contents('php://input'), true);
$debug_mode = isset($data['debug_mode']) ? (bool)$data['debug_mode'] : false;

// Update session
$_SESSION['debug_mode'] = $debug_mode;

// Update user preferences in database
$user_id = users::checkAuth()['id'];
tep_db_query(
    "UPDATE autobooks_users 
     SET preferences = JSON_SET(
         COALESCE(preferences, '{}'), 
         '$.debug_mode', 
         :debug_mode
     )
     WHERE id = :user_id",
    null,
    [
        ':debug_mode' => $debug_mode ? 1 : 0,
        ':user_id' => $user_id
    ]
);

// Return success response
echo json_encode(['success' => true]);

}