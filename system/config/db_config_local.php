<?php
/**
 * Local Database Configuration
 * Use this for local development with XAMPP
 */

// Detect if we're running locally
$is_local = (
    $_SERVER['SERVER_NAME'] === 'localhost' || 
    $_SERVER['SERVER_NAME'] === '127.0.0.1' ||
    strpos($_SERVER['SERVER_NAME'], 'localhost') !== false ||
    !isset($_SERVER['SERVER_NAME'])
);

if ($is_local) {
    // Local XAMPP configuration
    $db_server = '127.0.0.1';
    $db_username = 'root';
    $db_password = ''; // XAMPP default
    $db_database = 'wwwcadservicescouk';
    
    // Override the domain-based database name for local development
    $tcs_database = 'wwwcadservicescouk';
} else {
    // Production configuration (fallback to original)
    $tcs_database = str_replace('.', '', DOMAIN ?? 'wwwcadservicescouk');
    $db_server = 'localhost';
    $db_username = $tcs_database;
    $db_password = 'S96#1kvYuCGE';
    $db_database = $tcs_database;
}

// Debug info (remove in production)
if (defined('DEBUG_DB') && DEBUG_DB) {
    echo "<!-- DB Config: " . ($is_local ? 'LOCAL' : 'REMOTE') . " -->\n";
    echo "<!-- Host: $db_server, DB: $db_database, User: $db_username -->\n";
}
?>
