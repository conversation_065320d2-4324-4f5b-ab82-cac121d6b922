<?php

/**
 * Data Pattern Analysis Configuration
 * 
 * This file contains regex patterns and keyword lists used to analyze
 * actual data content to determine column types.
 */

return [
    
    /**
     * Regular Expression Patterns for Data Analysis
     */
    'regex_patterns' => [
        
        // Email patterns
        'email' => [
            'pattern' => '/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/',
            'confidence' => 95,
            'min_matches' => 1,
            'description' => 'Valid email address format'
        ],
        
        // Company/Business entity indicators
        'business_entity' => [
            'pattern' => '/\b(Ltd|Limited|Inc|Corp|Corporation|LLC|LLP|PLC|Co\.|Company|Solutions|Group|Associates|Services|Systems|Engineering|Consulting|Design|Construction|Manufacturing)\b/i',
            'confidence' => 90,
            'min_matches' => 1,
            'description' => 'Business entity suffixes and keywords'
        ],
        
        // Personal name indicators (common titles)
        'personal_title' => [
            'pattern' => '/^(Mr\.?|Mrs\.?|Ms\.?|Miss|Dr\.?|Prof\.?|Sir|Dame)\s+/i',
            'confidence' => 85,
            'min_matches' => 1,
            'description' => 'Personal titles indicating individual names'
        ],
        
        // Product/Software version patterns
        'software_version' => [
            'pattern' => '/\b(v\d+|\d+\.\d+|version\s+\d+|AutoCAD|Revit|Inventor|Maya|3ds Max|Fusion)\b/i',
            'confidence' => 75,
            'min_matches' => 2,
            'description' => 'Software names and version indicators'
        ],
        
        // Subscription/License identifiers
        'subscription_id' => [
            'pattern' => '/^(SUB|SUBSCRIPTION|LIC|LICENSE|REF)-?[A-Z0-9]+$/i',
            'confidence' => 90,
            'min_matches' => 1,
            'description' => 'Subscription or license ID patterns'
        ],
        
        // Reference numbers
        'reference_number' => [
            'pattern' => '/^(REF|ORDER|ORD|INV|QUOTE)-?[A-Z0-9]+$/i',
            'confidence' => 85,
            'min_matches' => 1,
            'description' => 'Reference number patterns'
        ],
        
        // Status values
        'status_values' => [
            'pattern' => '/^(Active|Inactive|Pending|Expired|Cancelled|Suspended|Trial|Complete|Processing|Draft)$/i',
            'confidence' => 80,
            'min_matches' => 2,
            'description' => 'Common status values'
        ],
        
        // UK postal codes (for address validation)
        'uk_postcode' => [
            'pattern' => '/^[A-Z]{1,2}[0-9R][0-9A-Z]?\s?[0-9][A-Z]{2}$/i',
            'confidence' => 95,
            'min_matches' => 1,
            'description' => 'UK postal code format'
        ],
        
        // Phone numbers (UK format)
        'uk_phone' => [
            'pattern' => '/^(\+44\s?|0)(\d{2,4}\s?\d{3,4}\s?\d{3,4}|\d{10,11})$/',
            'confidence' => 90,
            'min_matches' => 1,
            'description' => 'UK phone number format'
        ],
        
        // Numeric IDs
        'numeric_id' => [
            'pattern' => '/^\d{6,}$/',
            'confidence' => 60,
            'min_matches' => 3,
            'description' => 'Numeric identifier (6+ digits)'
        ]
    ],

    /**
     * Keyword Lists for Content Analysis
     */
    'keyword_lists' => [
        
        // Business/Company indicators
        'business_keywords' => [
            'keywords' => [
                'Limited', 'Ltd', 'Inc', 'Corp', 'Corporation', 'LLC', 'LLP', 'PLC',
                'Company', 'Co.', 'Solutions', 'Group', 'Associates', 'Services',
                'Systems', 'Engineering', 'Consulting', 'Design', 'Construction',
                'Manufacturing', 'Industries', 'Holdings', 'Enterprises', 'Partners',
                'Technologies', 'Innovations', 'Developments', 'Contractors',
                'Architects', 'Builders', 'Fabrication', 'Installations'
            ],
            'confidence' => 85,
            'min_matches' => 1,
            'type' => 'company_name'
        ],
        
        // Common first names (UK/International)
        'first_names' => [
            'keywords' => [
                'James', 'John', 'Robert', 'Michael', 'William', 'David', 'Richard',
                'Joseph', 'Thomas', 'Christopher', 'Charles', 'Daniel', 'Matthew',
                'Anthony', 'Mark', 'Donald', 'Steven', 'Paul', 'Andrew', 'Joshua',
                'Mary', 'Patricia', 'Jennifer', 'Linda', 'Elizabeth', 'Barbara',
                'Susan', 'Jessica', 'Sarah', 'Karen', 'Nancy', 'Lisa', 'Betty',
                'Helen', 'Sandra', 'Donna', 'Carol', 'Ruth', 'Sharon', 'Michelle',
                'Aurangzaib', 'Wojciech', 'Dominik', 'Mufajel', 'Dilesh', 'Piotr'
            ],
            'confidence' => 70,
            'min_matches' => 2,
            'type' => 'first_name'
        ],
        
        // Software/Product indicators
        'software_keywords' => [
            'keywords' => [
                'AutoCAD', 'Revit', 'Inventor', 'Maya', '3ds Max', 'Fusion',
                'Architecture', 'Engineering', 'Construction', 'Manufacturing',
                'Design', 'Modeling', 'CAD', 'BIM', 'Simulation', 'Rendering',
                'including specialized toolsets', 'Product Design', 'AEC',
                'PDM', 'toolsets', 'subscription', 'license', 'software'
            ],
            'confidence' => 80,
            'min_matches' => 1,
            'type' => 'product_name'
        ],
        
        // Industry/Sector keywords
        'industry_keywords' => [
            'keywords' => [
                'Architecture', 'Engineering', 'Construction', 'Manufacturing',
                'Automotive', 'Aerospace', 'Civil Infrastructure', 'Buildings',
                'Process Manufacturing', 'Industrial Machinery', 'Film & TV',
                'Government', 'Education', 'Healthcare', 'Utilities', 'Telecom'
            ],
            'confidence' => 60,
            'min_matches' => 1,
            'type' => 'industry_sector'
        ],
        
        // Geographic indicators (UK specific)
        'uk_locations' => [
            'keywords' => [
                'London', 'Manchester', 'Birmingham', 'Leeds', 'Glasgow', 'Liverpool',
                'Newcastle', 'Sheffield', 'Bristol', 'Cardiff', 'Edinburgh', 'Belfast',
                'United Kingdom', 'England', 'Scotland', 'Wales', 'Northern Ireland',
                'UK', 'GB', 'Great Britain'
            ],
            'confidence' => 50,
            'min_matches' => 1,
            'type' => 'location'
        ]
    ],

    /**
     * Data Sampling Configuration
     */
    'sampling_config' => [
        'sample_size' => 20,           // Number of records to sample
        'min_sample_size' => 5,       // Minimum records needed for analysis
        'max_sample_size' => 50,      // Maximum records to sample
        'sample_distribution' => [    // How to distribute samples across dataset
            'start' => 0.3,           // 30% from beginning
            'middle' => 0.4,          // 40% from middle
            'end' => 0.3              // 30% from end
        ],
        'null_threshold' => 0.8,      // If >80% of samples are null, skip analysis
        'confidence_threshold' => 50  // Minimum confidence to suggest rename
    ],

    /**
     * Analysis Weights for Multi-factor Scoring
     */
    'analysis_weights' => [
        'column_name_analysis' => 0.30,    // 30% weight
        'data_pattern_analysis' => 0.50,   // 50% weight  
        'context_analysis' => 0.20         // 20% weight
    ],

    /**
     * Decision Thresholds
     */
    'decision_thresholds' => [
        'high_confidence' => 50,      // >50% = Use suggestion
        'medium_confidence' => 30,    // 30-50% = Use highest scoring option
        'low_confidence' => 30,       // <30% = Keep original or add prefix
        'prefix_threshold' => 20      // <20% = Add source context prefix
    ],

    /**
     * Common Data Patterns by Column Type
     */
    'column_type_patterns' => [
        'company_name' => [
            'typical_length' => [10, 100],
            'contains_keywords' => 'business_keywords',
            'regex_patterns' => ['business_entity'],
            'excludes_patterns' => ['email', 'personal_title']
        ],
        
        'first_name' => [
            'typical_length' => [2, 30],
            'contains_keywords' => 'first_names',
            'regex_patterns' => ['personal_title'],
            'excludes_patterns' => ['email', 'business_entity']
        ],
        
        'email' => [
            'typical_length' => [5, 100],
            'regex_patterns' => ['email'],
            'excludes_patterns' => ['business_entity', 'personal_title']
        ],
        
        'product_name' => [
            'typical_length' => [5, 200],
            'contains_keywords' => 'software_keywords',
            'regex_patterns' => ['software_version'],
            'excludes_patterns' => ['email']
        ],
        
        'subscription_id' => [
            'typical_length' => [5, 50],
            'regex_patterns' => ['subscription_id', 'numeric_id'],
            'excludes_patterns' => ['email', 'business_entity']
        ],
        
        'status' => [
            'typical_length' => [3, 20],
            'regex_patterns' => ['status_values'],
            'limited_values' => true,
            'max_unique_values' => 10
        ]
    ]
];
