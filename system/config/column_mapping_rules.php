<?php

/**
 * Column Mapping Rules Configuration
 * 
 * This file contains the rules and patterns used by the column analyzer
 * to intelligently rename ambiguous column names based on data analysis.
 */

return [
    
    /**
     * Standard Column Name Mappings
     * 
     * These are the preferred column names based on existing autobooks patterns
     */
    'standard_column_names' => [
        // Company/Organization Names
        'company_name' => [
            'label' => 'Company Name',
            'description' => 'Primary company or organization name',
            'examples' => ['TCS CAD & BIM Solutions Limited', 'BUILDING STRUCTURES ASSOCIATES']
        ],
        'organization_name' => [
            'label' => 'Organization Name', 
            'description' => 'Alternative to company name for non-commercial entities',
            'examples' => ['NICTS', 'Lancashire CCC']
        ],
        
        // Personal Names
        'first_name' => [
            'label' => 'First Name',
            'description' => 'Individual first name',
            'examples' => ['Aurangzaib', 'David', '<PERSON>']
        ],
        'last_name' => [
            'label' => 'Last Name', 
            'description' => 'Individual surname',
            'examples' => ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>']
        ],
        'full_name' => [
            'label' => 'Full Name',
            'description' => 'Complete personal name',
            'examples' => ['Aurangzaib Mahmood', '<PERSON> W<PERSON>worth']
        ],
        'contact_name' => [
            'label' => 'Contact Name',
            'description' => 'Primary contact person name',
            'examples' => ['Aurangzaib Mahmood', 'David Wadsworth']
        ],
        
        // Product/Service Names
        'product_name' => [
            'label' => 'Product Name',
            'description' => 'Product or service name',
            'examples' => ['AutoCAD LT', 'Architecture Engineering & Construction']
        ],
        'offering_name' => [
            'label' => 'Offering Name',
            'description' => 'Service offering or product offering name',
            'examples' => ['AutoCAD LT - including specialized toolsets']
        ],
        'software_name' => [
            'label' => 'Software Name',
            'description' => 'Software product name',
            'examples' => ['AutoCAD', 'Revit', 'Inventor']
        ],
        
        // Email and Contact
        'email' => [
            'label' => 'Email Address',
            'description' => 'Email address',
            'examples' => ['<EMAIL>', '<EMAIL>']
        ],
        'primary_email' => [
            'label' => 'Primary Email',
            'description' => 'Primary contact email address',
            'examples' => ['<EMAIL>']
        ],
        'admin_email' => [
            'label' => 'Admin Email',
            'description' => 'Administrative contact email',
            'examples' => ['<EMAIL>']
        ],
        
        // Identifiers
        'subscription_id' => [
            'label' => 'Subscription ID',
            'description' => 'Unique subscription identifier',
            'examples' => ['SUB123456', 'AUTODESK-12345']
        ],
        'customer_id' => [
            'label' => 'Customer ID', 
            'description' => 'Unique customer identifier',
            'examples' => ['CUST001', '**********']
        ],
        'reference_number' => [
            'label' => 'Reference Number',
            'description' => 'Reference or tracking number',
            'examples' => ['REF-2024-001', 'ORD123456']
        ],
        
        // Status and Categories
        'status' => [
            'label' => 'Status',
            'description' => 'Current status or state',
            'examples' => ['Active', 'Inactive', 'Pending']
        ],
        'account_type' => [
            'label' => 'Account Type',
            'description' => 'Type or category of account',
            'examples' => ['Reseller', 'End Customer', 'Government']
        ],
        'license_type' => [
            'label' => 'License Type',
            'description' => 'Type of software license',
            'examples' => ['Commercial', 'Educational', 'Trial']
        ]
    ],

    /**
     * Column Name Pattern Matching Rules
     * 
     * These rules help identify what type of data a column contains
     * based on its name, with confidence scores.
     */
    'column_name_patterns' => [
        // Exact matches (high confidence)
        'exact_matches' => [
            'company_name' => ['type' => 'company_name', 'confidence' => 95],
            'organization_name' => ['type' => 'organization_name', 'confidence' => 95],
            'first_name' => ['type' => 'first_name', 'confidence' => 95],
            'last_name' => ['type' => 'last_name', 'confidence' => 95],
            'full_name' => ['type' => 'full_name', 'confidence' => 95],
            'email' => ['type' => 'email', 'confidence' => 95],
            'product_name' => ['type' => 'product_name', 'confidence' => 95],
            'offering_name' => ['type' => 'offering_name', 'confidence' => 95],
            'subscription_id' => ['type' => 'subscription_id', 'confidence' => 95],
            'customer_id' => ['type' => 'customer_id', 'confidence' => 95],
            'reference_number' => ['type' => 'reference_number', 'confidence' => 95],
            'status' => ['type' => 'status', 'confidence' => 95]
        ],
        
        // Partial matches (medium confidence)
        'partial_matches' => [
            // Company variations
            'comp_name' => ['type' => 'company_name', 'confidence' => 75],
            'company' => ['type' => 'company_name', 'confidence' => 70],
            'org_name' => ['type' => 'organization_name', 'confidence' => 75],
            'organization' => ['type' => 'organization_name', 'confidence' => 70],
            
            // Name variations
            'fname' => ['type' => 'first_name', 'confidence' => 75],
            'firstname' => ['type' => 'first_name', 'confidence' => 80],
            'lname' => ['type' => 'last_name', 'confidence' => 75],
            'lastname' => ['type' => 'last_name', 'confidence' => 80],
            'fullname' => ['type' => 'full_name', 'confidence' => 80],
            'contact_name' => ['type' => 'contact_name', 'confidence' => 80],
            'contact' => ['type' => 'contact_name', 'confidence' => 65],
            
            // Product variations
            'product' => ['type' => 'product_name', 'confidence' => 70],
            'offering' => ['type' => 'offering_name', 'confidence' => 70],
            'software' => ['type' => 'software_name', 'confidence' => 75],
            
            // Email variations
            'email_address' => ['type' => 'email', 'confidence' => 90],
            'mail' => ['type' => 'email', 'confidence' => 65],
            'primary_email' => ['type' => 'primary_email', 'confidence' => 85],
            'admin_email' => ['type' => 'admin_email', 'confidence' => 85],
            
            // ID variations
            'sub_id' => ['type' => 'subscription_id', 'confidence' => 75],
            'subscription' => ['type' => 'subscription_id', 'confidence' => 60],
            'cust_id' => ['type' => 'customer_id', 'confidence' => 75],
            'customer' => ['type' => 'customer_id', 'confidence' => 60],
            'ref_num' => ['type' => 'reference_number', 'confidence' => 75],
            'reference' => ['type' => 'reference_number', 'confidence' => 65],
            'ref' => ['type' => 'reference_number', 'confidence' => 60]
        ],
        
        // Generic patterns (low confidence - require data analysis)
        'generic_patterns' => [
            'name' => ['requires_analysis' => true, 'confidence' => 30],
            'title' => ['requires_analysis' => true, 'confidence' => 30],
            'description' => ['requires_analysis' => true, 'confidence' => 30],
            'type' => ['requires_analysis' => true, 'confidence' => 30],
            'id' => ['requires_analysis' => true, 'confidence' => 30]
        ]
    ],

    /**
     * Context Analysis Rules
     * 
     * These rules help determine column types based on other columns
     * present in the same table.
     */
    'context_rules' => [
        // If these columns exist, adjust confidence for 'name' column
        'name_disambiguation' => [
            'company_name_exists' => [
                'condition' => ['company_name', 'organization_name'],
                'effect' => ['name' => ['type' => 'contact_name', 'confidence_boost' => 20]]
            ],
            'first_last_exists' => [
                'condition' => ['first_name', 'last_name'],
                'effect' => ['name' => ['type' => 'full_name', 'confidence_boost' => 25]]
            ],
            'email_exists' => [
                'condition' => ['email', 'primary_email'],
                'effect' => ['name' => ['type' => 'contact_name', 'confidence_boost' => 15]]
            ]
        ],
        
        // Table type detection based on column patterns
        'table_type_detection' => [
            'customer_table' => [
                'indicators' => ['account_csn', 'primary_admin_email', 'address1', 'city', 'country'],
                'threshold' => 3,
                'effects' => [
                    'name' => ['type' => 'company_name', 'confidence_boost' => 30]
                ]
            ],
            'subscription_table' => [
                'indicators' => ['subscription_id', 'offering_name', 'start_date', 'end_date'],
                'threshold' => 2,
                'effects' => [
                    'name' => ['type' => 'offering_name', 'confidence_boost' => 25]
                ]
            ],
            'user_table' => [
                'indicators' => ['first_name', 'last_name', 'email', 'password'],
                'threshold' => 2,
                'effects' => [
                    'name' => ['type' => 'full_name', 'confidence_boost' => 25]
                ]
            ],
            'product_sales_table' => [
                'indicators' => ['product name', 'quantity', 'end date', 'contract', 'serial number'],
                'threshold' => 2,
                'effects' => [
                    'name' => ['type' => 'company_name', 'confidence_boost' => 35]
                ]
            ]
        ]
    ],

    /**
     * Data Source Context Prefixes
     * 
     * When ambiguity remains, prefix with data source context
     */
    'source_prefixes' => [
        'customer_' => ['customer', 'account', 'client'],
        'subscription_' => ['subscription', 'sub', 'license'],
        'product_' => ['product', 'offering', 'software'],
        'contact_' => ['contact', 'person', 'user'],
        'admin_' => ['admin', 'administrator', 'manager']
    ]
];
