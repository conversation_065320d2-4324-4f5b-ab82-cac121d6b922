
<div class="p-5">
  <!-- wrapper -->
  <!-- Main content -->

  <!-- /.card-header -->
  <div id="card-body">
    <x-forms-button
      id="Get_Products"
      label="Get Products"
      hx-post="{{ APP_ROOT }}/api/system/system/update_products"
      hx-target="#output_card_card_body"
      hx-swap="innerHtml"
    />

    <x-forms-button
      id="Get_prices"
      label="Update Prices"
      hx-post="{{ APP_ROOT }}/api/system/system/update_prices"
      hx-target="#output_card_card_body"
      hx-swap="innerHtml"
    />

    <x-forms-button
      label="Get Subscriptions for api"
      hx-post="{{ APP_ROOT }}/api/system/system/subscriptions_get_from_api"
      hx-target="#output_card_card_body"
      hx-swap="innerHtml"
    />
    <x-forms-button
      label="Get Quote JSON from api"
      hx-post="{{ APP_ROOT }}/api/system/system/quotes_get_from_api"
      hx-target="#output_card_card_body"
      hx-swap="innerHtml"
    />

    <x-forms-button
      label="Get Promos"
      hx-target="#output_card_body"
      hx-swap="innerHtml"
      hx-post="{{ APP_ROOT }}/api/system/system/autodesk/get_promos_from_api"
      :hx-vals='{{ json_encode(["action" => "autodesk_get_promos_csv"]) }}'
    />

    <x-forms-form
      id="autodesk_search_customers_form"
      class="form-control"
      hx-post="{{ APP_ROOT }}/api/system/system/api_h.php"
      hx-swap="innerHtml"
      hx-target="#output_card_body"
      hx-vals='{{ json_encode(["action" => "autodesk_search_customers"]) }}'
    >

    <h3>Search Customers</h3>
    <x-forms-input
      type="text"
      name="name"
      id="name_input"
      placeholder="name"
      class="form-control"
    />

    <x-forms-input
      type="text"
      name="countryCode"
      id="countryCode_input"
      placeholder="countryCode"
      class="form-control"
    />

    <x-forms-input
      type="text"
      name="contactEmail"
      id="email_input"
      placeholder="Email"
      class="form-control"
    />

    <x-forms-input
      type="text"
      name="subscriptionId"
      id="subscriptionId_input"
      placeholder="subscriptionId"
      class="form-control"
      required
    />

    <x-forms-input
      type="text"
      name="endpoint"
      id="endpoint_input"
      placeholder="endpoint"
      class="form-control"
      required
    />
    <x-forms-button
      label="Submit"
      class="btn btn-primary"
    />
    </x-forms-form><br><br>


    <h3>Get opportunity</h3>

    <x-forms-form
      id="autodesk_get_opportunity_form"
      hx-post="{{ APP_ROOT }}/api_h.php"
      hx-swap="innerHtml"
      hx-target="#output_card_body"
      hx-vals='{{ json_encode(["action" => "autodesk_get_opportunity"]) }}'
    >
    <x-forms-input
      type="text"
      name="endCustomerCsn"
      id="endCustomerCsn_input"
      placeholder="endCustomerCsn"
      class="form-control"
    />

    <x-forms-input
      type="text"
      name="opportunityNumber"
      id="opportunityNumber_input"
      placeholder="opportunityNumber"
      class="form-control"
    />

    <x-forms-button
      label="Submit"
      class="btn btn-primary"
    />

    </x-forms-form><br><br>
    <x-forms-form
      id="autodesk_raw_api_call_form"
      hx-post="{{ APP_ROOT }}/api/system/system/autodesk_send_raw_api_call"
      hx-swap="innerHtml"
      hx-target="#output_card_card_body"
      hx-vals='{{ json_encode(["action" => "autodesk_raw_api_call"]) }}'
    >
    <x-forms-input
      type="text"
      name="requestType"
      id="requestType_input"
      placeholder="requestType (POST, GET)"
      class="form-control"
      required
    />
    <x-forms-input
      type="text"
      name="endpoint"
      id="endpoint_input"
      placeholder="endpoint"
      class="form-control"
      required
    />
    <x-forms-input
      type="text"
      name="downloadFilepath"
      id="downloadFilepath_input"
      placeholder="downloadFilepath"
      class="form-control"
      required
    />

    <x-forms-textarea
      name="params"
      id="params_textarea"
      placeholder="params"
      label="params (php assoc array)"
      class="form-control"
    />

    <x-forms-textarea
      name="json"
      id="json"
      placeholder="json here"
      label="input (json)"
      class="form-control"
    />

    <button type="submit">Send Raw</button>
    </x-forms-form><br><br>


    <x-layout-card
      id="output_card"
      label="Output"
    >
      <div id="output_card_card_body"></div>
    </x-layout-card>
  </div>
</div>
