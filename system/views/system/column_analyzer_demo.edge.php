@props([
    'title' => 'Column Analyzer Demo',
    'description' => 'Intelligent Column Naming System Demonstration'
])

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">🧠 {{ $title }}</h3>
                    <p class="card-text">{{ $description }}</p>
                </div>
                <div class="card-body">
                    
                    <!-- Navigation Tabs -->
                    <ul class="nav nav-tabs" id="demoTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="demo-tab" data-bs-toggle="tab" data-bs-target="#demo" type="button" role="tab">
                                📊 Live Demo
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="analysis-tab" data-bs-toggle="tab" data-bs-target="#analysis" type="button" role="tab">
                                🔍 Table Analysis
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="column-tab" data-bs-toggle="tab" data-bs-target="#column" type="button" role="tab">
                                📝 Column Analysis
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="config-tab" data-bs-toggle="tab" data-bs-target="#config" type="button" role="tab">
                                ⚙️ Configuration
                            </button>
                        </li>
                    </ul>
                    
                    <div class="tab-content" id="demoTabsContent">
                        
                        <!-- Live Demo Tab -->
                        <div class="tab-pane fade show active" id="demo" role="tabpanel">
                            <div class="mt-3">
                                <h5>Interactive Data Table Demo</h5>
                                <p>Select a table to see the intelligent column naming system in action:</p>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="demoTableSelect" class="form-label">Select Table:</label>
                                        <select class="form-select" id="demoTableSelect">
                                            <option value="">Loading tables...</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 d-flex align-items-end">
                                        <button class="btn btn-primary" onclick="loadDemoTable()">
                                            🚀 Generate Demo Table
                                        </button>
                                    </div>
                                </div>
                                
                                <div id="demoTableContainer">
                                    <div class="alert alert-info">
                                        Select a table above to see the intelligent column naming system in action.
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Table Analysis Tab -->
                        <div class="tab-pane fade" id="analysis" role="tabpanel">
                            <div class="mt-3">
                                <h5>Table Column Analysis</h5>
                                <p>Analyze all columns in a table to see suggested improvements:</p>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="analysisTableSelect" class="form-label">Select Table:</label>
                                        <select class="form-select" id="analysisTableSelect">
                                            <option value="">Loading tables...</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 d-flex align-items-end">
                                        <button class="btn btn-success" onclick="analyzeTable()">
                                            🔍 Analyze Table
                                        </button>
                                    </div>
                                </div>
                                
                                <div id="analysisResults">
                                    <div class="alert alert-info">
                                        Select a table above to analyze its columns.
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Column Analysis Tab -->
                        <div class="tab-pane fade" id="column" role="tabpanel">
                            <div class="mt-3">
                                <h5>Individual Column Analysis</h5>
                                <p>Analyze a specific column to see detailed breakdown:</p>
                                
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="columnTableSelect" class="form-label">Select Table:</label>
                                        <select class="form-select" id="columnTableSelect" onchange="loadTableColumns()">
                                            <option value="">Loading tables...</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="columnSelect" class="form-label">Select Column:</label>
                                        <select class="form-select" id="columnSelect">
                                            <option value="">Select table first</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 d-flex align-items-end">
                                        <button class="btn btn-info" onclick="analyzeColumn()">
                                            📝 Analyze Column
                                        </button>
                                    </div>
                                </div>
                                
                                <div id="columnResults">
                                    <div class="alert alert-info">
                                        Select a table and column above to see detailed analysis.
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Configuration Tab -->
                        <div class="tab-pane fade" id="config" role="tabpanel">
                            <div class="mt-3">
                                <h5>System Configuration</h5>
                                <p>View the current configuration and patterns used by the analyzer:</p>
                                
                                <button class="btn btn-secondary mb-3" onclick="loadConfiguration()">
                                    ⚙️ Load Configuration
                                </button>
                                
                                <div id="configResults">
                                    <div class="alert alert-info">
                                        Click the button above to load the current system configuration.
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Load available tables on page load
document.addEventListener('DOMContentLoaded', function() {
    loadTables();
});

// Load available tables
function loadTables() {
    fetch('/api/system/column_analyzer_demo/get_tables')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const selects = ['demoTableSelect', 'analysisTableSelect', 'columnTableSelect'];
                selects.forEach(selectId => {
                    const select = document.getElementById(selectId);
                    select.innerHTML = '<option value="">Select a table...</option>';
                    data.tables.forEach(table => {
                        select.innerHTML += `<option value="${table}">${table}</option>`;
                    });
                });
            } else {
                console.error('Failed to load tables:', data.error);
            }
        })
        .catch(error => {
            console.error('Error loading tables:', error);
        });
}

// Load demo table
function loadDemoTable() {
    const tableName = document.getElementById('demoTableSelect').value;
    if (!tableName) {
        alert('Please select a table first');
        return;
    }
    
    const container = document.getElementById('demoTableContainer');
    container.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>Generating demo table...</p></div>';
    
    fetch(`/api/system/column_analyzer_demo/demo_data_table?table_name=${encodeURIComponent(tableName)}`)
        .then(response => response.text())
        .then(html => {
            container.innerHTML = html;
        })
        .catch(error => {
            container.innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
        });
}

// Analyze table
function analyzeTable() {
    const tableName = document.getElementById('analysisTableSelect').value;
    if (!tableName) {
        alert('Please select a table first');
        return;
    }
    
    const container = document.getElementById('analysisResults');
    container.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>Analyzing table...</p></div>';
    
    fetch(`/api/system/column_analyzer_demo/analyze_table?table_name=${encodeURIComponent(tableName)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = `
                    <div class="alert alert-success">
                        <h6>Analysis Complete for: ${data.table_name}</h6>
                        <p>Total columns: ${data.summary.total_columns} | 
                           Analyzed: ${data.summary.analyzed_columns} | 
                           Suggestions: ${data.summary.suggestions_count}</p>
                    </div>
                `;
                
                if (data.suggestions.length > 0) {
                    html += `
                        <h6>💡 Suggested Improvements:</h6>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Original Name</th>
                                        <th>Suggested Name</th>
                                        <th>Confidence</th>
                                        <th>Reasoning</th>
                                    </tr>
                                </thead>
                                <tbody>
                    `;
                    
                    data.suggestions.forEach(suggestion => {
                        html += `
                            <tr>
                                <td><code>${suggestion.original_name}</code></td>
                                <td><strong>${suggestion.suggested_name}</strong></td>
                                <td><span class="badge bg-${suggestion.confidence >= 70 ? 'success' : suggestion.confidence >= 50 ? 'warning' : 'secondary'}">${suggestion.confidence}%</span></td>
                                <td>${suggestion.reasoning}</td>
                            </tr>
                        `;
                    });
                    
                    html += '</tbody></table></div>';
                } else {
                    html += '<div class="alert alert-info">No improvements suggested for this table.</div>';
                }
                
                container.innerHTML = html;
            } else {
                container.innerHTML = `<div class="alert alert-danger">Error: ${data.error}</div>`;
            }
        })
        .catch(error => {
            container.innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
        });
}

// Load table columns
function loadTableColumns() {
    const tableName = document.getElementById('columnTableSelect').value;
    const columnSelect = document.getElementById('columnSelect');
    
    if (!tableName) {
        columnSelect.innerHTML = '<option value="">Select table first</option>';
        return;
    }
    
    columnSelect.innerHTML = '<option value="">Loading columns...</option>';
    
    // This is a simplified approach - in a real implementation you'd have an API endpoint for this
    fetch(`/api/system/column_analyzer_demo/analyze_table?table_name=${encodeURIComponent(tableName)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                columnSelect.innerHTML = '<option value="">Select a column...</option>';
                data.all_results.forEach(result => {
                    columnSelect.innerHTML += `<option value="${result.column_name}">${result.column_name}</option>`;
                });
            }
        })
        .catch(error => {
            columnSelect.innerHTML = '<option value="">Error loading columns</option>';
        });
}

// Analyze column
function analyzeColumn() {
    const tableName = document.getElementById('columnTableSelect').value;
    const columnName = document.getElementById('columnSelect').value;
    
    if (!tableName || !columnName) {
        alert('Please select both table and column');
        return;
    }
    
    const container = document.getElementById('columnResults');
    container.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>Analyzing column...</p></div>';
    
    fetch(`/api/system/column_analyzer_demo/analyze_column?table_name=${encodeURIComponent(tableName)}&column_name=${encodeURIComponent(columnName)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = `
                    <div class="alert alert-success">
                        <h6>Analysis for: ${data.table_name}.${data.column_name}</h6>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>📊 Analysis Results</h6>
                            <table class="table table-sm">
                                <tr><td><strong>Original Name:</strong></td><td><code>${data.analysis.original_name}</code></td></tr>
                                <tr><td><strong>Suggested Name:</strong></td><td><strong>${data.analysis.suggested_name}</strong></td></tr>
                                <tr><td><strong>Confidence:</strong></td><td><span class="badge bg-${data.analysis.confidence >= 70 ? 'success' : data.analysis.confidence >= 50 ? 'warning' : 'secondary'}">${data.analysis.confidence}%</span></td></tr>
                                <tr><td><strong>Reasoning:</strong></td><td>${data.analysis.reasoning}</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>📈 Column Statistics</h6>
                            <table class="table table-sm">
                                <tr><td><strong>Total Rows:</strong></td><td>${data.statistics.total_rows}</td></tr>
                                <tr><td><strong>Non-null Rows:</strong></td><td>${data.statistics.non_null_rows}</td></tr>
                                <tr><td><strong>Null Percentage:</strong></td><td>${data.statistics.null_percentage}%</td></tr>
                                <tr><td><strong>Unique Values:</strong></td><td>${data.statistics.unique_values}</td></tr>
                                <tr><td><strong>Avg Length:</strong></td><td>${data.statistics.avg_length}</td></tr>
                            </table>
                        </div>
                    </div>
                `;
                
                container.innerHTML = html;
            } else {
                container.innerHTML = `<div class="alert alert-danger">Error: ${data.error}</div>`;
            }
        })
        .catch(error => {
            container.innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
        });
}

// Load configuration
function loadConfiguration() {
    const container = document.getElementById('configResults');
    container.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>Loading configuration...</p></div>';
    
    fetch('/api/system/column_analyzer_demo/show_config')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const config = data.configuration;
                let html = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>📝 Standard Column Names</h6>
                            <div class="mb-3">
                                ${config.standard_column_names.map(name => `<span class="badge bg-primary me-1">${name}</span>`).join('')}
                            </div>
                            
                            <h6>🎯 Exact Matches</h6>
                            <div class="mb-3">
                                ${config.exact_matches.map(name => `<span class="badge bg-success me-1">${name}</span>`).join('')}
                            </div>
                            
                            <h6>🔍 Partial Matches</h6>
                            <div class="mb-3">
                                ${config.partial_matches.map(name => `<span class="badge bg-warning me-1">${name}</span>`).join('')}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>⚖️ Analysis Weights</h6>
                            <ul class="list-group mb-3">
                                <li class="list-group-item d-flex justify-content-between">
                                    Column Name Analysis <span class="badge bg-primary">${(config.analysis_weights.column_name_analysis * 100)}%</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    Data Pattern Analysis <span class="badge bg-primary">${(config.analysis_weights.data_pattern_analysis * 100)}%</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    Context Analysis <span class="badge bg-primary">${(config.analysis_weights.context_analysis * 100)}%</span>
                                </li>
                            </ul>
                            
                            <h6>🎚️ Decision Thresholds</h6>
                            <ul class="list-group">
                                <li class="list-group-item d-flex justify-content-between">
                                    High Confidence <span class="badge bg-success">${config.decision_thresholds.high_confidence}%</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    Medium Confidence <span class="badge bg-warning">${config.decision_thresholds.medium_confidence}%</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between">
                                    Prefix Threshold <span class="badge bg-secondary">${config.decision_thresholds.prefix_threshold}%</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                `;
                
                container.innerHTML = html;
            } else {
                container.innerHTML = `<div class="alert alert-danger">Error: ${data.error}</div>`;
            }
        })
        .catch(error => {
            container.innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
        });
}
</script>
