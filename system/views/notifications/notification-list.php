<?php
// This template displays the list of notifications in the dropdown
?>
<div class="notification-list py-2" id="notification-list">
    <?php if (empty($notification_list)): ?>
        <div class="px-4 py-3 text-sm text-gray-500 text-center">
            No notifications
        </div>
    <?php else: ?>
        <div class="max-h-96 overflow-y-auto">
            <?php foreach ($notification_list as $notification): ?>
                <div class="notification-item px-4 py-2 hover:bg-gray-50 <?= $notification['is_read'] ? 'bg-white' : 'bg-blue-50' ?>"
                     hx-post="<?= APP_ROOT ?>api/notifications/mark_as_read"
                     hx-vals='{"notification_id": <?= $notification['id'] ?>}'
                     hx-swap="none"
                     hx-trigger="click"
                     x-data="{ read: <?= $notification['is_read'] ? 'true' : 'false' ?> }"
                     @click="read = true; $el.classList.remove('bg-blue-50'); $el.classList.add('bg-white')">

                    <div class="flex items-start">
                        <div class="flex-shrink-0 pt-0.5">
                            <?php if (!$notification['is_read']): ?>
                                <span class="h-2 w-2 rounded-full bg-blue-600"></span>
                            <?php endif; ?>
                        </div>
                        <div class="ml-3 w-0 flex-1">
                            <p class="text-sm font-medium text-gray-900"><?= htmlspecialchars($notification['title']) ?></p>
                            <p class="mt-1 text-sm text-gray-500"><?= htmlspecialchars($notification['message']) ?></p>
                            <?php if ($notification['link']): ?>
                                <a href="<?= $notification['link'] ?>" class="mt-1 text-sm text-blue-600 hover:text-blue-800">View details</a>
                            <?php endif; ?>
                            <p class="mt-1 text-xs text-gray-400"><?= date('M j, Y g:i A', strtotime($notification['created_at'])) ?></p>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="border-t border-gray-200 py-2 px-4">
            <button class="w-full text-center text-sm text-blue-600 hover:text-blue-800"
                    hx-post="<?= APP_ROOT ?>api/notifications/mark_all_as_read"
                    hx-swap="none"
                    hx-trigger="click"
                    @click="document.querySelectorAll('.notification-item').forEach(el => { el.classList.remove('bg-blue-50'); el.classList.add('bg-white'); })">
                Mark all as read
            </button>
        </div>
    <?php endif; ?>
</div>
