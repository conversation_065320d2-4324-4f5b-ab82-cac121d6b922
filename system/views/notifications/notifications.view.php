<?php
use system\notification;
use system\notifications;

$notifications_manager = new notifications();
$notification_list = $notifications_manager->getForCurrentUser(false, 50); // Get more notifications for the full page view
$preferences = $notifications_manager->getPreferences();
?>

<div class="px-4 sm:px-6 lg:px-8 py-8">
    <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
            <h1 class="text-xl font-semibold text-gray-900">Notifications</h1>
            <p class="mt-2 text-sm text-gray-700">
                View and manage your notifications and preferences.
            </p>
        </div>
    </div>

    <div class="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-3">
        <!-- Notifications List -->
        <div class="lg:col-span-2">
            <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Notifications</h3>
                    <button class="text-sm text-blue-600 hover:text-blue-800"
                            hx-post="<?= APP_ROOT ?>api/notifications/mark_all_as_read"
                            hx-swap="none"
                            hx-trigger="click"
                            @click="document.querySelectorAll('.notification-item').forEach(el => { el.classList.remove('bg-blue-50'); el.classList.add('bg-white'); })">
                        Mark all as read
                    </button>
                </div>
                <ul role="list" class="divide-y divide-gray-200">
                    <?php if (empty($notification_list)): ?>
                        <li class="px-4 py-4 sm:px-6">
                            <div class="text-sm text-gray-500 text-center">
                                No notifications
                            </div>
                        </li>
                    <?php else: ?>
                        <?php foreach ($notification_list as $notification): ?>
                            <li class="notification-item px-4 py-4 sm:px-6 <?= $notification['is_read'] ? 'bg-white' : 'bg-blue-50' ?>"
                                hx-post="<?= APP_ROOT ?>api/notifications/mark_as_read"
                                hx-vals='{"notification_id": <?= $notification['id'] ?>}'
                                hx-swap="none"
                                hx-trigger="click"
                                x-data="{ read: <?= $notification['is_read'] ? 'true' : 'false' ?> }"
                                @click="read = true; $el.classList.remove('bg-blue-50'); $el.classList.add('bg-white')">

                                <div class="flex items-start">
                                    <div class="flex-shrink-0 pt-0.5">
                                        <?php if (!$notification['is_read']): ?>
                                            <span class="h-2 w-2 rounded-full bg-blue-600"></span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="ml-3 w-0 flex-1">
                                        <p class="text-sm font-medium text-gray-900"><?= htmlspecialchars($notification['title']) ?></p>
                                        <p class="mt-1 text-sm text-gray-500"><?= htmlspecialchars($notification['message']) ?></p>
                                        <?php if ($notification['link']): ?>
                                            <a href="<?= $notification['link'] ?>" class="mt-1 text-sm text-blue-600 hover:text-blue-800">View details</a>
                                        <?php endif; ?>
                                        <p class="mt-1 text-xs text-gray-400"><?= date('M j, Y g:i A', strtotime($notification['created_at'])) ?></p>
                                    </div>
                                </div>
                            </li>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </ul>
            </div>
        </div>

        <!-- Notification Preferences -->
        <div class="lg:col-span-1">
            <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Notification Preferences</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">
                        Choose which notifications you want to receive.
                    </p>
                </div>
                <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
                    <div id="notification-preferences"
                         hx-get="<?= APP_ROOT ?>api/notifications/get_preferences"
                         hx-trigger="load"
                         hx-swap="innerHTML">
                        <!-- Preferences will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
