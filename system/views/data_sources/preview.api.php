<?php
namespace api\data_sources;

use system\data_source_manager;

/**
 * Preview Data Source API Handler
 * Handles /data_sources/[id]/preview routes
 */

// Parse the URL to get the data source ID
$path = $_SERVER['REQUEST_URI'] ?? '';
$path_parts = array_filter(explode('/', trim($path, '/')));

// Find data_sources in the path and get the ID
$data_sources_index = array_search('data_sources', $path_parts);
$data_source_id = null;

if ($data_sources_index !== false && isset($path_parts[$data_sources_index + 1])) {
    $potential_id = $path_parts[$data_sources_index + 1];
    if (is_numeric($potential_id)) {
        $data_source_id = (int)$potential_id;
    }
}

// Validate data source exists
if (!$data_source_id) {
    header('Location: ' . APP_ROOT . '/data_sources');
    exit;
}

$data_source = data_source_manager::get_data_source($data_source_id);
if (!$data_source) {
    header('Location: ' . APP_ROOT . '/data_sources');
    exit;
}

// Set route parameters for the view
global $route_params;
$route_params = ['data_source_id' => $data_source_id];

// Include the preview view
include __DIR__ . '/preview.edge.php';
