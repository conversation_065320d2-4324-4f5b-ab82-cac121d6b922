@props([
    'title' => 'Create Data Source',
    'description' => 'Create a new database data source for tables and email campaigns'
])

@php
$selected_table = $_GET['table'] ?? '';
@endphp

<div class="py-8">
    <x-data-source-builder
        :title="$title"
        :description="$description"
        mode="create"
        :redirect_url="APP_ROOT . '/data_sources'"
    />
</div>

@if($selected_table)
<script>
// Auto-select table if provided in URL
document.addEventListener('DOMContentLoaded', function() {
    const tableSelect = document.querySelector('select[x-model="dataSource.table_name"]');
    if (tableSelect) {
        tableSelect.value = '{{ $selected_table }}';
        tableSelect.dispatchEvent(new Event('change'));
    }
});
</script>
@endif
