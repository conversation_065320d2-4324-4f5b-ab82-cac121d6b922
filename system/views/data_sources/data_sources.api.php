<?php
namespace api\data_sources;

use system\data_source_manager;
use Exception;

/**
 * Data Sources View API
 * Handles routing for data source views with dynamic parameters
 */

/**
 * Handle dynamic routing for data source operations
 * Routes like /data_sources/1/edit, /data_sources/1/preview, etc.
 */
function route($params) {
    // Parse the URL path to extract parameters
    $path = $_SERVER['REQUEST_URI'] ?? '';
    $path_parts = array_filter(explode('/', trim($path, '/')));
    
    // Remove query string
    if (strpos($path, '?') !== false) {
        $path = substr($path, 0, strpos($path, '?'));
        $path_parts = array_filter(explode('/', trim($path, '/')));
    }
    
    // Expected format: /data_sources/[id]/[action] or /data_sources/[action]
    if (count($path_parts) < 2) {
        return show_index();
    }
    
    // Remove base path parts to get to data_sources
    $data_sources_index = array_search('data_sources', $path_parts);
    if ($data_sources_index === false) {
        return show_index();
    }
    
    $remaining_parts = array_slice($path_parts, $data_sources_index + 1);
    
    if (empty($remaining_parts)) {
        return show_index();
    }
    
    $first_part = $remaining_parts[0];
    
    // Check if first part is numeric (ID) or action
    if (is_numeric($first_part)) {
        $id = (int)$first_part;
        $action = $remaining_parts[1] ?? 'view';
        
        switch ($action) {
            case 'edit':
                return show_edit($id);
            case 'preview':
                return show_preview($id);
            case 'delete':
                return handle_delete($id);
            default:
                return show_preview($id); // Default to preview for ID-based routes
        }
    } else {
        // First part is an action
        switch ($first_part) {
            case 'create':
                return show_create();
            default:
                return show_index();
        }
    }
}

/**
 * Show main data sources index
 */
function show_index() {
    return render_view('data_sources');
}

/**
 * Show create data source form
 */
function show_create() {
    return render_view('create');
}

/**
 * Show edit data source form
 */
function show_edit($id) {
    // Validate that data source exists
    $data_source = data_source_manager::get_data_source($id);
    if (!$data_source) {
        return render_error("Data source not found", 404);
    }

    return render_view('edit', ['data_source_id' => $id]);
}

/**
 * Show data source preview
 */
function show_preview($id) {
    // Validate that data source exists
    $data_source = data_source_manager::get_data_source($id);
    if (!$data_source) {
        return render_error("Data source not found", 404);
    }

    return render_view('preview', ['data_source_id' => $id]);
}

/**
 * Handle data source deletion
 */
function handle_delete($id) {
    try {
        // Validate that data source exists
        $data_source = data_source_manager::get_data_source($id);
        if (!$data_source) {
            return json_encode([
                'success' => false,
                'error' => 'Data source not found'
            ]);
        }
        
        // Delete the data source
        $db = \system\database::table('autobooks_data_sources');
        $db->where('id', $id)->delete();
        
        // Redirect to index
        header('Location: ' . APP_ROOT . '/data_sources');
        exit;
        
    } catch (Exception $e) {
        return render_error("Error deleting data source: " . $e->getMessage(), 500);
    }
}

/**
 * Render a view with parameters
 */
function render_view($view_name, $params = []) {
    global $route_params;
    $route_params = $params;
    
    $view_file = __DIR__ . '/' . $view_name . '.edge.php';
    if (file_exists($view_file)) {
        ob_start();
        include $view_file;
        return ob_get_clean();
    } else {
        return render_error("View not found: $view_name", 404);
    }
}

/**
 * Render error page
 */
function render_error($message, $code = 500) {
    http_response_code($code);
    return "
    <div class='max-w-md mx-auto mt-8 p-6 bg-red-50 border border-red-200 rounded-lg'>
        <div class='flex items-center'>
            <svg class='h-6 w-6 text-red-600 mr-3' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z'></path>
            </svg>
            <h3 class='text-lg font-medium text-red-800'>Error</h3>
        </div>
        <p class='mt-2 text-sm text-red-700'>$message</p>
        <div class='mt-4'>
            <a href='" . APP_ROOT . "/data_sources' class='inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500'>
                Back to Data Sources
            </a>
        </div>
    </div>";
}

// Default handler - route based on URL
if (!function_exists('api\\data_sources\\' . (INPUT_PARAMS['action'] ?? 'route'))) {
    echo route(INPUT_PARAMS);
}
