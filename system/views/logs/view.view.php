<?php
use system\users;
use edge\Edge;
include_once 'logs.fn.php';
// Ensure only admin/dev can access this page
users::requireRole('admin');
$logdata = process_log_content($_GET);
?>

<div class="p-10 sm:px-6 lg:px-8 max-w-full overflow-hidden">
    <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
            <h1 class="text-base font-semibold leading-6 text-gray-900">Log Viewer: <?= htmlspecialchars($logdata['logFile']); ?></h1>
            <p class="mt-2 text-sm text-gray-700">
                Size: <?= formatFileSize(filesize($logdata['filePath'])); ?> |
                Last Modified: <?= date('Y-m-d H:i:s', filemtime($logdata['filePath'])); ?>
            </p>
        </div>
        <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
            <a href="#" hx-get="<?= APP_ROOT . APP_PATH ?>/logs" hx-target="#content_wrapper" data-apppath="<?= APP_PATH ?>" class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                Back to List
            </a>
            <a href="#" hx-get="<?= APP_ROOT . APP_PATH ?>/logs?clear=<?= urlencode($logdata['logFile']); ?>" class="ml-3 rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600" onclick="return confirm('Are you sure you want to clear this log file?');">
                Clear Log
            </a>
        </div>
    </div>

    <div class="mt-8 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div class="flex-1">
            <input type="hidden" id="log_file" name="file" value="<?= htmlspecialchars($logdata['logFile']); ?>">
            <input type="hidden" id="refresh_rate" name="refresh" value="<?= $logdata['autoRefresh']; ?>">

            <!-- This will be used by the data table's built-in search functionality -->
            <div class="flex rounded-md shadow-sm">
                <input type="text"
                       id="log_search"
                       name="search_terms"
                       value="<?= htmlspecialchars($logdata['search']); ?>"
                       placeholder="Search in log..."
                       class="block w-full rounded-md border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                       hx-include="#log_file, #refresh_rate"
                       hx-trigger="keyup changed delay:500ms"
                       hx-get="<?= APP_ROOT . APP_PATH ?>/view"
                       hx-target="#log_table_container"
                       hx-swap="innerHTML">
            </div>
        </div>

        <div class="flex items-center">
            <div class="flex items-center">
                <label for="refresh" class="mr-2 text-sm font-medium text-gray-700">Auto-refresh:</label>
                <select name="refresh" id="refresh"
                        onchange="document.getElementById('refresh_rate').value = this.value; refreshLogTable();"
                        class="rounded-md border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                    <option value="0" <?= $logdata['autoRefresh'] == 0 ? 'selected' : ''; ?>>Off</option>
                    <option value="5" <?= $logdata['autoRefresh'] == 5 ? 'selected' : ''; ?>>5 seconds</option>
                    <option value="10" <?= $logdata['autoRefresh'] == 10 ? 'selected' : ''; ?>>10 seconds</option>
                    <option value="30" <?= $logdata['autoRefresh'] == 30 ? 'selected' : ''; ?>>30 seconds</option>
                    <option value="60" <?= $logdata['autoRefresh'] == 60 ? 'selected' : ''; ?>>1 minute</option>
                </select>
            </div>

            <?php if ($logdata['autoRefresh'] > 0): ?>
            <script>
                // Set up auto-refresh functionality
                let refreshInterval;

                function refreshLogTable() {
                    const refreshRate = document.getElementById('refresh_rate').value;
                    const logFile = document.getElementById('log_file').value;
                    const searchTerms = document.getElementById('log_search')?.value || '';

                    // Clear any existing interval
                    clearInterval(refreshInterval);

                    if (refreshRate > 0) {
                        // Set up new interval
                        refreshInterval = setInterval(() => {
                            // Use HTMX to refresh the table
                            htmx.ajax('GET', '<?= APP_ROOT . APP_PATH ?>/view', {
                                target: '#log_table_container',
                                swap: 'innerHTML',
                                values: {
                                    file: logFile,
                                    refresh: refreshRate,
                                    search_terms: searchTerms
                                }
                            });
                        }, refreshRate * 1000);
                    }
                }

                // Initialize auto-refresh
                document.addEventListener('DOMContentLoaded', refreshLogTable);
            </script>
            <?php endif; ?>
        </div>
    </div>

    <?php if (!empty($logdata['search'])): ?>
        <div class="mt-4 rounded-md bg-blue-50 p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-blue-800">Search results for: <strong><?= htmlspecialchars($logdata['search']); ?></strong> (<?= $logdata['total']; ?> matches)</p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if ($logdata['total'] > 0): ?>
        <div class="mt-4 flex items-center justify-between">
            <div class="text-sm text-gray-700">
                Showing <span class="font-medium"><?= count($logdata['entries']); ?></span> of <span class="font-medium"><?= $logdata['total']; ?></span> entries
            </div>

            <div class="flex items-center space-x-2">
                <div class="flex items-center">
                    <label for="per_page" class="mr-2 text-sm font-medium text-gray-700">Entries per page:</label>
                    <select name="per_page" id="per_page"
                            onchange="updatePerPage(this.value)"
                            class="rounded-md border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                        <option value="50" <?= $logdata['perPage'] == 50 ? 'selected' : ''; ?>>50</option>
                        <option value="100" <?= $logdata['perPage'] == 100 ? 'selected' : ''; ?>>100</option>
                        <option value="250" <?= $logdata['perPage'] == 250 ? 'selected' : ''; ?>>250</option>
                        <option value="500" <?= $logdata['perPage'] == 500 ? 'selected' : ''; ?>>500</option>
                        <option value="1000" <?= $logdata['perPage'] == 1000 ? 'selected' : ''; ?>>1000</option>
                    </select>
                </div>
            </div>
        </div>

        <script>
            function updatePerPage(perPage) {
                const logFile = document.getElementById('log_file').value;
                const refreshRate = document.getElementById('refresh_rate').value;
                const searchTerms = document.getElementById('log_search')?.value || '';

                // Use HTMX to refresh the table with new per_page value
                htmx.ajax('GET', '<?= APP_ROOT . APP_PATH ?>/view', {
                    target: '#log_table_container',
                    swap: 'innerHTML',
                    values: {
                        file: logFile,
                        refresh: refreshRate,
                        search_terms: searchTerms,
                        per_page: perPage,
                        page: 1 // Reset to first page when changing items per page
                    }
                });
            }
        </script>
    <?php endif; ?>

    <div id="log_table_container" class="mt-6 flow-root">
        <?php
        // Define columns for the data table with filtering capabilities
        $columns = [
            [
                'label' => 'Timestamp',
                'field' => 'timestamp',
                'filter' => true,
                'auto_filter' => true
            ],
            [
                'label' => 'Source',
                'field' => 'source',
                'filter' => true,
                'auto_filter' => true
            ],
            [
                'label' => 'File',
                'field' => 'file',
                'filter' => true,
                'auto_filter' => true
            ],
            [
                'label' => 'Line',
                'field' => 'line',
                'filter' => true
            ],
            [
                'label' => 'Message',
                'field' => 'message',
                'filter' => true,
                'content' => function($entry) {
                    $message = htmlspecialchars($entry['message']);
                    return '<div x-data="{ expanded: false }" class="relative">' .
                           '<div @click="expanded = !expanded" class="cursor-pointer">' .
                        '<pre x-show="!expanded" class="whitespace-nowrap overflow-hidden text-ellipsis w-full break-all" id="message">' . preg_replace('/(\\+r\\+n|\\+n|\\+r|array\([0-9]+\)|string\([0-9]+\)|\{|\}|\s{2,})/', ' ', $message) . '</pre>' .
                        '<pre x-show="expanded" class="whitespace-pre-wrap break-all w-full truncate">' . str_replace('\n', "\n", $message) . '</pre>' .
                           '</div>' .
                           '<button @click="expanded = !expanded" class="absolute right-0 top-0 text-indigo-600 hover:text-indigo-800">' .
                           '<span x-show="!expanded" class="text-xs bg-gray-100 px-1 py-0.5 rounded">Expand</span>' .
                           '<span x-show="expanded" class="text-xs bg-gray-100 px-1 py-0.5 rounded">Collapse</span>' .
                           '</button>' .
                           '</div>';
                }
            ]
        ];

        // Process distinct values for filter dropdowns
        if (!empty($logdata['entries'])) {
            // Get distinct sources
            $sources = array_unique(array_column($logdata['entries'], 'source'));
            $sources = array_filter($sources);
            if (!empty($sources)) {
                $columns[1]['filter_data'] = array_values($sources);
            }

            // Get distinct files
            $files = array_unique(array_column($logdata['entries'], 'file'));
            $files = array_filter($files);
            if (!empty($files)) {
                $columns[2]['filter_data'] = array_values($files);
            }
        }

        // Define row parameters
        $rows = [
            'id_prefix' => 'log_entry_',
            'id_field' => 'unixtime',
            'class_postfix' => '',
            'extra_parameters' => ''
        ];

        // Render the data table with filtering capabilities
        echo Edge::render('data-table', [
            'title' => '',
            'description' => '',
            'items' => $logdata['entries'],
            'columns' => $columns,
            'rows' => $rows,
            'just_body' => false,
            'just_rows' => false,
            'items_per_page' => $logdata['perPage'],
            'current_page_num' => $logdata['page'],
            'class' => 'w-full',
            'callback' => 'log_filter',
            'id_count' => rand(1000, 9999), // Generate a unique ID for this table instance
            'auto_update' => $logdata['autoRefresh'] > 0
        ]);
        ?>
    </div>

    <!-- Pagination is now handled by the data-table component -->
    <?php /*else: ?>
        <div class="mt-6 rounded-md bg-gray-50 p-8 text-center">
            <p class="text-sm text-gray-500">No log entries found<?= !empty($logdata['search']) ? ' matching your search criteria' : ''; ?>.</p>
        </div>
    <?php endif;*/ ?>
</div>
