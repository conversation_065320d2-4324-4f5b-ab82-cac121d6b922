<?php

function process_log_files($p){

// Set the path to the logs directory
    $logsDir = FS_SYS_LOGS;

// Handle log file deletion if requested
    if ( !empty($p['clear'])) {
        $logFile = $p['clear'];
        $filePath = $logsDir . sanitizeFileName($logFile);

        if (file_exists($filePath) && is_file($filePath)) {
            // Clear the file by writing an empty string to it
            file_put_contents($filePath, '');
            $message = "Log file '$logFile' has been cleared.";
        } else {
            $message = "Error: Log file '$logFile' not found.";
        }
    }

// Get all log files
    $logFiles = [];
    if (is_dir($logsDir)) {
        $files = scandir($logsDir);
        print_rr($files, 'tcs: log files');
        foreach ($files as $file) {
            $file_fq = tcs_path($logsDir,$file);
            if ($file != '.' && $file != '..' && is_file($file_fq) && strpos($file, '.log') !== false) {
                $logFiles[] = [
                    'name' => $file,
                    'size' => filesize($file_fq),
                    'modified' => filemtime($file_fq)
                ];
            }
        }
    } else{
        print_rr($logsDir, 'tcs: logsDir not found');
        tcs_log("Logs directory not found: $logsDir", 'logs_logging_itself');
    } // Sort log files by modification time (newest first)
    usort($logFiles, function ($a, $b) {
        return $b['modified'] - $a['modified'];
    });
    return $logFiles;
}

/**
 * Process log filter request from data table
 *
 * @param array $p Parameters from the request
 * @return string HTML content of filtered log entries
 */
function log_filter($p) {
    // Extract file and refresh parameters
    $file = isset($p['file']) ? sanitizeFileName($p['file']) : '';
    $refresh = isset($p['refresh']) ? intval($p['refresh']) : 0;
    $search = isset($p['search_terms']) ? trim($p['search_terms']) : '';

    // Set up criteria for filtering
    $criteria = [];

    // Process column filters
    if (isset($p['column'])) {
        foreach ($p['column'] as $column => $value) {
            if (empty($value)) continue;
            $criteria[$column] = $value;
        }
    }

    // Get the log file path
    $logsDir = FS_APP_ROOT . DIRECTORY_SEPARATOR . 'resources/logs/';
    $filePath = $file == 'all' ? 'all' : $logsDir . $file;

    // Get log entries with filtering
    $page = isset($p['page']) ? max(1, intval($p['page'])) : 1;
    $perPage = isset($p['per_page']) ? max(10, intval($p['per_page'])) : 100;

    $logData = getLogEntries($filePath, $page, $perPage, $search);

    // Apply additional column filters if needed
    if (!empty($criteria)) {
        $filteredEntries = [];
        foreach ($logData['entries'] as $entry) {
            $include = true;
            foreach ($criteria as $column => $value) {
                if (isset($entry[$column]) && stripos($entry[$column], $value) === false) {
                    $include = false;
                    break;
                }
            }
            if ($include) {
                $filteredEntries[] = $entry;
            }
        }
        $logData['entries'] = $filteredEntries;
        $logData['total'] = count($filteredEntries);
        $logData['pages'] = ceil($logData['total'] / $perPage);
    }

    // Define columns for the data table
    $columns = [
        [
            'label' => 'Timestamp',
            'field' => 'timestamp',
            'filter' => true,
            'auto_filter' => true
        ],
        [
            'label' => 'Source',
            'field' => 'source',
            'filter' => true,
            'auto_filter' => true
        ],
        [
            'label' => 'File',
            'field' => 'file',
            'filter' => true,
            'auto_filter' => true
        ],
        [
            'label' => 'Line',
            'field' => 'line',
            'filter' => true
        ],
        [
            'label' => 'Message',
            'field' => 'message',
            'filter' => true,
            'content' => function($entry) {
                $message = htmlspecialchars($entry['message']);
                return '<div x-data="{ expanded: false }" class="relative">' .
                       '<div @click="expanded = !expanded" class="cursor-pointer">' .
                       '<pre x-show="!expanded" class="whitespace-nowrap overflow-hidden text-ellipsis w-full break-all" id="message">' . preg_replace('/(\r\n|\n|\r|array\([0-9]+\)|string\([0-9]+\)|\{|\}|\s{2,})/', ' ', $message) . '</pre>' .
                       '<pre x-show="expanded" class="whitespace-pre-wrap break-all w-full truncate">' . str_replace('\n', "\n", $message) . '</pre>' .
                       '</div>' .
                       '<button @click="expanded = !expanded" class="absolute right-0 top-0 text-indigo-600 hover:text-indigo-800">' .
                       '<span x-show="!expanded" class="text-xs bg-gray-100 px-1 py-0.5 rounded">Expand</span>' .
                       '<span x-show="expanded" class="text-xs bg-gray-100 px-1 py-0.5 rounded">Collapse</span>' .
                       '</button>' .
                       '</div>';
            }
        ]
    ];

    // Process distinct values for filter dropdowns
    if (!empty($logData['entries'])) {
        // Get distinct sources
        $sources = array_unique(array_column($logData['entries'], 'source'));
        $sources = array_filter($sources);
        if (!empty($sources)) {
            $columns[1]['filter_data'] = array_values($sources);
        }

        // Get distinct files
        $files = array_unique(array_column($logData['entries'], 'file'));
        $files = array_filter($files);
        if (!empty($files)) {
            $columns[2]['filter_data'] = array_values($files);
        }
    }

    // Define row parameters
    $rows = [
        'id_prefix' => 'log_entry_',
        'id_field' => 'unixtime',
        'class_postfix' => '',
        'extra_parameters' => ''
    ];

    // Render the data table
    return Edge::render('data-table', [
        'title' => '',
        'description' => '',
        'items' => $logData['entries'],
        'columns' => $columns,
        'rows' => $rows,
        'just_body' => false,
        'just_rows' => false,
        'items_per_page' => $perPage,
        'current_page_num' => $page,
        'class' => 'w-full',
        'callback' => 'log_filter',
        'id_count' => rand(1000, 9999) // Generate a unique ID for this table instance
    ]);
}

/**
 * Format file size in human-readable format
 *
 * @param int $bytes File size in bytes
 * @return string Formatted file size
 */
function formatFileSize($bytes)
{
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];

    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);

    $bytes /= pow(1024, $pow);

    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * Sanitize file name to prevent directory traversal attacks
 *
 * @param string $fileName File name to sanitize
 * @return string Sanitized file name
 */
function sanitizeFileName($fileName){
    // Remove any directory traversal attempts
    $fileName = str_replace(['../', '..\\', '/', '\\'], '', $fileName);

    // Only allow alphanumeric characters, dots, underscores, and hyphens
    $fileName = preg_replace('/[^a-zA-Z0-9._-]/', '', $fileName);
    return $fileName;
}

function process_log_content($p){
    // Set the path to the logs directory
    $log_data['logsDir'] = FS_SYSTEM . DIRECTORY_SEPARATOR . 'logs/';

    // Get the requested log file
    $log_data['logFile'] = isset($p['file']) ? sanitizeFileName($p['file']) : '';

    // Now $log_data['files'] contains all files and directories in the specified folder
    $log_data['filePath'] = $log_data['logFile'] == 'all' ? 'all' : $log_data['logsDir'] . $log_data['logFile'];

    // Get pagination parameters
    $log_data['page'] = isset($p['page']) ? max(1, intval($p['page'])) : 1;
    $log_data['perPage'] = isset($p['per_page']) ? max(10, intval($p['per_page'])) : 100;

    // Get search parameter - support both 'search' (old) and 'search_terms' (new data table format)
    $log_data['search'] = '';
    if (isset($p['search']) && !empty($p['search'])) {
        $log_data['search'] = trim($p['search']);
    } elseif (isset($p['search_terms']) && !empty($p['search_terms'])) {
        $log_data['search'] = trim($p['search_terms']);
    }

    // Get auto-refresh parameter
    $log_data['autoRefresh'] = isset($p['refresh']) ? intval($p['refresh']) : 0;

    // Get log entries
    $log_data = array_merge($log_data, getLogEntries($log_data['filePath'], $log_data['page'], $log_data['perPage'], $log_data['search']));

    // If this is an AJAX request, don't set refresh headers
    $isAjax = isset($_SERVER['HTTP_HX_REQUEST']) ||
              (isset($_SERVER['HTTP_X_REQUESTED_WITH']) &&
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest');

    // Set refresh header if auto-refresh is enabled and not an AJAX request
    if ($log_data['autoRefresh'] > 0 && !$isAjax) {
        $log_data['refreshUrl'] = APP_ROOT . DIRECTORY_SEPARATOR . 'logs/view?file=' . urlencode($log_data['logFile']) .
            '&page=' . $log_data['page'] .
            '&per_page=' . $log_data['perPage'] .
            '&refresh=' . $log_data['autoRefresh'];

        if (!empty($log_data['search'])) {
            $log_data['refreshUrl'] .= '&search=' . urlencode($log_data['search']);
        }
        $ar = $log_data['autoRefresh'];
        $ru = $log_data['refreshUrl'];
        header("Refresh: $ar; url=$ru");
    }

    return $log_data;
}



///**
// * Format file size in human-readable format
// *
// * @param int $bytes File size in bytes
// * @return string Formatted file size
// */
//function formatFileSize($bytes)
//{
//    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
//
//    $bytes = max($bytes, 0);
//    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
//    $pow = min($pow, count($units) - 1);
//
//    $bytes /= pow(1024, $pow);
//
//    return round($bytes, 2) . ' ' . $units[$pow];
//}

/**
 * Get log file entries with pagination
 *
 * @param string $filePath Path to the log file
 * @param int $page Current page number
 * @param int $perPage Number of entries per page
 * @param string $search Search term (optional)
 * @return array Array containing log entries and pagination info
 */
function getLogEntries($filePath, $page = 1, $perPage = 100, $search = '', $logsDir = FS_APP_ROOT . DIRECTORY_SEPARATOR . 'resources/logs/'){
    // Initialize result array
    $result = [
        'entries' => [],
        'total' => 0,
        'pages' => 0,
        'current_page' => $page
    ];
    $content = '';

    if ($filePath == 'all') {
        $files = scandir($logsDir);
        // Remove . and .. entries
        $files = array_diff($files, array('.', '..'));
        foreach ($files as $file) {
            if (is_file($logsDir . $file) && strpos($file, '.log') !== false) {
                $content .= file_get_contents($logsDir . $file);
            }
        }
    } else {
        if (!file_exists($filePath) || !is_file($filePath)) {
            return $result;
        }
        $content = file_get_contents($filePath);
        if (empty($content)) {
            return $result;
        }

    }

    // Split content into lines
    $lines = explode("\n", $content);
    $lines = array_filter($lines); // Remove empty lines
    $log_entries = [];

    // Apply search filter if provided
    if (!empty($search)) {
        $lines = array_filter($lines, function ($line) use ($search) {
            return stripos($line, $search) !== false;
        });
    }
    foreach ($lines as $line) {
        $log_entries[] = parseLogEntry($line);
    }
    usort($log_entries, function ($a, $b) {
        return $a['unixtime'] - $b['unixtime'];
    });
    //$log_entries = array_reverse($log_entries);
    // Calculate pagination
    $result['total'] = count($log_entries);
    $result['pages'] = ceil($result['total'] / $perPage);
    $result['current_page'] = min(max(1, $page), max(1, $result['pages']));

    // Get entries for the current page
    $start = ($result['current_page'] - 1) * $perPage;
    $result['entries'] = array_slice($log_entries, $start, $perPage);
    return $result;
}

/**
 * Parse log entry to extract timestamp, file, line, and message
 *
 * @param string $entry Log entry
 * @return array Parsed log entry
 */
function parseLogEntry($entry)
{
    $parsed = [
        'unixtime' => 0,
        'timestamp' => '',
        'file' => '',
        'line' => '',
        'message' => $entry
    ];

    // Try to match the tcs_log format: [timestamp] [file:line] message
    if (preg_match('/^\[(.*?)] \[(.*?)] \[(.*?):(.*?)] (.*)$/', $entry, $matches)) {
        $parsed['unixtime'] = strtotime($matches[2]);
        $parsed['source'] = $matches[1];
        $parsed['timestamp'] = $matches[2];
        $parsed['file'] = $matches[3];
        $parsed['line'] = $matches[4];
        $parsed['message'] = $matches[5];
    }
    return $parsed;
}

?>
