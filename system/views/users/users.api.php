<?php
namespace api\users;
use system\users;
use edge\Edge;

function user_modal($p) {
    $user = null;
    if (isset($p['user_id'])) {
        $result = tep_db_query(
            "SELECT * FROM autobooks_users WHERE id = :id",
            null,
            [':id' => $p['user_id']]
        );
        $user = tep_db_fetch_array($result);
    }
    echo Edge::render('user-modal', ['user' => $user]);
}

function save_user($p) {
    users::requireRole('admin');

    if ($p['id'] && ($p['id'] != '')) {
        // Update existing user
        $updates = [];
        $params = [':id' => $p['id']];

        foreach (['email', 'role', 'status'] as $field) {
            if (isset($p[$field])) {
                $updates[] = "$field = :$field";
                $params[":$field"] = $p[$field];
            }
        }

        if (!empty($updates)) {
            print_rr( tcs_db_query(
                "UPDATE autobooks_users SET " . implode(", ", $updates) . " WHERE id = :id",
                $params
            ));
        }
    } else {
        // Create new user

        print_rr(
            tcs_db_query(
            "INSERT INTO autobooks_users (username, name,email, password, role, status)
             VALUES (:username,:name, :email, :password, :role, :status)",
            [
                ':username' => $p['email'],
                ':name' => $p['name'],
                ':email' => $p['email'],
                ':password' => password_hash($p['password'], PASSWORD_DEFAULT),
                ':role' => $p['role'],
                ':status' => $p['status']
            ]
        )
        );
    }
    echo generate_user_table();
}

function reset_password($p) {
    users::requireRole('admin');

    // Get user information to send email
    $result = tep_db_query(
        "SELECT * FROM autobooks_users WHERE id = :user_id",
        null,
        [':user_id' => $p['user_id']]
    );
    $user = tep_db_fetch_array($result);

    if (!$user) {
        header('HX-Trigger: {"password-reset": "User not found"}');
        return '';
    }

    // Generate a reset token and expiration time (24 hours from now)
    $reset_token = bin2hex(random_bytes(32));
    $expires = date('Y-m-d H:i:s', strtotime('+24 hours'));

    // Store the token in the database
    tep_db_query(
        "UPDATE autobooks_users SET reset_token = :reset_token, reset_token_expires = :expires WHERE id = :user_id",
        null,
        [':reset_token' => $reset_token, ':expires' => $expires, ':user_id' => $p['user_id']]
    );

    // Create reset URL - use full URL with domain
    $domain = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : DOMAIN;
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? "https://" : "http://";
    $reset_url = $protocol . $domain . '/' . rtrim(APP_ROOT, '/') . '/reset-password?token=' . $reset_token;

    // Log the reset URL for debugging
    tcs_log("Password reset URL generated: {$reset_url} for user ID: {$p['user_id']}", 'auth');

    // Email content
    $subject = "Password Reset Request";
    $email_body = "<html><body>"
        . "<h1>Password Reset</h1>"
        . "<p>Hello {$user['name']},</p>"
        . "<p>A password reset has been requested for your account. Please click the link below to set a new password:</p>"
        . "<p><a href='{$reset_url}'>{$reset_url}</a></p>"
        . "<p>This link will expire in 24 hours.</p>"
        . "<p>If you did not request this password reset, please ignore this email.</p>"
        . "<p>Regards,<br>The Admin Team</p>"
        . "</body></html>";

    // Send the email
    $headers = "From: noreply@" . DOMAIN . "\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";

    if (mail($user['email'], $subject, $email_body, $headers)) {
        header('HX-Trigger: {"password-reset": "Password reset email sent to ' . $user['email'] . '"}');
    } else {
        header('HX-Trigger: {"password-reset": "Failed to send password reset email"}');
    }

    return '';
}

function filter_users($p) {
    return generate_user_table(['search' => $p['search_terms']], true);
}