<?php
use system\users;
use edge\Edge;


// Ensure only dev/admin can access this page
users::requireRole('dev');
?>

<div class="p-10 sm:px-6 lg:px-8">
    <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
            <h1 class="text-base font-semibold leading-6 text-gray-900">Users</h1>
            <p class="mt-2 text-sm text-gray-700">Manage system users, their roles and access.</p>
        </div>
        <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
            <?= Edge::render('forms-button', [
                'type' => 'button',
                'label' => 'Add User',
                'icon' => 'plus',
                'variant' => 'primary',
                'hx-post' => APP_ROOT . DIRECTORY_SEPARATOR . 'api/user_modal',
                'hx-target' => '#modal_body',
                '@click' => 'showModal = true'
            ]) ?>
        </div>
    </div>

    <div class="mt-8 flow-root">
        <div id="users_table">
            <?= generate_user_table() ?>
        </div>
    </div>
