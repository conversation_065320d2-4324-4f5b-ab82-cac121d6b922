<?php
/**
 * Main Data Sources Router
 * Handles all data sources routes and delegates to appropriate sub-views
 */

// Parse the URL to determine the route
$path = $_SERVER['REQUEST_URI'] ?? '';
$path_parts = array_filter(explode('/', trim($path, '/')));

// Remove query string
if (strpos($path, '?') !== false) {
    $path = substr($path, 0, strpos($path, '?'));
    $path_parts = array_filter(explode('/', trim($path, '/')));
}

// Find data_sources in the path
$data_sources_index = array_search('data_sources', $path_parts);
if ($data_sources_index !== false) {
    $remaining_parts = array_slice($path_parts, $data_sources_index + 1);
    
    if (empty($remaining_parts)) {
        // Main index page - /data_sources
        include __DIR__ . '/data_sources/data_sources.edge.php';
    } else {
        $first_part = $remaining_parts[0];
        
        if (is_numeric($first_part)) {
            // ID-based route like /data_sources/1/edit or /data_sources/1/preview
            $id = (int)$first_part;
            $action = $remaining_parts[1] ?? 'preview';
            
            // Set route parameters for the sub-views
            global $route_params;
            $route_params = ['data_source_id' => $id];
            
            switch ($action) {
                case 'edit':
                    include __DIR__ . '/data_sources/edit.edge.php';
                    break;
                case 'preview':
                    include __DIR__ . '/data_sources/preview.edge.php';
                    break;
                default:
                    include __DIR__ . '/data_sources/preview.edge.php';
            }
        } else {
            // Action-based route like /data_sources/create
            switch ($first_part) {
                case 'create':
                    include __DIR__ . '/data_sources/create.edge.php';
                    break;
                default:
                    include __DIR__ . '/data_sources/data_sources.edge.php';
            }
        }
    }
} else {
    // Fallback to main index
    include __DIR__ . '/data_sources/data_sources.edge.php';
}
?>
