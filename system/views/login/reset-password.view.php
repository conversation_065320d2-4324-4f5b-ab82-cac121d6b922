<?php
use system\users;
use edge\Edge;

$token = $_GET['token'] ?? '';
$error = '';
$success = '';

// Check if token is valid
if (!empty($token)) {
    $result = tep_db_query(
        "SELECT * FROM autobooks_users WHERE reset_token = :token AND reset_token_expires > NOW()",
        null,
        [':token' => $token]
    );
    $user = tep_db_fetch_array($result);

    if (!$user) {
        $error = 'Invalid or expired reset token. Please request a new password reset.';
    }
} else {
    $error = 'Missing reset token. Please use the link provided in your email.';
}

// Debug information
tcs_log("Reset password page accessed with token: {$token}", 'auth');
if (isset($user)) {
    tcs_log("User found for token: {$token}, user ID: {$user['id']}", 'auth');
} else {
    tcs_log("No user found for token: {$token}", 'auth');
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($token) && empty($error)) {
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';

    // Validate password
    if (empty($password)) {
        $error = 'Password is required.';
    } elseif (strlen($password) < 8) {
        $error = 'Password must be at least 8 characters long.';
    } elseif ($password !== $confirm_password) {
        $error = 'Passwords do not match.';
    } else {
        // Update user's password and clear reset token
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);

        tep_db_query(
            "UPDATE autobooks_users SET password = :password, reset_token = NULL, reset_token_expires = NULL WHERE reset_token = :token",
            null,
            [':password' => $hashed_password, ':token' => $token]
        );

        $success = 'Your password has been updated successfully. You can now log in with your new password.';
    }
}
?>

<div class="flex min-h-full flex-col justify-center px-6 py-12 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-sm">
        <h2 class="mt-10 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900">Reset Your Password</h2>
    </div>

    <div class="mt-10 sm:mx-auto sm:w-full sm:max-w-sm">
        <?php if ($error): ?>
        <div class="rounded-md bg-red-50 p-4 mb-6">
            <div class="flex">
                <div class="shrink-0">
                    <svg class="size-5 text-red-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16ZM8.28 7.22a.75.75 0 0 0-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 1 0 1.06 1.06L10 11.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L11.06 10l1.72-1.72a.75.75 0 0 0-1.06-1.06L10 8.94 8.28 7.22Z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-red-800"><?= $error ?></p>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($success): ?>
        <div class="rounded-md bg-green-50 p-4 mb-6">
            <div class="flex">
                <div class="shrink-0">
                    <svg class="size-5 text-green-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm3.857-9.809a.75.75 0 0 0-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 1 0-1.06 1.061l2.5 2.5a.75.75 0 0 0 1.137-.089l4-5.5Z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-800"><?= $success ?></p>
                </div>
            </div>
        </div>
        <div class="text-center mt-6">
            <a href="<?= rtrim(APP_ROOT, '/') ?>/login" class="text-indigo-600 hover:text-indigo-500">Go to Login</a>
        </div>
        <?php else: ?>
        <form class="space-y-6" method="POST">
            <div>
                <label for="password" class="block text-sm/6 font-medium text-gray-900">New Password</label>
                <div class="mt-2">
                    <input type="password" name="password" id="password" required class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6">
                </div>
            </div>

            <div>
                <label for="confirm_password" class="block text-sm/6 font-medium text-gray-900">Confirm Password</label>
                <div class="mt-2">
                    <input type="password" name="confirm_password" id="confirm_password" required class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6">
                </div>
            </div>

            <div>
                <button type="submit" class="flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">Reset Password</button>
            </div>
        </form>
        <?php endif; ?>
    </div>
</div>
