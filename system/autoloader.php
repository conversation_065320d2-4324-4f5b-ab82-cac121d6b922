<?php
/**
 * Custom Autoloader for Autobooks
 *
 * This autoloader automatically loads classes based on their namespace and class name.
 * It maps namespaces to specific directories in the project structure.
 *
 * The autoloader follows these rules:
 * 1. First checks special case mappings for specific classes
 * 2. Then checks if the namespace has a special mapping
 * 3. If not, it looks for the class in a subdirectory matching the namespace structure
 * 4. Also tries the default classes directory as a fallback
 * 5. Tries various naming conventions (lowercase, original case, snake_case)
 */

/**
 * Autoloader function that loads classes based on their namespace
 *
 * @param string $class The fully-qualified class name to load
 * @return void
 */
function autobooks_autoloader(string $class) {
    // Debug output
     if (defined('API_RUN') && API_RUN) print_rr(tcs_log("Autoloader looking for class: {$class}",'autoloader'),'looking for class',true,true);


    // Convert namespace separators to directory separators
    $class_file = str_replace('\\', '/', $class);
    $class_name = basename(str_replace('\\', '/', $class));
    $potential_paths = [];

    // Define namespace to directory mappings for special cases
    $namespace_mappings = [
        'api' => FS_API,
        'icons' => FS_COMPONENTS,
        'ICONS' => FS_COMPONENTS,
        'system' => FS_SYS_CLASSES, // Add mapping for system namespace
        'default' => FS_CLASSES,
    ];

    // Extract the namespace from the class name
    $namespace_parts = explode('\\', $class);
    $namespace = $namespace_parts[0];

    // Special case handling for common classes
    $special_cases = [

    ];

    // Check for special cases first
    if (isset($special_cases[$class])) {
        $file_path = $special_cases[$class];
        if (file_exists($file_path)) {
            if (defined('API_RUN') && API_RUN) print_rr(tcs_log("Loading special case: {$file_path}",'autoloader'));
            require_once $file_path;
            return;
        }
    }
    $namespace_path = str_replace('\\', '/', $namespace);
    $namespace_parts = explode('\\', $namespace_path);
    $lowest = array_pop($namespace_parts);
    // Check if we have a special mapping for this namespace
    if (isset($namespace_mappings[$namespace])) {
        $base_dir = $namespace_mappings[$namespace];
    } else {        // If no special mapping, try to find the class in a subdirectory matching the namespace

        $base_dir = $namespace_mappings['default'] . '/' . $namespace_path;
        $base_dir = $namespace_mappings['default'] . '/' . implode('/', $namespace_parts);
        // Also add the default classes directory as a fallback
        $default_dir = $namespace_mappings['default'];
    }

    // Build the potential file paths
    $potential_paths = [
        $base_dir . '/' . strtolower($class_file) . '.class.php',
        $base_dir . '/' . strtolower($class_name) . '.class.php',
        $base_dir . '/' . $class_name . '.class.php',
        "{$base_dir}/{$lowest}_{$class_name}.class.php",
        isset($default_dir) ? $default_dir . '/' . strtolower($class_name) . '.class.php' : null,
        isset($default_dir) ? $default_dir . '/' . $class_name . '.class.php' : null,
        // API files
        FS_API . DS . strtolower($class_name) . '.api.php',
        // System files
        FS_SYS_CLASSES . DS . strtolower($class_file) . '.class.php',
        FS_SYS_CLASSES . DS . strtolower($class_name) . '.class.php',
        FS_SYS_CLASSES . DS . $class_name . '.class.php',
    ];
    // Filter out null entries first
    $potential_paths = array_filter($potential_paths);

    // Clean up the paths
    array_walk($potential_paths, function(&$value) {
        $value = str_replace('//', '/', $value);
        $value = str_replace( '.class.php.class.php', '.class.php',$value);
    });

    // Try each potential path
    foreach ($potential_paths as $path) {
        if (defined('API_RUN') && API_RUN)  tcs_log("Looking for class file: {$path}",'autoloader');
        if (file_exists($path)) {
            if (defined('API_RUN') && API_RUN)  print_rr(tcs_log("Loading file: {$path}",'autoloader'));
            require_once $path;
            return;
        }
    }

    //try snake case
    $snake_case = strtolower(preg_replace('/(?<!^)[A-Z]/', '_$0', $class_name));
    $snake_case_paths = [
        $base_dir . '/' . $snake_case . '.class.php',
        isset($default_dir) ? $default_dir . '/' . $snake_case . '.class.php' : null
    ];

    $snake_case_paths = array_filter($snake_case_paths);

    foreach ($snake_case_paths as $path) {
        if (file_exists($path)) {
            if (defined('API_RUN') && API_RUN) print_rr(tcs_log("Loading from snake case: {$path}",'autoloader'));
            require_once $path;
            return;
        }
    }

    // For function files, try to load from functions directory
    $function_file = FS_FUNCTIONS . DS . strtolower($class_name) . '.fn.php';
    if (file_exists($function_file)) {
        if (defined('API_RUN') && API_RUN) print_rr(tcs_log("Loading function file: {$function_file}",'autoloader'));
        require_once $function_file;
        return;
    }

    // If we reach here, the class file wasn't found
    if (defined('API_RUN') && API_RUN) print_rr(tcs_log("Could not find class file for: {$class}",'autoloader'));
    if (defined('API_RUN') && API_RUN) print_rr(tcs_log($potential_paths,'autoloader'),'places looked');

}

/**
 * Function to load function files based on page name or path
 * This is used by the router to load function files for the current page
 *
 * @param string $page_name The page name or path
 * @return bool True if a function file was loaded, false otherwise
 */
function autobooks_load_function_file($page_name) {
    // Skip if the page name is empty
    if (empty($page_name)) return false;

    // Check if this is a system path
    $is_system_path = (strpos($page_name, 'system/') === 0);
    if ($is_system_path) {
        // Extract the system page name (after 'system/')
        $page_name = str_replace('system/', '', $page_name);
    }

    // Clean up the page name
    $page_name = basename(str_replace('\\', '/', $page_name));

    // Build a list of potential function file paths
    $function_paths = [
        // Direct match in functions directory
        "functions_folder" => FS_FUNCTIONS . DS . $page_name . '.fn.php',
        
        // Try with lowercase
        "functions_folder_lowercase" => FS_FUNCTIONS . DS . strtolower($page_name) . '.fn.php',
        
        // Try in the current directory
        "current_folder" => __DIR__ . '/' . $page_name . '.fn.php',
        
        // Try in views directory with current page
        "views_directory_current_page" => FS_VIEWS . DS . $page_name . '/' . $page_name . '.fn.php',
        
        // System function paths
        "system_functions" => FS_SYS_FUNCTIONS . DS . $page_name . '.fn.php',
        "system_functions_lowercase" => FS_SYS_FUNCTIONS . DS . strtolower($page_name) . '.fn.php',
    ];

    // If this is a system path, prioritize system directories
    if ($is_system_path) {
        // Reorder to check system paths first
        $function_paths = array_merge(
            [
                "system_functions" => FS_SYS_FUNCTIONS . DS . $page_name . '.fn.php',
                "system_functions_lowercase" => FS_SYS_FUNCTIONS . DS . strtolower($page_name) . '.fn.php',
            ],
            $function_paths
        );
    }

    $tried_paths = [];
    // If SOURCE_APP_PATH and SOURCE_PAGE are defined, add those paths too
    if (defined('SOURCE_APP_PATH') && defined('SOURCE_PAGE')) {
        $source_app_path = SOURCE_APP_PATH;
        $source_page = SOURCE_PAGE;

        $additional_paths = [
            // Try in views with source app path and source page
            "source_app_path_source_page_x2" => tcs_path(FS_VIEWS, "/$source_app_path/$source_page/$source_page.fn.php"),
            "source_app_path_source_page" => tcs_path(FS_VIEWS, "/$source_app_path/$source_page.fn.php"),
            "source_app_path" => tcs_path(FS_VIEWS, "/$source_app_path.fn.php"),
            "functions_folder_source_page" =>  tcs_path(FS_FUNCTIONS,"/$source_page.fn.php"),
        ];
        foreach ($additional_paths as $key => $path) {
            // Clean up the path
            if ($path !== null) {
                $path = str_replace( '.fn.php.fn.php', '.fn.php',$path); //str_replace($replace, $subject)
            }
            if (file_exists($path)) {
                if (defined('API_RUN') && API_RUN) print_rr(tcs_log("Loading function file: {$path}",'autoloader'));
                include_once($path);
                break;
            }
            $tried_paths[$key] = $path;
        }
    }

    // Try each path
    foreach ($function_paths as $key => $path) {
        // Clean up the path
        if ($path !== null) {
            $path = str_replace('//', '/', $path);
            $path = str_replace( '.fn.php.fn.php', '.fn.php',$path); //str_replace($replace, $subject)
        }
        if (file_exists($path)) {
            if (defined('API_RUN') && API_RUN) print_rr(tcs_log("Loading function file: {$path}",'autoloader'));
            include_once($path);
            return true;
        }
        $tried_paths[$key] = $path;
    }
    if (defined('API_RUN') && API_RUN) print_rr(tcs_log('no function file found tried these paths' . print_rr($tried_paths),'autoloader'));
    return false;
}

/**
 * Function to load function files for all parts of a path
 * This is used by the router to load function files for all parts of the current path
 *
 * @param array $path_parts Array of path parts
 * @param string $current_page Current page name to skip
 * @return void
 */
function autobooks_load_path_functions($path_parts, $current_page = ''): void {
    if (!is_array($path_parts) || empty($path_parts)) return;

    foreach ($path_parts as $part) {
        // Skip the current page as it's already loaded
        if ($part === $current_page) continue;

        // Load the function file for this part
        autobooks_load_function_file($part);
    }
}

// Register the autoloader
spl_autoload_register('autobooks_autoloader');
