@props([
    'name' => 'template_file',
    'filetype' => '.html,.php,.txt',
    'uploadLocation' => 'uploads/templates/',
    'required' => false,
    'title' => 'File Upload Template',
    'description' => 'Upload a file that will be used as the template content.',
    'color' => 'purple',
    'label' => 'Upload Template File',
    'supportedTypes' => 'Supports HTML, PHP, and text files'
])

<div class="space-y-3">
    <div class="bg-{{ $color }}-50 border border-{{ $color }}-200 rounded-lg p-4">
        <h4 class="text-sm font-medium text-{{ $color }}-800 mb-2">{{ $title }}</h4>
        <p class="text-sm text-{{ $color }}-700 mb-3">{{ $description }}</p>

        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">{{ $label }}</label>
            <input
                type="file"
                name="{{ $name }}"
                accept="{{ $filetype }}"
                class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
                @if($required) required @endif
            />
        </div>
        
        <p class="text-xs text-{{ $color }}-600 mt-2">💡 {{ $supportedTypes }}</p>
    </div>
</div>
