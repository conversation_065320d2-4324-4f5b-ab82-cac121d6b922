@props([
    'table_info' => [],
    'table_index' => 0,
    'is_primary' => false,
    'table_alias' => ''
])

<div class="flex items-center p-4 bg-white border border-gray-200 rounded-lg shadow-sm" id="table-item-{{ $table_index }}">
    <div class="flex-1">
        <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
                @if($is_primary)
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 1.79 4 4 4h8c0-2.21-1.79-4-4-4H4V7z"></path>
                        </svg>
                    </div>
                @else
                    <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 1.79 4 4 4h8c0-2.21-1.79-4-4-4H4V7z"></path>
                        </svg>
                    </div>
                @endif
            </div>
            
            <div class="flex-1 min-w-0">
                <div class="flex items-center space-x-2">
                    <p class="text-sm font-medium text-gray-900 truncate">
                        {{ $table_info['display_name'] ?? $table_info['name'] }}
                    </p>
                    @if($is_primary)
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            Primary
                        </span>
                    @endif
                </div>
                <div class="flex items-center space-x-4 mt-1">
                    <p class="text-xs text-gray-500">
                        {{ $table_info['name'] }}
                    </p>
                    <p class="text-xs text-gray-500">
                        {{ number_format($table_info['row_count'] ?? 0) }} rows
                    </p>
                    <p class="text-xs text-gray-500">
                        {{ count($table_info['columns'] ?? []) }} columns
                    </p>
                </div>
            </div>
        </div>
        
        @if(!empty($table_info['description']))
            <p class="mt-2 text-xs text-gray-600">{{ $table_info['description'] }}</p>
        @endif

        <!-- Table Alias Input -->
        <div class="mt-3">
            <label class="block text-xs font-medium text-gray-700 mb-1">
                Table Alias (optional)
            </label>
            <x-forms-input
                name="table_aliases[{{ $table_info['name'] }}]"
                :value="$table_alias"
                placeholder="e.g., u, p, ord"
                class_suffix="text-sm"
                hx-post="{{ APP_ROOT }}/api/data_sources/update_query_preview"
                hx-target="#query-preview-container"
                hx-swap="innerHTML"
                hx-include="form"
                hx-trigger="input changed delay:500ms"
            />
            <p class="mt-1 text-xs text-gray-500">Short name to use in SQL queries (e.g., 'u' for users table)</p>
        </div>
    </div>
    
    <div class="flex-shrink-0 ml-4">
        @if(!$is_primary)
            <button type="button"
                    hx-delete="{{ APP_ROOT }}/api/data_sources/remove_table"
                    hx-target="#table-item-{{ $table_index }}"
                    hx-swap="outerHTML"
                    hx-vals='{"table_name": "{{ $table_info['name'] }}", "table_index": {{ $table_index }}}'
                    hx-include="#selected-tables-input"
                    hx-confirm="Remove this table from the data source?"
                    class="p-2 text-gray-400 hover:text-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </button>
        @else
            <div class="p-2 text-gray-300">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
            </div>
        @endif
    </div>
    
    <!-- Hidden input for this table -->
    <input type="hidden" name="tables[{{ $table_index }}]" value="{{ $table_info['name'] }}">
</div>
