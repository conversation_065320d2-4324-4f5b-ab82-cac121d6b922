@props([
    'columns' => [],
    'filter_operators' => [],
    'filter_index' => 0,
    'filter' => [
        'column' => '',
        'operator' => '=',
        'value' => ''
    ]
])

<div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-md" id="filter-row-{{ $filter_index }}" x-data="{ operator: '{{ $filter['operator'] }}' }">
    <div class="flex-1 grid grid-cols-1 gap-4 sm:grid-cols-3">
        <div>
            <label class="block text-xs font-medium text-gray-700">Column</label>
            @php
                $column_options = ['' => 'Select column...'];
                foreach ($columns as $column) {
                    $column_options[$column['Field']] = $column['Field'];
                }
            @endphp
            <x-forms-select
                name="filters[{{ $filter_index }}][column]"
                :options="$column_options"
                :selected="$filter['column']"
                class_suffix="text-sm"
                hx-post="{{ APP_ROOT }}/api/data_sources/update_query_preview"
                hx-target="#query-preview-container"
                hx-swap="innerHTML"
                hx-include="form"
                hx-trigger="change"
            />
        </div>

        <div>
            <label class="block text-xs font-medium text-gray-700">Operator</label>
            <x-forms-select
                name="filters[{{ $filter_index }}][operator]"
                :options="$filter_operators"
                :selected="$filter['operator']"
                class_suffix="text-sm"
                x-model="operator"
                hx-post="{{ APP_ROOT }}/api/data_sources/update_query_preview"
                hx-target="#query-preview-container"
                hx-swap="innerHTML"
                hx-include="form"
                hx-trigger="change"
            />
        </div>

        <div x-show="!['IS NULL', 'IS NOT NULL'].includes(operator)">
            <label class="block text-xs font-medium text-gray-700">Value</label>
            <x-forms-input
                name="filters[{{ $filter_index }}][value]"
                :value="$filter['value']"
                placeholder="Enter filter value"
                class="text-sm"
                hx-post="{{ APP_ROOT }}/api/data_sources/update_query_preview"
                hx-target="#query-preview-container"
                hx-swap="innerHTML"
                hx-include="form"
                hx-trigger="input changed delay:500ms"
            />
        </div>
    </div>
    
    <button type="button" 
            hx-delete="{{ APP_ROOT }}/api/data_sources/remove_filter_row"
            hx-target="#filter-row-{{ $filter_index }}"
            hx-swap="outerHTML"
            class="flex-shrink-0 p-2 text-red-600 hover:text-red-800">
        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
        </svg>
    </button>
</div>
