@props([
    'selected_tables' => [],
    'table_columns' => [],
    'existing_joins' => []
])

@if(count($selected_tables) < 2)
    <div class="text-center py-6 text-gray-500">
        Add at least 2 tables to configure joins
    </div>
@else
    <div class="space-y-4">
        <div class="flex items-center justify-between">
            <h4 class="text-sm font-medium text-gray-900">Table Relationships</h4>
            <button type="button"
                    hx-get="{{ APP_ROOT }}/api/data_sources/add_join_row"
                    hx-target="#joins-container"
                    hx-swap="beforeend"
                    hx-include="form"
                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Add Join
            </button>
        </div>
        
        <div id="joins-container" class="space-y-3">
            @if(empty($existing_joins))
                <div class="text-center py-4 text-gray-500" id="no-joins-message">
                    No joins configured. Tables will be cross-joined (Cartesian product).
                </div>
            @else
                @foreach($existing_joins as $index => $join)
                    <x-data-source-join-item
                        :join="$join"
                        :join_index="$index"
                        :selected_tables="$selected_tables"
                        :table_columns="$table_columns"
                    />
                @endforeach
            @endif
        </div>
        
        <!-- Join Types Reference -->
        <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <h5 class="text-xs font-medium text-blue-900 mb-2">Join Types:</h5>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-2 text-xs text-blue-800">
                <div><strong>INNER:</strong> Only matching records</div>
                <div><strong>LEFT:</strong> All from left table</div>
                <div><strong>RIGHT:</strong> All from right table</div>
            </div>
        </div>
        
        <!-- Joins are now stored in individual form fields -->
    </div>
@endif


