<?php
class
function data_table_filter_criteria($input) {
    print_rr($input, 'data_table_filter');
    $cols = $criteria = [];
    if (isset($input['column'])) {
        foreach ($input['column'] as $column => $value) {
            if (empty($value)) continue;
            print_rr($column, ' val: ' . $value);
            $col_parts = explode("_", $column, 2);
            $table = $col_parts[0];
            $column_name = $col_parts[1];
            $cols["{$table}.{$column_name}"] = ['=', $value];
        }
        if (count($cols) > 0) $criteria["where"] = $cols;
    }
    if (isset($input['search_terms']) && $input['search_terms'] != '') $criteria["search"] = $input['search_terms'];
    return $criteria;
}
?>