@props([
    'join' => [
        'type' => 'INNER',
        'left_table' => '',
        'left_column' => '',
        'right_table' => '',
        'right_column' => '',
        'left_alias' => '',
        'right_alias' => ''
    ],
    'join_index' => 0,
    'selected_tables' => [],
    'table_columns' => [],
    'join_types' => [
                        'INNER' => 'INNER',
                        'LEFT' => 'LEFT',
                        'RIGHT' => 'RIGHT'
                    ]
])

<div class="flex items-center space-x-5 p-4 bg-gray-50 border border-gray-400 rounded-lg" id="join-item-{{ $join_index }}">
    <div class="flex-1 space-y-4">
        <!-- First row: Join Type and Table selections -->
        <div class="grid grid-cols-5 gap-4 sm:grid-cols-5 relative ">
            <!-- Join Type -->
            <div class="absolute flex items-center bg-white border border-gray-400 rounded-lg -top-8 left-6 pl-2 gap-1">
                <label class="text-xs font-medium text-gray-700 ">Join Type</label>

                <x-forms-select
                        name="joins[{{ $join_index }}][type]"
                        :options="$join_types"
                        :selected="$join['type']"
                        class_suffix="sm:text-xs  sm:py-1 sm:px-2 sm:pr-8"
                        hx-post="{{ APP_ROOT }}/api/data_sources/update_query_preview"
                        hx-target="#query-preview-container"
                        hx-swap="innerHTML"
                        hx-include="form"
                        hx-trigger="change"
                />
                <!-- Remove Button -->
                <button type="button"
                        hx-delete="{{ APP_ROOT }}/api/data_sources/remove_join_row"
                        hx-target="#join-item-{{ $join_index }}"
                        hx-swap="outerHTML"
                        hx-vals='{"join_index": {{ $join_index }}}'
                        hx-confirm="Remove this join?"
                        class="flex-shrink-0 p-2 text-red-600 hover:text-red-800">
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Left Table -->
            <div>
                <label class="block text-xs font-medium text-gray-700">Left Table</label>
                @php
                    $table_options = ['' => 'Select table...'];
                    foreach ($selected_tables as $table) {
                        $table_options[$table] = $table;
                    }
                @endphp
                <x-forms-select
                        name="joins[{{ $join_index }}][left_table]"
                        :options="$table_options"
                        :selected="$join['left_table']"
                        class_suffix="text-sm"
                        hx-get="{{ APP_ROOT }}/api/data_sources/update_join_columns"
                        hx-target="#join-item-{{ $join_index }}"
                        hx-swap="outerHTML"
                        hx-include="form"
                        hx-vals='{"join_index": {{ $join_index }}, "field": "left_table"}'
                        hx-trigger="change"
                />
            </div>

            <!-- Left Column -->
            <div>
                <label class="block text-xs font-medium text-gray-700">Left Column</label>
                @php
                    $left_columns = ['' => 'Select column...'];
                    if (!empty($join['left_table']) && isset($table_columns[$join['left_table']])) {
                        foreach ($table_columns[$join['left_table']] as $column) {
                            $left_columns[$join['left_table'] . '.' . $column['Field']] = $column['Field'];
                        }
                    }
                @endphp
                <x-forms-select
                        name="joins[{{ $join_index }}][left_column]"
                        :options="$left_columns"
                        :selected="$join['left_column']"
                        class_suffix="text-sm"
                        hx-post="{{ APP_ROOT }}/api/data_sources/update_query_preview"
                        hx-target="#query-preview-container"
                        hx-swap="innerHTML"
                        hx-include="form"
                        hx-trigger="change"
                />
            </div>
                     <!-- Right Table -->
            <div>
                <label class="block text-xs font-medium text-gray-700">Right Table</label>
                <x-forms-select
                        name="joins[{{ $join_index }}][right_table]"
                        :options="$table_options"
                        :selected="$join['right_table']"
                        class_suffix="text-sm"
                        hx-get="{{ APP_ROOT }}/api/data_sources/update_join_columns"
                        hx-target="#join-item-{{ $join_index }}"
                        hx-swap="outerHTML"
                        hx-include="form"
                        hx-vals='{"join_index": {{ $join_index }}, "field": "right_table"}'
                        hx-trigger="change"
                />
            </div>

            <!-- Right Column -->
            <div>
                <label class="block text-xs font-medium text-gray-700">Right Column</label>
                @php
                    $right_columns = ['' => 'Select column...'];
                    if (!empty($join['right_table']) && isset($table_columns[$join['right_table']])) {
                        foreach ($table_columns[$join['right_table']] as $column) {
                            $right_columns[$join['right_table'] . '.' . $column['Field']] = $column['Field'];
                        }
                    }
                @endphp
                <x-forms-select
                        name="joins[{{ $join_index }}][right_column]"
                        :options="$right_columns"
                        :selected="$join['right_column']"
                        class_suffix="text-sm"
                        hx-post="{{ APP_ROOT }}/api/data_sources/update_query_preview"
                        hx-target="#query-preview-container"
                        hx-swap="innerHTML"
                        hx-include="form"
                        hx-trigger="change"
                />
            </div>

           <div>
                <!-- join Alias -->

                    <label class="block text-xs font-medium text-gray-700">join Alias (optional)</label>
                    <x-forms-input
                            name="joins[{{ $join_index }}][right_alias]"
                            :value="$join['right_alias'] ?? ''"
                            placeholder="e.g., p"
                            class_suffix="text-sm"
                            hx-post="{{ APP_ROOT }}/api/data_sources/update_query_preview"
                            hx-target="#query-preview-container"
                            hx-swap="innerHTML"
                            hx-include="form"
                            hx-trigger="input changed delay:500ms"
                    />


            </div>
        </div>



        <!-- Hidden inputs for this join -->
        <input type="hidden" name="joins[{{ $join_index }}][table]" value="{{ $join['right_table'] ?? '' }}">
    </div>
</div>