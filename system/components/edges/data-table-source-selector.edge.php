@props([
    'table_name' => '',
    'current_data_source_type' => 'hardcoded',
    'current_data_source_id' => null,
    'callback' => '',
    'show_preview' => true
])

@php
    use system\data_source_manager;

    // Get available data sources
    $available_data_sources = [];
    try {
        $data_sources = database::table('autobooks_data_sources')
            ->where('status', '=', 'active')
            ->get();
        
        foreach ($data_sources as $source) {
            $available_data_sources[] = [
                'id' => $source['id'],
                'name' => $source['name'],
                'description' => $source['description'],
                'category' => $source['category'],
                'table_name' => $source['table_name']
            ];
        }
    } catch (Exception $e) {
        // Handle error gracefully
        $available_data_sources = [];
    }
    
    // Group data sources by category
    $grouped_sources = [];
    foreach ($available_data_sources as $source) {
        $category = $source['category'] ?? 'other';
        if (!isset($grouped_sources[$category])) {
            $grouped_sources[$category] = [];
        }
        $grouped_sources[$category][] = $source;
    }
    
    $category_labels = [
        'data_table' => 'Data Tables',
        'email' => 'Email & Campaigns',
        'users' => 'User Management',
        'system' => 'System Tables',
        'autodesk' => 'Autodesk Integration',
        'other' => 'Other'
    ];
@endphp

<div class="data-table-source-selector bg-white border border-gray-200 rounded-lg p-4 mb-4" 
     x-data="{
         dataSourceType: '{{ $current_data_source_type }}',
         dataSourceId: {{ $current_data_source_id ?? 'null' }},
         showPreview: false,
         previewData: null,
         previewLoading: false
     }">
    
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Data Source Configuration</h3>
        <div class="flex items-center space-x-2">
            <span class="text-sm text-gray-500">Current:</span>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="dataSourceType === 'hardcoded' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'">
                <span x-text="dataSourceType === 'hardcoded' ? 'Hardcoded Data' : 'Data Source'"></span>
            </span>
        </div>
    </div>
    
    <!-- Data Source Type Selection -->
    <div class="space-y-4">
        <div>
            <label class="text-base font-medium text-gray-900">Data Source Type</label>
            <p class="text-sm leading-5 text-gray-500">Choose how data is provided to this table</p>
            <fieldset class="mt-4">
                <div class="space-y-4">
                    <!-- Hardcoded Data Option -->
                    <div class="flex items-center">
                        <input id="hardcoded" 
                               name="data_source_type" 
                               type="radio" 
                               value="hardcoded"
                               x-model="dataSourceType"
                               class="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500">
                        <label for="hardcoded" class="ml-3 block text-sm font-medium leading-6 text-gray-900">
                            Hardcoded Data
                        </label>
                    </div>
                    <p class="ml-7 text-sm text-gray-500">Use data provided directly in the code (current method)</p>
                    
                    <!-- Data Source Option -->
                    <div class="flex items-center">
                        <input id="data_source" 
                               name="data_source_type" 
                               type="radio" 
                               value="data_source"
                               x-model="dataSourceType"
                               class="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500">
                        <label for="data_source" class="ml-3 block text-sm font-medium leading-6 text-gray-900">
                            Database Data Source
                        </label>
                    </div>
                    <p class="ml-7 text-sm text-gray-500">Use a configured data source with filters and joins</p>
                </div>
            </fieldset>
        </div>
        
        <!-- Data Source Selection (shown when data_source is selected) -->
        <div x-show="dataSourceType === 'data_source'" x-transition class="space-y-4">
            <div>
                <label for="data_source_select" class="block text-sm font-medium leading-6 text-gray-900">
                    Select Data Source
                </label>
                <select id="data_source_select" 
                        x-model="dataSourceId"
                        class="mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-blue-600 sm:text-sm sm:leading-6">
                    <option value="">Choose a data source...</option>
                    @foreach($grouped_sources as $category => $sources)
                        <optgroup label="{{ $category_labels[$category] ?? ucfirst($category) }}">
                            @foreach($sources as $source)
                                <option value="{{ $source['id'] }}" 
                                        {{ $current_data_source_id == $source['id'] ? 'selected' : '' }}>
                                    {{ $source['name'] }}
                                    @if($source['description'])
                                        - {{ $source['description'] }}
                                    @endif
                                </option>
                            @endforeach
                        </optgroup>
                    @endforeach
                </select>
            </div>
            
            @if($show_preview)
            <!-- Preview Button -->
            <div class="flex items-center space-x-3">
                <button type="button"
                        @click="if(dataSourceId) { showPreview = true; previewLoading = true; 
                                htmx.ajax('GET', '{{ APP_ROOT }}/api/data_table_storage/preview_data_source', {
                                    values: {data_source_id: dataSourceId, limit: 5},
                                    target: '#preview-container',
                                    swap: 'innerHTML'
                                }).then(() => previewLoading = false); }"
                        :disabled="!dataSourceId"
                        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
                    <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    Preview Data
                </button>
                
                <a href="{{ APP_ROOT }}/system/data_sources" 
                   target="_blank"
                   class="text-sm text-blue-600 hover:text-blue-500">
                    Manage Data Sources →
                </a>
            </div>
            @endif
        </div>
    </div>
    
    @if($show_preview)
    <!-- Preview Container -->
    <div x-show="showPreview" x-transition class="mt-6 border-t border-gray-200 pt-4">
        <div class="flex items-center justify-between mb-3">
            <h4 class="text-sm font-medium text-gray-900">Data Preview</h4>
            <button @click="showPreview = false" class="text-gray-400 hover:text-gray-500">
                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        
        <div id="preview-container" class="bg-gray-50 rounded-md p-3 min-h-[100px]">
            <div x-show="previewLoading" class="flex items-center justify-center py-8">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span class="ml-2 text-sm text-gray-600">Loading preview...</span>
            </div>
        </div>
    </div>
    @endif
    
    <!-- Save Button -->
    <div class="mt-6 flex justify-end space-x-3">
        <button type="button"
                hx-post="{{ APP_ROOT }}/api/data_table_storage/update_data_source"
                hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}"}'
                hx-include="closest .data-table-source-selector"
                hx-target=".data_table"
                hx-swap="outerHTML"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            Apply Data Source
        </button>
    </div>
    
    <!-- Hidden inputs for HTMX -->
    <input type="hidden" name="data_source_type" :value="dataSourceType">
    <input type="hidden" name="data_source_id" :value="dataSourceId">
</div>
