@props([
    'campaign' => [],
    'history' => []
])

<div class="space-y-6">
    <!-- Header -->
    <div class="border-b border-gray-200 pb-4">
        <h2 class="text-lg font-medium text-gray-900">Send History: {{ $campaign['name'] }}</h2>
        <p class="mt-1 text-sm text-gray-600">
            View detailed send history and performance metrics for this campaign.
        </p>
    </div>

    <!-- Campaign Summary -->
    <div class="bg-gray-50 rounded-lg p-4">
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-4">
            <div class="text-center">
                <div class="text-2xl font-bold text-gray-900">{{ $campaign['total_sent'] ?? 0 }}</div>
                <div class="text-sm text-gray-500">Total Sent</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-green-600">{{ $campaign['total_delivered'] ?? 0 }}</div>
                <div class="text-sm text-gray-500">Delivered</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-red-600">{{ $campaign['total_failed'] ?? 0 }}</div>
                <div class="text-sm text-gray-500">Failed</div>
            </div>
            <div class="text-center">
                @php
                    $total = $campaign['total_sent'] ?? 0;
                    $delivered = $campaign['total_delivered'] ?? 0;
                    $rate = $total > 0 ? round(($delivered / $total) * 100, 1) : 0;
                @endphp
                <div class="text-2xl font-bold text-blue-600">{{ $rate }}%</div>
                <div class="text-sm text-gray-500">Delivery Rate</div>
            </div>
        </div>
    </div>

    <!-- History Table -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Send History</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                Last {{ count($history) }} email sends for this campaign.
            </p>
        </div>
        
        @if(empty($history))
        <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No emails sent</h3>
            <p class="mt-1 text-sm text-gray-500">This campaign hasn't sent any emails yet.</p>
        </div>
        @else
        <ul class="divide-y divide-gray-200">
            @foreach($history as $record)
            <li class="px-4 py-4 sm:px-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            @php
                                $status_colors = [
                                    'sent' => 'bg-blue-100 text-blue-800',
                                    'delivered' => 'bg-green-100 text-green-800',
                                    'failed' => 'bg-red-100 text-red-800',
                                    'bounced' => 'bg-yellow-100 text-yellow-800',
                                    'pending' => 'bg-gray-100 text-gray-800'
                                ];
                                $color_class = $status_colors[$record['send_status']] ?? 'bg-gray-100 text-gray-800';
                            @endphp
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $color_class }}">
                                {{ ucfirst($record['send_status']) }}
                            </span>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">
                                {{ $record['recipient_email'] }}
                            </div>
                            <div class="text-sm text-gray-500">
                                @if($record['recipient_name'])
                                    {{ $record['recipient_name'] }} • 
                                @endif
                                {{ $record['subject'] ?? 'No subject' }}
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center text-sm text-gray-500">
                        <div class="text-right">
                            <div>{{ date('M j, Y H:i', strtotime($record['sent_at'] ?? $record['created_at'])) }}</div>
                            @if($record['triggered_by_name'])
                                <div class="text-xs">by {{ $record['triggered_by_name'] }}</div>
                            @endif
                            @if($record['triggered_by_rule'] && $record['triggered_by_rule'] !== 'manual_test')
                                <div class="text-xs">Rule: {{ $record['triggered_by_rule'] }}</div>
                            @endif
                        </div>
                    </div>
                </div>
                
                @if($record['send_result'] && $record['send_status'] === 'failed')
                <div class="mt-2 text-sm text-red-600 bg-red-50 p-2 rounded">
                    <strong>Error:</strong> {{ $record['send_result'] }}
                </div>
                @endif
            </li>
            @endforeach
        </ul>
        @endif
    </div>

    <!-- Export Options -->
    <div class="bg-white shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Export History</h3>
            <div class="mt-2 max-w-xl text-sm text-gray-500">
                <p>Download the complete send history for this campaign.</p>
            </div>
            <div class="mt-5 flex space-x-3">
                <button type="button"
                        hx-get="{{ APP_ROOT }}/api/email_campaigns/export_history"
                        hx-vals='{"id": {{ $campaign['id'] }}, "format": "csv"}'
                        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export CSV
                </button>

                <button type="button"
                        hx-get="{{ APP_ROOT }}/api/email_campaigns/export_history"
                        hx-vals='{"id": {{ $campaign['id'] }}, "format": "excel"}'
                        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export Excel
                </button>
            </div>
        </div>
    </div>

    <!-- Close Button -->
    <div class="flex justify-end">
        <button type="button"
                @click="showCampaignModal = false"
                class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            Close
        </button>
    </div>
</div>
