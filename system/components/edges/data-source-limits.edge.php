@props([
    'limits' => [
        'enabled' => false,
        'limit' => '',
        'offset' => ''
    ]
])

<div class="space-y-4">
    <!-- Header with Enable Toggle -->
    <div class="flex items-center justify-between">
        <div>
            <h3 class="text-sm font-medium text-gray-900">Result Limits</h3>
            <p class="text-xs text-gray-500 mt-1">Control how many results are returned</p>
        </div>
        <label class="inline-flex items-center">
            <input type="checkbox" 
                   name="limits[enabled]" 
                   value="1"
                   {{ ($limits['enabled'] ?? false) ? 'checked' : '' }}
                   hx-post="{{ APP_ROOT }}/api/data_sources/update_query_preview"
                   hx-target="#query-preview-container"
                   hx-swap="innerHTML"
                   hx-include="form"
                   hx-trigger="change"
                   class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
            <span class="ml-2 text-sm text-gray-700">Enable limits</span>
        </label>
    </div>

    <!-- Limits Configuration -->
    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 {{ ($limits['enabled'] ?? false) ? '' : 'opacity-50 pointer-events-none' }}">
        <!-- Limit (Number of Records) -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
                Limit <span class="text-gray-500">(max records)</span>
            </label>
            <x-forms-input
                name="limits[limit]"
                :value="$limits['limit'] ?? ''"
                type="number"
                min="1"
                placeholder="e.g., 100, 1000"
                class_suffix="text-sm"
                hx-post="{{ APP_ROOT }}/api/data_sources/update_query_preview"
                hx-target="#query-preview-container"
                hx-swap="innerHTML"
                hx-include="form"
                hx-trigger="input changed delay:500ms"
            />
            <p class="mt-1 text-xs text-gray-500">
                Maximum number of records to return
            </p>
        </div>

        <!-- Offset (Skip Records) -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
                Offset <span class="text-gray-500">(skip records)</span>
            </label>
            <x-forms-input
                name="limits[offset]"
                :value="$limits['offset'] ?? ''"
                type="number"
                min="0"
                placeholder="e.g., 0, 50, 100"
                class_suffix="text-sm"
                hx-post="{{ APP_ROOT }}/api/data_sources/update_query_preview"
                hx-target="#query-preview-container"
                hx-swap="innerHTML"
                hx-include="form"
                hx-trigger="input changed delay:500ms"
            />
            <p class="mt-1 text-xs text-gray-500">
                Number of records to skip (for pagination)
            </p>
        </div>
    </div>

    <!-- Examples Section -->
    <div class="mt-4 p-4 bg-green-50 rounded-lg">
        <h4 class="text-sm font-medium text-green-900 mb-2">Common Use Cases:</h4>
        <div class="space-y-2 text-xs text-green-800">
            <div>
                <strong>Top 10 results:</strong> 
                <span class="text-green-600">Limit: 10, Offset: 0</span>
            </div>
            <div>
                <strong>Pagination (page 2, 25 per page):</strong> 
                <span class="text-green-600">Limit: 25, Offset: 25</span>
            </div>
            <div>
                <strong>Skip first 100, get next 50:</strong> 
                <span class="text-green-600">Limit: 50, Offset: 100</span>
            </div>
            <div>
                <strong>Sample data (first 1000):</strong> 
                <span class="text-green-600">Limit: 1000, Offset: 0</span>
            </div>
        </div>
    </div>

    <!-- Performance Warning -->
    @if(!($limits['enabled'] ?? false))
        <div class="p-3 bg-yellow-50 rounded-lg">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">Performance Tip</h3>
                    <div class="mt-2 text-sm text-yellow-700">
                        <p>Without limits, queries may return very large result sets. Consider enabling limits for better performance.</p>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
