@props([
    'title' => '',
    'subtitle' => '',
    'metadata' => [],
    'actions' => '',
    'class' => 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6'
])

@php
// Parse slots from tag content if it exists
$slotContent = isset($tag_content) && is_string($tag_content) ? $tag_content : '';
$slots = slot_helper::parse($slotContent);
$actionsSlot = slot_helper::get($slots, 'actions', $actions);
@endphp

<div class="{{ $class }}">
    <div class="bg-white shadow-sm rounded-lg mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div class="flex-1 min-w-0">
                    <h1 class="text-2xl font-bold text-gray-900 truncate">
                        {{ $title }}
                    </h1>
                    @if($subtitle)
                        <p class="mt-1 text-sm text-gray-500">{{ $subtitle }}</p>
                    @endif
                    @if(!empty($metadata))
                        <div class="mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:space-x-6">
                            @foreach($metadata as $item)
                                <div class="mt-2 flex items-center text-sm text-gray-500">
                                    @if(isset($item['label']))
                                        <span class="font-medium">{{ $item['label'] }}:</span>
                                    @endif
                                    <span class="ml-1 {{ $item['class'] ?? '' }}">{{ $item['value'] ?? $item }}</span>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
                @if($actionsSlot)
                    <div class="mt-4 sm:mt-0 sm:ml-4 flex space-x-2">
                        {!! $actionsSlot !!}
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
