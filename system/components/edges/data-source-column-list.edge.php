@props([
    'columns' => []
])

@if(empty($columns))
    <div class="text-center py-6 text-gray-500">
        No columns available
    </div>
@else
    <div class="mt-6">
        <h3 class="text-sm font-medium text-gray-900 mb-3">Available Columns</h3>
        <div class="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
            @foreach($columns as $column)
                <div class="flex items-center p-2 bg-gray-50 rounded-md">
                    <div class="flex-1">
                        <div class="text-sm font-medium text-gray-900">{{ $column['Field'] }}</div>
                        <div class="text-xs text-gray-500">{{ $column['Type'] }}</div>
                    </div>
                    @if($column['Key'] === 'PRI')
                        <div class="ml-2">
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                Primary
                            </span>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>
    </div>
@endif
