@props([
    'title' => 'main view',
    'description' => '',
    'view' => 'dashboard'
])
<x-layout-head />
<body class="h-full" x-data="{showModal: false}" x-bind:class="showModal && 'overflow-hidden'">
@php
    print_rr($view,'launched main edge view with:');
@endphp
    <x-notification-handler />
    <div class="bg-gray-100">
        <div x-data="sidebarState()" x-init="initialize()" @keydown.window.escape="open = false">
            <div class="hidden lg:w-56 lg:fixed lg:inset-y-0 lg:z-40 lg:flex lg:flex-col">
                <x-layout-sidebar />
            </div>
            <div id="content_wrapper" class="h-screen flex flex-col transition-all duration-300" :class='collapsed ? "lg:pl-11" : "lg:pl-44"'>
                <!--  Edge::render('navbar');  -->
                @php
                    print_rr($view,'main edge view');
                @endphp
                <x-layout-view :view='$view' :view_content='$view_content ?? null' />
            </div>
            <x-layout-footer />
        </div>
    </div>
<script>function sidebarState() {return {collapsed: false,initialize() {const cookieValue = document.cookie.split('; ').find(row => row.startsWith('sidebar-collapsed='))?.split('=')[1];this.collapsed = cookieValue === 'true';},toggle() {this.collapsed = !this.collapsed;document.cookie = `sidebar-collapsed=${this.collapsed}; path=/; max-age=31536000`;},};}</script>