@props([
    'title' => 'card',
    'description' => 'A layout card',
    'content' => [], // An array of [('header'|'body'|'footer') => 'content']
    'title' => 'Users',
    'description' => 'A list of all the users in your account including their name, title, email and role.',
    'addButtonText' => 'Add user',
    'grouped_data' => [], // An array of groups, each group having 'name' and 'items'
    'columns' => [] // An array of column definitions: ['label' => 'Name', 'field' => 'name', 'filter' => false]
])


<div class="relative z-50" aria-labelledby="modal-title" role="dialog" aria-modal="true" x-show="showModal" style="display:none;">
    <div class="bg-opacity-75 fixed inset-0 bg-gray-500 transition-opacity" aria-hidden="true"
         x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"></div>
    <div class="w-screen overflow-y-auto fixed inset-0 z-50">
        <div class="min-h-full flex justify-center items-end p-4 text-center sm:items-center sm:p-0">
            <div class="transform overflow-hidden relative text-left bg-white rounded-lg shadow-xl transition-all sm:w-full sm:m-8xl sm:my-8"
                 @click.away="showModal = false" x-transition:enter="ease-out duration-300"
                 x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100">
                <div class="px-4 pt-5 pb-4 bg-white sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div id="modal_body" class="w-full mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left"></div>
                    </div>
                </div>
                <!--                    <div class="px-4 py-3 bg-gray-50 sm:flex sm:flex-row-reverse sm:px-6">-->
                <!--                        <button type="button" class="w-full inline-flex justify-center px-3 py-2 text-sm font-semibold text-white bg-red-600 rounded-md shadow-sm hover:bg-red-500 sm:w-auto sm:ml-3">Deactivate</button>-->
                <!--                        <button type="button" class="w-full inline-flex justify-center px-3 py-2 mt-3 text-sm font-semibold text-gray-900 bg-white rounded-md ring-1 ring-inset ring-gray-300 shadow-sm hover:bg-gray-50 sm:w-auto sm:mt-0">Cancel</button>-->
                <!--                    </div>-->
            </div>
        </div>
    </div>
</div>

