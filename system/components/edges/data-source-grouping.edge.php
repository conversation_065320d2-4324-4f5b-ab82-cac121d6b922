@props([
    'grouping' => [],
    'available_columns' => []
])

<div class="space-y-4">
    <!-- Header with Add Button -->
    <div class="flex items-center justify-between">
        <div>
            <h3 class="text-sm font-medium text-gray-900">Grouping (GROUP BY)</h3>
            <p class="text-xs text-gray-500 mt-1">Group results by one or more columns</p>
        </div>
        <button type="button"
                hx-post="{{ APP_ROOT }}/api/data_sources/add_group_row"
                hx-target="#grouping-container"
                hx-swap="beforeend"
                hx-include="form"
                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            Add Group
        </button>
    </div>

    <!-- Grouping Container -->
    <div id="grouping-container" class="space-y-3">
        @if(empty($grouping))
            <div id="no-grouping-message" class="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H5m14 14H5"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No grouping rules</h3>
                <p class="mt-1 text-sm text-gray-500">Results will not be grouped. Add columns to group by.</p>
            </div>
        @else
            @foreach($grouping as $index => $group)
                <x-data-source-grouping-item
                    :group="$group"
                    :group_index="$index"
                    :available_columns="$available_columns"
                />
            @endforeach
        @endif
    </div>

    <!-- Info Section -->
    <div class="mt-4 p-3 bg-blue-50 rounded-lg">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">About Grouping</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <p>GROUP BY combines rows with identical values in specified columns into summary rows.</p>
                    <p class="mt-1">When using GROUP BY, all selected columns must either be in the GROUP BY clause or use aggregate functions (COUNT, SUM, AVG, etc.).</p>
                    <p class="mt-1">Use custom columns with aggregate functions like COUNT(*), SUM(column), AVG(column) for calculations.</p>
                </div>
            </div>
        </div>
    </div>
</div>
