@props([
    'title' => 'Users',
    'description' => 'A list of all the users in your account including their name, title, email and role.',
    'items' => [], // data array of items
    'id_count' => edge::id_count(),
    'columns' => [], // An array of column definitions: ['label' => 'Name', 'field' => 'name', 'filter' => false]
    'available_fields' => [], // Array of available fields for column manager
    'rows' => [
       'id_prefix' => 'row_',
       'id_field' => 'id',
       'class_postfix' => '',
       'extra_parameters' => ''
    ],
    'just_rows' => false, // just return rows
    'just_body' => false, // just return body
    'just_table' => false, // just return table no extras
    'items_per_page' => 30, //max items to display before pagination
    'current_page_num' => 1,
    'class' => '', //extra classes
    'sort_column' => '',
    'sort_direction' => '',
    'auto_update' => false,
    'callback' => null,
    'table_name' => str_replace('/','_',strtolower(APP_PATH . '_' . CURRENT_PAGE)),
    'db_table' => '',
    'show_column_manager' => true,
    'column_preferences' => [],
    'criteria' => [],
    'config' => null

])

@php
    // Auto-load data from data source if no items provided and no callback
    if (empty($items) && empty($callback)) {
        $user_id = $_SESSION['user_id'] ?? null;

        // Try to get data from data source
        $data_result = \data_table_storage::get_table_data($table_name, [], $criteria, $user_id);
        $columns = count($columns) > 0 ? $columns : $data_result['config']['columns'];
        print_rr($columns,'colsy');
        foreach ($columns as $col) {
            $available_fields[] = $col['field'];
        }
        // Debug auto-loading
        tcs_log("Auto-loading data: table=$table_name, success=" . ($data_result['success'] ? 'true' : 'false') . ", source=" . ($data_result['source'] ?? 'unknown') . ", count=" . ($data_result['count'] ?? 0), 'data_table_saga');
        print_rr($columns,'colsy');
        // Load data if successful, regardless of source type (could be data_source, hardcoded, or hardcoded_fallback)
        if ($data_result['success']) {
            $items = $data_result['data'];

            // Auto-generate columns from data source if no columns provided or default columns
            // Only do this if we have data and are using default columns
            if (!empty($items) && empty($columns)) {
                $columns = [];
                $available_fields = [];
                // Get first item to determine columns
                $first_item = reset($items);
                if ($first_item) {
                    foreach (array_keys($first_item) as $field) {
                        $columns[] = [
                            'label' => ucwords(str_replace('_', ' ', $field)),
                            'field' => $field,
                            'filter' => false,
                            'extra_parameters' => ''
                        ];
                        $available_fields[] = $field;
                    }
                }
            }
        }

        // Get column preferences for this table
        $config = $data_result['config'] ?? null;
        $column_preferences = $config;

    }

    // Get configuration for determining current data source (outside the auto-loading block)
    $user_id = $_SESSION['user_id'] ?? null;
    if (!$config) {
        $config = \data_table_storage::get_configuration($table_name, $user_id);
    }

    // Ensure column_preferences is properly set
    // Priority: 1) Passed in column_preferences, 2) Config from database, 3) Fresh from API
    if (!isset($column_preferences) || empty($column_preferences)) {
        if ($config && isset($config['configuration'])) {
            $column_preferences = $config['configuration'];
        } else {
            // If no column preferences passed in and no config, get fresh from database
          // $column_preferences = get_column_preferences($table_name);
        }
    }

    // Determine current data source information for column manager
    $current_data_source_type = 'hardcoded';
    $current_data_source_id = null;

    if ($config) {
        // Check database field first (most reliable)
        if (!empty($config['data_source_id'])) {
            $current_data_source_type = 'data_source';
            $current_data_source_id = $config['data_source_id'];
        } elseif (!empty($column_preferences)) {
            // Fallback to configuration JSON
            $current_data_source_type = $column_preferences['data_source_type'] ?? 'hardcoded';
            $current_data_source_id = $column_preferences['data_source_id'] ?? null;
        }
    }

    // Debug output
    tcs_log("Data table template: table=$table_name, type=$current_data_source_type, id=$current_data_source_id", 'data_table_saga');

    print_rr([
        'data_table_template_start' => [
            'table_name' => $table_name,
            'has_column_preferences' => !empty($column_preferences),
            'hidden_count' => count($column_preferences['hidden'] ?? []),
            'columns_count' => count($columns),
            'show_column_manager' => $show_column_manager ?? 'not set'
        ]
    ], 'data_table_template_start');
@endphp

@if (!$just_body && !$just_rows && !$just_table)

    @if($auto_update)
        <x-forms-input
           type='hidden'
           name='last_update'
           :value='date("Y-m-d H:i:s")'
           hx-post='{{ APP_ROOT }}/api/system/update'
           hx-swap='outerHTML'
           hx-trigger='every 10s'
        />
    @endif
    <input type="hidden" class="data_table_filter" name="callback" value="{{ $callback }}">
    @if($table_name)
        <input type="hidden" class="data_table_filter" name="table_name" value="{{ $table_name }}">
    @endif
    <div class="relative">
        @if($show_column_manager)
            <div class="absolute top-0 right-0 z-10 p-2">
                <x-data-table-column-manager
                    :columns="$columns"
                    :table_name="$table_name"
                    :callback="$callback"
                    :column_preferences="$column_preferences"
                    :db_table="$db_table"
                    :available_fields="$available_fields"
                    :current_data_source_type="$current_data_source_type"
                    :current_data_source_id="$current_data_source_id"
                ></x-data-table-column-manager>
            </div>
        @endif
@endif
@if (!$just_body && !$just_rows)
        <table class="min-w-full border-collapse search_target data_table {{ $class }}" >
            <thead>
                <tr>
                    @php
                        print_rr([
                            'before_column_processing' => [
                                'column_preferences' => $column_preferences,
                                'hidden_count' => count($column_preferences['hidden'] ?? []),
                                'first_hidden_id' => ($column_preferences['hidden'][0] ?? 'none')
                            ]
                        ], 'before_column_processing');
                    @endphp
                    @foreach($columns as $col)
                        @php
                            // Use column ID from structure by matching index (columns and structure should be in same order)
                            $structure_col = $column_preferences['structure'][$loop->index] ?? null;

                            if ($structure_col) {
                                $column_id = $structure_col['id'];
                                $is_hidden = !$structure_col['visible'];
                            } else {
                                // Fallback to old method if no structure match
                                $column_id = 'col_' . $loop->index . '_' . md5($col['label']);
                                $is_hidden = isset($column_preferences['hidden']) && in_array($column_id, $column_preferences['hidden']);
                            }

                            // Debug first few columns
                            if ($loop->index < 3) {
                                print_rr([
                                    'column_debug_index' => [
                                        'index' => $loop->index,
                                        'col_label' => $col['label'],
                                        'structure_label' => $structure_col['label'] ?? 'none',
                                        'column_id' => $column_id,
                                        'is_hidden' => $is_hidden,
                                        'structure_visible' => $structure_col['visible'] ?? 'n/a'
                                    ]
                                ], 'column_debug_index_' . $loop->index);
                            }
                        @endphp
                        @if(!$is_hidden)
                            <th scope="col"
                                class="{{ $loop->first ? 'relative sticky top-0 border-b border-gray-300 bg-gray-200 py-1.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 lg:pl-8' : 'relative sticky top-0 hidden border-b border-gray-300 bg-gray-200 px-3 py-1.5 text-left text-sm font-semibold text-gray-900 sm:table-cell' }}"
                                style="isolation: isolate;"
                                data-column-field="{{ $col['field'] }}">
                                <x-data-table-filter
                                   :label="$col['label']"
                                   :col="$col"
                                   :sort_col="$sort_column"
                                   :sort_dir="$sort_direction"
                                   :callback="$callback"
                                   :id_count="$id_count"
                                ></x-data-table-filter>
                            </th>
                        @endif
                    @endforeach
                </tr>
            </thead>
@endif <!-- just_body & rows -->
@if (!$just_rows)
<tbody class="bg-white data_table_body">
@endif <!-- just rows -->
    @foreach ($items as $item)
        @if ($loop->first)
           {{ print_rr($item,'itamage') }}
        @endif
        <tr class="border-t {{ $loop->first ? 'border-gray-300' : 'border-gray-200' }} {{ $rows['class_postfix'] }}" id="{{ $rows['id_prefix'] . $item[$rows['id_field']] . $rows['id_postfix'] }}" {{ $rows['extra_parameters'] }}>
            @foreach ($columns as $col)
                @php
                    // Use column ID from structure by matching index (columns and structure should be in same order)
                    $structure_col = $column_preferences['structure'][$loop->index] ?? null;

                    if ($structure_col) {
                        $column_id = $structure_col['id'];
                        $is_hidden = !$structure_col['visible'];
                    } else {
                        // Fallback to old method if no structure match
                        $column_id = 'col_' . $loop->index . '_' . md5($col['label']);
                        $is_hidden = isset($column_preferences['hidden']) && in_array($column_id, $column_preferences['hidden']);
                    }
                @endphp
                @if(!$is_hidden)
                    @if ($col['replacements'])
                        @php $item[$col['field']] = str_replace(array_keys($col['replacements']),$col['replacements'],$item[$col['field']]) @endphp
                    @endif
                    <td class="{{ $loop->first ? 'whitespace-nowrap border-b border-gray-200 py-2 pl-2 pr-1 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8' : 'whitespace-nowrap border-b border-gray-200 hidden px-2 py-2 text-sm text-gray-500 sm:table-cell' }}"
                        data-column-field="{{ $col['field'] }}">
                        @if (isset($col['content']))
                            @if (is_callable($col['content']))
                                {!! $col['content']($item,$loop->first) !!}
                            @else
                                {!! $col['content'] ?? 'content' !!}
                            @endif
                        @elseif (isset($col['field']))
                            @if (is_array($col['field']))
                                {!! implode('<br>', array_map(fn($f) => $item[$f] ?? '', $col['field'])) !!}
                            @elseif (is_string($col['field']))
                                {{ $item[$col['field']] ?? '' }}
                            @endif
                        @endif
                    </td>
                @endif
            @endforeach
        </tr>
        @if ($items_per_page > 1 && $loop->iteration > $items_per_page )
            @break
        @endif
    @endforeach
@if (!$just_rows)
</tbody>
@endif
@if (!$just_body && !$just_rows)
        <tfoot>
            <tr>
                <td colspan="{{ count($columns) }}">
                    <x-pagination-strip
                        :item_count="isset($total_count) ? $total_count : count($items)"
                        :items_per_page="$items_per_page"
                        :current_page_num="$current_page_num"
                        :first_item="($current_page_num - 1) * $items_per_page"
                    ></x-pagination-strip>
                </td>
            </tr>
        </tfoot>
        </table>
    </div>
@endif
