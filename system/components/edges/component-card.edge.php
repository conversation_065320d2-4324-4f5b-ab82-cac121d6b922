@props([
    'title' => '',
    'description' => '',
    'headerClass' => '',
    'contentClass' => '',
    'cardClass' => 'bg-white shadow-sm rounded-lg',
    'showBorder' => true,
    'actions' => ''
])

<div class="{{ $cardClass }}">
    @if($title || $description || $actions)
        <div class="px-6 py-4 {{ $showBorder ? 'border-b border-gray-200' : '' }} {{ $headerClass }}">
            <div class="flex items-center justify-between">
                <div class="flex-1 min-w-0">
                    @if($title)
                        <h2 class="text-lg font-medium text-gray-900">{{ $title }}</h2>
                    @endif
                    @if($description)
                        <p class="mt-1 text-sm text-gray-500">{{ $description }}</p>
                    @endif
                </div>
                @if($actions)
                    <div class="flex items-center space-x-2">
                        {!! $actions !!}
                    </div>
                @endif
            </div>
        </div>
    @endif

    <div class="px-6 py-4 {{ $contentClass }}">
        @if(isset($content))
            {!! $content !!}
        @else
            {{= tag_content =}}
        @endif
    </div>
</div>
