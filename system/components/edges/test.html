<!-- starting systems --><!-- starting ss -->
<!-- startup_sequence.class.php > start() 21: initializing autoloader
-->

<!-- startup_sequence.class.php > start() 24: starting
-->

<!--
********************************************************************************************************************************************************
startup_sequence.class.php > start() 87
array(53) {
  ["fs_app_root"]: string(75) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/"
  ["fs_system"]: string(81) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system"
  ["input_params"]: array(14) {
    ["id"]: string(1) "2"
    ["name"]: string(9) "test_send"
    ["category"]: string(5) "other"
    ["add_table"]: string(12) "address_book"
    ["table_aliases"]: array(1) {
      ["test_mail"]: string(0) ""
    }
    ["tables"]: array(1) {
      [0]: string(9) "test_mail"
    }
    ["selected_tables"]: string(1) "["
    ["description"]: string(0) ""
    ["selected_columns"]: array(4) {
      [0]: string(12) "test_mail.id"
      [1]: string(15) "test_mail.email"
      [2]: string(14) "test_mail.name"
      [3]: string(14) "test_mail.misc"
    }
    ["column_aliases"]: array(4) {
      ["test_mail.id"]: string(0) ""
      ["test_mail.email"]: string(0) ""
      ["test_mail.name"]: string(0) ""
      ["test_mail.misc"]: string(0) ""
    }
    ["selected_columns_json"]: string(1) "["
    ["filters"]: array(1) {
      [0]: array(3) {
        ["column"]: string(4) "misc"
        ["operator"]: string(8) "NOT LIKE"
        ["value"]: string(4) "nooo"
      }
    }
    ["grouping"]: array(1) {
      [0]: array(1) {
        ["column"]: string(14) "test_mail.name"
      }
    }
    ["limits"]: array(2) {
      ["limit"]: string(0) ""
      ["offset"]: string(0) ""
    }
  }
  ["system_views"]: array(5) {
    [0]: string(6) "system"
    [1]: string(5) "login"
    [2]: string(6) "logout"
    [3]: string(14) "reset-password"
    [4]: string(8) "settings"
  }
  ["request_uri"]: string(59) "/baffletrain/autocadlt/autobooks/api/data_sources/add_table"
  ["domain"]: string(21) "www.cadservices.co.uk"
  ["script_name"]: string(42) "/baffletrain/autocadlt/autobooks/index.php"
  ["fs_doc_root"]: string(42) "/var/www/vhosts/cadservices.co.uk/httpdocs"
  ["doc_root"]: string(42) "/var/www/vhosts/cadservices.co.uk/httpdocs"
  ["fs_app"]: string(75) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/"
  ["fs_resources"]: string(84) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources"
  ["fs_api"]: string(88) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/api"
  ["fs_classes"]: string(92) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/classes"
  ["fs_functions"]: string(94) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/functions"
  ["fs_views"]: string(90) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views"
  ["fs_config"]: string(91) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/config"
  ["fs_templates"]: string(94) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/templates"
  ["fs_components"]: string(95) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/components"
  ["fs_logs"]: string(89) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/logs"
  ["fs_sys_api"]: string(85) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api"
  ["fs_sys_classes"]: string(89) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes"
  ["fs_sys_functions"]: string(91) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/functions"
  ["fs_sys_views"]: string(87) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/views"
  ["fs_sys_config"]: string(88) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/config"
  ["fs_sys_templates"]: string(91) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templates"
  ["fs_sys_components"]: string(92) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/components"
  ["fs_sys_logs"]: string(86) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/logs"
  ["fs_uploads"]: string(83) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/"
  ["fs_cache"]: string(48) "/var/www/vhosts/cadservices.co.uk/temp/autobooks"
  ["fs_temp"]: string(48) "/var/www/vhosts/cadservices.co.uk/temp/autobooks"
  ["app_root"]: string(32) "/baffletrain/autocadlt/autobooks"
  ["app_path"]: string(16) "api/data_sources"
  ["path_parts"]: array(3) {
    [0]: string(3) "api"
    [1]: string(12) "data_sources"
    [2]: string(9) "add_table"
  }
  ["top_level"]: string(3) "api"
  ["current_page"]: string(9) "add_table"
  ["fs_app_path"]: string(107) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_sources"
  ["fs_full_path"]: string(91) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/api/data_sources"
  ["full_path"]: string(48) "baffletrain/autocadlt/autobooks/api/data_sources"
  ["fs_full_page"]: string(117) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/api/data_sources/add_table"
  ["full_page"]: string(58) "baffletrain/autocadlt/autobooks/api/data_sources/add_table"
  ["set_by"]: string(19) "HTTP_HX_CURRENT_URL"
  ["source_path"]: string(45) "/baffletrain/autocadlt/autobooks/data_sources"
  ["source_page"]: string(9) "edit_view"
  ["source_path_parts"]: array(4) {
    [0]: string(11) "baffletrain"
    [1]: string(9) "autocadlt"
    [2]: string(9) "autobooks"
    [3]: string(12) "data_sources"
  }
  ["source_app_path"]: string(12) "data_sources"
  ["hx_current_url"]: string(84) "https://www.cadservices.co.uk/baffletrain/autocadlt/autobooks/data_sources/edit_view"
  ["hx_current_url_parts"]: array(3) {
    ["scheme"]: string(5) "https"
    ["host"]: string(21) "www.cadservices.co.uk"
    ["path"]: string(55) "/baffletrain/autocadlt/autobooks/data_sources/edit_view"
  }
  ["source_app_path_parts"]: array(1) {
    [0]: string(12) "data_sources"
  }
  ["source_fs_path"]: string(103) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/data_sources"
  ["fs_sys_db_class"]: string(108) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php"
  ["route_tree"]: array(5) {
    ["dashboard"]: array(7) {
      ["name"]: string(9) "Dashboard"
      ["icon"]: string(5) "chart"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(9) "dashboard"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["autodesk"]: array(8) {
      ["name"]: string(8) "Autodesk"
      ["icon"]: string(8) "autodesk"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
      ["sub_folder"]: array(5) {
        ["customers"]: array(7) {
          ["name"]: string(9) "Customers"
          ["icon"]: string(4) "user"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(8) "autodesk"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(false)
        }
        ["orders"]: array(7) {
          ["name"]: string(6) "Orders"
          ["icon"]: string(13) "shopping-cart"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(8) "autodesk"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(false)
        }
        ["quotes"]: array(7) {
          ["name"]: string(6) "Quotes"
          ["icon"]: string(13) "speech_bubble"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(8) "autodesk"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(false)
        }
        ["products"]: array(7) {
          ["name"]: string(8) "Products"
          ["icon"]: string(16) "computer_desktop"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(8) "autodesk"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(false)
        }
        ["subscriptions"]: array(7) {
          ["name"]: string(13) "Subscriptions"
          ["icon"]: string(6) "ticket"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(8) "autodesk"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(false)
        }
      }
    }
    ["email_campaigns"]: array(7) {
      ["name"]: string(15) "Email Campaigns"
      ["icon"]: string(8) "envelope"
      ["required_roles"]: array(3) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
        [2]: string(7) "manager"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(15) "email_campaigns"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["system"]: array(8) {
      ["sub_folder"]: array(2) {
        ["logs"]: array(7) {
          ["name"]: string(4) "logs"
          ["icon"]: string(4) "code"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(6) "system"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(true)
        }
        ["data_sources"]: array(7) {
          ["name"]: string(12) "Data Sources"
          ["icon"]: string(12) "circle-stack"
          ["required_roles"]: array(0) {
          }
          ["show_navbar"]: bool(true)
          ["file_path"]: string(12) "data_sources"
          ["can_delete"]: bool(false)
          ["is_system"]: bool(true)
        }
      }
      ["name"]: string(6) "System"
      ["icon"]: string(4) "code"
      ["required_roles"]: array(1) {
        [0]: string(3) "dev"
      }
      ["show_navbar"]: bool(false)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["data_sources"]: array(7) {
      ["name"]: string(12) "Data Sources"
      ["icon"]: string(8) "database"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(12) "data_sources"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
  }
  ["route_list"]: array(11) {
    ["dashboard"]: array(10) {
      ["id"]: int(2)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(9) "dashboard"
      ["name"]: string(9) "Dashboard"
      ["icon"]: string(5) "chart"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(9) "dashboard"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["autodesk"]: array(10) {
      ["id"]: int(10)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(8) "autodesk"
      ["name"]: string(8) "Autodesk"
      ["icon"]: string(8) "autodesk"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["customers"]: array(10) {
      ["id"]: int(6)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(9) "customers"
      ["name"]: string(9) "Customers"
      ["icon"]: string(4) "user"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["orders"]: array(10) {
      ["id"]: int(3)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(6) "orders"
      ["name"]: string(6) "Orders"
      ["icon"]: string(13) "shopping-cart"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["quotes"]: array(10) {
      ["id"]: int(4)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(6) "quotes"
      ["name"]: string(6) "Quotes"
      ["icon"]: string(13) "speech_bubble"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["products"]: array(10) {
      ["id"]: int(7)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(8) "products"
      ["name"]: string(8) "Products"
      ["icon"]: string(16) "computer_desktop"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["subscriptions"]: array(10) {
      ["id"]: int(5)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(13) "subscriptions"
      ["name"]: string(13) "Subscriptions"
      ["icon"]: string(6) "ticket"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    ["email_campaigns"]: array(10) {
      ["id"]: int(35)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(15) "email_campaigns"
      ["name"]: string(15) "Email Campaigns"
      ["icon"]: string(8) "envelope"
      ["required_roles"]: array(3) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
        [2]: string(7) "manager"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(15) "email_campaigns"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["logs"]: array(10) {
      ["id"]: int(9)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(4) "logs"
      ["name"]: string(4) "logs"
      ["icon"]: string(4) "code"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["system"]: array(10) {
      ["id"]: int(8)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(6) "system"
      ["name"]: string(6) "System"
      ["icon"]: string(4) "code"
      ["required_roles"]: array(1) {
        [0]: string(3) "dev"
      }
      ["show_navbar"]: bool(false)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    ["data_sources"]: array(10) {
      ["id"]: int(39)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(12) "data_sources"
      ["name"]: string(12) "Data Sources"
      ["icon"]: string(8) "database"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(12) "data_sources"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
  }
  ["routes"]: array(12) {
    [0]: array(10) {
      ["id"]: int(2)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(9) "dashboard"
      ["name"]: string(9) "Dashboard"
      ["icon"]: string(5) "chart"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(9) "dashboard"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [1]: array(10) {
      ["id"]: int(10)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(8) "autodesk"
      ["name"]: string(8) "Autodesk"
      ["icon"]: string(8) "autodesk"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(0) ""
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [2]: array(10) {
      ["id"]: int(6)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(9) "customers"
      ["name"]: string(9) "Customers"
      ["icon"]: string(4) "user"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [3]: array(10) {
      ["id"]: int(3)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(6) "orders"
      ["name"]: string(6) "Orders"
      ["icon"]: string(13) "shopping-cart"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [4]: array(10) {
      ["id"]: int(4)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(6) "quotes"
      ["name"]: string(6) "Quotes"
      ["icon"]: string(13) "speech_bubble"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [5]: array(10) {
      ["id"]: int(7)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(8) "products"
      ["name"]: string(8) "Products"
      ["icon"]: string(16) "computer_desktop"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [6]: array(10) {
      ["id"]: int(5)
      ["parent_path"]: string(8) "autodesk"
      ["route_key"]: string(13) "subscriptions"
      ["name"]: string(13) "Subscriptions"
      ["icon"]: string(6) "ticket"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(8) "autodesk"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(false)
    }
    [7]: array(10) {
      ["id"]: int(35)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(15) "email_campaigns"
      ["name"]: string(15) "Email Campaigns"
      ["icon"]: string(8) "envelope"
      ["required_roles"]: array(3) {
        [0]: string(5) "admin"
        [1]: string(3) "dev"
        [2]: string(7) "manager"
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(15) "email_campaigns"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [8]: array(10) {
      ["id"]: int(9)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(4) "logs"
      ["name"]: string(4) "logs"
      ["icon"]: string(4) "code"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [9]: array(10) {
      ["id"]: int(8)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(6) "system"
      ["name"]: string(6) "System"
      ["icon"]: string(4) "code"
      ["required_roles"]: array(1) {
        [0]: string(3) "dev"
      }
      ["show_navbar"]: bool(false)
      ["file_path"]: string(6) "system"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [10]: array(10) {
      ["id"]: int(38)
      ["parent_path"]: string(6) "system"
      ["route_key"]: string(12) "data_sources"
      ["name"]: string(12) "Data Sources"
      ["icon"]: string(12) "circle-stack"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(12) "data_sources"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
    [11]: array(10) {
      ["id"]: int(39)
      ["parent_path"]: string(4) "root"
      ["route_key"]: string(12) "data_sources"
      ["name"]: string(12) "Data Sources"
      ["icon"]: string(8) "database"
      ["required_roles"]: array(0) {
      }
      ["show_navbar"]: bool(true)
      ["file_path"]: string(12) "data_sources"
      ["can_delete"]: bool(false)
      ["is_system"]: bool(true)
    }
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> start, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 26
         <strong>Arguments:</strong>
         0: {"fs_app_root":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/...
         1: {"base":{"app_root":"{fs_app_root}","doc_root":"{fs_doc_root}"},"resources":{"root":"resources","api...

----------------------------------------------------------------------------
-->

<!--
********************************************************************************************************************************************************
startup_sequence.class.php > start() 88
array(4) {
  ["auth_token"]: string(64) "de5e61ac63a794a69e9c927042ad4146463042665d35b58717f208e262b34cdc"
  ["user_id"]: int(2)
  ["user_role"]: string(3) "dev"
  ["debug_mode"]: bool(true)
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> start, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 26
         <strong>Arguments:</strong>
         0: {"fs_app_root":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/...
         1: {"base":{"app_root":"{fs_app_root}","doc_root":"{fs_doc_root}"},"resources":{"root":"resources","api...

----------------------------------------------------------------------------
-->

<!-- startup_sequence.class.php > start() 92: am I in debug mode?
-->

<!--
********************************************************************************************************************************************************
$row: startup_sequence.class.php > start() 96
array(1) {
  ["preferences"]: string(19) "{"debug_mode":true}"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> start, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 26
         <strong>Arguments:</strong>
         0: {"fs_app_root":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/...
         1: {"base":{"app_root":"{fs_app_root}","doc_root":"{fs_doc_root}"},"resources":{"root":"resources","api...

----------------------------------------------------------------------------
-->

<!-- preferences: startup_sequence.class.php > start() 97: {"debug_mode":true}
-->

<!-- debug mode is: : startup_sequence.class.php > start() 98: on
-->

<!--
********************************************************************************************************************************************************
startup_sequence.class.php > start() 106
array(4) {
  ["auth_token"]: string(64) "de5e61ac63a794a69e9c927042ad4146463042665d35b58717f208e262b34cdc"
  ["user_id"]: int(2)
  ["user_role"]: string(3) "dev"
  ["debug_mode"]: bool(true)
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> start, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 26
         <strong>Arguments:</strong>
         0: {"fs_app_root":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/...
         1: {"base":{"app_root":"{fs_app_root}","doc_root":"{fs_doc_root}"},"resources":{"root":"resources","api...

----------------------------------------------------------------------------
-->

<!-- index.php > global() 29: starting route
-->
<!-- starting route -->
<!-- router.class.php > route() 18: starting route for api/data_sources/add_table
-->

<!-- router.class.php > get_api() 431: Looking for API file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/api/data_sources.api.php
-->

<!-- router.class.php > get_api() 431: Looking for API file: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_sources.api.php
-->

<!-- router.class.php > get_api() 433: API file found: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_sources.api.php
-->

<!--
********************************************************************************************************************************************************
endpointy: router.class.php > route() 54
array(5) {
  ["parts"]: array(2) {
    [0]: string(3) "api"
    [1]: string(12) "data_sources"
  }
  ["endpoint"]: string(106) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_sources.api.php"
  ["function_call"]: string(9) "add_table"
  ["api_result"]: array(2) {
    ["status"]: string(7) "success"
    ["path"]: string(106) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_sources.api.php"
  }
  ["api_result_status"]: string(7) "success"
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> route, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php, Line: 31
         <strong>Arguments:</strong>

----------------------------------------------------------------------------
-->

<!-- endpoint: router.class.php > route() 150: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_sources.api.php
-->

<!-- launching layout-api with: router.class.php > route() 194: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_sources.api.php
-->

<!-- router.class.php > route() 208: regular view found
-->


<!--
********************************************************************************************************************************************************
calling: layout-api.edge.php > include() 44
array(5) {
  ["namespace"]: string(17) "api\data_sources\"
  ["view"]: string(106) "/var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/api/data_sources.api.php"
  ["view_exists"]: string(4) "true"
  ["function_call"]: string(9) "add_table"
  ["path_parts"]: array(1) {
    [0]: string(12) "data_sources"
  }
}

    ----------------------------------------------------------------------------
      <strong>Function:</strong> include, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 134
         <strong>Arguments:</strong>
         0: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"
      <strong>Function:</strong> phprender, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php, Line: 127
         <strong>Arguments:</strong>
         0: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...
         1: "\/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/layout-api.edge.php"
      <strong>Function:</strong> render, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php, Line: 210
         <strong>Arguments:</strong>
         0: "layout-api"
         1: {"view":"\/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\...

----------------------------------------------------------------------------
-->

<!-- calling: layout-api.edge.php > include() 52: api\data_sources\add_table
-->

<div class="flex items-center p-4 bg-white border border-gray-200 rounded-lg shadow-sm" id="table-item-1">
    <div class="flex-1">
        <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M4 7v10c0 2.21 1.79 4 4 4h8c0-2.21-1.79-4-4-4H4V7z"></path>
                    </svg>
                </div>
            </div>

            <div class="flex-1 min-w-0">
                <div class="flex items-center space-x-2">
                    <p class="text-sm font-medium text-gray-900 truncate">
                        Address Book </p>
                </div>
                <div class="flex items-center space-x-4 mt-1">
                    <p class="text-xs text-gray-500">
                        address_book </p>
                    <p class="text-xs text-gray-500">
                        26,532 rows
                    </p>
                    <p class="text-xs text-gray-500">
                        14 columns
                    </p>
                </div>
            </div>
        </div>

        <p class="mt-2 text-xs text-gray-600">Database table with 14 columns and 26532 records</p>


        <div class="mt-3">
            <label class="block text-xs font-medium text-gray-700 mb-1">
                Table Alias (optional)
            </label>
            <input type="text"
                   name="table_aliases[address_book]" id=""
                   class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 "
                   placeholder="e.g., u, p, ord"
                   value='' hx-post='/baffletrain/autocadlt/autobooks/api/data_sources/update_query_preview'
                   hx-target='#query-preview-container' hx-swap='innerHTML' hx-include='form'
                   hx-trigger='input changed delay:500ms' tag_content='0'/>
            <p class="mt-1 text-xs text-gray-500">Short name to use in SQL queries (e.g., 'u' for users table)</p>
        </div>
    </div>

    <div class="flex-shrink-0 ml-4">
        <button type="button"
                hx-delete="/baffletrain/autocadlt/autobooks/api/data_sources/remove_table"
                hx-target="#table-item-1"
                hx-swap="outerHTML"
                hx-vals='{"table_name": "address_book", "table_index": 1}'
                hx-include="#selected-tables-input"
                hx-confirm="Remove this table from the data source?"
                class="p-2 text-gray-400 hover:text-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
        </button>
    </div>


    <input type="hidden" name="tables[1]" value="address_book">
</div>
<div id="join-configuration-container" hx-swap-oob="innerHTML">
    <div class="space-y-4">
        <div class="flex items-center justify-between">
            <h4 class="text-sm font-medium text-gray-900">Table Relationships</h4>
            <button type="button"
                    hx-get="/baffletrain/autocadlt/autobooks/api/data_sources/add_join_row"
                    hx-target="#joins-container"
                    hx-swap="beforeend"
                    hx-include="form"
                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Add Join
            </button>
        </div>

        <div id="joins-container" class="space-y-3">
            <div class="text-center py-4 text-gray-500" id="no-joins-message">
                No joins configured. Tables will be cross-joined (Cartesian product).
            </div>
        </div>


        <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <h5 class="text-xs font-medium text-blue-900 mb-2">Join Types:</h5>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-2 text-xs text-blue-800">
                <div><strong>INNER:</strong> Only matching records</div>
                <div><strong>LEFT:</strong> All from left table</div>
                <div><strong>RIGHT:</strong> All from right table</div>
            </div>
        </div>


    </div>


</div>
<div id="column-selection-container" hx-swap-oob="innerHTML">
    <div class="space-y-6">
        <div class="flex items-center justify-between">
            <h4 class="text-sm font-medium text-gray-900">Column Selection</h4>
            <div class="flex items-center space-x-2">
                <button type="button"
                        hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/select_all_columns_with_preview"
                        hx-target="#column-selection-container"
                        hx-swap="innerHTML"
                        hx-include="form"
                        class="text-xs text-indigo-600 hover:text-indigo-800">
                    Select All
                </button>
                <span class="text-gray-300">|</span>
                <button type="button"
                        hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/clear_all_columns_with_preview"
                        hx-target="#column-selection-container"
                        hx-swap="innerHTML"
                        hx-include="form"
                        class="text-xs text-indigo-600 hover:text-indigo-800">
                    Clear All
                </button>
            </div>
        </div>

        <div class="border border-gray-200 rounded-lg">
            <div class="px-4 py-3 bg-gray-50 border-b border-gray-200 rounded-t-lg">
                <div class="flex items-center justify-between">
                    <h5 class="text-sm font-medium text-gray-900">test_mail</h5>
                    <div class="flex items-center space-x-2">
                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/select_table_columns_with_preview"
                                hx-target="#column-selection-container"
                                hx-swap="innerHTML"
                                hx-include="form"
                                hx-vals='{"table_name": "test_mail"}'
                                class="text-xs text-indigo-600 hover:text-indigo-800">
                            Select All
                        </button>
                        <span class="text-gray-300">|</span>
                        <button type="button"
                                hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/clear_table_columns_with_preview"
                                hx-target="#column-selection-container"
                                hx-swap="innerHTML"
                                hx-include="form"
                                hx-vals='{"table_name": "test_mail"}'
                                class="text-xs text-indigo-600 hover:text-indigo-800">
                            Clear
                        </button>
                    </div>
                </div>
            </div>

            <div class="">
                <table class="min-w-full divide-y divide-gray-300">
                    <thead>
                    <tr>
                        <th scope="col" class="relative px-7 sm:w-12 sm:px-6">
                            <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                                <input type="checkbox"
                                       class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                                <svg viewBox="0 0 14 14" fill="none"
                                     class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                    <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                          stroke-linejoin="round" class="opacity-0 group-has-[:checked]:opacity-100"/>
                                    <path d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                          class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                                </svg>
                            </div>
                        </th>
                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                            Title
                        </th>
                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Type
                        </th>
                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                            Flags
                        </th>
                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                            Alias
                        </th>

                    </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 bg-white">


                    <tr class="group has-[:checked]:bg-gray-50">

                        <td class="relative px-7 sm:w-12 sm:px-6">
                            <div class="absolute inset-y-0 left-0 hidden w-0.5 bg-indigo-600 group-has-[:checked]:block"></div>

                            <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                                <input
                                        type="checkbox"
                                        name="selected_columns[]"
                                        value="test_mail.id"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/update_column_selection"
                                        hx-target="#column-selection-container"
                                        hx-swap="innerHTML"
                                        hx-include="form"
                                        hx-trigger="change"
                                        class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                                <svg viewBox="0 0 14 14" fill="none"
                                     class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                    <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                          stroke-linejoin="round" class="opacity-0 group-has-[:checked]:opacity-100"/>
                                    <path d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                          class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                                </svg>
                            </div>
                        </td>

                        <td class="px-2 py-2 text-sm font-medium whitespace-nowrap text-gray-900">id</td>

                        <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">int(4)</td>
                        <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                                                                                    <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                                    PK
                                                </span>
                        </td>
                        <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                            <input type="text"
                                   name="" id=""
                                   class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900  placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 "
                                   placeholder=""
                                   tag_content='0'/></td>
                    </tr>


                    <tr class="group has-[:checked]:bg-gray-50">

                        <td class="relative px-7 sm:w-12 sm:px-6">
                            <div class="absolute inset-y-0 left-0 hidden w-0.5 bg-indigo-600 group-has-[:checked]:block"></div>

                            <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                                <input
                                        type="checkbox"
                                        name="selected_columns[]"
                                        value="test_mail.email"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/update_column_selection"
                                        hx-target="#column-selection-container"
                                        hx-swap="innerHTML"
                                        hx-include="form"
                                        hx-trigger="change"
                                        class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                                <svg viewBox="0 0 14 14" fill="none"
                                     class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                    <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                          stroke-linejoin="round" class="opacity-0 group-has-[:checked]:opacity-100"/>
                                    <path d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                          class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                                </svg>
                            </div>
                        </td>

                        <td class="px-2 py-2 text-sm font-medium whitespace-nowrap text-gray-900">email</td>

                        <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">varchar(253)</td>
                        <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                        </td>
                        <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                            <input type="text"
                                   name="" id=""
                                   class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900  placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 "
                                   placeholder=""
                                   tag_content='0'/></td>
                    </tr>


                    <tr class="group has-[:checked]:bg-gray-50">

                        <td class="relative px-7 sm:w-12 sm:px-6">
                            <div class="absolute inset-y-0 left-0 hidden w-0.5 bg-indigo-600 group-has-[:checked]:block"></div>

                            <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                                <input
                                        type="checkbox"
                                        name="selected_columns[]"
                                        value="test_mail.name"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/update_column_selection"
                                        hx-target="#column-selection-container"
                                        hx-swap="innerHTML"
                                        hx-include="form"
                                        hx-trigger="change"
                                        class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                                <svg viewBox="0 0 14 14" fill="none"
                                     class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                    <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                          stroke-linejoin="round" class="opacity-0 group-has-[:checked]:opacity-100"/>
                                    <path d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                          class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                                </svg>
                            </div>
                        </td>

                        <td class="px-2 py-2 text-sm font-medium whitespace-nowrap text-gray-900">name</td>

                        <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">varchar(253)</td>
                        <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                        </td>
                        <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                            <input type="text"
                                   name="" id=""
                                   class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900  placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 "
                                   placeholder=""
                                   tag_content='0'/></td>
                    </tr>


                    <tr class="group has-[:checked]:bg-gray-50">

                        <td class="relative px-7 sm:w-12 sm:px-6">
                            <div class="absolute inset-y-0 left-0 hidden w-0.5 bg-indigo-600 group-has-[:checked]:block"></div>

                            <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                                <input
                                        type="checkbox"
                                        name="selected_columns[]"
                                        value="test_mail.misc"
                                        hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/update_column_selection"
                                        hx-target="#column-selection-container"
                                        hx-swap="innerHTML"
                                        hx-include="form"
                                        hx-trigger="change"
                                        class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                                <svg viewBox="0 0 14 14" fill="none"
                                     class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                    <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                          stroke-linejoin="round" class="opacity-0 group-has-[:checked]:opacity-100"/>
                                    <path d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                          class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                                </svg>
                            </div>
                        </td>

                        <td class="px-2 py-2 text-sm font-medium whitespace-nowrap text-gray-900">misc</td>

                        <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">varchar(253)</td>
                        <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                        </td>
                        <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                            <input type="text"
                                   name="" id=""
                                   class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900  placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 "
                                   placeholder=""
                                   tag_content='0'/></td>
                    </tr>

                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="border border-gray-200 rounded-lg">
        <div class="px-4 py-3 bg-gray-50 border-b border-gray-200 rounded-t-lg">
            <div class="flex items-center justify-between">
                <h5 class="text-sm font-medium text-gray-900">address_book</h5>
                <div class="flex items-center space-x-2">
                    <button type="button"
                            hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/select_table_columns_with_preview"
                            hx-target="#column-selection-container"
                            hx-swap="innerHTML"
                            hx-include="form"
                            hx-vals='{"table_name": "address_book"}'
                            class="text-xs text-indigo-600 hover:text-indigo-800">
                        Select All
                    </button>
                    <span class="text-gray-300">|</span>
                    <button type="button"
                            hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/clear_table_columns_with_preview"
                            hx-target="#column-selection-container"
                            hx-swap="innerHTML"
                            hx-include="form"
                            hx-vals='{"table_name": "address_book"}'
                            class="text-xs text-indigo-600 hover:text-indigo-800">
                        Clear
                    </button>
                </div>
            </div>
        </div>

        <div class="">
            <table class="min-w-full divide-y divide-gray-300">
                <thead>
                <tr>
                    <th scope="col" class="relative px-7 sm:w-12 sm:px-6">
                        <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                            <input type="checkbox"
                                   class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                            <svg viewBox="0 0 14 14" fill="none"
                                 class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                      stroke-linejoin="round" class="opacity-0 group-has-[:checked]:opacity-100"/>
                                <path d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                      class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                            </svg>
                        </div>
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                        Title
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Type
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                        Flags
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                        Alias
                    </th>

                </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">


                <tr class="group has-[:checked]:bg-gray-50">

                    <td class="relative px-7 sm:w-12 sm:px-6">
                        <div class="absolute inset-y-0 left-0 hidden w-0.5 bg-indigo-600 group-has-[:checked]:block"></div>

                        <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                            <input
                                    type="checkbox"
                                    name="selected_columns[]"
                                    value="address_book.address_book_id"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/update_column_selection"
                                    hx-target="#column-selection-container"
                                    hx-swap="innerHTML"
                                    hx-include="form"
                                    hx-trigger="change"
                                    class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                            <svg viewBox="0 0 14 14" fill="none"
                                 class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                      stroke-linejoin="round" class="opacity-0 group-has-[:checked]:opacity-100"/>
                                <path d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                      class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                            </svg>
                        </div>
                    </td>

                    <td class="px-2 py-2 text-sm font-medium whitespace-nowrap text-gray-900">address_book_id</td>

                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">int(11)</td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                                                                                    <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                                    PK
                                                </span>
                    </td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                        <input type="text"
                               name="" id=""
                               class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900  placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 "
                               placeholder=""
                               tag_content='0'/></td>
                </tr>


                <tr class="group has-[:checked]:bg-gray-50">

                    <td class="relative px-7 sm:w-12 sm:px-6">
                        <div class="absolute inset-y-0 left-0 hidden w-0.5 bg-indigo-600 group-has-[:checked]:block"></div>

                        <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                            <input
                                    type="checkbox"
                                    name="selected_columns[]"
                                    value="address_book.customers_id"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/update_column_selection"
                                    hx-target="#column-selection-container"
                                    hx-swap="innerHTML"
                                    hx-include="form"
                                    hx-trigger="change"
                                    class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                            <svg viewBox="0 0 14 14" fill="none"
                                 class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                      stroke-linejoin="round" class="opacity-0 group-has-[:checked]:opacity-100"/>
                                <path d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                      class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                            </svg>
                        </div>
                    </td>

                    <td class="px-2 py-2 text-sm font-medium whitespace-nowrap text-gray-900">customers_id</td>

                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">int(11)</td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                    </td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                        <input type="text"
                               name="" id=""
                               class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900  placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 "
                               placeholder=""
                               tag_content='0'/></td>
                </tr>


                <tr class="group has-[:checked]:bg-gray-50">

                    <td class="relative px-7 sm:w-12 sm:px-6">
                        <div class="absolute inset-y-0 left-0 hidden w-0.5 bg-indigo-600 group-has-[:checked]:block"></div>

                        <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                            <input
                                    type="checkbox"
                                    name="selected_columns[]"
                                    value="address_book.entry_gender"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/update_column_selection"
                                    hx-target="#column-selection-container"
                                    hx-swap="innerHTML"
                                    hx-include="form"
                                    hx-trigger="change"
                                    class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                            <svg viewBox="0 0 14 14" fill="none"
                                 class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                      stroke-linejoin="round" class="opacity-0 group-has-[:checked]:opacity-100"/>
                                <path d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                      class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                            </svg>
                        </div>
                    </td>

                    <td class="px-2 py-2 text-sm font-medium whitespace-nowrap text-gray-900">entry_gender</td>

                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">char(1)</td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                    </td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                        <input type="text"
                               name="" id=""
                               class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900  placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 "
                               placeholder=""
                               tag_content='0'/></td>
                </tr>


                <tr class="group has-[:checked]:bg-gray-50">

                    <td class="relative px-7 sm:w-12 sm:px-6">
                        <div class="absolute inset-y-0 left-0 hidden w-0.5 bg-indigo-600 group-has-[:checked]:block"></div>

                        <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                            <input
                                    type="checkbox"
                                    name="selected_columns[]"
                                    value="address_book.entry_company"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/update_column_selection"
                                    hx-target="#column-selection-container"
                                    hx-swap="innerHTML"
                                    hx-include="form"
                                    hx-trigger="change"
                                    class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                            <svg viewBox="0 0 14 14" fill="none"
                                 class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                      stroke-linejoin="round" class="opacity-0 group-has-[:checked]:opacity-100"/>
                                <path d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                      class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                            </svg>
                        </div>
                    </td>

                    <td class="px-2 py-2 text-sm font-medium whitespace-nowrap text-gray-900">entry_company</td>

                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">varchar(255)</td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                    </td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                        <input type="text"
                               name="" id=""
                               class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900  placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 "
                               placeholder=""
                               tag_content='0'/></td>
                </tr>


                <tr class="group has-[:checked]:bg-gray-50">

                    <td class="relative px-7 sm:w-12 sm:px-6">
                        <div class="absolute inset-y-0 left-0 hidden w-0.5 bg-indigo-600 group-has-[:checked]:block"></div>

                        <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                            <input
                                    type="checkbox"
                                    name="selected_columns[]"
                                    value="address_book.entry_firstname"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/update_column_selection"
                                    hx-target="#column-selection-container"
                                    hx-swap="innerHTML"
                                    hx-include="form"
                                    hx-trigger="change"
                                    class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                            <svg viewBox="0 0 14 14" fill="none"
                                 class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                      stroke-linejoin="round" class="opacity-0 group-has-[:checked]:opacity-100"/>
                                <path d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                      class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                            </svg>
                        </div>
                    </td>

                    <td class="px-2 py-2 text-sm font-medium whitespace-nowrap text-gray-900">entry_firstname</td>

                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">varchar(255)</td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                    </td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                        <input type="text"
                               name="" id=""
                               class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900  placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 "
                               placeholder=""
                               tag_content='0'/></td>
                </tr>


                <tr class="group has-[:checked]:bg-gray-50">

                    <td class="relative px-7 sm:w-12 sm:px-6">
                        <div class="absolute inset-y-0 left-0 hidden w-0.5 bg-indigo-600 group-has-[:checked]:block"></div>

                        <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                            <input
                                    type="checkbox"
                                    name="selected_columns[]"
                                    value="address_book.entry_lastname"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/update_column_selection"
                                    hx-target="#column-selection-container"
                                    hx-swap="innerHTML"
                                    hx-include="form"
                                    hx-trigger="change"
                                    class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                            <svg viewBox="0 0 14 14" fill="none"
                                 class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                      stroke-linejoin="round" class="opacity-0 group-has-[:checked]:opacity-100"/>
                                <path d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                      class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                            </svg>
                        </div>
                    </td>

                    <td class="px-2 py-2 text-sm font-medium whitespace-nowrap text-gray-900">entry_lastname</td>

                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">varchar(255)</td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                    </td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                        <input type="text"
                               name="" id=""
                               class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900  placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 "
                               placeholder=""
                               tag_content='0'/></td>
                </tr>


                <tr class="group has-[:checked]:bg-gray-50">

                    <td class="relative px-7 sm:w-12 sm:px-6">
                        <div class="absolute inset-y-0 left-0 hidden w-0.5 bg-indigo-600 group-has-[:checked]:block"></div>

                        <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                            <input
                                    type="checkbox"
                                    name="selected_columns[]"
                                    value="address_book.entry_street_address"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/update_column_selection"
                                    hx-target="#column-selection-container"
                                    hx-swap="innerHTML"
                                    hx-include="form"
                                    hx-trigger="change"
                                    class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                            <svg viewBox="0 0 14 14" fill="none"
                                 class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                      stroke-linejoin="round" class="opacity-0 group-has-[:checked]:opacity-100"/>
                                <path d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                      class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                            </svg>
                        </div>
                    </td>

                    <td class="px-2 py-2 text-sm font-medium whitespace-nowrap text-gray-900">entry_street_address</td>

                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">varchar(255)</td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                    </td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                        <input type="text"
                               name="" id=""
                               class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900  placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 "
                               placeholder=""
                               tag_content='0'/></td>
                </tr>


                <tr class="group has-[:checked]:bg-gray-50">

                    <td class="relative px-7 sm:w-12 sm:px-6">
                        <div class="absolute inset-y-0 left-0 hidden w-0.5 bg-indigo-600 group-has-[:checked]:block"></div>

                        <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                            <input
                                    type="checkbox"
                                    name="selected_columns[]"
                                    value="address_book.entry_suburb"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/update_column_selection"
                                    hx-target="#column-selection-container"
                                    hx-swap="innerHTML"
                                    hx-include="form"
                                    hx-trigger="change"
                                    class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                            <svg viewBox="0 0 14 14" fill="none"
                                 class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                      stroke-linejoin="round" class="opacity-0 group-has-[:checked]:opacity-100"/>
                                <path d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                      class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                            </svg>
                        </div>
                    </td>

                    <td class="px-2 py-2 text-sm font-medium whitespace-nowrap text-gray-900">entry_suburb</td>

                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">varchar(255)</td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                    </td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                        <input type="text"
                               name="" id=""
                               class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900  placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 "
                               placeholder=""
                               tag_content='0'/></td>
                </tr>


                <tr class="group has-[:checked]:bg-gray-50">

                    <td class="relative px-7 sm:w-12 sm:px-6">
                        <div class="absolute inset-y-0 left-0 hidden w-0.5 bg-indigo-600 group-has-[:checked]:block"></div>

                        <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                            <input
                                    type="checkbox"
                                    name="selected_columns[]"
                                    value="address_book.entry_postcode"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/update_column_selection"
                                    hx-target="#column-selection-container"
                                    hx-swap="innerHTML"
                                    hx-include="form"
                                    hx-trigger="change"
                                    class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                            <svg viewBox="0 0 14 14" fill="none"
                                 class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                      stroke-linejoin="round" class="opacity-0 group-has-[:checked]:opacity-100"/>
                                <path d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                      class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                            </svg>
                        </div>
                    </td>

                    <td class="px-2 py-2 text-sm font-medium whitespace-nowrap text-gray-900">entry_postcode</td>

                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">varchar(255)</td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                    </td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                        <input type="text"
                               name="" id=""
                               class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900  placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 "
                               placeholder=""
                               tag_content='0'/></td>
                </tr>


                <tr class="group has-[:checked]:bg-gray-50">

                    <td class="relative px-7 sm:w-12 sm:px-6">
                        <div class="absolute inset-y-0 left-0 hidden w-0.5 bg-indigo-600 group-has-[:checked]:block"></div>

                        <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                            <input
                                    type="checkbox"
                                    name="selected_columns[]"
                                    value="address_book.entry_city"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/update_column_selection"
                                    hx-target="#column-selection-container"
                                    hx-swap="innerHTML"
                                    hx-include="form"
                                    hx-trigger="change"
                                    class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                            <svg viewBox="0 0 14 14" fill="none"
                                 class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                      stroke-linejoin="round" class="opacity-0 group-has-[:checked]:opacity-100"/>
                                <path d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                      class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                            </svg>
                        </div>
                    </td>

                    <td class="px-2 py-2 text-sm font-medium whitespace-nowrap text-gray-900">entry_city</td>

                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">varchar(255)</td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                    </td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                        <input type="text"
                               name="" id=""
                               class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900  placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 "
                               placeholder=""
                               tag_content='0'/></td>
                </tr>


                <tr class="group has-[:checked]:bg-gray-50">

                    <td class="relative px-7 sm:w-12 sm:px-6">
                        <div class="absolute inset-y-0 left-0 hidden w-0.5 bg-indigo-600 group-has-[:checked]:block"></div>

                        <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                            <input
                                    type="checkbox"
                                    name="selected_columns[]"
                                    value="address_book.entry_state"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/update_column_selection"
                                    hx-target="#column-selection-container"
                                    hx-swap="innerHTML"
                                    hx-include="form"
                                    hx-trigger="change"
                                    class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                            <svg viewBox="0 0 14 14" fill="none"
                                 class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                      stroke-linejoin="round" class="opacity-0 group-has-[:checked]:opacity-100"/>
                                <path d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                      class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                            </svg>
                        </div>
                    </td>

                    <td class="px-2 py-2 text-sm font-medium whitespace-nowrap text-gray-900">entry_state</td>

                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">varchar(255)</td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                    </td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                        <input type="text"
                               name="" id=""
                               class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900  placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 "
                               placeholder=""
                               tag_content='0'/></td>
                </tr>


                <tr class="group has-[:checked]:bg-gray-50">

                    <td class="relative px-7 sm:w-12 sm:px-6">
                        <div class="absolute inset-y-0 left-0 hidden w-0.5 bg-indigo-600 group-has-[:checked]:block"></div>

                        <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                            <input
                                    type="checkbox"
                                    name="selected_columns[]"
                                    value="address_book.entry_country_id"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/update_column_selection"
                                    hx-target="#column-selection-container"
                                    hx-swap="innerHTML"
                                    hx-include="form"
                                    hx-trigger="change"
                                    class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                            <svg viewBox="0 0 14 14" fill="none"
                                 class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                      stroke-linejoin="round" class="opacity-0 group-has-[:checked]:opacity-100"/>
                                <path d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                      class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                            </svg>
                        </div>
                    </td>

                    <td class="px-2 py-2 text-sm font-medium whitespace-nowrap text-gray-900">entry_country_id</td>

                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">int(11)</td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                    </td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                        <input type="text"
                               name="" id=""
                               class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900  placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 "
                               placeholder=""
                               tag_content='0'/></td>
                </tr>


                <tr class="group has-[:checked]:bg-gray-50">

                    <td class="relative px-7 sm:w-12 sm:px-6">
                        <div class="absolute inset-y-0 left-0 hidden w-0.5 bg-indigo-600 group-has-[:checked]:block"></div>

                        <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                            <input
                                    type="checkbox"
                                    name="selected_columns[]"
                                    value="address_book.entry_zone_id"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/update_column_selection"
                                    hx-target="#column-selection-container"
                                    hx-swap="innerHTML"
                                    hx-include="form"
                                    hx-trigger="change"
                                    class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                            <svg viewBox="0 0 14 14" fill="none"
                                 class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                      stroke-linejoin="round" class="opacity-0 group-has-[:checked]:opacity-100"/>
                                <path d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                      class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                            </svg>
                        </div>
                    </td>

                    <td class="px-2 py-2 text-sm font-medium whitespace-nowrap text-gray-900">entry_zone_id</td>

                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">int(11)</td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                    </td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                        <input type="text"
                               name="" id=""
                               class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900  placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 "
                               placeholder=""
                               tag_content='0'/></td>
                </tr>


                <tr class="group has-[:checked]:bg-gray-50">

                    <td class="relative px-7 sm:w-12 sm:px-6">
                        <div class="absolute inset-y-0 left-0 hidden w-0.5 bg-indigo-600 group-has-[:checked]:block"></div>

                        <div class="group absolute left-4 top-1/2 -mt-2 grid size-4 grid-cols-1">
                            <input
                                    type="checkbox"
                                    name="selected_columns[]"
                                    value="address_book.entry_street_address2"
                                    hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/update_column_selection"
                                    hx-target="#column-selection-container"
                                    hx-swap="innerHTML"
                                    hx-include="form"
                                    hx-trigger="change"
                                    class="col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-indigo-600 checked:bg-indigo-600 indeterminate:border-indigo-600 indeterminate:bg-indigo-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto"/>
                            <svg viewBox="0 0 14 14" fill="none"
                                 class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25">
                                <path d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round"
                                      stroke-linejoin="round" class="opacity-0 group-has-[:checked]:opacity-100"/>
                                <path d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                      class="opacity-0 group-has-[:indeterminate]:opacity-100"/>
                            </svg>
                        </div>
                    </td>

                    <td class="px-2 py-2 text-sm font-medium whitespace-nowrap text-gray-900">entry_street_address2</td>

                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">varchar(64)</td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                    </td>
                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                        <input type="text"
                               name="" id=""
                               class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900  placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 "
                               placeholder=""
                               tag_content='0'/></td>
                </tr>

                </tbody>
            </table>
        </div>
    </div>
</div>


<div class="bg-blue-50 border border-blue-200 rounded-md p-3">
    <div class="flex items-center">
        <svg class="h-4 w-4 text-blue-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="text-sm text-blue-800">
                    <span id="selected-count">0</span> columns selected
                    <span class="text-blue-600">
                        (Leave empty to select all columns with table prefixes)
                    </span>
                </span>
    </div>
</div>


<input type="hidden" name="selected_columns_json" id="selected-columns-json-input"
       value="[]">
</div>

<script>
    // Update selected columns JSON input when checkboxes change
    document.addEventListener('DOMContentLoaded', function () {
        updateSelectedColumnsJsonInput();
    });

    document.addEventListener('htmx:afterSwap', function (event) {
        if (event.target.id === 'column-selection-container') {
            updateSelectedColumnsJsonInput();
        }
    });

    function updateSelectedColumnsJsonInput() {
        try {
            // Get all checked column checkboxes
            const checkedColumns = document.querySelectorAll('#column-selection-container input[name="selected_columns[]"]:checked');
            const selectedColumns = Array.from(checkedColumns).map(input => input.value);

            // Update the JSON input
            const jsonInput = document.getElementById('selected-columns-json-input');
            if (jsonInput) {
                jsonInput.value = JSON.stringify(selectedColumns);
                console.log('Updated selected_columns_json input:', selectedColumns);
            }
        } catch (error) {
            console.error('Error updating selected columns JSON input:', error);
        }
    }
</script>


</div>
<div id="query-preview-container" hx-swap-oob="innerHTML">
    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
            <h4 class="text-sm font-medium text-gray-900">Generated SQL Query</h4>
            <div class="flex items-center space-x-2">
                <button type="button"
                        onclick="toggleQueryFormat()"
                        id="format-toggle-btn"
                        class="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                    Compact
                </button>
                <button type="button"
                        onclick="copyToClipboard('SELECT * FROM `test_mail`')"
                        class="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                    Copy
                </button>
            </div>
        </div>

        <div class="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm overflow-x-auto">
        <pre id="formatted-query">SELECT *

FROM `test_mail`</pre>
            <pre id="compact-query" style="display: none;">SELECT * FROM `test_mail`</pre>
        </div>

        <div class="mt-3 pt-3 border-t border-gray-200 space-y-3">

            <div>
                <h5 class="text-xs font-medium text-gray-700 mb-2">Tables (2):</h5>
                <div class="flex flex-wrap gap-1">
                                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            test_mail                                                            <span
                                                    class="ml-1 text-blue-600">(Primary)</span>
                                                    </span>
                    <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                            address_book                                                    </span>
                </div>
            </div>


            <p class="text-xs text-gray-500">Basic query - will return all records with default column selection</p>
        </div>
    </div>

    <script>
        // Use window property to avoid redeclaration issues with HTMX updates
        if (typeof window.queryPreviewState === 'undefined') {
            window.queryPreviewState = {
                isFormatted: true
            };
        }

        function toggleQueryFormat() {
            const formattedQuery = document.getElementById('formatted-query');
            const compactQuery = document.getElementById('compact-query');
            const toggleBtn = document.getElementById('format-toggle-btn');

            if (window.queryPreviewState.isFormatted) {
                // Switch to compact view
                formattedQuery.style.display = 'none';
                compactQuery.style.display = 'block';
                toggleBtn.innerHTML = '<svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path></svg>Formatted';
                window.queryPreviewState.isFormatted = false;
            } else {
                // Switch to formatted view
                formattedQuery.style.display = 'block';
                compactQuery.style.display = 'none';
                toggleBtn.innerHTML = '<svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg>Compact';
                window.queryPreviewState.isFormatted = true;
            }
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function () {
                // Show temporary success message
                const button = event.target.closest('button');
                const originalText = button.innerHTML;
                button.innerHTML = '<svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Copied!';
                button.classList.add('text-green-600');

                setTimeout(function () {
                    button.innerHTML = originalText;
                    button.classList.remove('text-green-600');
                }, 2000);
            }).catch(function (err) {
                console.error('Could not copy text: ', err);
                alert('Failed to copy query to clipboard');
            });
        }
    </script>
</div>
<div id="grouping-container" hx-swap-oob="outerHTML" class="space-y-3">
    <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-md" id="group-item-0">
        <div class="flex-1">

            <div>
                <label class="block text-xs font-medium text-gray-700">Column</label>

                <div>
                    <label for="select" class="block text-sm/6 font-medium text-gray-900">
                    </label>
                    <div class="grid grid-cols-1">
                        <select
                                id="select"
                                name="grouping[0][column]"
                                selected='test_mail.name'
                                hx-post='/baffletrain/autocadlt/autobooks/api/data_sources/update_query_preview'
                                hx-target='#query-preview-container' hx-swap='innerHTML' hx-include='form'
                                hx-trigger='change' tag_content='0'
                                class="col-start-1 row-start-1 w-full appearance-none rounded-md bg-white py-1.5 pl-3 pr-8 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 text-sm">
                            <option value="test_mail.id">
                                test_mail.id
                            </option>
                            <option value="test_mail.email">
                                test_mail.email
                            </option>
                            <option value="test_mail.name" selected>
                                test_mail.name
                            </option>
                            <option value="test_mail.misc">
                                test_mail.misc
                            </option>
                        </select>
                        <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 sm:size-4"
                             viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd"
                                  d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z"
                                  clip-rule="evenodd"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>


        <div class="flex-shrink-0">
            <button type="button"
                    hx-post="/baffletrain/autocadlt/autobooks/api/data_sources/remove_group_row"
                    hx-target="#group-item-0"
                    hx-swap="outerHTML"
                    hx-include="form"
                    hx-vals='{"group_index": "0"}'
                    class="inline-flex items-center p-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </button>
        </div>
    </div>
</div>
<div id="sorting-container" hx-swap-oob="outerHTML" class="space-y-3">
    <div id="no-sorting-message"
         class="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No sorting rules</h3>
        <p class="mt-1 text-sm text-gray-500">Results will be returned in database default order.</p>
    </div>
</div>
<div id="filters-container" hx-swap-oob="outerHTML" class="space-y-4">
    <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-md" id="filter-row-0"
         x-data="{ operator: 'NOT LIKE' }">
        <div class="flex-1 grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div>
                <label class="block text-xs font-medium text-gray-700">Column</label>

                <div>
                    <label for="select" class="block text-sm/6 font-medium text-gray-900">
                    </label>
                    <div class="grid grid-cols-1">
                        <select
                                id="select"
                                name="filters[0][column]"
                                selected='misc'
                                hx-post='/baffletrain/autocadlt/autobooks/api/data_sources/update_query_preview'
                                hx-target='#query-preview-container' hx-swap='innerHTML' hx-include='form'
                                hx-trigger='change' tag_content='0'
                                class="col-start-1 row-start-1 w-full appearance-none rounded-md bg-white py-1.5 pl-3 pr-8 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 text-sm">
                            <option value="">
                                Select column...
                            </option>
                            <option value="test_mail.id">
                                test_mail.id
                            </option>
                            <option value="test_mail.email">
                                test_mail.email
                            </option>
                            <option value="test_mail.name">
                                test_mail.name
                            </option>
                            <option value="test_mail.misc">
                                test_mail.misc
                            </option>
                        </select>
                        <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 sm:size-4"
                             viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd"
                                  d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z"
                                  clip-rule="evenodd"/>
                        </svg>
                    </div>
                </div>
            </div>

            <div>
                <label class="block text-xs font-medium text-gray-700">Operator</label>

                <div>
                    <label for="select" class="block text-sm/6 font-medium text-gray-900">
                    </label>
                    <div class="grid grid-cols-1">
                        <select
                                id="select"
                                name="filters[0][operator]"
                                selected='NOT LIKE' x-model='operator'
                                hx-post='/baffletrain/autocadlt/autobooks/api/data_sources/update_query_preview'
                                hx-target='#query-preview-container' hx-swap='innerHTML' hx-include='form'
                                hx-trigger='change' tag_content='0'
                                class="col-start-1 row-start-1 w-full appearance-none rounded-md bg-white py-1.5 pl-3 pr-8 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 text-sm">
                            <option value="=">
                                Equals
                            </option>
                            <option value="!=">
                                Not Equals
                            </option>
                            <option value=">">
                                Greater Than
                            </option>
                            <option value=">=">
                                Greater Than or Equal
                            </option>
                            <option value="<">
                                Less Than
                            </option>
                            <option value="<=">
                                Less Than or Equal
                            </option>
                            <option value="LIKE">
                                Contains
                            </option>
                            <option value="NOT LIKE" selected>
                                Does Not Contain
                            </option>
                            <option value="IN">
                                In List
                            </option>
                            <option value="NOT IN">
                                Not In List
                            </option>
                            <option value="IS NULL">
                                Is Empty
                            </option>
                            <option value="IS NOT NULL">
                                Is Not Empty
                            </option>
                        </select>
                        <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 sm:size-4"
                             viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd"
                                  d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z"
                                  clip-rule="evenodd"/>
                        </svg>
                    </div>
                </div>
            </div>

            <div x-show="!['IS NULL', 'IS NOT NULL'].includes(operator)">
                <label class="block text-xs font-medium text-gray-700">Value</label>
                <input type="text"
                       name="filters[0][value]" id=""
                       class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 text-sm"
                       placeholder="Enter filter value"
                       value='nooo' class='text-sm'
                       hx-post='/baffletrain/autocadlt/autobooks/api/data_sources/update_query_preview'
                       hx-target='#query-preview-container' hx-swap='innerHTML' hx-include='form'
                       hx-trigger='input changed delay:500ms' tag_content='0'/></div>
        </div>

        <button type="button"
                hx-delete="/baffletrain/autocadlt/autobooks/api/data_sources/remove_filter_row"
                hx-target="#filter-row-0"
                hx-swap="outerHTML"
                class="flex-shrink-0 p-2 text-red-600 hover:text-red-800">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
        </button>
    </div>
</div>
<!-- end route -->