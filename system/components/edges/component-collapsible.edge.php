@props([
    'title' => '',
    'expanded' => false,
    'cardClass' => 'bg-white shadow-sm rounded-lg',
    'headerClass' => '',
    'contentClass' => '',
    'titleClass' => 'text-lg font-medium text-gray-900'
])

<div x-data="{ expanded: {{ $expanded ? 'true' : 'false' }} }" class="{{ $cardClass }}">
    <div class="px-6 py-4 border-b border-gray-200 {{ $headerClass }}">
        <button @click="expanded = !expanded" 
                class="flex items-center justify-between w-full text-left">
            <h2 class="{{ $titleClass }}">{{ $title }}</h2>
            <svg :class="{'rotate-180': expanded}" 
                 class="w-5 h-5 text-gray-400 transform transition-transform duration-200" 
                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
        </button>
    </div>
    <div x-show="expanded" x-transition class="px-6 py-4 {{ $contentClass }}">
        {{ $slot }}
    </div>
</div>
