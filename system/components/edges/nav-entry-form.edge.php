@props([
    'parent_path' => '',
    'icons' => []
])
@php
// Initialize hilt templates array - only show hilt templates
$templates = [
    'default_template' => [
        'filename' => 'default_template.hilt.php',
        'id' => 'default_template',
        'name' => 'Basic Template',
        'description' => 'A basic hilt template for general content',
        'icon' => 'document-text',
        'type' => 'default'
    ],
    'custom_html_template' => [
        'filename' => 'custom_html_template.hilt.php',
        'id' => 'custom_html_template',
        'name' => 'Custom HTML',
        'description' => 'Custom HTML content template',
        'icon' => 'code-bracket',
        'type' => 'custom_html'
    ],
    'file_upload_template' => [
        'filename' => 'file_upload_template.hilt.php',
        'id' => 'file_upload_template',
        'name' => 'File Upload',
        'description' => 'Template based on uploaded file content',
        'icon' => 'arrow-up-tray',
        'type' => 'file_upload'
    ]
];

// Get all hilt template files from the templates directory
$template_dir = FS_SYS_TEMPLATES;
print_rr(FS_SYS_TEMPLATES, "searching for hilt files");
$hilt_files = glob($template_dir . DS . '*.hilt.php');
print_rr($hilt_files, "found hilt files");

// Process each hilt template file
foreach ($hilt_files as $file) {
    $filename = basename($file);
    print_rr($file, "hilt file");
    $template_key = str_replace('.hilt.php', '', $filename);

    // Skip if already defined above
    if (isset($templates[$template_key])) {
        continue;
    }

    $template_id = pathinfo($filename, PATHINFO_FILENAME);
    $display_name = ucwords(str_replace(['-', '_'], ' ', $template_id));

    // Read the file to extract props information
    $content = file_get_contents($file);
    $title = $display_name;
    $type = 'default';
    $description = 'Custom hilt template';

    // Extract props from the file
    if (preg_match("/'name' => '([^']+)'/", $content, $matches)) {
        $title = $matches[1];
    }

    if (preg_match("/'type' => 'hilt-([^']+)'/", $content, $matches)) {
        $type = $matches[1];
    } elseif (preg_match("/'type' => '([^']+)'/", $content, $matches)) {
        $type = $matches[1];
    }

    if (preg_match("/'description' => '([^']+)'/", $content, $matches)) {
        $description = $matches[1];
    }

    $templates[$template_key] = [
        'filename' => $filename,
        'id' => $template_id,
        'name' => $title,
        'description' => $description,
        'path' => $file,
        'icon' => 'document-text',
        'type' => $type
    ];
}

@endphp

<!-- Include HTMX indicator styles -->
<link rel="stylesheet" href="{{ APP_ROOT }}/resources/css/htmx-indicators.css">

<div class="p-6">
    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Add Navigation Entry</h3>

    <!-- Progress Bar Container - Initially hidden, will be shown during form submission -->
    <div id="nav-entry-progress-container" style="display: none;">
        <!-- Progress bar will be loaded here -->
    </div>

    <form
        hx-post="{{ APP_ROOT }}/api/system/nav_tree/save_nav_entry"
        hx-target="#nav-entry-progress-container"
        hx-swap="innerHTML"
        hx-trigger="submit"
        hx-on::before-request="if(event.detail.elt.tagName === 'FORM') { document.getElementById('nav-entry-progress-container').style.display = 'block'; document.getElementById('nav-entry-form').style.display = 'none'; }"
        @submit="$dispatch('hide-modal')"
        class="space-y-4"
        enctype="multipart/form-data"
        id="nav-entry-form"
    >
        <input type="hidden" name="parent_path" value="{{ $parent_path }}">
        <input type="hidden" name="template_type" id="template_type_hidden" value="default">
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Hilt Template</label>
            <select
                    name="template"
                    hx-post="{{ APP_ROOT }}/api/system/nav_tree/get_template_fields"
                    hx-target="#template-specific-fields"
                    hx-swap="innerHTML"
                    hx-trigger="change"
                    hx-include="this"
                    onchange="document.getElementById('template_type_hidden').value = this.options[this.selectedIndex].dataset.type"
                    class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
            >
                @foreach($templates as $key => $template)
                    @php
                        $isDefault = $key === 'default_template';
                    @endphp
                    <option value="{{ $key }}"
                            data-type="{{ $template['type'] }}"
                            @if($isDefault) selected @endif>
                        {{ $template['name'] }}
                        @if(!empty($template['description']))
                            - {{ $template['description'] }}
                        @endif
                    </option>
                @endforeach
            </select>
            <p class="mt-1 text-sm text-gray-500">All templates use the Hilt system for enhanced functionality</p>
        </div>

        <!-- Template-specific fields container - populated dynamically via htmx -->
        <div id="template-specific-fields">
            <!-- Default template info will be loaded here initially -->
            <x-component-forms-basic-info template_type="default" />
        </div>


        
        <div>
            <x-forms-input 
                name="name" 
                label="Display Name" 
                placeholder="e.g., Dashboard" 
                required
                hx-trigger="input changed"
                hx-replace="innerHTML"
                hx-target="#route_key_input_container"
                hx-get="{{ APP_ROOT }}/api/system/nav_tree/update_route_key"
                hx-include="this"
            />
        </div>

        <div id="route_key_input_container">
            <x-forms-input
                name="key"
                id="route_key_input"
                label="Route Key"
                placeholder="e.g., dashboard"
                required
            />
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Icon</label>
            <x-forms-select-with-icons name="icon" :options="array_keys(ICONS)"></x-forms-select-with-icons>
        </div>


        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Required Roles</label>
            <div class="space-y-2">
                @foreach(['user', 'admin', 'dev'] as $role)
                    <div class="flex items-center">
                        <input type="checkbox" name="required_roles[]" value="{{ $role }}" id="role_{{ $role }}" class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                        <label for="role_{{ $role }}" class="ml-2 block text-sm text-gray-900">{{ ucfirst($role) }}</label>
                    </div>
                @endforeach
            </div>
        </div>
        
        <div class="flex justify-end space-x-3 pt-4">
            <button 
                type="button" 
                @click="$dispatch('hide-modal')"
                class="rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
            >
                Cancel
            </button>
            <button
                type="submit"
                class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
            >
                Save
            </button>
        </div>
    </form>
</div>