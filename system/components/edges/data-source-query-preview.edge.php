@props([
    'query' => '',
    'table_name' => '',
    'filters' => [],
    'tables' => [],
    'joins' => [],
    'selected_columns' => []
])

<div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
    <div class="flex items-center justify-between mb-3">
        <h4 class="text-sm font-medium text-gray-900">Generated SQL Query</h4>
        <div class="flex items-center space-x-2">
            <button type="button"
                    onclick="toggleQueryFormat()"
                    id="format-toggle-btn"
                    class="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
                Compact
            </button>
            <button type="button"
                    onclick="copyToClipboard('{{ addslashes($query) }}')"
                    class="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-600 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
                Copy
            </button>
        </div>
    </div>
    
    <div class="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm overflow-x-auto">
        <pre id="formatted-query">{{ \api\data_sources\format_sql_query($query) }}</pre>
        <pre id="compact-query" style="display: none;">{{ $query }}</pre>
    </div>
    
    <div class="mt-3 pt-3 border-t border-gray-200 space-y-3">
        <!-- Tables -->
        @if(!empty($tables))
            <div>
                <h5 class="text-xs font-medium text-gray-700 mb-2">Tables ({{ count($tables) }}):</h5>
                <div class="flex flex-wrap gap-1">
                    @foreach($tables as $index => $table)
                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium {{ $index === 0 ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800' }}">
                            {{ $table }}
                            @if($index === 0)
                                <span class="ml-1 text-blue-600">(Primary)</span>
                            @endif
                        </span>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Joins -->
        @if(!empty($joins))
            <div>
                <h5 class="text-xs font-medium text-gray-700 mb-2">Joins ({{ count($joins) }}):</h5>
                <div class="space-y-1">
                    @foreach($joins as $join)
                        <div class="text-xs text-gray-600">
                            <span class="font-medium">{{ strtoupper($join['type'] ?? 'INNER') }}</span>
                            <span class="text-gray-500">JOIN</span>
                            <span class="font-medium">{{ $join['table'] ?? 'unknown' }}</span>
                            <span class="text-gray-500">ON</span>
                            <span class="font-medium">{{ $join['left_column'] ?? 'unknown' }}</span>
                            <span class="text-gray-500">=</span>
                            <span class="font-medium">{{ $join['right_column'] ?? 'unknown' }}</span>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Selected Columns -->
        @if(!empty($selected_columns))
            <div>
                <h5 class="text-xs font-medium text-gray-700 mb-2">Selected Columns:</h5>
                <div class="space-y-1">
                    @foreach($selected_columns as $table => $columns)
                        <div class="text-xs text-gray-600">
                            <span class="font-medium">{{ $table }}:</span>
                            <span class="text-gray-500">{{ implode(', ', $columns) }}</span>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Filters -->
        @if(!empty($filters))
            <div>
                <h5 class="text-xs font-medium text-gray-700 mb-2">Applied Filters ({{ count($filters) }}):</h5>
                <div class="space-y-1">
                    @foreach($filters as $filter)
                        <div class="text-xs text-gray-600">
                            <span class="font-medium">{{ $filter['column'] }}</span>
                            <span class="text-gray-500">{{ $filter['operator'] }}</span>
                            @if(!in_array($filter['operator'], ['IS NULL', 'IS NOT NULL']))
                                <span class="font-medium">"{{ $filter['value'] }}"</span>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        @if(empty($filters) && empty($joins) && empty($selected_columns))
            <p class="text-xs text-gray-500">Basic query - will return all records with default column selection</p>
        @endif
    </div>
</div>

<script>
// Use window property to avoid redeclaration issues with HTMX updates
if (typeof window.queryPreviewState === 'undefined') {
    window.queryPreviewState = {
        isFormatted: true
    };
}

function toggleQueryFormat() {
    const formattedQuery = document.getElementById('formatted-query');
    const compactQuery = document.getElementById('compact-query');
    const toggleBtn = document.getElementById('format-toggle-btn');

    if (window.queryPreviewState.isFormatted) {
        // Switch to compact view
        formattedQuery.style.display = 'none';
        compactQuery.style.display = 'block';
        toggleBtn.innerHTML = '<svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path></svg>Formatted';
        window.queryPreviewState.isFormatted = false;
    } else {
        // Switch to formatted view
        formattedQuery.style.display = 'block';
        compactQuery.style.display = 'none';
        toggleBtn.innerHTML = '<svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg>Compact';
        window.queryPreviewState.isFormatted = true;
    }
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show temporary success message
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Copied!';
        button.classList.add('text-green-600');

        setTimeout(function() {
            button.innerHTML = originalText;
            button.classList.remove('text-green-600');
        }, 2000);
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
        alert('Failed to copy query to clipboard');
    });
}
</script>
