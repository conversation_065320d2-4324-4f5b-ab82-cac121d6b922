@props([
    'name' => 'template_data[html]',
    'required' => false,
    'rows' => 6,
    'title' => 'Custom HTML Template',
    'description' => 'Enter custom HTML content that will be embedded in the template.',
    'color' => 'green',
    'placeholder' => 'Enter custom HTML here...'
])

<div class="space-y-3">
    <div class="bg-{{ $color }}-50 border border-{{ $color }}-200 rounded-lg p-4">
        <h4 class="text-sm font-medium text-{{ $color }}-800 mb-2">{{ $title }}</h4>
        <p class="text-sm text-{{ $color }}-700 mb-3">{{ $description }}</p>

        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Custom HTML</label>
            <textarea
                name="{{ $name }}"
                rows="{{ $rows }}"
                placeholder="{{ $placeholder }}"
                class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
                @if($required) required @endif
            ></textarea>
        </div>
        
        <p class="text-xs text-{{ $color }}-600 mt-2">💡 You can use full HTML including CSS and JavaScript</p>
    </div>
</div>
