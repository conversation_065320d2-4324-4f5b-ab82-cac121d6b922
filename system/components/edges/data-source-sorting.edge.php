@props([
    'sorting' => [],
    'available_columns' => []
])

<div class="space-y-4">
    <!-- Header with Add Button -->
    <div class="flex items-center justify-between">
        <div>
            <h3 class="text-sm font-medium text-gray-900">Sorting (ORDER BY)</h3>
            <p class="text-xs text-gray-500 mt-1">Control the order of results</p>
        </div>
        <button type="button"
                hx-post="{{ APP_ROOT }}/api/data_sources/add_sort_row"
                hx-target="#sorting-container"
                hx-swap="beforeend"
                hx-include="form"
                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            Add Sort
        </button>
    </div>

    <!-- Sorting Container -->
    <div id="sorting-container" class="space-y-3">
        @if(empty($sorting))
            <div id="no-sorting-message" class="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No sorting rules</h3>
                <p class="mt-1 text-sm text-gray-500">Results will be returned in database default order.</p>
            </div>
        @else
            @foreach($sorting as $index => $sort)
                <x-data-source-sorting-item
                    :sort="$sort"
                    :sort_index="$index"
                    :available_columns="$available_columns"
                />
            @endforeach
        @endif
    </div>

    <!-- Info Section -->
    <div class="mt-4 p-3 bg-blue-50 rounded-lg">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Sorting Order</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <p>Sort rules are applied in the order they appear. The first rule has the highest priority.</p>
                    <p class="mt-1">You can sort by regular columns, aliased columns, or custom columns.</p>
                </div>
            </div>
        </div>
    </div>
</div>
