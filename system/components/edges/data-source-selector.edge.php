@props([
    'name' => 'data_source_id',
    'label' => 'Data Source',
    'selected' => '',
    'show_preview' => true,
    'show_create_new' => true,
    'onchange' => null,
    'required' => false,
    'class' => '',
    'campaign_id' => null
])

@php
use system\data_source_manager;

// Get available data sources, filtered by campaign if specified
$criteria = [];
if ($campaign_id) {
    $criteria['campaign_id'] = $campaign_id;
}
$data_sources = data_source_manager::get_data_sources($criteria);
$available_tables = data_source_manager::get_available_tables();

// Group data sources by category
$grouped_sources = [];
foreach ($data_sources as $source) {
    $category = $source['category'] ?? 'other';
    if (!isset($grouped_sources[$category])) {
        $grouped_sources[$category] = [];
    }
    $grouped_sources[$category][] = $source;
}

// Category labels
$category_labels = [
    'data_table' => 'Data Tables',
    'email' => 'Email & Campaigns', 
    'users' => 'User Management',
    'system' => 'System Tables',
    'autodesk' => 'Autodesk Integration',
    'other' => 'Other Tables'
];
@endphp

<div class="space-y-4" x-data="{ selectedSource: '{{ $selected }}', showCreateModal: false }">
    <!-- Data Source Selection -->
    <div class="space-y-2">
        <label for="{{ $name }}" class="block text-sm font-medium text-gray-700">
            {{ $label }}
            @if($required)
                <span class="text-red-500">*</span>
            @endif
        </label>

        <div class="flex space-x-2">
            <div class="flex-1">
                <select
                    name="{{ $name }}"
                    id="{{ $name }}"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm {{ $class }}"
                    x-model="selectedSource"
                    hx-get="{{ APP_ROOT }}/api/data_sources/source_info"
                    hx-target="#source_info_container"
                    hx-trigger="change"
                    hx-include="this"
                    @if($show_preview)
                    hx-on:htmx:after-request="if(this.value && !this.value.startsWith('new:')) { htmx.ajax('GET', '{{ APP_ROOT }}/api/data_sources/preview_data', {values: {data_source_id: this.value, limit: 5}, target: '#data_preview_container', swap: 'innerHTML'}); }"
                    @endif
                    @if($onchange) onchange="{{ $onchange }}" @endif
                    @if($required) required @endif
                >
                    <option value="">Select a data source...</option>
                    
                    @if(!empty($data_sources))
                        <optgroup label="Configured Data Sources">
                            @foreach($grouped_sources as $category => $sources)
                                @if(!empty($sources))
                                    <optgroup label="{{ $category_labels[$category] ?? ucfirst($category) }}">
                                        @foreach($sources as $source)
                                            <option value="{{ $source['id'] }}" 
                                                    data-table="{{ $source['table_name'] }}"
                                                    data-category="{{ $source['category'] }}"
                                                    data-description="{{ $source['description'] ?? '' }}">
                                                {{ $source['name'] }} ({{ $source['table_name'] }})
                                            </option>
                                        @endforeach
                                    </optgroup>
                                @endif
                            @endforeach
                        </optgroup>
                    @endif
                    
                    @if($show_create_new && !empty($available_tables))
                        <optgroup label="Create New Data Source">
                            @foreach($available_tables as $table)
                                <option value="new:{{ $table['name'] }}" 
                                        data-table="{{ $table['name'] }}"
                                        data-category="{{ $table['category'] }}"
                                        data-rows="{{ $table['row_count'] }}"
                                        data-description="{{ $table['description'] }}">
                                    New: {{ $table['display_name'] }} ({{ number_format($table['row_count']) }} rows)
                                </option>
                            @endforeach
                        </optgroup>
                    @endif
                </select>
            </div>
            
            @if($show_create_new)
                <button type="button"
                        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        hx-get="{{ APP_ROOT }}/api/data_sources/create_modal"
                        hx-target="#create_modal_container"
                        hx-swap="innerHTML">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    New
                </button>
            @endif
        </div>
        
        <!-- Data Source Info -->
        <div id="source_info_container">
            @if($selected)
                @php
                    $selected_source = null;
                    foreach($data_sources as $source) {
                        if($source['id'] == $selected) {
                            $selected_source = $source;
                            break;
                        }
                    }
                @endphp
                @if($selected_source)
                    <div class="mt-2 p-3 bg-gray-50 rounded-md">
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 1.79 4 4 4h8c0 2.21 1.79 4 4 4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z"></path>
                                </svg>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900">{{ $selected_source['name'] }}</p>
                                <p class="text-sm text-gray-500">{{ $selected_source['description'] ?? '' }}</p>
                                <div class="mt-1 flex items-center space-x-4 text-xs text-gray-500">
                                    <span>Table: <span class="font-mono">{{ $selected_source['table_name'] }}</span></span>
                                    <span>Category: <span class="capitalize">{{ $selected_source['category'] ?? 'other' }}</span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            @endif
        </div>
    </div>
    
    <!-- Data Preview -->
    @if($show_preview)
        <div id="data_preview_container">
            @if($selected && !str_starts_with($selected, 'new:'))
                <div class="space-y-2">
                    <div class="flex items-center justify-between">
                        <h4 class="text-sm font-medium text-gray-900">Data Preview</h4>
                        <button type="button"
                                class="text-sm text-indigo-600 hover:text-indigo-500"
                                hx-get="{{ APP_ROOT }}/api/data_sources/preview_data"
                                hx-vals='{"data_source_id": "{{ $selected }}", "limit": 5}'
                                hx-target="#data_preview_container"
                                hx-swap="innerHTML"
                                hx-indicator="#preview_loading">
                            Refresh
                        </button>
                    </div>

                    <div class="border rounded-md overflow-hidden">
                        <div id="preview_loading" class="htmx-indicator p-4 text-center text-gray-500">
                            <svg class="animate-spin h-5 w-5 mx-auto mb-2" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Loading preview...
                        </div>

                        <div class="p-4 text-center text-gray-500">
                            Select "Refresh" to load data preview
                        </div>
                    </div>
                </div>
            @endif
        </div>
    @endif
    
    <!-- Create New Data Source Modal Container -->
    <div id="create_modal_container"></div>
</div>

<script>
// Handle data source creation success
document.body.addEventListener('dataSourceCreated', function(event) {
    // Refresh the page to update the data source list
    window.location.reload();
});

// Handle modal closing
document.body.addEventListener('closeDataSourceModal', function(event) {
    // Clear the modal container
    document.getElementById('create_modal_container').innerHTML = '';
});
</script>
