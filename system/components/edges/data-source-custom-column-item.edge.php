@props([
    'custom_column' => [
        'sql' => '',
        'alias' => ''
    ],
    'column_index' => 0
])

<div class="border border-gray-200 rounded-lg p-4 bg-gray-50" id="custom-column-item-{{ $column_index }}">
    <div class="flex items-start space-x-4">
        <div class="flex-1 space-y-4">
            <!-- SQL Expression -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    SQL Expression
                </label>
                <textarea
                    name="custom_columns[{{ $column_index }}][sql]"
                    rows="3"
                    placeholder="e.g., CONCAT(first_name, ' ', last_name) or CASE WHEN status = 'active' THEN 1 ELSE 0 END"
                    class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm font-mono"
                    hx-post="{{ APP_ROOT }}/api/data_sources/update_query_preview"
                    hx-target="#query-preview-container"
                    hx-swap="innerHTML"
                    hx-include="form"
                    hx-trigger="input changed delay:1000ms"
                >{{ $custom_column['sql'] ?? '' }}</textarea>
                <p class="mt-1 text-xs text-gray-500">
                    Enter any valid SQL expression. You can use functions, CASE statements, calculations, etc.
                </p>
            </div>

            <!-- Column Alias -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Column Alias <span class="text-red-500">*</span>
                </label>
                <x-forms-input
                    name="custom_columns[{{ $column_index }}][alias]"
                    :value="$custom_column['alias'] ?? ''"
                    placeholder="e.g., full_name, days_remaining, status_flag"
                    class_suffix="text-sm"
                    hx-post="{{ APP_ROOT }}/api/data_sources/update_dependent_sections"
                    hx-target="#query-preview-container"
                    hx-swap="innerHTML"
                    hx-include="form"
                    hx-trigger="input changed delay:500ms"
                />
                <p class="mt-1 text-xs text-gray-500">
                    Required. This will be the column name in your results.
                </p>
            </div>
        </div>

        <!-- Remove Button -->
        <div class="flex-shrink-0">
            <button type="button"
                    hx-delete="{{ APP_ROOT }}/api/data_sources/remove_custom_column"
                    hx-target="#custom-column-item-{{ $column_index }}"
                    hx-swap="outerHTML"
                    hx-include="form"
                    hx-vals='{"column_index": "{{ $column_index }}"}'
                    class="inline-flex items-center p-2 border border-transparent rounded-full text-red-400 hover:text-red-600 hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </button>
        </div>
    </div>
</div>
