@props([
    'fields' => [],
    'data' => [],
    'label' => 'Data Display',
    'class_suffix' => ''
])

<div class="space-y-4 {{ $class_suffix }}">
    @if($label)
        <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $label }}</h3>
    @endif
    
    <div class="grid grid-cols-1 gap-4">
        @foreach($fields as $field_key => $field_label)
            <div class="flex justify-between py-2 border-b border-gray-100">
                <dt class="text-sm font-medium text-gray-500">{{ $field_label }}</dt>
                <dd class="text-sm text-gray-900">
                    @if(isset($data[$field_key]) && !empty($data[$field_key]))
                        {{ $data[$field_key] }}
                    @else
                        <span class="text-gray-400">-</span>
                    @endif
                </dd>
            </div>
        @endforeach
    </div>
</div>
