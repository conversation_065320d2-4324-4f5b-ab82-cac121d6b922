@props([
    'items' => [],
    'columns' => 1,
    'spacing' => 'normal',
    'labelClass' => 'text-sm font-medium text-gray-500',
    'valueClass' => 'mt-1 text-sm text-gray-900',
    'class' => ''
])

@php
    $gridClasses = [
        1 => 'grid-cols-1',
        2 => 'grid-cols-1 sm:grid-cols-2',
        3 => 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
        4 => 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4'
    ];
    
    $spacingClasses = [
        'tight' => 'space-y-2',
        'normal' => 'space-y-3',
        'loose' => 'space-y-4'
    ];
    
    $containerClass = 'grid gap-4 ' . ($gridClasses[$columns] ?? $gridClasses[1]) . ' ' . $class;
    $itemSpacing = $spacingClasses[$spacing] ?? $spacingClasses['normal'];
@endphp

<div class="{{ $containerClass }}">
    @foreach($items as $item)
        @if(is_array($item))
            <div class="{{ $itemSpacing }}">
                <dt class="{{ $item['labelClass'] ?? $labelClass }}">{{ $item['label'] ?? '' }}</dt>
                <dd class="{{ $item['valueClass'] ?? $valueClass }}">
                    @if(isset($item['link']) && $item['link'])
                        <a href="{{ $item['link'] }}" class="text-indigo-600 hover:text-indigo-500">
                            {{ $item['value'] ?? 'N/A' }}
                        </a>
                    @else
                        {{ $item['value'] ?? 'N/A' }}
                    @endif
                </dd>
            </div>
        @else
            <div class="{{ $itemSpacing }}">
                {{ $item }}
            </div>
        @endif
    @endforeach
</div>
