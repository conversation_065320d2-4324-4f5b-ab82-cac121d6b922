@props([
    'custom_table_index' => 0,
    'custom_table' => []
])

<div id="custom-table-row-{{ $custom_table_index }}" class="border border-gray-200 rounded-lg p-4 bg-gray-50">
    <div class="flex items-start justify-between">
        <div class="flex-grow space-y-4">
            <!-- Table Alias -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        Table Alias *
                    </label>
                    <x-forms-input
                        name="custom_tables[{{ $custom_table_index }}][alias]"
                        :value="$custom_table['alias'] ?? ''"
                        placeholder="e.g., lastquote, subq1"
                        required
                        class_suffix="text-sm"
                        hx-post="{{ APP_ROOT }}/api/data_sources/update_query_preview"
                        hx-target="#query-preview-container"
                        hx-swap="innerHTML"
                        hx-include="form"
                        hx-trigger="input changed delay:500ms"
                    />
                    <p class="mt-1 text-xs text-gray-500">Unique alias to reference this table in queries</p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        Join Type
                    </label>
                    <x-forms-select
                        name="custom_tables[{{ $custom_table_index }}][join_type]"
                        :options="[
                            'LEFT JOIN' => 'LEFT JOIN',
                            'INNER JOIN' => 'INNER JOIN', 
                            'RIGHT JOIN' => 'RIGHT JOIN',
                            'FULL JOIN' => 'FULL JOIN'
                        ]"
                        :selected="$custom_table['join_type'] ?? 'LEFT JOIN'"
                        class_suffix="text-sm"
                        hx-post="{{ APP_ROOT }}/api/data_sources/update_query_preview"
                        hx-target="#query-preview-container"
                        hx-swap="innerHTML"
                        hx-include="form"
                        hx-trigger="change"
                    />
                </div>
            </div>

            <!-- SQL Definition -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    SQL Definition *
                </label>
                <textarea 
                    name="custom_tables[{{ $custom_table_index }}][sql]"
                    rows="8"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm font-mono"
                    placeholder="(
    SELECT 
        qi.subscription_id,
        q.id AS quote_id,
        q.quote_status
    FROM autodesk_quote_line_items qi
    JOIN autodesk_quotes q ON q.id = qi.quote_id
    WHERE qi.subscription_id IS NOT NULL
    ORDER BY q.quoted_date DESC
    LIMIT 1
)"
                    hx-post="{{ APP_ROOT }}/api/data_sources/update_query_preview"
                    hx-target="#query-preview-container"
                    hx-swap="innerHTML"
                    hx-include="form"
                    hx-trigger="input changed delay:1000ms"
                    required>{{ $custom_table['sql'] ?? '' }}</textarea>
                <p class="mt-1 text-xs text-gray-500">
                    Enter the SQL for this custom table. Can be a subquery, CTE, or complex join. 
                    Wrap subqueries in parentheses.
                </p>
            </div>

            <!-- Join Condition -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    Join Condition
                </label>
                <x-forms-input
                    name="custom_tables[{{ $custom_table_index }}][join_condition]"
                    :value="$custom_table['join_condition'] ?? ''"
                    placeholder="e.g., subs.subscriptionId = lastquote.subscription_id"
                    class_suffix="text-sm font-mono"
                    hx-post="{{ APP_ROOT }}/api/data_sources/update_query_preview"
                    hx-target="#query-preview-container"
                    hx-swap="innerHTML"
                    hx-include="form"
                    hx-trigger="input changed delay:500ms"
                />
                <p class="mt-1 text-xs text-gray-500">
                    ON condition for the join (e.g., main_table.id = custom_table.foreign_id)
                </p>
            </div>

            <!-- Available Columns (for reference) -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    Available Columns (Optional)
                </label>
                <x-forms-input
                    name="custom_tables[{{ $custom_table_index }}][columns]"
                    :value="$custom_table['columns'] ?? ''"
                    placeholder="e.g., subscription_id, quote_id, quote_status, quoted_date"
                    class_suffix="text-sm"
                />
                <p class="mt-1 text-xs text-gray-500">
                    Comma-separated list of columns this table provides (for reference in column selection)
                </p>
            </div>

            <!-- Description -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    Description (Optional)
                </label>
                <x-forms-input
                    name="custom_tables[{{ $custom_table_index }}][description]"
                    :value="$custom_table['description'] ?? ''"
                    placeholder="e.g., Latest quote for each subscription"
                    class_suffix="text-sm"
                />
                <p class="mt-1 text-xs text-gray-500">
                    Brief description of what this custom table provides
                </p>
            </div>
        </div>

        <!-- Remove Button -->
        <div class="flex-shrink-0 ml-4">
            <button type="button"
                    hx-delete="{{ APP_ROOT }}/api/data_sources/remove_custom_table"
                    hx-target="#custom-table-row-{{ $custom_table_index }}"
                    hx-swap="outerHTML"
                    hx-vals='{"custom_table_index": {{ $custom_table_index }}}'
                    hx-include="form"
                    hx-confirm="Remove this custom table?"
                    class="p-2 text-gray-400 hover:text-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </button>
        </div>
    </div>
</div>
