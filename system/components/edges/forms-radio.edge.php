@props([
    'title' => 'form radio control',
    'description' => 'Basic form radio button',
    'name' => '',
    'value' => '',
    'checked' => false,
    'label' => '',
    'id' => '',
    'class_suffix' => '',
    'extra_attributes' => '',
])

<div class="flex items-center">
    <input id="{{ $id }}"
           name="{{ $name }}"
           type="radio"
           value="{{ $value }}"
           {{ $checked ? 'checked' : '' }}
           class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600 {{ $class_suffix }}"
           {{ $extra_attributes }}
    >
    <label for="{{ $id }}" class="ml-3 block text-sm font-medium leading-6 text-gray-900">{{ $label }}</label>
</div>
