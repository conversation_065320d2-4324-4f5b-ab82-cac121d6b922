@props([
    'title' => 'card',
    'description' => 'A layout card',
    'content' => '',
    'tag_content' => '',  // This will contain the wrapped content
    'padding' => '5',
    'label' => '',
    'id' => 'card_' . rand(0,10000),
    'class_suffix' => '',
    'internal_class_suffix' => '',
    'scrolling' => false,
    'collapsible' => false,
    'collapsed' => false,
])
@php
    print_rr($collapsed,'collapsedy' . $id,true,true);
@endphp
@if ($label != '' && $collapsible)
    @print_rr('start of card' ,'')
<div x-data="{ open_{{ $id }}: {{ (($collapsed || $collapsed == 'true') && $collapsed != 'false') ? 'false' : 'true' }} }" class="m-1 h-full flex flex-col rounded-lg ">
    <div Class="h-full top-0 overflow-{{ $scrolling ? 'auto' : 'hidden' }} bg-white shadow {{ $class_suffix }}" >
         <button @click='open_{{ $id }} = !open_{{ $id }}'
                    class='w-full px-4 py-2 text-left text-lg font-semibold text-gray-700 transition-colors duration-200'
                    :class='open_{{ $id }} ? "bg-gray-100 hover:bg-gray-200" : "bg-white hover:bg-gray-50"'>
                {{ $label }}
                <span x-show='!open_{{ $id }}' class='ml-2'>+</span>
                <span x-show='open_{{ $id }}' class='ml-2'>-</span>
            </button>
    </div>
    <div @if($collapsible) x-show='open_{{ $id }}' @endif class='h-min bg-white shadow px-4 py-2 gap-4 border-t border-gray-200 {{ $internal_class_suffix }}' :class="{ 'flex-grow': open_{{ $id }} }">
@else
<div class="m-1 top-0 overflow-{{ $scrolling ? 'auto' : 'hidden' }} rounded-lg bg-white shadow {{ $class_suffix }}">
        @if ($label != '')
            <div class="px-4 py-2 text-left text-lg font-semibold text-gray-700 bg-gray-100 hover:bg-gray-300 focus:outline-none">
                {{ $label }}
            </div>
        @endif
    <div class='h-min px-4 py-2 gap-4 border-t border-gray-200 bg-white shadow {{ $internal_class_suffix }}'>
@endif
            {{ $content }}
            {{= tag_content =}}
    </div>
</div>
            @print_rr('end of card' ,'')
