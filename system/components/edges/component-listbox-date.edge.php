{{-- File: /resources/components/listbox-date.blade.php --}}
@props(['dueDates', 'dated', 'setDated'])

<div x-data="{ open: false }" @click.away="open = false" class="shrink-0">
    <label class="sr-only">Add a due date</label>
    <div class="relative">
        <button
                @click="open = !open"
                type="button"
                class="relative inline-flex items-center whitespace-nowrap rounded-full bg-gray-50 px-2 py-2 text-sm font-medium text-gray-500 hover:bg-gray-100 sm:px-3">
            <svg
                    aria-hidden="true"
                    class="{{ $dated['value'] === null ? 'text-gray-300' : 'text-gray-500' }} size-5 shrink-0 sm:-ml-1"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg">
                <path
                        fill-rule="evenodd"
                        d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                        clip-rule="evenodd"></path>
            </svg>
            <span
                    class="{{ $dated['value'] === null ? '' : 'text-gray-900' }} hidden truncate sm:ml-2 sm:block">
    {{ $dated['value'] === null ? 'Due date' : $dated['name'] }}
   </span>
        </button>

        <div
                x-show="open"
                x-transition:enter="transition ease-out duration-100"
                x-transition:enter-start="transform opacity-0 scale-95"
                x-transition:enter-end="transform opacity-100 scale-100"
                x-transition:leave="transition ease-in duration-75"
                x-transition:leave-start="transform opacity-100 scale-100"
                x-transition:leave-end="transform opacity-0 scale-95"
                class="absolute right-0 z-10 mt-1 max-h-56 w-52 overflow-auto rounded-lg bg-white py-3 text-base shadow outline outline-1 outline-black/5 sm:text-sm"
                style="display: none;"
                @click.away="open = false">
            @foreach ($dueDates as $dueDate)
                <button
                        wire:click="$set('dated', {{ json_encode($dueDate) }})"
                        type="button"
                        class="cursor-default select-none bg-white px-3 py-2 data-[focus]:relative data-[focus]:bg-gray-100 data-[focus]:hover:outline-none w-full text-left">
                    <div class="flex items-center">
                        <span class="block truncate font-medium">{{ $dueDate['name'] }}</span>
                    </div>
                </button>
            @endforeach
        </div>
    </div>
</div>
