@props([
    'address' => [],
    'fields' => [
        'address1' => 'address1',
        'address2' => 'address2', 
        'address3' => 'address3',
        'city' => 'city',
        'state' => 'state_province',
        'postal' => 'postal_code',
        'country' => 'country'
    ],
    'emptyMessage' => 'No address information available',
    'class' => 'text-sm text-gray-900 space-y-1'
])

@php
    $hasAddress = false;
    foreach($fields as $field) {
        if (!empty($address[$field])) {
            $hasAddress = true;
            break;
        }
    }
@endphp

<div class="{{ $class }}">
    @if($hasAddress)
        @if(!empty($address[$fields['address1']]))
            <div>{{ $address[$fields['address1']] }}</div>
        @endif
        @if(!empty($address[$fields['address2']]))
            <div>{{ $address[$fields['address2']] }}</div>
        @endif
        @if(!empty($address[$fields['address3']]))
            <div>{{ $address[$fields['address3']] }}</div>
        @endif
        <div>
            {{ $address[$fields['city']] ?? '' }}
            @if(!empty($address[$fields['state']]) && !empty($address[$fields['city']]))
                , {{ $address[$fields['state']] }}
            @elseif(!empty($address[$fields['state']]))
                {{ $address[$fields['state']] }}
            @endif
            {{ $address[$fields['postal']] ?? '' }}
        </div>
        @if(!empty($address[$fields['country']]))
            <div>{{ $address[$fields['country']] }}</div>
        @endif
    @else
        <div class="text-gray-500">{{ $emptyMessage }}</div>
    @endif
</div>
