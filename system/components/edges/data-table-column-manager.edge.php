@props([
    'columns' => [],
    'table_name' => '',
    'db_table' => '',
    'callback' => '',
    'column_preferences' => [],
    'available_fields' => '',
    'current_data_source_type' => 'hardcoded',
    'current_data_source_id' => null,
    'keep_open' => false
])

@php
// Process existing preferences to match new structure
$column_structure = $column_preferences['structure'] ?? [];
$hidden_columns = $column_preferences['hidden'] ?? [];

// Debug removed for production

// Use structure from preferences if it exists, otherwise generate from original columns
if (!empty($column_structure)) {
    $processed_columns = $column_structure;
} else {
    // Generate unique column IDs and prepare column structure
    $processed_columns = [];
    foreach ($columns as $index => $col) {
        $column_id = 'col_' . $index . '_' . md5($col['label']);
        $processed_columns[] = [
            'id' => $column_id,
            'label' => $col['label'],
            'field' => $col['field'],
            'filter' => $col['filter'] ?? false,
            'fields' => is_array($col['field']) ? $col['field'] : [$col['field']], // Always array for consistency
            'visible' => !in_array($column_id, $hidden_columns)
        ];
    }

    // Note: We don't automatically save the configuration here anymore to prevent
    // overwriting data source settings during table regeneration. Configuration
    // is only saved when there are explicit user actions (column changes, data source changes, etc.)
}

// Use the data source settings passed as props (these are already determined in the parent component)
tcs_log("Column manager props: table=$table_name, type=$current_data_source_type, id=$current_data_source_id", 'data_table_saga');

// Get available data sources
$available_data_sources = [];
try {
    $data_sources = database::table('autobooks_data_sources')
        ->where('status', '=', 'active')
        ->get();

    foreach ($data_sources as $source) {
        $available_data_sources[] = [
            'id' => $source['id'],
            'name' => $source['name'],
            'description' => $source['description'],
            'category' => $source['category'] ?? 'other'
        ];
    }
} catch (Exception $e) {
    // Handle error gracefully
    $available_data_sources = [];
}

$column_string = '';

    if (!empty($available_fields)) {
        $column_string = count($available_fields) > 0 ? '["' . implode('","', $available_fields) . '"]' : '[]';
    } else if (!empty($db_table)) {
        $db_columns = database::table($db_table)->getColumns();
        $column_string = count($db_columns) > 0 ? '["' . implode('","', $db_columns) . '"]' : '[]';
    } else {
        $column_string = '[]'; // Default to empty array if no fields available
    }
@endphp
<div id="column-manager-{{ $table_name }}" x-data='{
    open: {{ $keep_open ? 'true' : 'false' }},
    newColumnName: "",
    showAddColumn: false,


    initSortable() {
        this.$nextTick(() => {
            const container = this.$refs.columnList;
            if (container && window.Sortable) {
                // Main column sorting - server-side save on drag end
                new Sortable(container, {
                    animation: 150,
                    handle: ".column-drag-handle",
                    filter: ".field-item", // Don"t drag field items when dragging columns
                    onEnd: (evt) => {
                        // Send column reorder to server via HTMX
                        const columnIds = Array.from(container.querySelectorAll("[data-column-id]"))
                            .map(item => item.dataset.columnId);

                        htmx.ajax("POST", "{{ APP_ROOT }}/api/data_table/column_preferences/reorder_columns", {
                            values: {
                                table_name: "{{ $table_name }}",
                                callback: "{{ $callback }}",
                                data_source: "{{ $current_data_source_id  }}",
                                column_order: JSON.stringify(columnIds)
                            },
                            target: ".data_table",
                            swap: "outerHTML"
                        });
                    }
                });

                // Field sorting within columns - server-side save on drag end
                container.querySelectorAll(".field-container").forEach(fieldContainer => {
                    new Sortable(fieldContainer, {
                        group: "fields", // Allow dragging between columns
                        animation: 150,
                        handle: ".field-drag-handle",
                        onEnd: (evt) => {
                            const fieldName = evt.item.dataset.fieldName;
                            const targetColumnId = evt.to.closest("[data-column-id]").dataset.columnId;
                            const sourceColumnId = evt.from.closest("[data-column-id]").dataset.columnId;

                            if (targetColumnId !== sourceColumnId) {
                                // Send field move to server via HTMX
                                htmx.ajax("POST", "{{ APP_ROOT }}/api/data_table/column_preferences/move_field", {
                                    values: {
                                        table_name: "{{ $table_name }}",
                                        callback: "{{ $callback }}",
                                        data_source: "{{ $current_data_source_id  }}",
                                        field_name: fieldName,
                                        source_column_id: sourceColumnId,
                                        target_column_id: targetColumnId
                                    },
                                    target: ".data_table",
                                    swap: "outerHTML"
                                });
                            }
                        }
                    });
                });
            }
        });
    }
}'
x-init="$nextTick(() => initSortable())"
class="relative inline-block text-left"
@click.away="open = false">

    <!-- Column Manager Button -->
    <button type="button"
            @click="open = !open; if(open) $nextTick(() => initSortable())"
            class="inline-flex items-center gap-x-1.5 rounded-md bg-white px-2.5 py-1.5 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
            aria-expanded="false"
            aria-haspopup="true">
        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 4.5v15m6-15v15m-10.875 0h15.75c.621 0 1.125-.504 1.125-1.125V5.625c0-.621-.504-1.125-1.125-1.125H4.125C3.504 4.5 3 5.004 3 5.625v13.5c0 .621.504 1.125 1.125 1.125z" />
        </svg>
        Columns
        <svg class="h-4 w-4" :class="{'rotate-180': open}" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
        </svg>
    </button>

    <!-- Dropdown Panel -->
    <div x-show="open"
         x-transition:enter="transition ease-out duration-100"
         x-transition:enter-start="transform opacity-0 scale-95"
         x-transition:enter-end="transform opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="transform opacity-100 scale-100"
         x-transition:leave-end="transform opacity-0 scale-95"
         class="absolute right-0 z-20 mt-2 w-96 max-h-screen origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none overflow-hidden flex flex-col"
         style="height: 80vh;"
         role="menu"
         aria-orientation="vertical"
         tabindex="-1">
        
        <!-- Header -->
        <div class="p-4 border-b border-gray-200 bg-gray-50">
            <div class="flex items-center justify-between mb-3">
                <h3 class="text-sm font-medium text-gray-900">Manage Columns</h3>
                <div class="flex gap-2">

                    <button type="button"
                            @click="showAddColumn = !showAddColumn"
                            class="text-xs px-2 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                        + Column
                    </button>
                    <button type="button"
                            hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/show_all_columns"
                            hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}"}'
                            hx-target=".data_table"
                            hx-swap="outerHTML"
                            class="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                        Show All
                    </button>
                    <button type="button"
                            hx-post="{{ APP_ROOT }}/api/data_table_storage/hide_all_columns"
                            hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}"}'
                            hx-target=".data_table"
                            hx-swap="outerHTML"
                            class="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200">
                        Hide All
                    </button>
                </div>
            </div>

            <!-- Data Source Selector -->
            <div class="flex items-center space-x-3 mb-3">
                <label for="data-source-select" class="text-xs font-medium text-gray-700">Data Source:</label>
                <select id="data-source-select"
                        hx-post="{{ APP_ROOT }}/api/data_table_storage/update_data_source_and_columns"
                        hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}"}'
                        hx-target=".data_table"
                        hx-swap="outerHTML"
                        hx-trigger="change"
                        name="data_source_selection"
                        class="text-xs border border-gray-300 rounded px-2 py-1 bg-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="hardcoded" {{ $current_data_source_type === 'hardcoded' ? 'selected' : '' }}>
                        Default (Hardcoded Data)
                    </option>
                    @if(!empty($available_data_sources))
                        @php
                            $grouped_sources = [];
                            foreach ($available_data_sources as $source) {
                                $category = $source['category'];
                                if (!isset($grouped_sources[$category])) {
                                    $grouped_sources[$category] = [];
                                }
                                $grouped_sources[$category][] = $source;
                            }

                            $category_labels = [
                                'data_table' => 'Data Tables',
                                'email' => 'Email & Campaigns',
                                'users' => 'User Management',
                                'system' => 'System Tables',
                                'autodesk' => 'Autodesk Integration',
                                'other' => 'Other'
                            ];
                        @endphp
                        @foreach($grouped_sources as $category => $sources)
                            <optgroup label="{{ $category_labels[$category] ?? ucfirst($category) }}">
                                @foreach($sources as $source)
                                    <option value="{{ $source['id'] }}"
                                            {{ $current_data_source_id == $source['id'] ? 'selected' : '' }}>
                                        {{ $source['name'] }}
                                        @if($source['description'])
                                            - {{ $source['description'] }}
                                        @endif
                                    </option>
                                @endforeach
                            </optgroup>
                        @endforeach
                    @endif
                </select>
                @if($current_data_source_type === 'data_source' && $current_data_source_id)
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Data Source
                    </span>
                @else
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Hardcoded
                    </span>
                @endif
            </div>

            <!-- Add Column Form -->
            <div x-show="showAddColumn" x-transition class="mb-3 p-2 bg-blue-50 rounded border">
                <form hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/add_column"
                      hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}"}'
                      hx-target=".data_table"
                      hx-swap="outerHTML"
                      @htmx:after-request="showAddColumn = false; newColumnName = ''"
                      class="flex gap-2 items-center">
                    <input type="text"
                           name="column_name"
                           x-model="newColumnName"
                           placeholder="Column name (e.g., Contact Info)"
                           class="flex-1 text-xs px-2 py-1 border border-gray-300 rounded"
                           required>
                    <button type="submit"
                            class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                        Add
                    </button>
                    <button type="button"
                            @click="showAddColumn = false"
                            class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                        Cancel
                    </button>
                </form>
            </div>

            <div class="text-xs text-gray-500">
                Drag columns to reorder • Drag fields between columns to combine • Click to show/hide
            </div>
        </div>

        <!-- Column List -->
        <div class="flex-1 overflow-y-auto p-4">
            <div x-ref="columnList" class="space-y-3">
                @foreach($processed_columns as $column)
                    <div class="border border-gray-200 rounded-lg bg-white"
                         data-column-id="{{ $column['id'] }}">

                        <!-- Column Header -->
                        <div class="flex items-center p-3 bg-gray-50 rounded-t-lg">
                            <!-- Column Drag Handle -->
                            <div class="column-drag-handle mr-2 text-gray-400 hover:text-gray-600 cursor-move">
                                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                </svg>
                            </div>

                            <!-- Visibility Checkbox -->
                            <label class="flex items-center flex-1 cursor-pointer">
                                @php
                                    $is_checked = !in_array($column['id'], $hidden_columns);

                                    print_rr("is_checked: " . ($is_checked ? 'true' : 'false') . " for column " . $column['id']);
                                @endphp
                                <input type="checkbox"
                                       {{ $is_checked ? 'checked' : '' }}
                                       hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/toggle_column"
                                       hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}", "column_id": "{{ $column['id'] }}", "data_source": "{{ $current_data_source_id }}"}'
                                       hx-target=".data_table"
                                       hx-swap="outerHTML"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                <span class="text-sm font-medium text-gray-900">{{ $column['label'] }}</span>
                            </label>

                            <!-- Column Type Indicator -->
                            <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium {{ $column['filter'] ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800' }}">
                                    {{ $column['filter'] ? 'Filterable' : 'Display' }}
                                </span>
                            </div>

                            <!-- Field Count Badge -->
                            <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                    {{ count($column['fields']) }} field{{ count($column['fields']) !== 1 ? 's' : '' }}
                                </span>
                            </div>

                            <!-- Remove Column Button -->
                            <button type="button"
                                    hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/remove_column"
                                    hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}", "column_id": "{{ $column['id'] }}"}'
                                    hx-target=".data_table"
                                    hx-swap="outerHTML"
                                    hx-confirm="Are you sure you want to remove this column?"
                                    class="ml-2 text-red-400 hover:text-red-600"
                                    title="Remove Column">
                                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                            </button>
                        </div>

                        <!-- Fields Container -->
                        <div class="field-container p-2 min-h-12 bg-gray-25 rounded-b-lg border-t border-gray-100">
                            <!-- Add Field Dropdown -->
                            <div class="mb-2">
                                <select hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/add_field_to_column"
                                        hx-target=".data_table"
                                        hx-swap="outerHTML"
                                        hx-trigger="change[target.value != '']"
                                        hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}", "column_id": "{{ $column['id'] }}"}'
                                        name="field_name"
                                        @htmx:after-request="$event.target.value = ''"
                                        class="text-xs px-2 py-1 border border-gray-300 rounded bg-white">
                                    <option value="">+ Add field...</option>
                                    @php
                                        $available_field_list = !empty($available_fields) ? $available_fields : (
                                            !empty($db_table) ? database::table($db_table)->getColumns() : []
                                        );
                                    @endphp
                                    @foreach($available_field_list as $field)
                                        @if(!in_array($field, $column['fields']))
                                            <option value="{{ $field }}">{{ $field }}</option>
                                        @endif
                                    @endforeach
                                </select>
                            </div>

                            <!-- Existing Fields -->
                            @foreach($column['fields'] as $field)
                                <div class="field-item inline-flex items-center gap-x-1 bg-white px-2 py-1 m-1 rounded border border-gray-200 text-xs cursor-move"
                                     data-field-name="{{ $field }}">
                                    <!-- Field Drag Handle -->
                                    <div class="field-drag-handle text-gray-400 hover:text-gray-600">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />
                                        </svg>
                                    </div>
                                    <span class="text-gray-700">{{ $field }}</span>
                                    <!-- Remove Field Button -->
                                    <button type="button"
                                            hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/remove_field_from_column"
                                            hx-target=".data_table"
                                            hx-swap="outerHTML"
                                            hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}", "column_id": "{{ $column['id'] }}", "field_name": "{{ $field }}"}'
                                            class="text-gray-400 hover:text-red-600 ml-1">
                                        <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>
                            @endforeach

                            <!-- Drop Zone Indicator -->
                            @if(empty($column['fields']))
                                <div class="text-xs text-gray-400 text-center py-2">
                                    Drop fields here or use dropdown above
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Footer Actions -->
        <div class="p-4 border-t border-gray-200 bg-gray-50">
            <div class="flex justify-between items-center">
                <div class="flex gap-2 items-center">
                    <button type="button"
                            hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/hide_all_columns"
                            hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}"}'
                            hx-target=".data_table"
                            hx-swap="outerHTML"
                            class="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200">
                        Hide All
                    </button>
                    @php
                        $total_fields = array_sum(array_map(function($col) { return count($col['fields']); }, $processed_columns));
                    @endphp
                    <span class="text-xs text-gray-500">{{ count($processed_columns) }} columns, {{ $total_fields }} fields</span>
                </div>
                <button type="button"
                        @click="open = false"
                        class="text-xs px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                    Done
                </button>
            </div>
        </div>
    </div>
</div>
