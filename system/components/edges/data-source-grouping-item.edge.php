@props([
    'group' => [
        'column' => ''
    ],
    'group_index' => 0,
    'available_columns' => []
])

<div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-md" id="group-item-{{ $group_index }}">
    <div class="flex-1">
        <!-- Column Selection -->
        <div>
            <label class="block text-xs font-medium text-gray-700">Column</label>
            @php
                $column_options = [];
                foreach ($available_columns as $column) {
                    // Handle both string and array formats
                    if (is_array($column)) {
                        // If it's an array, it might be a column info array with 'Field' key
                        $column_name = $column['Field'] ?? $column['name'] ?? (string)$column;
                    } else {
                        // If it's a string, use it directly
                        $column_name = (string)$column;
                    }

                    if (!empty($column_name)) {
                        $column_options[$column_name] = $column_name;
                    }
                }
            @endphp
            <x-forms-select 
                name="grouping[{{ $group_index }}][column]"
                :options="$column_options"
                :selected="$group['column']"
                class_suffix="text-sm"
                hx-post="{{ APP_ROOT }}/api/data_sources/update_query_preview"
                hx-target="#query-preview-container"
                hx-swap="innerHTML"
                hx-include="form"
                hx-trigger="change"
            />
        </div>
    </div>
    
    <!-- Remove Button -->
    <div class="flex-shrink-0">
        <button type="button"
                hx-post="{{ APP_ROOT }}/api/data_sources/remove_group_row"
                hx-target="#group-item-{{ $group_index }}"
                hx-swap="outerHTML"
                hx-include="form"
                hx-vals='{"group_index": "{{ $group_index }}"}'
                class="inline-flex items-center p-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
        </button>
    </div>
</div>
