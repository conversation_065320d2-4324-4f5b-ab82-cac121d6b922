@props([
    'type' => 'default',
    'size' => 'sm',
    'icon' => null,
    'class' => ''
])

@php
    $baseClasses = 'inline-flex items-center font-medium rounded-full';

    $sizeClasses = [
        'xs' => 'px-2 py-1 text-xs',
        'sm' => 'px-2 py-1 text-xs',
        'md' => 'px-3 py-1 text-sm',
        'lg' => 'px-4 py-2 text-base'
    ];

    $typeClasses = [
        'default' => 'bg-gray-100 text-gray-800',
        'primary' => 'bg-blue-100 text-blue-800',
        'success' => 'bg-green-100 text-green-800',
        'warning' => 'bg-yellow-100 text-yellow-800',
        'danger' => 'bg-red-100 text-red-800',
        'info' => 'bg-indigo-100 text-indigo-800',
        'commercial' => 'bg-blue-100 text-blue-800',
        'active' => 'bg-green-100 text-green-800'
    ];

    // Define icon paths for badges
    $iconPaths = [
        'check' => 'M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
    ];

    $classes = $baseClasses . ' ' . ($sizeClasses[$size] ?? $sizeClasses['sm']) . ' ' . ($typeClasses[$type] ?? $typeClasses['default']) . ' ' . $class;
@endphp

<span class="{{ $classes }}">
    @if($icon)
        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            @if(isset($iconPaths[$icon]))
                <path fill-rule="evenodd" d="{{ $iconPaths[$icon] }}" clip-rule="evenodd"></path>
            @else
                {!! $icon !!}
            @endif
        </svg>
    @endif
    {{= tag_content =}}
</span>
