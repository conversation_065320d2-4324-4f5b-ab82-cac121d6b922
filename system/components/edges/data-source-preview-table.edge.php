@props([
    'data' => [],
    'columns' => []
])
<template>
<div id="preview-loading" class="htmx-indicator text-center py-8 text-gray-500">
    <svg class="animate-spin h-8 w-8 mx-auto mb-4" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    Loading preview data...
</div>
</template>
@if(empty($data))
    <div class="text-center py-8 text-gray-500">
        No data available for preview
    </div>
@else
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    @foreach($columns as $column)
                        <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ $column }}</th>
                    @endforeach
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @foreach($data as $row)
                    <tr>
                        @foreach($columns as $column)
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                                @php
                                    $value = $row[$column] ?? '';
                                    if (is_string($value) && strlen($value) > 50) {
                                        $value = substr($value, 0, 50) . '...';
                                    }
                                @endphp
                                {{ $value }}
                            </td>
                        @endforeach
                    </tr>
                @endforeach
            </tbody>
        </table>
        
        <div class="mt-4 text-sm text-gray-500 text-center">
            Showing {{ count($data) }} sample records
        </div>
    </div>
@endif
