@props([
    'custom_columns' => []
])

<div class="space-y-4">
    <!-- Header with Add Button -->
    <div class="flex items-center justify-between">
        <div>
            <h3 class="text-sm font-medium text-gray-900">Custom Columns</h3>
            <p class="text-xs text-gray-500 mt-1">Add calculated fields, functions, and custom SQL expressions</p>
        </div>
        <button type="button"
                hx-post="{{ APP_ROOT }}/api/data_sources/add_custom_column"
                hx-target="#custom-columns-container"
                hx-swap="beforeend"
                hx-include="form"
                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            Add Custom Column
        </button>
    </div>

    <!-- Custom Columns Container -->
    <div id="custom-columns-container" class="space-y-3">
        @if(empty($custom_columns))
            <div id="no-custom-columns-message" class="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No custom columns</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by adding a custom SQL column.</p>
            </div>
        @else
            @foreach($custom_columns as $index => $custom_column)
                <x-data-source-custom-column-item
                    :custom_column="$custom_column"
                    :column_index="$index"
                />
            @endforeach
        @endif
    </div>

    <!-- Examples Section -->
    <div class="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 class="text-sm font-medium text-blue-900 mb-2">Examples:</h4>
        <div class="space-y-2 text-xs text-blue-800">
            <div>
                <strong>Concatenation:</strong> 
                <code class="bg-blue-100 px-1 rounded">CONCAT(first_name, ' ', last_name)</code>
                <span class="text-blue-600">→ alias: full_name</span>
            </div>
            <div>
                <strong>Conditional:</strong> 
                <code class="bg-blue-100 px-1 rounded">CASE WHEN status = 'active' THEN 'Active' ELSE 'Inactive' END</code>
                <span class="text-blue-600">→ alias: status_label</span>
            </div>
            <div>
                <strong>Date calculation:</strong> 
                <code class="bg-blue-100 px-1 rounded">DATEDIFF(end_date, NOW())</code>
                <span class="text-blue-600">→ alias: days_remaining</span>
            </div>
            <div>
                <strong>Math:</strong> 
                <code class="bg-blue-100 px-1 rounded">price * quantity * (1 - discount/100)</code>
                <span class="text-blue-600">→ alias: total_amount</span>
            </div>
        </div>
    </div>
</div>
