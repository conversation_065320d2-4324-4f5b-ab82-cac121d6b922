<?php
/**
 * Test custom table row component
 */

// Include the necessary files
require_once 'system/classes/startup_sequence.class.php';
require_once 'system/functions/functions.php';

// Initialize the system
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . '/system/config/path_schema.php';
$path['fs_system'] = $path['fs_app_root'] . '/' . $schema['system']['root'];
startup_sequence::start($path, $schema);

use edge\edge;

echo "<h1>Test Custom Table Row Component</h1>";

try {
    // Test data matching your database entry
    $test_custom_table = [
        'alias' => 'lastquote',
        'sql' => 'SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id

FROM autodesk_quote_line_items qi
JOIN autodesk_quotes q ON q.id = qi.quote_id

WHERE qi.subscription_id IS NOT NULL
  AND q.quote_status NOT IN (\'Expired\', \'Cancelled\')

ORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC

LIMIT 1',
        'join_type' => 'LEFT JOIN',
        'join_condition' => 'subs.subscriptionId = lastquote.subscription_id',
        'columns' => 'quote_id, quote_status, quote_number, quoted_date',
        'description' => ''
    ];
    
    echo "<h2>Test Custom Table Data:</h2>";
    echo "<pre>" . json_encode($test_custom_table, JSON_PRETTY_PRINT) . "</pre>";
    
    echo "<h2>Testing data-source-custom-table-row Component:</h2>";
    
    $component_html = Edge::render('data-source-custom-table-row', [
        'custom_table_index' => 0,
        'custom_table' => $test_custom_table
    ]);
    
    if (is_string($component_html) && strlen($component_html) > 100) {
        echo "<p style='color: green;'>✓ Component rendered successfully (length: " . strlen($component_html) . " characters)</p>";
        
        // Check for key elements
        $checks = [
            'lastquote' => strpos($component_html, 'lastquote') !== false,
            'LEFT JOIN' => strpos($component_html, 'LEFT JOIN') !== false,
            'quote_id' => strpos($component_html, 'quote_id') !== false,
            'textarea' => strpos($component_html, '<textarea') !== false,
            'custom_tables[0]' => strpos($component_html, 'custom_tables[0]') !== false
        ];
        
        echo "<h3>Component Content Checks:</h3>";
        foreach ($checks as $check => $found) {
            $status = $found ? '✓' : '✗';
            $color = $found ? 'green' : 'red';
            echo "<p style='color: $color;'>$status $check</p>";
        }
        
        echo "<h3>Rendered Component HTML:</h3>";
        echo "<div style='border: 1px solid #ddd; padding: 10px; background: #f9f9f9;'>";
        echo $component_html;
        echo "</div>";
        
        echo "<h3>Raw HTML (for debugging):</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; max-height: 400px;'>";
        echo htmlspecialchars($component_html);
        echo "</pre>";
        
    } else {
        echo "<p style='color: red;'>✗ Component failed to render or returned empty content</p>";
        echo "<pre>" . htmlspecialchars(print_r($component_html, true)) . "</pre>";
    }
    
    // Test with empty custom table (like when adding new)
    echo "<h2>Testing with Empty Custom Table (New):</h2>";
    
    $empty_component_html = Edge::render('data-source-custom-table-row', [
        'custom_table_index' => 1,
        'custom_table' => []
    ]);
    
    if (is_string($empty_component_html) && strlen($empty_component_html) > 100) {
        echo "<p style='color: green;'>✓ Empty component rendered successfully</p>";
        
        // Check that form fields are empty but present
        if (strpos($empty_component_html, 'name="custom_tables[1]') !== false) {
            echo "<p style='color: green;'>✓ Form fields have correct names for index 1</p>";
        } else {
            echo "<p style='color: red;'>✗ Form fields don't have correct names</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Empty component failed to render</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='data_sources'>← Back to Data Sources</a></p>";
?>
