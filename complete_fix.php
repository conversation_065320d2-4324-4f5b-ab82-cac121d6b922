<?php
// Complete fix for data sources routing
const DS = DIRECTORY_SEPARATOR;
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . DS . 'system'. DS . 'config'. DS . 'path_schema.php';
$ds = DS;
$path['fs_system'] = "{$path['fs_app_root']}{$ds}{$schema['system']['root']}" ;

$input_params = array_merge($_GET, $_POST);
define('INPUT_PARAMS',$input_params);
const DEBUG_MODE = true;
const API_RUN = false;

require_once $path['fs_system'] . DS . 'classes'. DS . 'startup_sequence.class.php';
require_once $path['fs_system'] . DS . 'functions'. DS . 'functions.php';

startup_sequence::start($path,$schema);

use system\database;

echo "<h1>Complete Data Sources Routing Fix</h1>";

try {
    // Step 1: Fix the navigation entry
    echo "<h2>Step 1: Fixing Navigation Entry</h2>";
    
    $nav = database::table('autobooks_navigation')
        ->where('route_key', 'data_sources')
        ->where('parent_path', 'root')
        ->first();
    
    if ($nav) {
        echo "<p>Current navigation entry:</p>";
        echo "<ul>";
        echo "<li>file_path: " . $nav['file_path'] . "</li>";
        echo "<li>is_system: " . ($nav['is_system'] ? 'true' : 'false') . "</li>";
        echo "</ul>";
        
        // Update the navigation entry
        database::table('autobooks_navigation')
            ->where('id', $nav['id'])
            ->update([
                'file_path' => 'data_sources',
                'is_system' => 1,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        
        echo "<p style='color: green;'>✓ Navigation entry updated</p>";
    } else {
        echo "<p style='color: red;'>Navigation entry not found - creating it...</p>";
        
        database::table('autobooks_navigation')->insert([
            'parent_path' => 'root',
            'route_key' => 'data_sources',
            'name' => 'Data Sources',
            'icon' => 'database',
            'file_path' => 'data_sources',
            'required_roles' => '[]',
            'sort_order' => 15,
            'show_navbar' => 1,
            'can_delete' => 0,
            'is_system' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
        echo "<p style='color: green;'>✓ Navigation entry created</p>";
    }
    
    // Step 2: Create fallback files in resources/views as backup
    echo "<h2>Step 2: Creating Fallback Files</h2>";
    
    $resources_dir = 'resources/views/data_sources';
    if (!is_dir($resources_dir)) {
        mkdir($resources_dir, 0755, true);
        echo "<p>✓ Created directory: $resources_dir</p>";
    }
    
    // Create main data_sources.edge.php in resources/views
    $main_fallback = 'resources/views/data_sources.edge.php';
    if (!file_exists($main_fallback)) {
        $content = '<?php
// Fallback router for data sources - redirects to system views
$path = $_SERVER["REQUEST_URI"] ?? "";
$system_file = __DIR__ . "/../system/views/data_sources.edge.php";

if (file_exists($system_file)) {
    include $system_file;
} else {
    echo "<div class=\"p-4 bg-red-100 border border-red-400 text-red-700 rounded\">";
    echo "<h3 class=\"font-bold\">Error</h3>";
    echo "<p>Data sources system files not found</p>";
    echo "</div>";
}
?>';
        file_put_contents($main_fallback, $content);
        echo "<p style='color: green;'>✓ Created fallback file: $main_fallback</p>";
    }
    
    // Create individual fallback files
    $fallback_files = [
        'create.edge.php' => 'create.edge.php',
        'edit.edge.php' => 'edit.edge.php', 
        'preview.edge.php' => 'preview.edge.php'
    ];
    
    foreach ($fallback_files as $filename => $system_file) {
        $fallback_path = "$resources_dir/$filename";
        if (!file_exists($fallback_path)) {
            $content = '<?php
// Fallback for ' . $filename . ' - redirects to system views
$system_file = __DIR__ . "/../../system/views/data_sources/' . $system_file . '";

if (file_exists($system_file)) {
    include $system_file;
} else {
    echo "<div class=\"p-4 bg-red-100 border border-red-400 text-red-700 rounded\">";
    echo "<h3 class=\"font-bold\">Error</h3>";
    echo "<p>System file not found: ' . $system_file . '</p>";
    echo "</div>";
}
?>';
            file_put_contents($fallback_path, $content);
            echo "<p style='color: green;'>✓ Created fallback file: $fallback_path</p>";
        }
    }
    
    // Step 3: Verify system files exist
    echo "<h2>Step 3: Verifying System Files</h2>";
    
    $system_files = [
        'system/views/data_sources.edge.php',
        'system/views/data_sources/data_sources.edge.php',
        'system/views/data_sources/create.edge.php',
        'system/views/data_sources/edit.edge.php',
        'system/views/data_sources/preview.edge.php'
    ];
    
    foreach ($system_files as $file) {
        if (file_exists($file)) {
            echo "<p style='color: green;'>✓ Found: $file</p>";
        } else {
            echo "<p style='color: red;'>✗ Missing: $file</p>";
        }
    }
    
    echo "<h2 style='color: green;'>Fix Complete!</h2>";
    echo "<p>The routing should now work. Try these links:</p>";
    echo "<ul>";
    echo "<li><a href='data_sources'>Main Data Sources Page</a></li>";
    echo "<li><a href='data_sources/create'>Create Data Source</a></li>";
    echo "<li><a href='data_sources/1/edit'>Edit Data Source 1</a></li>";
    echo "<li><a href='data_sources/1/preview'>Preview Data Source 1</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
