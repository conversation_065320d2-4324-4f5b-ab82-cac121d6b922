<?php
/**
 * Test Data Source Integration with Data Tables
 * 
 * This demonstrates the new data source selector functionality
 */

require_once 'system/autoloader.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<!DOCTYPE html>";
echo "<html><head><title>Data Source Integration Test</title>";
echo "<script src='https://unpkg.com/htmx.org@1.9.10'></script>";
echo "<script src='https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js' defer></script>";
echo "<link href='https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css' rel='stylesheet'>";
echo "</head><body class='bg-gray-100'>";

echo "<div class='container mx-auto py-8'>";
echo "<h1 class='text-3xl font-bold mb-8'>Data Source Integration Test</h1>";

// Test 1: Table with Data Source Selector
echo "<div class='bg-white rounded-lg shadow p-6 mb-8'>";
echo "<h2 class='text-xl font-semibold mb-4'>Test 1: Data Table with Data Source Selector</h2>";
echo "<p class='text-gray-600 mb-4'>This table shows the data source selector that allows switching between hardcoded data and database data sources.</p>";

// Sample hardcoded data
$sample_data = [
    ['id' => 1, 'name' => 'John Doe', 'email' => '<EMAIL>', 'status' => 'Active'],
    ['id' => 2, 'name' => 'Jane Smith', 'email' => '<EMAIL>', 'status' => 'Inactive'],
    ['id' => 3, 'name' => 'Bob Johnson', 'email' => '<EMAIL>', 'status' => 'Active']
];

// Sample columns
$sample_columns = [
    [
        'label' => 'ID',
        'field' => 'id',
        'filter' => false,
        'extra_parameters' => ''
    ],
    [
        'label' => 'Name',
        'field' => 'name',
        'filter' => true,
        'extra_parameters' => ''
    ],
    [
        'label' => 'Email',
        'field' => 'email',
        'filter' => true,
        'extra_parameters' => ''
    ],
    [
        'label' => 'Status',
        'field' => 'status',
        'filter' => false,
        'extra_parameters' => ''
    ]
];

try {
    $table_result = data_table::process_data_table(
        ['columns' => $sample_columns],
        $sample_data,
        '', // No db_table
        'test_data_source_callback', // Callback function
        [], // No replacements
        [], // No criteria
        [], // No hidden columns
        false, // Not just body
        false, // Not just rows
        false, // Not just table
        false, // No HTMX OOB
        null, // No total count
        'test_data_source_table' // Table name for storage
    );
    
    // Enable data source selector by modifying the result
    $table_result = str_replace(
        'show_data_source_selector="false"',
        'show_data_source_selector="true"',
        $table_result
    );
    
    echo $table_result;
    
} catch (Exception $e) {
    echo "<p class='text-red-600'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Test 2: Available Data Sources
echo "<div class='bg-white rounded-lg shadow p-6 mb-8'>";
echo "<h2 class='text-xl font-semibold mb-4'>Test 2: Available Data Sources</h2>";
echo "<p class='text-gray-600 mb-4'>List of available data sources in the system:</p>";

try {
    $db = database::table('autobooks_data_sources');
    $data_sources = $db->where('status', '=', 'active')->get();
    
    if (!empty($data_sources)) {
        echo "<div class='overflow-x-auto'>";
        echo "<table class='min-w-full divide-y divide-gray-200'>";
        echo "<thead class='bg-gray-50'>";
        echo "<tr>";
        echo "<th class='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>ID</th>";
        echo "<th class='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>Name</th>";
        echo "<th class='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>Description</th>";
        echo "<th class='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>Category</th>";
        echo "<th class='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>Table</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody class='bg-white divide-y divide-gray-200'>";
        
        foreach ($data_sources as $source) {
            echo "<tr>";
            echo "<td class='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>" . htmlspecialchars($source['id']) . "</td>";
            echo "<td class='px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900'>" . htmlspecialchars($source['name']) . "</td>";
            echo "<td class='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>" . htmlspecialchars($source['description'] ?? '') . "</td>";
            echo "<td class='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>" . htmlspecialchars($source['category']) . "</td>";
            echo "<td class='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>" . htmlspecialchars($source['table_name']) . "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
    } else {
        echo "<p class='text-gray-500'>No data sources found. <a href='" . APP_ROOT . "/system/data_sources' class='text-blue-600 hover:text-blue-500'>Create one →</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p class='text-red-600'>Error loading data sources: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Test 3: Configuration Status
echo "<div class='bg-white rounded-lg shadow p-6 mb-8'>";
echo "<h2 class='text-xl font-semibold mb-4'>Test 3: Current Configuration</h2>";
echo "<p class='text-gray-600 mb-4'>Current configuration for the test table:</p>";

try {
    $user_id = data_table_storage::get_current_user_id();
    $config = data_table_storage::get_configuration('test_data_source_table', $user_id);
    
    if ($config) {
        echo "<div class='bg-gray-50 rounded-md p-4'>";
        echo "<pre class='text-sm'>" . json_encode($config, JSON_PRETTY_PRINT) . "</pre>";
        echo "</div>";
    } else {
        echo "<p class='text-gray-500'>No configuration found for test table.</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='text-red-600'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Test 4: API Endpoints
echo "<div class='bg-white rounded-lg shadow p-6 mb-8'>";
echo "<h2 class='text-xl font-semibold mb-4'>Test 4: API Endpoints</h2>";
echo "<p class='text-gray-600 mb-4'>Test the new API endpoints:</p>";

echo "<div class='space-y-4'>";

// Test preview endpoint
echo "<div>";
echo "<h3 class='font-medium mb-2'>Preview Data Source</h3>";
echo "<div class='flex items-center space-x-2'>";
echo "<input type='number' id='preview-source-id' placeholder='Data Source ID' class='border border-gray-300 rounded px-3 py-2 text-sm'>";
echo "<button onclick='previewDataSource()' class='bg-blue-600 text-white px-4 py-2 rounded text-sm hover:bg-blue-700'>Preview</button>";
echo "</div>";
echo "<div id='preview-result' class='mt-2'></div>";
echo "</div>";

echo "</div>";

echo "</div>";

// JavaScript for testing
echo "<script>";
echo "function previewDataSource() {";
echo "    const sourceId = document.getElementById('preview-source-id').value;";
echo "    if (!sourceId) { alert('Please enter a data source ID'); return; }";
echo "    ";
echo "    htmx.ajax('GET', '" . APP_ROOT . "/api/data_table_storage/preview_data_source', {";
echo "        values: { data_source_id: sourceId, limit: 3 },";
echo "        target: '#preview-result',";
echo "        swap: 'innerHTML'";
echo "    });";
echo "}";
echo "</script>";

echo "</div>"; // container
echo "</body></html>";

// Callback function for the data table
function test_data_source_callback($criteria = []) {
    // This would normally process the criteria and return filtered data
    // For testing, we'll just return the same data
    $sample_data = [
        ['id' => 1, 'name' => 'John Doe', 'email' => '<EMAIL>', 'status' => 'Active'],
        ['id' => 2, 'name' => 'Jane Smith', 'email' => '<EMAIL>', 'status' => 'Inactive'],
        ['id' => 3, 'name' => 'Bob Johnson', 'email' => '<EMAIL>', 'status' => 'Active']
    ];
    
    $sample_columns = [
        [
            'label' => 'ID',
            'field' => 'id',
            'filter' => false,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Name',
            'field' => 'name',
            'filter' => true,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Email',
            'field' => 'email',
            'filter' => true,
            'extra_parameters' => ''
        ],
        [
            'label' => 'Status',
            'field' => 'status',
            'filter' => false,
            'extra_parameters' => ''
        ]
    ];
    
    return data_table::process_data_table(
        ['columns' => $sample_columns],
        $sample_data,
        '',
        'test_data_source_callback',
        [],
        $criteria,
        [],
        false,
        false,
        false,
        false,
        null,
        'test_data_source_table'
    );
}
?>
