# Notifications Class Documentation

## Overview

The `notifications` class is a comprehensive notification management system that handles user notifications and notification preferences within the AutoBooks application. It provides functionality for creating, retrieving, managing, and customizing notifications for users.

## Class Information

- **Namespace**: `system`
- **File Location**: `system/classes/notifications.class.php`
- **Dependencies**: 
  - `system\notification` class
  - `system\users` class
  - Global database connection (`$db`)

## Database Tables

The notifications system uses two main database tables:

### autobooks_notifications
```sql
CREATE TABLE `autobooks_notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` varchar(50) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `link` varchar(255) DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `read_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
);
```

### autobooks_notification_preferences
```sql
CREATE TABLE `autobooks_notification_preferences` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` varchar(50) NOT NULL,
  `enabled` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_type` (`user_id`, `type`)
);
```

## Properties

### Private Properties

- **`$db`** - Database connection object
- **`$user_id`** - Current authenticated user's ID (null if not authenticated)

## Constructor

```php
public function __construct()
```

Initializes the notifications class by:
- Setting up the global database connection
- Retrieving the current authenticated user's ID using `users::checkAuth()`

## Methods

### Core Notification Methods

#### create()
```php
public function create($user_id, $type, $title, $message, $link = null)
```

Creates a new notification for a specific user.

**Parameters:**
- `$user_id` (int) - Target user ID
- `$type` (string) - Notification type (e.g., 'system', 'order', 'quote')
- `$title` (string) - Notification title
- `$message` (string) - Notification message content
- `$link` (string|null) - Optional URL link associated with the notification

**Returns:** `int|bool` - Notification ID if successful, false otherwise

**Example:**
```php
$notifications = new notifications();
$notification_id = $notifications->create(123, 'order', 'Order Updated', 'Your order #456 has been processed', '/orders/456');
```

#### getForCurrentUser()
```php
public function getForCurrentUser($unread_only = false, $limit = 10, $offset = 0)
```

Retrieves notifications for the currently authenticated user.

**Parameters:**
- `$unread_only` (bool) - Whether to return only unread notifications (default: false)
- `$limit` (int) - Maximum number of notifications to return (default: 10)
- `$offset` (int) - Pagination offset (default: 0)

**Returns:** `array` - Array of notification records, empty array if user not authenticated

**Example:**
```php
$notifications = new notifications();
$recent_notifications = $notifications->getForCurrentUser(false, 20, 0);
$unread_only = $notifications->getForCurrentUser(true, 5, 0);
```

#### getUnreadCount()
```php
public function getUnreadCount()
```

Gets the count of unread notifications for the current user.

**Returns:** `int` - Number of unread notifications (0 if user not authenticated)

**Example:**
```php
$notifications = new notifications();
$unread_count = $notifications->getUnreadCount();
echo "You have {$unread_count} unread notifications";
```

#### markAsRead()
```php
public function markAsRead($notification_id)
```

Marks a specific notification as read for the current user.

**Parameters:**
- `$notification_id` (int) - ID of the notification to mark as read

**Returns:** `bool` - True if successful, false otherwise

**Example:**
```php
$notifications = new notifications();
$success = $notifications->markAsRead(123);
```

#### markAllAsRead()
```php
public function markAllAsRead()
```

Marks all unread notifications as read for the current user.

**Returns:** `bool` - True if successful, false otherwise

**Example:**
```php
$notifications = new notifications();
$success = $notifications->markAllAsRead();
```

### Preference Management Methods

#### getPreferences()
```php
public function getPreferences()
```

Retrieves notification preferences for the current user.

**Returns:** `array` - Associative array with notification types as keys and boolean enabled status as values

**Example:**
```php
$notifications = new notifications();
$preferences = $notifications->getPreferences();
// Returns: ['system' => true, 'order' => false, 'quote' => true]
```

#### updatePreference()
```php
public function updatePreference($type, $enabled)
```

Updates a notification preference for the current user.

**Parameters:**
- `$type` (string) - Notification type to update
- `$enabled` (bool) - Whether notifications of this type should be enabled

**Returns:** `bool` - True if successful, false otherwise

**Example:**
```php
$notifications = new notifications();
$success = $notifications->updatePreference('order', false); // Disable order notifications
```

### Static Methods

#### get_notifications()
```php
public static function get_notifications($p)
```

Static method for rendering notifications list HTML (used by API endpoints).

**Parameters:**
- `$p` (array) - Request parameters array containing:
  - `unread_only` (string) - 'true' to show only unread notifications
  - `limit` (string) - Maximum number of notifications
  - `offset` (string) - Pagination offset

**Returns:** `string` - Rendered HTML for notifications list

## Notification Types

The system supports various notification types:

- **`system`** - System-wide notifications and announcements
- **`order`** - Order-related updates and status changes
- **`quote`** - Quote-related notifications
- **`subscription`** - Subscription updates and renewals
- **`customer`** - Customer-related notifications

## Usage Examples

### Basic Notification Creation
```php
// Create a new notifications instance
$notifications = new notifications();

// Send a system notification to user ID 123
$notification_id = $notifications->create(
    123, 
    'system', 
    'Welcome!', 
    'Welcome to AutoBooks. Your account has been activated.',
    '/dashboard'
);
```

### Retrieving User Notifications
```php
$notifications = new notifications();

// Get the latest 10 notifications for current user
$recent = $notifications->getForCurrentUser();

// Get only unread notifications
$unread = $notifications->getForCurrentUser(true);

// Get unread count for badge display
$count = $notifications->getUnreadCount();
```

### Managing Notification Preferences
```php
$notifications = new notifications();

// Get current preferences
$preferences = $notifications->getPreferences();

// Disable order notifications
$notifications->updatePreference('order', false);

// Enable system notifications
$notifications->updatePreference('system', true);
```

### Marking Notifications as Read
```php
$notifications = new notifications();

// Mark specific notification as read
$notifications->markAsRead(456);

// Mark all notifications as read
$notifications->markAllAsRead();
```

## Related Files

- **Helper Functions**: `system/functions/notifications.fn.php`
- **API Endpoints**: `system/api/notifications.api.php`
- **Views**: `system/views/notifications/`
- **JavaScript Handler**: `resources/components/js/notification-handler.js`
- **Individual Notification Class**: `system/classes/notification.class.php`

## Security Considerations

- All methods check for user authentication before performing operations
- Database queries use named parameters to prevent SQL injection
- User can only access their own notifications and preferences
- Notification creation requires explicit user ID parameter

## Error Handling

- Methods return `false` or empty arrays when user is not authenticated
- Database errors are handled gracefully with boolean return values
- Invalid parameters are handled through type checking and validation
