# JSON Column Support and Fallback

The database class now includes automatic detection of JSON column support with intelligent fallback to TEXT-based JSON storage for older MySQL versions.

## Overview

JSON columns were introduced in MySQL 5.7.8. For servers running older versions, the database class automatically falls back to using TEXT columns with JSON encoding/decoding handled in PHP.

## Features

### Automatic Detection
- Detects MySQL/MariaDB version
- Tests actual JSON column creation capability
- Caches detection result for performance
- Provides detailed support information

### Seamless Fallback
- `Blueprint::json()` automatically uses TEXT when JSON columns aren't supported
- `applyCasts()` handles both native JSON and TEXT-based JSON storage
- All existing code continues to work without changes

### Enhanced Error Handling
- Graceful handling of JSON decode errors
- Comprehensive logging of fallback usage
- Detailed error reporting for debugging

## Usage

### Creating Tables with JSON Columns

```php
use system\database;

// This automatically uses JSON or TEXT based on server support
database::schema()::create('products', function($table) {
    $table->increments('id');
    $table->string('name');
    $table->json('metadata'); // Auto-fallback to TEXT if needed
    $table->timestamps();
});
```

### Inserting JSON Data

```php
// Use prepareJsonData() for consistent JSON encoding
$data = [
    'name' => 'Product Name',
    'metadata' => database::prepareJsonData([
        'category' => 'electronics',
        'tags' => ['laptop', 'computer'],
        'price' => 999.99
    ])
];

database::table('products')->insert($data);
```

### Retrieving and Casting JSON Data

```php
// Cast works with both JSON columns and TEXT-based JSON
$products = database::table('products')
    ->cast(['metadata' => 'json'])
    ->get();

foreach ($products as $product) {
    // metadata is automatically decoded to PHP array
    $category = $product['metadata']['category'];
    $tags = $product['metadata']['tags'];
}
```

## Detection Methods

### Version Check
The system checks MySQL/MariaDB version:
- **MySQL**: Requires 5.7.8 or higher
- **MariaDB**: Requires 10.2.7 or higher (JSON as LONGTEXT alias)

### Capability Test
After version check, the system attempts to create a temporary table with a JSON column to verify actual support.

### Caching
Detection results are cached in static variables to avoid repeated checks.

## Checking Support Status

### Get Support Information
```php
$info = database::getJsonSupportInfo();
print_r($info);
```

Example output:
```php
Array
(
    [mysql_version] => 5.6.51
    [supports_json_columns] => false
    [is_mariadb] => false
    [fallback_type] => TEXT
    [recommendation] => Using TEXT columns with JSON encoding - consider upgrading MySQL to 5.7.8+ for native JSON support
)
```

### Simple Support Check
```php
if (database::supportsJsonColumns()) {
    echo "Native JSON columns available";
} else {
    echo "Using TEXT-based JSON fallback";
}
```

## Logging

The system logs JSON column usage and fallback information:

### JSON Column Creation
```php
// Logged to 'database_json_columns'
[
    'table' => 'products',
    'column' => 'metadata',
    'type' => 'TEXT (JSON fallback)',
    'fallback_used' => true,
    'reason' => 'Server does not support JSON columns'
]
```

### Support Detection
```php
// Logged to 'database_json_support'
[
    'mysql_version' => '5.6.51',
    'supports_json' => false,
    'detection_method' => 'version_check_and_test'
]
```

### JSON Casting Errors
```php
// Logged to 'database_json_cast_errors'
[
    'column' => 'metadata',
    'value' => 'invalid json string',
    'json_error' => 'Syntax error',
    'action' => 'keeping_original_value'
]
```

## Best Practices

### 1. Always Use prepareJsonData()
```php
// Good
$data = ['metadata' => database::prepareJsonData($array)];

// Avoid
$data = ['metadata' => json_encode($array)]; // No error handling
```

### 2. Always Cast JSON Columns
```php
// Good - handles both JSON and TEXT storage
$results = database::table('products')
    ->cast(['metadata' => 'json'])
    ->get();

// Avoid - may return string instead of array
$results = database::table('products')->get();
```

### 3. Handle JSON Decode Errors
The casting system automatically handles JSON decode errors, but you should still validate data:

```php
$product = database::table('products')->cast(['metadata' => 'json'])->first();

if (is_array($product['metadata'])) {
    // Safe to use as array
    $category = $product['metadata']['category'] ?? 'unknown';
} else {
    // Handle case where JSON was invalid
    $category = 'unknown';
}
```

## Migration from Existing Code

Existing code using JSON columns will continue to work without changes. The system automatically:

1. Detects server capabilities
2. Uses appropriate column types
3. Handles data encoding/decoding
4. Provides consistent API

No code changes are required for existing applications.

## Testing

Use the provided test script to verify JSON column support:

```bash
php test_json_support.php
```

This script will:
1. Check JSON support detection
2. Create a test table with JSON column
3. Insert and retrieve JSON data
4. Test JSON functions (if supported)
5. Clean up test data

## Performance Considerations

### Native JSON Columns (MySQL 5.7.8+)
- Optimized storage format
- Native JSON functions available
- Better query performance for JSON operations

### TEXT-based Fallback
- Standard TEXT column storage
- JSON encoding/decoding in PHP
- No native JSON query functions
- Slightly higher PHP processing overhead

## Troubleshooting

### Common Issues

1. **JSON functions not working**: Check if `database::supportsJsonColumns()` returns true
2. **Data not decoding**: Ensure you're using `->cast(['column' => 'json'])`
3. **Invalid JSON errors**: Use `database::prepareJsonData()` for encoding

### Debug Information

Enable logging to see detailed information about JSON column usage:
- Set `STORE_DB_TRANSACTIONS = 'true'` for query logging
- Check logs for 'database_json_*' entries
- Use `database::getJsonSupportInfo()` for support details
