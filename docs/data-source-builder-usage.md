# Data Source Builder Usage Guide

The Data Source Builder provides a UI-driven way to configure database data sources for tables and email campaigns. This system allows users to select database tables, configure filters, and use the data sources in templates and email campaigns.

## Components Overview

### 1. Data Source Manager Class (`system/classes/data_source_manager.class.php`)
Backend class that handles:
- Discovering available database tables
- Managing data source configurations
- Retrieving data with filters
- Table metadata analysis

### 2. Data Source Selector Component (`system/components/edges/data-source-selector.edge.php`)
UI component for selecting existing data sources or creating new ones:
- Dropdown with grouped data sources by category
- Preview functionality
- Create new data source modal
- Integration with existing forms

### 3. Data Source Builder Component (`system/components/edges/data-source-builder.edge.php`)
Complete configuration interface for creating/editing data sources:
- Table selection and information display
- Filter configuration
- Data preview
- Connection testing

### 4. API Endpoints (`system/api/data_sources.api.php`)
RESTful API for data source operations:
- CRUD operations for data sources
- Data retrieval with filtering
- Table information and preview
- Connection testing

## Usage Examples

### Basic Data Source Selection in Forms

```php
<!-- In your Edge template -->
<form>
    <!-- Other form fields -->
    
    <x-data-source-selector 
        name="data_source_id"
        label="Select Data Source"
        :selected="$selected_data_source ?? ''"
        :show_preview="true"
        :show_create_new="true"
    />
    
    <!-- More form fields -->
</form>
```

### Email Template with Data Source Integration

```php
<!-- Enhanced email template selector -->
<x-email-template-selector 
    name="template"
    label="Email Template"
    :selected="$selected_template ?? ''"
    :show_data_source="true"
    data_source_name="data_source_id"
    :selected_data_source="$selected_data_source ?? ''"
    :show_actions="true"
/>
```

### Using Data Sources in Templates

```php
@props([
    'data_source_id' => null,
    'search' => '',
    'limit' => 50
])

@php
use system\data_source_manager;

if ($data_source_id) {
    $criteria = [
        'limit' => $limit
    ];
    
    if (!empty($search)) {
        $criteria['search'] = $search;
    }
    
    $result = data_source_manager::get_data_source_data($data_source_id, $criteria);
    $data = $result['success'] ? $result['data'] : [];
} else {
    $data = [];
}
@endphp

@if(!empty($data))
    <div class="data-table">
        @foreach($data as $row)
            <div class="data-row">
                <!-- Display row data -->
                @foreach($row as $column => $value)
                    <div class="data-cell">
                        <strong>{{ $column }}:</strong> {{ $value }}
                    </div>
                @endforeach
            </div>
        @endforeach
    </div>
@else
    <p>No data available</p>
@endif
```

### Programmatic Data Source Management

```php
use system\data_source_manager;

// Get all available tables
$tables = data_source_manager::get_available_tables();

// Create a new data source
$data_source_id = data_source_manager::create_data_source([
    'name' => 'My Data Source',
    'table_name' => 'autobooks_users',
    'description' => 'User data for email campaigns',
    'category' => 'users',
    'filters' => [
        [
            'column' => 'status',
            'operator' => '=',
            'value' => 'active'
        ]
    ]
]);

// Get data from a data source
$result = data_source_manager::get_data_source_data($data_source_id, [
    'search' => 'john',
    'limit' => 10,
    'sort_column' => 'created_at',
    'sort_direction' => 'desc'
]);

if ($result['success']) {
    $data = $result['data'];
    // Process data...
}
```

## API Usage

### Get Available Tables
```javascript
fetch('/api/data_sources/get_tables')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Available tables:', data.data);
        }
    });
```

### Create Data Source
```javascript
fetch('/api/data_sources/create', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        name: 'My Data Source',
        table_name: 'autobooks_users',
        description: 'User data source',
        category: 'users',
        filters: [
            {
                column: 'status',
                operator: '=',
                value: 'active'
            }
        ]
    })
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('Data source created:', data.data_source_id);
    }
});
```

### Get Data from Data Source
```javascript
fetch('/api/data_sources/get_data', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        data_source_id: 1,
        search: 'search term',
        limit: 50,
        offset: 0,
        sort_column: 'created_at',
        sort_direction: 'desc'
    })
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('Data:', data.data);
    }
});
```

## Database Schema

The system creates an `autobooks_data_sources` table to store data source configurations:

```sql
CREATE TABLE `autobooks_data_sources` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `table_name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `category` enum('data_table','email','users','system','autodesk','other') NOT NULL DEFAULT 'other',
  `column_mapping` longtext DEFAULT NULL,
  `filters` longtext DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
);
```

## Integration with Existing Systems

### Email Campaigns
The data source builder integrates with the existing email campaign system by:
- Providing data sources for recipient lists
- Supporting dynamic template data
- Enabling filtered recipient selection

### Data Tables
Integration with data table templates:
- Automatic table creation from data sources
- Dynamic column configuration
- Search and filter capabilities

### Navigation System
Can be added to the navigation system for easy access:
```sql
INSERT INTO `autobooks_navigation` 
(`parent_path`, `route_key`, `name`, `icon`, `file_path`, `sort_order`, `show_navbar`) 
VALUES 
('root', 'data_sources', 'Data Sources', 'database', 'data_sources', 10, 1);
```

## Best Practices

1. **Naming Convention**: Use descriptive names for data sources that indicate their purpose
2. **Categories**: Properly categorize data sources for better organization
3. **Filters**: Use filters to limit data scope and improve performance
4. **Testing**: Always test data sources before using in production
5. **Documentation**: Document custom data sources and their intended use
6. **Security**: Ensure proper access controls for sensitive data sources

## Troubleshooting

### Common Issues

1. **Table Not Found**: Ensure the database table exists and is accessible
2. **Permission Errors**: Check database user permissions
3. **Filter Errors**: Validate filter syntax and column names
4. **Performance Issues**: Add appropriate database indexes for filtered columns

### Debug Mode

Enable debug logging by setting the log target:
```php
data_source_manager::$log_target = "debug_data_sources";
```

This will provide detailed logging of data source operations for troubleshooting.
