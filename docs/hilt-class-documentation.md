# Hilt Class Documentation

## Overview

The `hilt` class is a comprehensive template processing system designed for database-driven views in the autobooks application. It provides functionality for creating, managing, and processing templates with the `.hilt.php` extension, supporting multiple template types including database-driven, HTML, file upload, and basic templates.

## Namespace and Dependencies

```php
namespace system;

use system\database;
use system\data_importer;
use edge\Edge;
```

## Class Features

- **Database Integration**: Automatic table creation with naming convention `autobooks_{route_key}_data`
- **CSV Data Import**: Seamless CSV file processing and database storage
- **Multiple Template Types**: Support for database, HTML, file upload, and basic templates
- **Edge Template Engine**: Integration with Edge templating system
- **Error Handling**: Comprehensive error handling with logging
- **Flexible Data Storage**: JSON-based data storage for CSV flexibility

## Public Methods

### `process_template()`
[users.class.php](../system/classes/users.class.php)
Main method for processing hilt templates based on template type.

**Signature:**
```php
public static function process_template(
    string $template_file, 
    array $template_data, 
    array $files, 
    string $key, 
    string $template_type = 'database'
): string
```

**Parameters:**
- `$template_file`: Path to the hilt template file
- `$template_data`: Template data from form submission
- `$files`: Uploaded files array
- `$key`: Route key for the template
- `$template_type`: Type of template ('database', 'html', 'file_upload', 'default')

**Returns:** Generated view content as string

**Supported Template Types:**
- `database`/`csv`: Database-driven templates with CSV import
- `html`/`custom_html`: Custom HTML content templates
- `file_upload`: File upload processing templates
- `default`/`none`: Basic templates

### `ensure_table_exists()`

Creates database table if it doesn't exist using the hilt naming convention.

**Signature:**
```php
public static function ensure_table_exists(string $table_name): bool
```

**Table Structure:**
- `id`: Auto-incrementing primary key
- `data_json`: JSON field for flexible CSV data storage
- `data_hash`: MD5 hash for duplicate detection
- `created_at`/`updated_at`: Timestamps

### `import_csv_data()`

Imports CSV data into the specified database table.

**Signature:**
```php
public static function import_csv_data(
    string $table_name, 
    array $template_data, 
    array $files
): array
```

**Features:**
- File upload handling
- Direct CSV data processing
- Duplicate detection via hash comparison
- Integration with `data_importer` class

### `get_table_data()`

Retrieves data from hilt tables with query criteria support.

**Signature:**
```php
public static function get_table_data(
    string $table_name, 
    array $criteria = []
): array
```

**Supported Criteria:**
- `limit`: Number of records to retrieve
- `offset`: Starting position for pagination
- `search`: Search term for JSON data filtering

### `update_csv_data()`

Updates existing CSV data in database tables.

**Signature:**
```php
public static function update_csv_data(
    string $table_name, 
    array $template_data, 
    array $files, 
    bool $replace_all = false
): array
```

### `create_default_hilt_template()`

Creates default hilt template files for different template types.

**Signature:**
```php
public static function create_default_hilt_template(
    string $template_file, 
    string $template_type = 'database'
): void
```

## Template Types

### Database Templates
- **Purpose**: Display data from CSV imports in database tables
- **Features**: Pagination, search, sorting, data table components
- **File Extension**: `.hilt.php`
- **Table Convention**: `autobooks_{route_key}_data`

### HTML Templates
- **Purpose**: Custom HTML content with dynamic placeholders
- **Features**: HTML content replacement, route key substitution
- **Placeholders**: `/* HTML_CONTENT */`, `{{ $route_key }}`

### File Upload Templates
- **Purpose**: Process uploaded files as template content
- **Supported Files**: PHP, HTML, and other file types
- **Processing**: Automatic content wrapping and PHP tag insertion

### Basic Templates
- **Purpose**: Simple templates with minimal functionality
- **Features**: Route key display, basic styling with Tailwind CSS
- **Use Case**: Starting point for custom development

## Template Structure

All hilt templates use the Edge template syntax with `@props` declarations:

```php
@props([
    'name' => 'template-name',
    'description' => 'Template description',
    'type' => 'hilt-type',
    'route_key' => '{{ $route_key }}'
])

@php
// PHP logic here
@endphp

<!-- HTML content -->
```

## Database Integration

### Table Naming Convention
- Pattern: `autobooks_{route_key}_data`
- Example: `autobooks_products_data`, `autobooks_customers_data`

### Data Storage Format
```json
{
    "id": 1,
    "data_json": "{\"name\":\"John Doe\",\"email\":\"<EMAIL>\",\"age\":\"30\"}",
    "data_hash": "5d41402abc4b2a76b9719d911017c592",
    "created_at": "2024-01-01 12:00:00",
    "updated_at": "2024-01-01 12:00:00"
}
```

## Error Handling

The class includes comprehensive error handling:
- Exception catching with detailed logging
- Graceful error view generation
- User-friendly error messages
- System logging with `tcs_log()` function

## Usage Examples

### Basic Template Processing
```php
use system\hilt;

$result = hilt::process_template(
    'templates/my_template.hilt.php',
    ['csv_data' => $csv_content],
    $_FILES,
    'products',
    'database'
);
```

### Table Creation and Data Import
```php
// Ensure table exists
$table_name = 'autobooks_products_data';
hilt::ensure_table_exists($table_name);

// Import CSV data
$import_result = hilt::import_csv_data($table_name, $form_data, $_FILES);
```

### Data Retrieval with Pagination
```php
$data = hilt::get_table_data('autobooks_products_data', [
    'limit' => 50,
    'offset' => 0,
    'search' => 'electronics'
]);
```

## Integration Points

- **Edge Template Engine**: For template compilation and rendering
- **Database Class**: For schema operations and queries
- **Data Importer Class**: For CSV processing and import operations
- **Logging System**: Via `tcs_log()` function
- **File System**: For template file management and uploads

## Configuration

The class uses several system constants:
- `FS_SYSTEM`: System directory path
- `FS_UPLOADS`: Upload directory path
- Template directories under `system/templates/`

## Best Practices

1. **Template Naming**: Use descriptive names with `.hilt.php` extension
2. **Route Keys**: Use lowercase with underscores (e.g., `product_catalog`)
3. **CSV Structure**: Maintain consistent column headers for data integrity
4. **Error Handling**: Always check return values for error conditions
5. **Security**: Validate file uploads and sanitize user input
6. **Performance**: Use pagination for large datasets

## Related Files

- `system/classes/hilt.class.php` - Main class file
- `system/templates/*.hilt.php` - Template files
- `system/api/hilt_settings.api.php` - Settings management API
- `test_hilt_system.php` - Testing utilities
