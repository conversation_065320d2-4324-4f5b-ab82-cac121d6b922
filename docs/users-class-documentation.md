# Users Class Documentation

## Overview

The `users` class is a comprehensive user authentication and session management system for the autobooks application. It provides secure login/logout functionality, role-based access control, session management, and user operations with comprehensive logging.

## Namespace and Dependencies

```php
namespace system;

// Uses global database functions:
// - tep_db_query()
// - tep_db_fetch_array()
// - tcs_log()
// - tcs_db_query()
```

## Class Features

- **Secure Authentication**: Password hashing with P<PERSON>'s `password_verify()`
- **Session Management**: Token-based sessions with expiration
- **Role-Based Access Control**: Hierarchical role system (dev > admin > user)
- **Comprehensive Logging**: All authentication events logged to 'auth' channel
- **Dual Storage**: Sessions stored in both cookies and PHP sessions
- **Password Reset**: Token-based password reset functionality
- **User Management**: CRUD operations for user accounts

## Properties

```php
private static $db;              // Database connection (global $db)
public static int $id;           // Current authenticated user ID
public static string $username;  // Current user's username
public static string $email;     // Current user's email address
public static string $role;      // Current user's role (dev/admin/user)
public static bool $started;     // Session initialization flag
```

## Core Methods

### `login($username, $password)`

Authenticates a user with username and password.

**Parameters:**
- `$username` (string): Username or email address
- `$password` (string): Plain text password

**Returns:** `bool` - True on successful authentication, false otherwise

**Process:**
1. Logs login attempt
2. Queries `autobooks_users` table for active user
3. Verifies password using `password_verify()`
4. Creates session token (64-character hex string)
5. Sets session expiration (8 hours)
6. Updates user's last login timestamp
7. Sets both cookie and PHP session variables

**Security Features:**
- Only active users can login (`status = 'active'`)
- Passwords are hashed using PHP's `PASSWORD_DEFAULT`
- Session tokens are cryptographically secure (32 random bytes)
- Comprehensive logging of all authentication events

### `logout()`

Terminates user session and clears authentication data.

**Process:**
1. Retrieves current session token
2. Removes token from `autobooks_sessions` table
3. Clears authentication cookie
4. Destroys PHP session
5. Logs logout event

### `checkAuth()`

Validates current user session and returns user data.

**Returns:** `array|false` - User data array if authenticated, false otherwise

**Process:**
1. Retrieves token from session or cookie
2. Validates token against database with expiration check
3. Ensures user account is still active
4. Updates session variables
5. Auto-logout if token is invalid or expired

**User Data Structure:**
```php
[
    'id' => 123,
    'username' => 'john_doe',
    'email' => '<EMAIL>',
    'role' => 'admin',
    'status' => 'active',
    'last_login' => '2024-01-01 12:00:00',
    'created_at' => '2023-01-01 10:00:00',
    'updated_at' => '2024-01-01 12:00:00'
]
```

### `requireRole($required_role)`

Enforces role-based access control for protected resources.

**Parameters:**
- `$required_role` (string|array): Required role(s) for access

**Returns:** `bool` - True if access granted

**Role Hierarchy:**
```php
'dev' => 3    // Highest level - full system access
'admin' => 2  // Administrative access
'user' => 1   // Basic user access
```

**Access Control:**
- **Array Input**: User role must be in the provided array
- **String Input**: User role level must be >= required role level
- **Unauthorized Access**: Redirects to login page
- **Insufficient Role**: Returns 403 Forbidden error

### `getUserById($user_id)`

Retrieves user details by user ID.

**Parameters:**
- `$user_id` (int): User ID to retrieve

**Returns:** `array|false` - User data array or false if not found

## Database Schema

### autobooks_users Table

```sql
CREATE TABLE autobooks_users (
    id INT(11) NOT NULL AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL,
    name VARCHAR(100),
    email VARCHAR(100) NOT NULL,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL DEFAULT 'user',
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    preferences JSON,
    reset_token VARCHAR(64),
    reset_token_expires DATETIME,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY username (username),
    UNIQUE KEY email (email)
);
```

### autobooks_sessions Table

```sql
CREATE TABLE autobooks_sessions (
    id INT(11) NOT NULL AUTO_INCREMENT,
    user_id INT(11) NOT NULL,
    token VARCHAR(64) NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY token (token),
    KEY user_id (user_id),
    KEY expires_at (expires_at)
);
```

## Security Features

### Password Security
- Passwords hashed using `password_hash()` with `PASSWORD_DEFAULT`
- Password verification with `password_verify()`
- No plain text password storage

### Session Security
- Cryptographically secure session tokens (32 random bytes)
- Token expiration (8 hours for sessions, 24 hours for cookies)
- Automatic cleanup of expired sessions
- HttpOnly and Secure cookie flags

### Access Control
- Role-based permissions with hierarchical levels
- Automatic redirects for unauthorized access
- Session validation on every request
- Account status checking (active/inactive)

### Logging
- All authentication events logged
- Failed login attempts tracked
- Role access violations recorded
- User actions audited

## API Integration

### User Management API (`system/views/users/users.api.php`)

**Functions:**
- `user_modal($p)` - Display user edit modal
- `save_user($p)` - Create/update user accounts
- `reset_password($p)` - Generate password reset tokens
- `filter_users($p)` - Search and filter users

### User Table Generation (`system/functions/users.fn.php`)

**Function:** `generate_user_table($criteria, $just_body)`

**Features:**
- Sortable columns (Name, Email, Role, Status, Last Login)
- Auto-filtering on Role and Status fields
- Action buttons (Edit, Reset Password)
- HTMX integration for dynamic updates

## Usage Examples

### Basic Authentication Check
```php
use system\users;

// Check if user is authenticated
$user = users::checkAuth();
if ($user) {
    echo "Welcome, " . $user['username'];
} else {
    header('Location: /login');
    exit;
}
```

### Role-Based Access Control
```php
// Require admin access
users::requireRole('admin');

// Allow multiple roles
users::requireRole(['admin', 'dev']);

// Hierarchical access (admin or higher)
users::requireRole('admin'); // allows admin and dev
```

### User Login Process
```php
if ($_POST['username'] && $_POST['password']) {
    if (users::login($_POST['username'], $_POST['password'])) {
        header('Location: /dashboard');
        exit;
    } else {
        $error = "Invalid credentials";
    }
}
```

### User Logout
```php
users::logout();
header('Location: /login');
exit;
```

## Error Handling

### Logging System
- **Channel**: 'auth' - All authentication events
- **Function**: `tcs_log($message, 'auth')`
- **Events Logged**:
  - Login attempts (success/failure)
  - Password verification results
  - Session creation/destruction
  - Role access violations
  - User data retrieval

### Error Responses
- **401 Unauthorized**: Invalid credentials
- **403 Forbidden**: Insufficient role permissions
- **Redirect**: Automatic redirect to login for unauthenticated users

## Configuration

### Session Settings
- **Session Duration**: 8 hours
- **Cookie Duration**: 24 hours
- **Token Length**: 64 characters (32 bytes hex-encoded)

### Role Definitions
- **dev**: Full system access, debug capabilities
- **admin**: User management, system configuration
- **user**: Basic application access

## Related Files

- `system/classes/users.class.php` - Main class file
- `system/users.class.php` - Duplicate class file (legacy)
- `system/views/users/users.api.php` - User management API
- `system/functions/users.fn.php` - User table generation
- `system/api/users.api.php` - Additional user API endpoints
- `resources/views/users/users.php` - User management interface

## Best Practices

1. **Always Check Authentication**: Use `checkAuth()` before accessing protected resources
2. **Use Role Requirements**: Implement `requireRole()` for sensitive operations
3. **Log Security Events**: Monitor authentication logs for suspicious activity
4. **Session Management**: Allow users to logout properly
5. **Password Security**: Never store or transmit plain text passwords
6. **Token Validation**: Regularly clean up expired sessions
7. **Error Handling**: Provide user-friendly error messages without exposing system details

## Integration Points

- **Database**: Uses global `tep_db_*` functions for database operations
- **Logging**: Integrates with `tcs_log()` system for audit trails
- **Sessions**: Works with PHP sessions and HTTP cookies
- **Edge Templates**: User data available in template rendering
- **HTMX**: Supports dynamic user interface updates
- **Email System**: Password reset functionality with email notifications
