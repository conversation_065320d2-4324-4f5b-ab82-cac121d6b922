# Database Class Documentation

## Overview

The `database` class is a comprehensive PHP database abstraction layer built on top of PDO, providing a fluent query builder interface, schema management capabilities, and robust error handling. It implements the singleton pattern for connection management and supports MySQL databases.

## Table of Contents

1. [Core Classes](#core-classes)
2. [Connection Management](#connection-management)
3. [Query Builder](#query-builder)
4. [CRUD Operations](#crud-operations)
5. [Schema Management](#schema-management)
6. [<PERSON>rro<PERSON> Handling](#error-handling)
7. [Usage Examples](#usage-examples)
8. [Best Practices](#best-practices)

## Core Classes

### database
The main database class providing query builder functionality and connection management.

### DatabaseException
Custom exception class for database errors with additional context including query and parameters.

### Schema
Static class for database schema operations like creating, dropping, and checking tables/columns.

### Blueprint
Schema builder class for defining table structures with a fluent interface.

## Connection Management

### Singleton Pattern
The database class uses a singleton pattern to ensure only one connection instance exists:

```php
// Get database instance
$db = database::connection();

// Or use with custom PDO connection
$customPdo = new PDO($dsn, $username, $password);
$db = database::connection($customPdo);
```

### Configuration
Database connection details are loaded from `system/config/db_config.php` with fallback defaults:
- **Host**: localhost or 127.0.0.1 (with TCP/IP fallback)
- **Charset**: UTF8
- **Error Mode**: Exception mode enabled
- **SQL Mode**: Disabled for compatibility

## Query Builder

### Table Selection
```php
// Select a table to work with
$query = database::table('users');
```

### Column Selection
```php
// Select specific columns
$query->select(['name', 'email']);
$query->select('name, email, created_at');
```

### Where Conditions
```php
// Single condition
$query->where('status', 'active');
$query->where('age', '>', 18);

// Multiple conditions (array)
$query->where([
    'status' => 'active',
    'verified' => 1
]);
```

### Joins
```php
// Inner join
$query->join('profiles', 'users.id', '=', 'profiles.user_id');

// Left join
$query->leftJoin('orders', 'users.id', '=', 'orders.user_id');
```

### Ordering and Limiting
```php
// Order by
$query->orderBy('created_at', 'desc');
$query->orderBy('name'); // defaults to 'asc'

// Limit and offset
$query->limit(10);
$query->offset(20);
```

### Type Casting
```php
// Cast result columns to specific types
$query->cast([
    'id' => 'int',
    'price' => 'float',
    'is_active' => 'bool',
    'metadata' => 'json',
    'created_at' => 'date'
]);
```

## CRUD Operations

### Reading Data
```php
// Get all results
$users = database::table('users')->get();

// Get first result
$user = database::table('users')->where('id', 1)->first();

// Complex query
$activeUsers = database::table('users')
    ->select(['id', 'name', 'email'])
    ->where('status', 'active')
    ->orderBy('created_at', 'desc')
    ->limit(10)
    ->get();
```

### Inserting Data
```php
// Single insert
database::table('users')->insert([
    'name' => 'John Doe',
    'email' => '<EMAIL>',
    'status' => 'active'
]);

// Batch insert
database::table('users')->insert([
    ['name' => 'User 1', 'email' => '<EMAIL>'],
    ['name' => 'User 2', 'email' => '<EMAIL>']
]);

// Insert with numeric array (maps to table columns)
database::table('users')->insert(['John Doe', '<EMAIL>', 'active']);
```

### Updating Data
```php
// Update with conditions
database::table('users')
    ->where('id', 1)
    ->update([
        'name' => 'Jane Doe',
        'updated_at' => date('Y-m-d H:i:s')
    ]);

// Update multiple records
database::table('users')
    ->where('status', 'pending')
    ->update(['status' => 'active']);
```

### Deleting Data
```php
// Delete with conditions
database::table('users')
    ->where('status', 'inactive')
    ->delete();

// Truncate table (remove all data)
database::table('users')->truncate();
```

## Schema Management

### Checking Existence
```php
// Check if table exists
if (Schema::hasTable('users')) {
    // Table exists
}

// Check if column exists
if (Schema::hasColumn('users', 'email')) {
    // Column exists
}
```

### Creating Tables
```php
// Create new table
Schema::create('users', function (Blueprint $table) {
    $table->increments('id');
    $table->string('name');
    $table->string('email', 100);
    $table->integer('age');
    $table->text('bio');
    $table->json('metadata');
    $table->timestamps(); // created_at, updated_at
});
```

### Modifying Tables
```php
// Add columns to existing table
Schema::table('users', function (Blueprint $table) {
    $table->string('phone', 20);
    $table->timestamp('last_login');
});
```

### Dropping Tables
```php
// Drop table
Schema::drop('users');

// Drop table if exists
Schema::dropIfExists('users');
```

## Error Handling

### DatabaseException
Custom exception with enhanced context:

```php
try {
    database::table('users')->where('id', 1)->get();
} catch (DatabaseException $e) {
    echo $e->getDetailedMessage(); // Includes query and parameters
    echo $e->getQuery();
    print_r($e->getParams());
}
```

### Error Logging
All database errors are automatically logged with comprehensive details:
- Error code and message
- SQL query and parameters
- User context (ID, IP, User Agent)
- Stack trace
- Request information

### Debug vs Production Mode
- **Debug Mode**: Shows detailed error information
- **Production Mode**: Shows generic error message with unique error ID

## Usage Examples

### Basic CRUD Example
```php
// Create
$userId = database::table('users')->insert([
    'name' => 'John Doe',
    'email' => '<EMAIL>'
]);

// Read
$user = database::table('users')->where('id', $userId)->first();

// Update
database::table('users')
    ->where('id', $userId)
    ->update(['name' => 'Jane Doe']);

// Delete with conditions (safe)
database::table('users')
    ->where('status', 'inactive')
    ->delete();

// Clear entire table (use with caution)
database::table('temp_data')->drop();

// Truncate table (faster for large tables)
database::table('logs')->truncate();

// Delete
database::table('users')->where('id', $userId)->delete();
```

### Complex Query Example
```php
$results = database::table('orders')
    ->select(['orders.*', 'users.name as user_name', 'products.title'])
    ->join('users', 'orders.user_id', '=', 'users.id')
    ->leftJoin('products', 'orders.product_id', '=', 'products.id')
    ->where('orders.status', 'completed')
    ->where('orders.total', '>', 100)
    ->orderBy('orders.created_at', 'desc')
    ->limit(50)
    ->cast([
        'total' => 'float',
        'quantity' => 'int'
    ])
    ->get();
```

## Best Practices

1. **Use Named Parameters**: The class automatically generates named parameters matching column names
2. **Type Casting**: Use the `cast()` method for proper data type conversion
3. **Error Handling**: Always wrap database operations in try-catch blocks
4. **Connection Reuse**: The singleton pattern ensures efficient connection reuse
5. **Schema Validation**: Use `Schema::hasTable()` and `Schema::hasColumn()` before operations
6. **Batch Operations**: Use batch inserts for multiple records to improve performance
7. **Query Logging**: Database queries are automatically logged when `STORE_DB_TRANSACTIONS` is enabled

## Integration with Templates

The database class integrates with Edge templates through custom directives:
- `@table()` - Create table reference
- `@get()` - Execute query and store results
- `@db()` - Execute raw SQL queries

This allows database operations directly within template files for dynamic content generation.
