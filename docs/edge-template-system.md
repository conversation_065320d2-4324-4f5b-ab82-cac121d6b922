# Edge Template System Documentation

## Overview

The Edge Template System is a custom PHP templating engine inspired by Laravel Blade, designed to provide a clean and expressive syntax for building dynamic web interfaces. Edge templates use the `.edge.php` file extension and are compiled to optimized PHP code for performance.

## File Structure

Edge templates are stored in the following directories:
- `system/components/edges/` - System-level edge components
- `resources/components/edges/` - Application-level edge components  
- `resources/components/templates/` - Template components
- `system/templates/` - System templates

Compiled templates are cached in `FS_TEMP/` directory for performance.

## Basic Syntax

### Props Declaration

Every Edge template should start with a `@props` declaration to define the component's interface:

```php
@props([
    'title' => 'Default Title',
    'description' => 'Default description',
    'class' => '',
    'required_prop' => null,
    'optional_prop' => 'default_value'
])
```

### Variable Output

Display variables using double curly braces:

```php
{{ $variable }}
{{ $user->name }}
{{ $data['key'] }}
```

For unescaped output, use `{!! !!}`:

```php
{!! $html_content !!}
```

### PHP Code Blocks

Execute PHP code using `@php` and `@endphp`:

```php
@php
    $calculated_value = $price * $quantity;
    $formatted_date = date('Y-m-d', $timestamp);
@endphp
```

## Control Structures

### Conditional Statements

```php
@if($condition)
    <p>Condition is true</p>
@elseif($other_condition)
    <p>Other condition is true</p>
@else
    <p>No conditions met</p>
@endif
```

### Loops

#### Foreach Loops

```php
@foreach($items as $item)
    <div>{{ $item->name }}</div>
@endforeach

@foreach($users as $key => $user)
    <p>{{ $key }}: {{ $user->email }}</p>
@endforeach
```

The Edge system provides a `$loop` variable with useful properties:
- `$loop->index` - Current iteration index (0-based)
- `$loop->iteration` - Current iteration number (1-based)
- `$loop->first` - True if first iteration
- `$loop->last` - True if last iteration
- `$loop->count` - Total number of items
- `$loop->depth` - Nesting level of current loop
- `$loop->parent` - Parent loop object (for nested loops)

#### Break Statement

```php
@foreach($items as $item)
    @if($item->hidden)
        @break
    @endif
    <div>{{ $item->name }}</div>
@endforeach
```

## Components

### Component Tags

Render other Edge components using `<x-component-name>` syntax:

```php
<x-button label="Click Me" class="btn-primary" />

<x-card title="User Profile">
    <p>{{ $user->bio }}</p>
</x-card>
```

### Component with Attributes

Pass data to components using attributes:

```php
<x-forms-input 
    type="text"
    name="email"
    :value="$user->email"
    placeholder="Enter email"
    required
/>
```

### Bound Attributes

Use `:attribute` syntax to pass PHP expressions:

```php
<x-data-table 
    :headers="$table_headers"
    :rows="$table_data"
    :striped="true"
    :items-per-page="$pagination_size"
/>
```

### Dynamic Components

```php
<x-dynamic-component :component="$component_name" :data="$component_data" />
```

### Component Directive

Alternative component syntax:

```php
@component('component-name', ['prop' => 'value'])
    Content goes here
@endcomponent
```

## Icons

The Edge system includes built-in icon support:

```php
@icon('user')
@icon('chevron-down', 'text-gray-500')
@icon_micro('check', 'w-4 h-4')
```

## Utility Functions

### Debugging

```php
@print_rr($variable)
@log($debug_info)
```

### Views and Routing

```php
@view route_name
@include file_path
@require file_path
```

## Comments

Edge templates support two types of comments:

```php
{{-- This is an Edge comment --}}
<!-- This is an HTML comment -->
```

Edge comments are removed during compilation and won't appear in the output.

## Advanced Features

### Template Sections

Use template sections for conditional content rendering:

```php
{{= tag_content =}}
```

This creates conditional sections that can be controlled by the `$tag_content` variable.

### Automatic Dependency Injection

The Edge system automatically detects and injects dependencies based on usage:

- `$loop` - Loop helper object
- `$pagination` - Pagination helper
- `router::` - Router functionality
- `ICONS` - Icon constants
- Various utility classes

### HTML Minification

Edge automatically minifies HTML output by:
- Removing unnecessary whitespace
- Combining consecutive PHP tags
- Optimizing tag structures

## Best Practices

1. **Always declare props** at the top of your template
2. **Use descriptive prop names** with default values
3. **Escape output** unless you specifically need unescaped HTML
4. **Keep logic minimal** in templates - complex logic should be in PHP classes
5. **Use components** to break down complex templates
6. **Comment complex sections** for maintainability

## Example Template

```php
@props([
    'title' => 'Data Table',
    'headers' => [],
    'rows' => [],
    'striped' => true,
    'class' => ''
])

<div class="table-container {{ $class }}">
    <h2>{{ $title }}</h2>
    
    @if(empty($headers) || empty($rows))
        <div class="alert alert-info">
            No data available.
        </div>
    @else
        <table class="table @if($striped) table-striped @endif">
            <thead>
                <tr>
                    @foreach($headers as $header)
                        <th>{{ $header }}</th>
                    @endforeach
                </tr>
            </thead>
            <tbody>
                @foreach($rows as $row)
                    <tr>
                        @foreach($row as $cell)
                            <td>{{ $cell }}</td>
                        @endforeach
                    </tr>
                @endforeach
            </tbody>
        </table>
    @endif
</div>
```

## Rendering Templates

Templates are rendered using the `Edge::render()` method:

```php
// Render a template with data
echo Edge::render('template-name', [
    'title' => 'My Title',
    'data' => $some_data
]);

// Render a view file
echo Edge::renderView('path/to/view.edge.php', $data);
```

## Performance

- Templates are compiled and cached automatically
- Recompilation occurs only when source files change
- Compiled templates are optimized PHP code
- HTML minification reduces output size
- Dependency injection is optimized based on actual usage
