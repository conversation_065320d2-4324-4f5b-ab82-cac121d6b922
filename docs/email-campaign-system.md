# Email Campaign Management System

## Overview

The Email Campaign Management System is a comprehensive replacement for the existing `send_email.php` script, providing a flexible and scalable solution for managing mass email campaigns. The system maintains backward compatibility while adding powerful new features for campaign management, template editing, and send history tracking.

## Features

### 🎯 Campaign Management
- **Multiple Campaign Types**: Support for subscription renewal, promotional, notification, and general campaigns
- **Campaign Profiles**: Create reusable campaign configurations with templates and rules
- **Status Management**: Draft, active, paused, completed, and archived campaign states
- **Scheduling**: Flexible scheduling with day-of-week and time-of-day controls

### 📧 Template System
- **Rich Template Editor**: Integrated Jodit editor with placeholder support
- **Template Versioning**: Automatic versioning of template changes
- **Placeholder System**: Dynamic content replacement using existing placeholder infrastructure
- **Template Preview**: Preview emails before sending

### 📊 Send History & Tracking
- **Comprehensive Logging**: Detailed send history with status tracking
- **Performance Metrics**: Delivery rates, failure tracking, and campaign statistics
- **Export Capabilities**: CSV and Excel export of send history
- **Real-time Status Updates**: Track sent, delivered, failed, and bounced emails

### 🎛️ Rule Engine
- **Time-based Rules**: Send emails based on subscription expiration dates
- **Conditional Logic**: Complex rule configurations for different scenarios
- **Manual Overrides**: Force send capabilities for testing and urgent communications
- **Integration**: Seamless integration with existing Autodesk subscription data

### 🔒 Unsubscription Management
- **Secure Tokens**: Cryptographically secure unsubscribe links
- **Campaign-specific**: Unsubscribe from individual campaigns or all communications
- **Compliance**: Built-in compliance with email marketing regulations
- **Integration**: Works with existing Autodesk unsubscribe system

## Database Schema

### Core Tables

#### `email_campaigns`
Main campaign definitions and settings
- Campaign metadata (name, description, type, status)
- Email settings (from address, subject template)
- Send rules and scheduling configuration
- Performance statistics

#### `email_campaign_templates`
Template storage with versioning
- HTML/text email templates
- Placeholder definitions
- Version control and activation status

#### `email_campaign_history`
Comprehensive send history tracking
- Individual email send records
- Status tracking (pending, sent, delivered, failed, bounced)
- Recipient information and metadata
- Performance timestamps

#### `email_campaign_recipients`
Target audience management
- Manual recipient lists
- Subscription-based targeting
- Unsubscribe status tracking

#### `email_unsubscribe_tokens`
Secure unsubscribe token management
- Cryptographically secure tokens
- Expiration handling
- Usage tracking

## Installation & Migration

### 1. Database Setup
```bash
# Run the database schema creation
mysql -u username -p database_name < system/sql/email_campaigns.sql

# Add navigation entry
mysql -u username -p database_name < system/sql/email_campaigns_navigation.sql
```

### 2. Migration from Legacy System
```bash
# Run the migration script to convert existing data
php system/migrations/migrate_email_campaigns.php
```

This migration will:
- Create all necessary database tables
- Convert existing email settings to campaign format
- Create default Autodesk subscription renewal campaign
- Migrate existing email history
- Add navigation menu entry

### 3. File Permissions
Ensure the web server has write access to:
- `resources/email_templates/` directory
- `system/logs/` directory for logging

## Usage

### Web Interface

#### Accessing the System
Navigate to **Email Campaigns** in the main navigation menu (requires admin/dev/manager role).

#### Creating a Campaign
1. Click "Create Campaign" button
2. Fill in campaign details:
   - Name and description
   - Campaign type (subscription renewal, promotional, etc.)
   - Email settings (from address, subject template)
   - Scheduling rules (for subscription renewal campaigns)
3. Save the campaign

#### Managing Templates
1. Edit an existing campaign
2. Use the integrated template editor with placeholder support
3. Preview templates before saving
4. Templates are automatically versioned

#### Sending Test Emails
1. Select an active campaign
2. Click "Test" button
3. Enter test email address
4. Review preview and send

#### Viewing Send History
1. Click "History" button for any campaign
2. View detailed send logs with status information
3. Export history data in CSV or Excel format

### Command Line Interface

#### Manual Campaign Execution
```bash
# Send subscription renewal campaigns
php send_email_campaigns.php subscription_renewal

# Force send (ignore time/day restrictions)
php send_email_campaigns.php subscription_renewal --force

# Send promotional campaigns
php send_email_campaigns.php promotional
```

#### Scheduled Execution
Add to crontab for automated sending:
```bash
# Run subscription renewal campaigns daily at 9 AM
0 9 * * * /usr/bin/php /path/to/send_email_campaigns.php subscription_renewal

# Run promotional campaigns weekly
0 10 * * 1 /usr/bin/php /path/to/send_email_campaigns.php promotional
```

### API Integration

#### Campaign Management
```php
use system\email_campaign;

$campaign_manager = new email_campaign();

// Create campaign
$campaign_id = $campaign_manager->create_campaign([
    'name' => 'Monthly Newsletter',
    'type' => 'promotional',
    'from_email' => '<EMAIL>',
    'subject_template' => 'Monthly Update - {{month_name}}'
]);

// Execute campaign
$results = $campaign_manager->execute_campaign($campaign_id);
```

#### Template Management
```php
// Save template
$template_id = $campaign_manager->save_template($campaign_id, [
    'name' => 'Newsletter Template',
    'body_template' => '<html>...</html>',
    'placeholders' => ['month_name' => 'Month Name']
]);
```

## Integration with Existing Systems

### Autodesk Subscription System
- **Seamless Integration**: Uses existing `autodesk_api` class for subscription data
- **Placeholder Compatibility**: Maintains existing placeholder system
- **Unsubscribe Integration**: Works with current unsubscribe functionality
- **History Preservation**: Migrates existing email history

### HTMX and Edge Templates
- **UI Consistency**: Uses established HTMX patterns for dynamic updates
- **Component Reuse**: Leverages existing form and dialog components
- **Navigation Integration**: Follows database-driven navigation system

### Database Patterns
- **New Database Class**: Uses the modern `database.class.php` for all operations
- **Consistent Patterns**: Follows established database query patterns
- **Performance Optimized**: Proper indexing and query optimization

## Security Considerations

### Access Control
- **Role-based Access**: Admin, dev, and manager roles required
- **Campaign Isolation**: Users can only access campaigns they have permission for
- **Audit Trail**: All actions logged with user attribution

### Email Security
- **Secure Tokens**: Cryptographically secure unsubscribe tokens
- **Input Validation**: All user inputs validated and sanitized
- **SQL Injection Protection**: Parameterized queries throughout

### Data Protection
- **Personal Data**: Proper handling of email addresses and personal information
- **GDPR Compliance**: Unsubscribe mechanisms and data retention policies
- **Encryption**: Sensitive data encrypted where appropriate

## Troubleshooting

### Common Issues

#### Campaign Not Sending
1. Check campaign status is "active"
2. Verify template exists and is active
3. Check scheduling rules (day/time restrictions)
4. Review error logs in send history

#### Template Issues
1. Verify placeholders are correctly formatted: `{{placeholder_name}}`
2. Check template HTML validity
3. Ensure template is marked as active

#### Permission Errors
1. Verify user has required role (admin/dev/manager)
2. Check file permissions on template directories
3. Review database connection settings

### Logging
All campaign activities are logged to:
- **Application Logs**: `system/logs/` directory
- **Database History**: `email_campaign_history` table
- **Error Tracking**: Failed sends with detailed error messages

## Future Enhancements

### Planned Features
- **A/B Testing**: Template and subject line testing
- **Advanced Analytics**: Open rates, click tracking, engagement metrics
- **Automation Workflows**: Trigger-based email sequences
- **Integration APIs**: REST API for external system integration
- **Mobile Optimization**: Responsive email templates
- **Personalization**: Advanced personalization beyond placeholders

### Extensibility
The system is designed for easy extension:
- **Plugin Architecture**: Add new campaign types via plugins
- **Custom Rules**: Extend rule engine for specific business logic
- **Template Engines**: Support for additional template formats
- **Delivery Providers**: Integration with external email services

## Support

For technical support or questions about the Email Campaign Management System:
1. Check this documentation first
2. Review error logs and send history
3. Contact the development team with specific error messages and steps to reproduce issues

## Migration Notes

### Backward Compatibility
- **Legacy Script**: Original `send_email.php` continues to work during transition
- **Gradual Migration**: Can migrate campaigns one at a time
- **Data Preservation**: All existing email history is preserved
- **Settings Migration**: Existing email rules and schedules are automatically converted

### Post-Migration Steps
1. Test the new system with a few test campaigns
2. Verify all existing functionality works as expected
3. Update any external scripts or cron jobs to use new system
4. Train users on the new interface
5. Archive or remove legacy scripts once confident in new system
