<?php
/**
 * Test script for data source duplication functionality
 * Run this script to verify that the duplicate feature works correctly
 */

// Include the necessary files
require_once 'system/classes/startup_sequence.class.php';
require_once 'system/functions/functions.php';

// Initialize the system
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . '/system/config/path_schema.php';
$path['fs_system'] = $path['fs_app_root'] . '/' . $schema['system']['root'];
startup_sequence::start($path, $schema);

use system\data_source_manager;

echo "<h1>Data Source Duplication Test</h1>";

try {
    // Get all existing data sources
    $data_sources = data_source_manager::get_data_sources();
    
    if (empty($data_sources)) {
        echo "<p style='color: orange;'>No data sources found. Please create a data source first to test duplication.</p>";
        exit;
    }
    
    echo "<h2>Existing Data Sources:</h2>";
    echo "<ul>";
    foreach ($data_sources as $source) {
        echo "<li>ID: {$source['id']}, Name: {$source['name']}, Table: {$source['table_name']}</li>";
    }
    echo "</ul>";
    
    // Test duplication with the first data source
    $test_source = $data_sources[0];
    echo "<h2>Testing Duplication</h2>";
    echo "<p>Duplicating data source: <strong>{$test_source['name']}</strong> (ID: {$test_source['id']})</p>";
    
    // Test the duplicate_data_source method
    $new_id = data_source_manager::duplicate_data_source($test_source['id']);
    
    echo "<p style='color: green;'>✓ Successfully duplicated data source. New ID: {$new_id}</p>";
    
    // Verify the new data source
    $new_source = data_source_manager::get_data_source($new_id);
    if ($new_source) {
        echo "<h3>New Data Source Details:</h3>";
        echo "<ul>";
        echo "<li><strong>ID:</strong> {$new_source['id']}</li>";
        echo "<li><strong>Name:</strong> {$new_source['name']}</li>";
        echo "<li><strong>Table:</strong> {$new_source['table_name']}</li>";
        echo "<li><strong>Description:</strong> " . ($new_source['description'] ?? 'None') . "</li>";
        echo "<li><strong>Category:</strong> {$new_source['category']}</li>";
        echo "<li><strong>Status:</strong> {$new_source['status']}</li>";
        echo "</ul>";
        
        // Compare configurations
        echo "<h3>Configuration Comparison:</h3>";
        $original = data_source_manager::get_data_source($test_source['id']);
        
        $config_fields = ['tables', 'joins', 'selected_columns', 'filters', 'table_aliases', 'column_aliases', 'custom_columns', 'sorting', 'grouping', 'limits'];
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Field</th><th>Original</th><th>Duplicate</th><th>Match</th></tr>";
        
        foreach ($config_fields as $field) {
            $original_value = json_encode($original[$field] ?? null);
            $new_value = json_encode($new_source[$field] ?? null);
            $match = $original_value === $new_value ? '✓' : '✗';
            $color = $match === '✓' ? 'green' : 'red';
            
            echo "<tr>";
            echo "<td><strong>{$field}</strong></td>";
            echo "<td>" . htmlspecialchars(substr($original_value, 0, 100)) . (strlen($original_value) > 100 ? '...' : '') . "</td>";
            echo "<td>" . htmlspecialchars(substr($new_value, 0, 100)) . (strlen($new_value) > 100 ? '...' : '') . "</td>";
            echo "<td style='color: {$color};'>{$match}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p style='color: green;'>✓ Duplication test completed successfully!</p>";
        
    } else {
        echo "<p style='color: red;'>✗ Error: Could not retrieve the newly created data source.</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error during duplication test: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='data_sources'>← Back to Data Sources</a></p>";
?>
