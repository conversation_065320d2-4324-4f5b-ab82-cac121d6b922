<?php
// Clear compiled template cache
$temp_dir = 'temp/autobooks/';

if (is_dir($temp_dir)) {
    $files = glob($temp_dir . '*.edge.php');
    foreach ($files as $file) {
        if (unlink($file)) {
            echo "Deleted: " . basename($file) . "\n";
        }
    }
    echo "Template cache cleared!\n";
} else {
    echo "Temp directory not found: $temp_dir\n";
}

// Also check if the specific file exists
$specific_file = $temp_dir . 'view-customer_display.edge.php';
if (file_exists($specific_file)) {
    if (unlink($specific_file)) {
        echo "Deleted specific file: view-customer_display.edge.php\n";
    }
} else {
    echo "Specific file not found: $specific_file\n";
}
