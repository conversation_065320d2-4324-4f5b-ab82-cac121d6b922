<?php
// Test script to verify operator fixes
require_once 'system/functions/database.php';

// Mock schema for testing
$schema = [
    'subs' => [
        'query' => 'FROM autodesk_subscriptions subs',
        'fields' => ['endDate', 'id', 'status']
    ]
];

$populate_column = function($column) {
    // Mock function
};

echo "Testing database operator fixes...\n\n";

// Test BETWEEN
echo "1. Testing BETWEEN clause:\n";
$key = 'subs.endDate';
$value = ['BETWEEN', ['2025-07-15', '2025-10-13']];
$result = build_where_string($key, $value, $schema, $populate_column);
echo "Input: " . json_encode($value) . "\n";
echo "Result: " . ($result[0] ?? 'No result') . "\n";
if (!empty($result) && strpos($result[0], "'2025-07-15' AND '2025-10-13'") !== false) {
    echo "✓ BETWEEN: SUCCESS\n\n";
} else {
    echo "✗ BETWEEN: FAILED\n\n";
}

// Test NOT BETWEEN
echo "2. Testing NOT BETWEEN clause:\n";
$value = ['NOT BETWEEN', ['2025-01-01', '2025-12-31']];
$result = build_where_string($key, $value, $schema, $populate_column);
echo "Input: " . json_encode($value) . "\n";
echo "Result: " . ($result[0] ?? 'No result') . "\n";
if (!empty($result) && strpos($result[0], "'2025-01-01' AND '2025-12-31'") !== false) {
    echo "✓ NOT BETWEEN: SUCCESS\n\n";
} else {
    echo "✗ NOT BETWEEN: FAILED\n\n";
}

// Test IN
echo "3. Testing IN clause:\n";
$key = 'subs.status';
$value = ['IN', ['Active', 'Pending', 'Expired']];
$result = build_where_string($key, $value, $schema, $populate_column);
echo "Input: " . json_encode($value) . "\n";
echo "Result: " . ($result[0] ?? 'No result') . "\n";
if (!empty($result) && strpos($result[0], "('Active','Pending','Expired')") !== false) {
    echo "✓ IN: SUCCESS\n\n";
} else {
    echo "✗ IN: FAILED\n\n";
}

// Test NOT IN
echo "4. Testing NOT IN clause:\n";
$value = ['NOT IN', ['Cancelled', 'Suspended']];
$result = build_where_string($key, $value, $schema, $populate_column);
echo "Input: " . json_encode($value) . "\n";
echo "Result: " . ($result[0] ?? 'No result') . "\n";
if (!empty($result) && strpos($result[0], "('Cancelled','Suspended')") !== false) {
    echo "✓ NOT IN: SUCCESS\n\n";
} else {
    echo "✗ NOT IN: FAILED\n\n";
}

echo "All tests completed!\n";
?>
