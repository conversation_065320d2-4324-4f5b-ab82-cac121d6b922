<?php
/**
 * Test custom table columns in column selector
 */

// Include the necessary files
require_once 'system/classes/startup_sequence.class.php';
require_once 'system/functions/functions.php';

// Initialize the system
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . '/system/config/path_schema.php';
$path['fs_system'] = $path['fs_app_root'] . '/' . $schema['system']['root'];
startup_sequence::start($path, $schema);

// Include the API functions
require_once 'system/api/data_sources.api.php';

echo "<h1>Test Custom Table Columns in Column Selector</h1>";

try {
    // Test parameters with custom tables
    $test_params = [
        'selected_tables' => '["autodesk_subscriptions"]',
        'table_aliases' => ['autodesk_subscriptions' => 'subs'],
        'custom_tables' => [
            [
                'alias' => 'lastquote',
                'join_type' => 'LEFT JOIN',
                'sql' => 'SELECT qi.subscription_id, q.id AS quote_id, q.quote_status FROM autodesk_quote_line_items qi JOIN autodesk_quotes q ON q.id = qi.quote_id WHERE qi.subscription_id IS NOT NULL ORDER BY q.quoted_date DESC LIMIT 1',
                'join_condition' => 'subs.subscriptionId = lastquote.subscription_id',
                'columns' => 'subscription_id, quote_id, quote_status, quote_number, quoted_date',
                'description' => 'Latest quote for each subscription'
            ],
            [
                'alias' => 'firstorder',
                'join_type' => 'LEFT JOIN',
                'sql' => 'SELECT subscription_id, order_id, order_date FROM orders WHERE subscription_id IS NOT NULL ORDER BY order_date ASC LIMIT 1',
                'join_condition' => 'subs.subscriptionId = firstorder.subscription_id',
                'columns' => 'subscription_id, order_id, order_date',
                'description' => 'First order for each subscription'
            ]
        ],
        'selected_columns' => [],
        'joins' => []
    ];
    
    echo "<h2>Test Configuration:</h2>";
    echo "<h3>Custom Tables:</h3>";
    echo "<pre>" . json_encode($test_params['custom_tables'], JSON_PRETTY_PRINT) . "</pre>";
    
    // Test column_selection_fragment
    echo "<h2>1. Testing column_selection_fragment:</h2>";
    $column_selection = \api\data_sources\column_selection_fragment($test_params);
    
    if (strpos($column_selection, 'lastquote (custom)') !== false) {
        echo "<p style='color: green;'>✓ Custom table 'lastquote' appears in column selector</p>";
    } else {
        echo "<p style='color: red;'>✗ Custom table 'lastquote' not found in column selector</p>";
    }
    
    if (strpos($column_selection, 'firstorder (custom)') !== false) {
        echo "<p style='color: green;'>✓ Custom table 'firstorder' appears in column selector</p>";
    } else {
        echo "<p style='color: red;'>✗ Custom table 'firstorder' not found in column selector</p>";
    }
    
    // Check for specific columns
    $expected_columns = ['subscription_id', 'quote_id', 'quote_status', 'order_id', 'order_date'];
    foreach ($expected_columns as $column) {
        if (strpos($column_selection, $column) !== false) {
            echo "<p style='color: green;'>✓ Column '$column' found in selector</p>";
        } else {
            echo "<p style='color: orange;'>⚠ Column '$column' not found in selector</p>";
        }
    }
    
    echo "<h4>Column Selection HTML (first 1000 characters):</h4>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; max-height: 300px;'>";
    echo htmlspecialchars(substr($column_selection, 0, 1000));
    echo "</pre>";
    
    // Test get_all_available_columns
    echo "<h2>2. Testing get_all_available_columns:</h2>";
    $all_columns = \api\data_sources\get_all_available_columns($test_params);
    
    echo "<p><strong>Available columns:</strong> " . implode(', ', $all_columns) . "</p>";
    
    $expected_custom_columns = ['lastquote.subscription_id', 'lastquote.quote_id', 'lastquote.quote_status', 'firstorder.order_id'];
    foreach ($expected_custom_columns as $column) {
        if (in_array($column, $all_columns)) {
            echo "<p style='color: green;'>✓ Custom column '$column' found in available columns</p>";
        } else {
            echo "<p style='color: red;'>✗ Custom column '$column' not found in available columns</p>";
        }
    }
    
    // Test get_available_columns_for_sorting
    echo "<h2>3. Testing get_available_columns_for_sorting:</h2>";
    $sorting_columns = \api\data_sources\get_available_columns_for_sorting($test_params);
    
    echo "<p><strong>Sorting columns:</strong> " . implode(', ', $sorting_columns) . "</p>";
    
    $expected_sorting_columns = ['lastquote_subscription_id', 'lastquote_quote_id', 'firstorder_order_id'];
    foreach ($expected_sorting_columns as $column) {
        if (in_array($column, $sorting_columns)) {
            echo "<p style='color: green;'>✓ Custom column '$column' found in sorting options</p>";
        } else {
            echo "<p style='color: red;'>✗ Custom column '$column' not found in sorting options</p>";
        }
    }
    
    // Test get_available_columns_for_grouping
    echo "<h2>4. Testing get_available_columns_for_grouping:</h2>";
    $grouping_columns = \api\data_sources\get_available_columns_for_grouping($test_params);
    
    echo "<p><strong>Grouping columns:</strong> " . implode(', ', $grouping_columns) . "</p>";
    
    $expected_grouping_columns = ['lastquote.subscription_id', 'lastquote.quote_id', 'firstorder.order_id'];
    foreach ($expected_grouping_columns as $column) {
        if (in_array($column, $grouping_columns)) {
            echo "<p style='color: green;'>✓ Custom column '$column' found in grouping options</p>";
        } else {
            echo "<p style='color: red;'>✗ Custom column '$column' not found in grouping options</p>";
        }
    }
    
    // Test query generation with custom table columns selected
    echo "<h2>5. Testing Query Generation with Custom Table Columns:</h2>";
    $test_params_with_selection = $test_params;
    $test_params_with_selection['selected_columns'] = [
        'subs.subscriptionId',
        'subs.status',
        'lastquote.quote_id',
        'lastquote.quote_status',
        'firstorder.order_date'
    ];
    
    $query_preview = \api\data_sources\query_preview_fragment($test_params_with_selection);
    
    if (strpos($query_preview, 'lastquote.quote_id') !== false) {
        echo "<p style='color: green;'>✓ Custom table columns appear in generated query</p>";
    } else {
        echo "<p style='color: red;'>✗ Custom table columns not found in generated query</p>";
    }
    
    echo "<h4>Generated Query Preview:</h4>";
    echo "<div style='border: 1px solid #ddd; padding: 10px; background: #f9f9f9;'>";
    echo $query_preview;
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='data_sources'>← Back to Data Sources</a></p>";
?>
