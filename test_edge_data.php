<?php
/**
 * Test Edge template data passing
 */

// Include the necessary files
require_once 'system/classes/startup_sequence.class.php';
require_once 'system/functions/functions.php';

// Initialize the system
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . '/system/config/path_schema.php';
$path['fs_system'] = $path['fs_app_root'] . '/' . $schema['system']['root'];
startup_sequence::start($path, $schema);

use system\data_source_manager;
use edge\edge;

// Get the ID from URL parameter
$id = $_GET['id'] ?? null;

if (!$id) {
    // Show available data sources
    $data_sources = data_source_manager::get_data_sources();
    
    echo "<h1>Select a Data Source to Test</h1>";
    
    if (empty($data_sources)) {
        echo "<p>No data sources found. Please create one first.</p>";
        echo "<p><a href='data_sources'>Go to Data Sources</a></p>";
        exit;
    }
    
    echo "<ul>";
    foreach ($data_sources as $source) {
        echo "<li><a href='?id={$source['id']}'>{$source['name']} (ID: {$source['id']})</a></li>";
    }
    echo "</ul>";
    exit;
}

echo "<h1>Testing Edge Template Data Passing</h1>";

try {
    // Get the original data source
    $original = data_source_manager::get_data_source($id);
    if (!$original) {
        echo "<p style='color: red;'>Data source not found!</p>";
        exit;
    }
    
    // Prepare duplicate data
    $duplicate_data = $original;
    unset($duplicate_data['id']);
    $duplicate_data['name'] = 'Copy of ' . $original['name'];
    
    echo "<h2>Data being passed to template:</h2>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; max-height: 400px;'>";
    print_r($duplicate_data);
    echo "</pre>";
    
    // Create a simple test template inline
    $test_template_content = '
    @props([
        "test_data" => null,
        "mode" => "test"
    ])
    
    <div style="border: 2px solid #4CAF50; padding: 20px; margin: 20px 0;">
        <h3>Template Test Results</h3>
        <p><strong>Mode:</strong> {{ $mode }}</p>
        <p><strong>Data Source Name:</strong> {{ $test_data["name"] ?? "NOT FOUND" }}</p>
        <p><strong>Table Name:</strong> {{ $test_data["table_name"] ?? "NOT FOUND" }}</p>
        <p><strong>Category:</strong> {{ $test_data["category"] ?? "NOT FOUND" }}</p>
        
        @if($test_data)
            <p style="color: green;">✓ Data source object is available</p>
            <p><strong>Keys in data:</strong> {{ implode(", ", array_keys($test_data)) }}</p>
        @else
            <p style="color: red;">✗ Data source object is null or empty</p>
        @endif
        
        <h4>Form Field Test:</h4>
        <input type="text" name="test_name" value="{{ $test_data["name"] ?? "" }}" placeholder="Should show copied name" style="width: 300px; padding: 5px;">
    </div>
    ';
    
    // Save the test template
    $test_template_path = 'system/components/edges/test-template.edge.php';
    file_put_contents($test_template_path, $test_template_content);
    
    echo "<h2>Rendering Test Template:</h2>";
    
    // Render the test template
    $rendered = Edge::render('test-template', [
        'test_data' => $duplicate_data,
        'mode' => 'duplicate_test'
    ]);
    
    echo $rendered;
    
    // Clean up the test template
    unlink($test_template_path);
    
    echo "<h2>Direct Template Variable Test:</h2>";
    echo "<p>If the above shows the correct data, then the issue is not with Edge template data passing.</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='?'>← Back to selection</a> | <a href='data_sources'>← Back to Data Sources</a></p>";
?>
