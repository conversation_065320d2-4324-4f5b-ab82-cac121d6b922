<?php
// Quick fix for data sources navigation
const DS = DIRECTORY_SEPARATOR;
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . DS . 'system'. DS . 'config'. DS . 'path_schema.php';
$ds = DS;
$path['fs_system'] = "{$path['fs_app_root']}{$ds}{$schema['system']['root']}" ;

$input_params = array_merge($_GET, $_POST);
define('INPUT_PARAMS',$input_params);
const DEBUG_MODE = true;
const API_RUN = false;

require_once $path['fs_system'] . DS . 'classes'. DS . 'startup_sequence.class.php';
require_once $path['fs_system'] . DS . 'functions'. DS . 'functions.php';

startup_sequence::start($path,$schema);

use system\database;

echo "<h1>Quick Fix for Data Sources Navigation</h1>";

try {
    // Update the navigation entry
    $updated = database::table('autobooks_navigation')
        ->where('route_key', 'data_sources')
        ->where('parent_path', 'root')
        ->update([
            'file_path' => 'data_sources',
            'is_system' => 1,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    
    if ($updated) {
        echo "<p style='color: green;'>✓ Navigation entry updated successfully</p>";
    } else {
        echo "<p style='color: orange;'>No rows updated - entry may not exist</p>";
    }
    
    // Verify the update
    $nav = database::table('autobooks_navigation')
        ->where('route_key', 'data_sources')
        ->where('parent_path', 'root')
        ->first();
    
    if ($nav) {
        echo "<h2>Current navigation entry:</h2>";
        echo "<ul>";
        echo "<li><strong>route_key:</strong> " . $nav['route_key'] . "</li>";
        echo "<li><strong>file_path:</strong> " . $nav['file_path'] . "</li>";
        echo "<li><strong>is_system:</strong> " . ($nav['is_system'] ? 'true' : 'false') . "</li>";
        echo "</ul>";
    }
    
    echo "<h2>Test Links:</h2>";
    echo "<ul>";
    echo "<li><a href='data_sources'>Main Data Sources Page</a></li>";
    echo "<li><a href='data_sources/create'>Create Data Source</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
