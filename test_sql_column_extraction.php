<?php
/**
 * Test SQL column extraction function
 */

// Include the necessary files
require_once 'system/classes/startup_sequence.class.php';
require_once 'system/functions/functions.php';

// Initialize the system
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . '/system/config/path_schema.php';
$path['fs_system'] = $path['fs_app_root'] . '/' . $schema['system']['root'];
startup_sequence::start($path, $schema);

// Include the API functions
require_once 'system/api/data_sources.api.php';

echo "<h1>Test SQL Column Extraction</h1>";

// Test with your actual SQL from the database
$test_sql = "SELECT qi.subscription_id, q.id AS quote_id, q.quote_status, q.quote_number, q.quoted_date, qi.id AS qitem_id

FROM autodesk_quote_line_items qi
JOIN autodesk_quotes q ON q.id = qi.quote_id

WHERE qi.subscription_id IS NOT NULL
  AND q.quote_status NOT IN ('Expired', 'Cancelled')

ORDER BY qi.subscription_id, q.quoted_date DESC, qi.id DESC

LIMIT 1";

echo "<h2>Test SQL:</h2>";
echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto;'>";
echo htmlspecialchars($test_sql);
echo "</pre>";

try {
    $extracted_columns = \api\data_sources\extract_columns_from_sql($test_sql);
    
    echo "<h2>Extracted Columns:</h2>";
    if (!empty($extracted_columns)) {
        echo "<ul>";
        foreach ($extracted_columns as $column) {
            echo "<li><strong>" . htmlspecialchars($column) . "</strong></li>";
        }
        echo "</ul>";
        
        echo "<p style='color: green;'>✓ Successfully extracted " . count($extracted_columns) . " columns</p>";
        
        // Expected columns from your SQL
        $expected_columns = ['subscription_id', 'quote_id', 'quote_status', 'quote_number', 'quoted_date', 'qitem_id'];
        
        echo "<h3>Validation:</h3>";
        foreach ($expected_columns as $expected) {
            if (in_array($expected, $extracted_columns)) {
                echo "<p style='color: green;'>✓ Found expected column: $expected</p>";
            } else {
                echo "<p style='color: red;'>✗ Missing expected column: $expected</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>✗ No columns extracted</p>";
    }
    
    // Test with the actual custom table data from your database
    echo "<h2>Testing with Custom Table Configuration:</h2>";
    
    $custom_table_config = [
        'alias' => 'lastquote',
        'sql' => $test_sql,
        'join_type' => 'LEFT JOIN',
        'join_condition' => 'subs.subscriptionId = lastquote.subscription_id',
        'columns' => '', // Empty as in your database
        'description' => ''
    ];
    
    $test_params = [
        'selected_tables' => '["autodesk_subscriptions"]',
        'table_aliases' => ['autodesk_subscriptions' => 'subs'],
        'custom_tables' => [$custom_table_config],
        'selected_columns' => [],
        'joins' => []
    ];
    
    echo "<h3>Testing column_selection_fragment:</h3>";
    $column_selection = \api\data_sources\column_selection_fragment($test_params);
    
    if (strpos($column_selection, 'lastquote (custom)') !== false) {
        echo "<p style='color: green;'>✓ Custom table appears in column selector</p>";
    } else {
        echo "<p style='color: red;'>✗ Custom table not found in column selector</p>";
    }
    
    // Check for specific columns
    foreach ($expected_columns as $column) {
        if (strpos($column_selection, $column) !== false) {
            echo "<p style='color: green;'>✓ Column '$column' found in selector</p>";
        } else {
            echo "<p style='color: red;'>✗ Column '$column' not found in selector</p>";
        }
    }
    
    echo "<h3>Testing get_all_available_columns:</h3>";
    $all_columns = \api\data_sources\get_all_available_columns($test_params);
    
    echo "<p><strong>Available columns:</strong> " . implode(', ', $all_columns) . "</p>";
    
    $expected_custom_columns = ['lastquote.subscription_id', 'lastquote.quote_id', 'lastquote.quote_status'];
    foreach ($expected_custom_columns as $column) {
        if (in_array($column, $all_columns)) {
            echo "<p style='color: green;'>✓ Custom column '$column' found in available columns</p>";
        } else {
            echo "<p style='color: red;'>✗ Custom column '$column' not found in available columns</p>";
        }
    }
    
    echo "<h3>Testing query generation:</h3>";
    $test_params_with_selection = $test_params;
    $test_params_with_selection['selected_columns'] = [
        'subs.subscriptionId',
        'subs.status',
        'lastquote.quote_id',
        'lastquote.quote_status'
    ];
    
    $query_preview = \api\data_sources\query_preview_fragment($test_params_with_selection);
    
    if (strpos($query_preview, 'lastquote.quote_id') !== false) {
        echo "<p style='color: green;'>✓ Custom table columns appear in generated query</p>";
    } else {
        echo "<p style='color: red;'>✗ Custom table columns not found in generated query</p>";
    }
    
    echo "<h4>Generated Query Preview:</h4>";
    echo "<div style='border: 1px solid #ddd; padding: 10px; background: #f9f9f9;'>";
    echo $query_preview;
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='data_sources'>← Back to Data Sources</a></p>";
?>
