<?php
/**
 * Test script to verify the data_table_storage class is loading correctly
 */

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Class Loading</title></head><body>";
echo "<h1>Test Class Loading</h1>";

echo "<pre>";

// Try to include the autoloader
echo "1. Loading autoloader...\n";
try {
    require_once 'system/autoloader.php';
    echo "✓ Autoloader loaded successfully\n";
} catch (Exception $e) {
    echo "✗ Failed to load autoloader: " . $e->getMessage() . "\n";
    exit;
}

// Try to include the config
echo "\n2. Loading config...\n";
try {
    require_once 'system/config.php';
    echo "✓ Config loaded successfully\n";
} catch (Exception $e) {
    echo "✗ Failed to load config: " . $e->getMessage() . "\n";
    exit;
}

// Check if the class file exists
echo "\n3. Checking class file existence...\n";
$class_file = 'system/classes/data_table_storage.class.php';
if (file_exists($class_file)) {
    echo "✓ Class file exists: $class_file\n";
} else {
    echo "✗ Class file not found: $class_file\n";
    exit;
}

// Try to manually include the class
echo "\n4. Manually including class file...\n";
try {
    require_once $class_file;
    echo "✓ Class file included successfully\n";
} catch (Exception $e) {
    echo "✗ Failed to include class file: " . $e->getMessage() . "\n";
    exit;
}

// Check if the class exists
echo "\n5. Checking if class exists...\n";
if (class_exists('data_table_storage')) {
    echo "✓ data_table_storage class exists\n";
} else {
    echo "✗ data_table_storage class not found\n";
    
    // List all declared classes to debug
    echo "\nDeclared classes containing 'storage':\n";
    $classes = get_declared_classes();
    foreach ($classes as $class) {
        if (stripos($class, 'storage') !== false) {
            echo "  - $class\n";
        }
    }
    exit;
}

// Test basic class methods
echo "\n6. Testing class methods...\n";
$methods = get_class_methods('data_table_storage');
if ($methods) {
    echo "✓ Class methods found:\n";
    foreach ($methods as $method) {
        echo "  - $method\n";
    }
} else {
    echo "✗ No methods found\n";
}

// Test database connection
echo "\n7. Testing database connection...\n";
try {
    $test_query = "SELECT 1 as test";
    $result = tep_db_query($test_query);
    if ($result) {
        echo "✓ Database connection working\n";
    } else {
        echo "✗ Database query failed\n";
    }
} catch (Exception $e) {
    echo "✗ Database error: " . $e->getMessage() . "\n";
}

// Test the get_current_user_id method
echo "\n8. Testing get_current_user_id method...\n";
try {
    $user_id = data_table_storage::get_current_user_id();
    echo "✓ get_current_user_id returned: " . ($user_id ?? 'NULL') . "\n";
} catch (Exception $e) {
    echo "✗ get_current_user_id failed: " . $e->getMessage() . "\n";
}

// Test table existence
echo "\n9. Checking if autobooks_data_table_storage table exists...\n";
try {
    $table_check = "SHOW TABLES LIKE 'autobooks_data_table_storage'";
    $result = tep_db_query($table_check);
    if ($result && tep_db_num_rows($result) > 0) {
        echo "✓ autobooks_data_table_storage table exists\n";
    } else {
        echo "✗ autobooks_data_table_storage table not found\n";
        echo "  Run create_table_only.php first to create the table\n";
    }
} catch (Exception $e) {
    echo "✗ Table check failed: " . $e->getMessage() . "\n";
}

echo "\n=== Test Summary ===\n";
echo "If all tests passed, the class should work correctly.\n";
echo "If any tests failed, check the error messages above.\n";

echo "</pre>";

echo "<hr>";
echo "<h2>Next Steps</h2>";
echo "<ul>";
echo "<li>If table doesn't exist: <a href='create_table_only.php'>Create Table</a></li>";
echo "<li>If everything looks good: <a href='test_database_storage.php'>Test Full System</a></li>";
echo "<li>Check your original error by visiting the subscriptions page</li>";
echo "</ul>";

echo "</body></html>";
?>
