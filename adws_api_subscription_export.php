<?php
/**
 * Autodesk Subscription Webhook Handler
 *
 * This file handles incoming webhook requests from Autodesk's API
 * for subscription updates. It bypasses the normal authentication flow
 * but implements its own security measures using HMAC signature verification.
 */

use autodesk_api\autodesk_api;
use autodesk_api\autodesk_subscription;
const DS = DIRECTORY_SEPARATOR;
// Set up the application environment
$path['fs_app_root'] = __DIR__ . "/baffletrain/autocadlt/autobooks";
require_once $path['fs_app_root'] . DS . 'system' . DS . 'startup_sequence_minimal.php';

// Set up logging
tcs_log('Webhook request received at ' . date('Y-m-d H:i:s'),'subscription_import');

// Load webhook configuration
$webhook_config = include(FS_SYSTEM . DS . 'webhook_config.php');

// Get the raw POST data before doing anything else
$raw_post_data = file_get_contents('php://input');

// Verify Autodesk signature
$provided_signature = $_SERVER['HTTP_X_ADSK_SIGNATURE'] ?? '';
$webhook_secret = $webhook_config['webhook_secrets']['autodesk_subscription'];

// Calculate expected signature
$calculated_signature = 'sha256=' . hash_hmac('sha256', $raw_post_data, $webhook_secret);

// Log signature information for debugging (remove in production)
tcs_log('Provided signature: ' . $provided_signature,'subscription_import');
tcs_log('Calculated signature: ' . $calculated_signature,'subscription_import');

// Verify signature
if (empty($provided_signature) || $provided_signature !== $calculated_signature) {
    tcs_log('Invalid or missing signature','subscription_import');
    http_response_code(401);
    echo json_encode(['status' => 'error', 'message' => 'Unauthorized - Invalid signature']);
    exit;
}

// Get headers for logging
$headers = [];
foreach ($_SERVER as $name => $value) {
    if (substr($name, 0, 5) == 'HTTP_') {
        $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
    }
}
tcs_log('Request headers: ' . print_r($headers, true),'subscription_import');

// Log the webhook data (we already got the raw POST data earlier)
tcs_log('Received webhook data: ' . $raw_post_data,'subscription_import');

// Process the webhook
try {
    // Decode the JSON payload
    $json_payload = json_decode($raw_post_data, true);

    if (!$json_payload) {
        throw new Exception('Invalid JSON payload');
    }

    // Handle different webhook topics
    switch ($json_payload['topic']) {
        case 'subscription-change':
            $result = autodesk_subscription::update_from_api($json_payload);
            break;
        case 'quote-status':
            switch ($json_payload['event']) {
                case 'created':
                case 'changed':
                case 'deleted':
                    $result = json_encode(['status' => 'success', 'message' => 'Quote status update received']);
                    break;
            }
            break;
        case 'quote-import':
            $result = json_encode(['status' => 'success', 'message' => 'Quote import received']);
            break;
        case 'product-catalog':
            switch ($json_payload['event']) {
                case 'changed':
                    $result = json_encode(['status' => 'success', 'message' => 'Product catalog update received']);
                    break;
            }
            break;
        default:
            $result = json_encode(['status' => 'success', 'message' => 'Nothing to do']);
            break;
    }

    // Return success response
    header('Content-Type: application/json');
    echo $result;

} catch (Exception $e) {
    // Log the error
    tcs_log('Error processing webhook: ' . $e->getMessage(),'subscription_import');

    // Return error response
    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => 'Error processing webhook: ' . $e->getMessage()
    ]);
}
