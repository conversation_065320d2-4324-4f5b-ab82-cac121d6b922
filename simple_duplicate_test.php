<?php
/**
 * Simple test to check if duplicate functionality works
 */

// Include the necessary files
require_once 'system/classes/startup_sequence.class.php';
require_once 'system/functions/functions.php';

// Initialize the system
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . '/system/config/path_schema.php';
$path['fs_system'] = $path['fs_app_root'] . '/' . $schema['system']['root'];
startup_sequence::start($path, $schema);

use system\data_source_manager;

// Get the ID from URL parameter
$id = $_GET['id'] ?? null;

if (!$id) {
    // Show available data sources
    $data_sources = data_source_manager::get_data_sources();
    
    echo "<h1>Select a Data Source to Duplicate</h1>";
    
    if (empty($data_sources)) {
        echo "<p>No data sources found. Please create one first.</p>";
        echo "<p><a href='data_sources'>Go to Data Sources</a></p>";
        exit;
    }
    
    echo "<ul>";
    foreach ($data_sources as $source) {
        echo "<li><a href='?id={$source['id']}'>{$source['name']} (ID: {$source['id']})</a></li>";
    }
    echo "</ul>";
    exit;
}

// Test the duplication
echo "<h1>Testing Duplication for Data Source ID: $id</h1>";

try {
    // Get the original data source
    $original = data_source_manager::get_data_source($id);
    if (!$original) {
        echo "<p style='color: red;'>Data source not found!</p>";
        exit;
    }
    
    echo "<h2>Original Data Source:</h2>";
    echo "<p><strong>Name:</strong> {$original['name']}</p>";
    echo "<p><strong>Table:</strong> {$original['table_name']}</p>";
    echo "<p><strong>Category:</strong> {$original['category']}</p>";
    
    // Prepare duplicate data
    $duplicate_data = $original;
    unset($duplicate_data['id']);
    $duplicate_data['name'] = 'Copy of ' . $original['name'];
    
    echo "<h2>Duplicate Data (what would be passed to form):</h2>";
    echo "<p><strong>Name:</strong> {$duplicate_data['name']}</p>";
    echo "<p><strong>Table:</strong> {$duplicate_data['table_name']}</p>";
    echo "<p><strong>Category:</strong> {$duplicate_data['category']}</p>";
    
    // Show key fields that should be populated
    $key_fields = ['tables', 'selected_columns', 'filters', 'joins'];
    echo "<h3>Key Configuration Fields:</h3>";
    echo "<ul>";
    foreach ($key_fields as $field) {
        $value = $duplicate_data[$field] ?? null;
        if (is_array($value)) {
            echo "<li><strong>$field:</strong> Array with " . count($value) . " items</li>";
        } elseif (is_string($value)) {
            echo "<li><strong>$field:</strong> String: " . substr($value, 0, 100) . (strlen($value) > 100 ? '...' : '') . "</li>";
        } else {
            echo "<li><strong>$field:</strong> " . gettype($value) . " - " . var_export($value, true) . "</li>";
        }
    }
    echo "</ul>";
    
    // Test the actual API call
    echo "<h2>Testing API Call:</h2>";
    require_once 'system/api/data_sources.api.php';
    
    $params = ['id' => $id];
    $result = \api\data_sources\duplicate_view($params);
    
    if (is_string($result) && strlen($result) > 100) {
        echo "<p style='color: green;'>✓ API call successful, returned HTML content</p>";
        
        // Check for key indicators in the HTML
        $indicators = [
            'Copy of' => strpos($result, 'Copy of') !== false,
            'Name field' => strpos($result, 'name="name"') !== false,
            'Category field' => strpos($result, 'name="category"') !== false,
            'Form tag' => strpos($result, '<form') !== false
        ];
        
        echo "<h3>HTML Content Checks:</h3>";
        echo "<ul>";
        foreach ($indicators as $check => $found) {
            $status = $found ? '✓' : '✗';
            $color = $found ? 'green' : 'red';
            echo "<li style='color: $color;'>$status $check</li>";
        }
        echo "</ul>";
        
        // Show the actual form if we can find it
        if (preg_match('/<form[^>]*>.*?<\/form>/s', $result, $matches)) {
            echo "<h3>Form HTML (first 1000 characters):</h3>";
            echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; max-height: 300px;'>";
            echo htmlspecialchars(substr($matches[0], 0, 1000));
            echo "</pre>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ API call failed or returned unexpected result</p>";
        echo "<pre>" . htmlspecialchars(print_r($result, true)) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='?'>← Back to selection</a> | <a href='data_sources'>← Back to Data Sources</a></p>";
?>
