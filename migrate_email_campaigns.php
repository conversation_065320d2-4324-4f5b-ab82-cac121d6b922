<?php
/**
 * Migration Script: Convert Existing Email System to Campaign Management
 * 
 * This script migrates the existing send_email.php functionality to the new
 * email campaign management system while preserving existing data and settings.
 */

require_once __DIR__ . '/system/startup_sequence.php';

use system\database;
use system\email_campaign;
use autodesk_api\autodesk_api;

#[AllowDynamicProperties] class EmailCampaignMigration {
    
    private database $db;
    private email_campaign $campaign_manager;
    private autodesk_api $autodesk;
    
    public function __construct() {
        $this->db = database::getInstance();
        $this->campaign_manager = new email_campaign();
        $this->autodesk = new autodesk_api();
    }
    
    /**
     * Run the complete migration
     */
    public function run() {
        echo "Starting Email Campaign Migration...\n";
        
        try {
            // Step 1: Create database tables
            $this->createTables();
            
            // Step 2: Migrate existing settings
            $this->migrateSettings();
            
            // Step 3: Create default Autodesk campaign
            $this->createDefaultCampaign();
            
            // Step 4: Migrate existing email history
            $this->migrateEmailHistory();
            
            // Step 5: Add navigation entry
            $this->addNavigationEntry();
            
            echo "Migration completed successfully!\n";
            
        } catch (Exception $e) {
            echo "Migration failed: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
    
    /**
     * Create the new database tables
     */
    private function createTables() {
        echo "Creating database tables...\n";
        
        $sql_file = __DIR__ . '/system/sql/email_campaigns.sql';
        if (file_exists($sql_file)) {
            $sql = file_get_contents($sql_file);
            
            // Split by semicolon and execute each statement
            $statements = array_filter(array_map('trim', explode(';', $sql)));
            
            foreach ($statements as $statement) {
                if (!empty($statement) && !str_starts_with($statement, '--')) {
                    $this->db->getPdo()->exec($statement);
                }
            }
            
            echo "Database tables created successfully.\n";
        } else {
            throw new Exception("SQL file not found: $sql_file");
        }
    }
    
    /**
     * Migrate existing email settings from the old system
     */
    private function migrateSettings() {
        echo "Migrating existing email settings...\n";
        
        // Get existing settings from the old system
        $email_rules = autodesk_api::database_get_storage('subscription_renew_email_send_rules');
        $settings_days = autodesk_api::database_get_storage('subscription_renew_email_send_days');
        $settings_time = autodesk_api::database_get_storage('subscription_renew_email_send_time');
        
        // Store these settings for use in the default campaign
        $this->legacy_settings = [
            'rules' => $email_rules ? explode(',', $email_rules) : ['90', '60', '30', '15'],
            'days' => $settings_days ?: [0, 1, 1, 1, 1, 1, 0], // Default: Mon-Fri
            'time' => $settings_time ?: 9
        ];
        
        echo "Legacy settings migrated: Rules=" . implode(',', $this->legacy_settings['rules']) . 
             ", Time=" . $this->legacy_settings['time'] . "\n";
    }
    
    /**
     * Create the default Autodesk subscription renewal campaign
     */
    private function createDefaultCampaign() {
        echo "Creating default Autodesk subscription renewal campaign...\n";
        
        // Check if campaign already exists
        $existing = $this->db->table('email_campaigns')
            ->where('type', 'subscription_renewal')
            ->where('is_system', 1)
            ->first();
            
        if ($existing) {
            echo "Default campaign already exists (ID: {$existing['id']})\n";
            $campaign_id = $existing['id'];
        } else {
            // Create the campaign
            $campaign_data = [
                'name' => 'Autodesk Subscription Renewal Reminders',
                'description' => 'Automated reminders for Autodesk subscription renewals based on expiration dates (migrated from legacy system)',
                'type' => 'subscription_renewal',
                'status' => 'active',
                'from_email' => '<EMAIL>',
                'from_name' => 'TCS CAD & BIM Solutions Limited',
                'subject_template' => 'Your {{subs_product_name}} subscription {{subs_status,"active":"is ending soon", "expired": "has expired"}}',
                'send_rules' => [
                    'days_before_expiry' => $this->legacy_settings['rules'],
                    'send_days' => $this->legacy_settings['days'],
                    'send_time' => $this->legacy_settings['time']
                ],
                'target_audience' => [
                    'type' => 'autodesk_subscriptions',
                    'criteria' => [
                        'renewable' => true,
                        'not_unsubscribed' => true
                    ]
                ],
                'is_system' => 1,
                'created_by' => 1
            ];
            
            $campaign_id = $this->campaign_manager->create_campaign($campaign_data);
            echo "Created default campaign (ID: $campaign_id)\n";
        }
        
        // Create/update the email template
        $this->createDefaultTemplate($campaign_id);
        
        return $campaign_id;
    }
    
    /**
     * Create the default email template from existing template file
     */
    private function createDefaultTemplate($campaign_id) {
        echo "Creating default email template...\n";
        
        // Load existing template
        $template_file = FS_APP_ROOT . DS . 'resources/email_templates/autodesk/reminder_email.emlt.php';
        
        if (file_exists($template_file)) {
            $template_content = file_get_contents($template_file);
            
            // Parse the template (first line is from, second is subject, rest is body)
            $lines = explode("\n", $template_content);
            $from_line = trim($lines[0] ?? '');
            $subject_line = trim($lines[1] ?? '');
            $body_content = implode("\n", array_slice($lines, 2));
            
            $template_data = [
                'name' => 'Autodesk Subscription Renewal Template',
                'subject_template' => $subject_line,
                'body_template' => $body_content,
                'template_type' => 'html',
                'placeholders' => [
                    'subs_product_name' => 'Product Name',
                    'subs_status' => 'Subscription Status',
                    'endcust_primary_admin_first_name' => 'First Name',
                    'endcust_primary_admin_last_name' => 'Last Name',
                    'endcust_primary_admin_email' => 'Email Address',
                    'subs_endDate' => 'End Date',
                    'subs_quantity' => 'Quantity/Seats',
                    'subs_subscriptionReferenceNumber' => 'Reference Number',
                    'subs_opportunityNumber' => 'Opportunity Number'
                ]
            ];
            
            $template_id = $this->campaign_manager->save_template($campaign_id, $template_data);
            echo "Created template (ID: $template_id)\n";
            
        } else {
            echo "Warning: Template file not found at $template_file\n";
        }
    }
    
    /**
     * Migrate existing email history to the new system
     */
    private function migrateEmailHistory() {
        echo "Migrating existing email history...\n";
        
        // Get the default campaign ID
        $campaign = $this->db->table('email_campaigns')
            ->where('type', 'subscription_renewal')
            ->where('is_system', 1)
            ->first();
            
        if (!$campaign) {
            echo "Warning: Default campaign not found, skipping history migration\n";
            return;
        }
        
        // Migrate from autodesk_email_history table
        $old_history = $this->db->table('autodesk_email_history')
            ->select(['*'])
            ->orderBy('date_sent', 'desc')
            ->limit(1000) // Migrate last 1000 records
            ->get();
            
        $migrated = 0;
        foreach ($old_history as $record) {
            try {
                $history_data = [
                    'campaign_id' => $campaign['id'],
                    'recipient_email' => $record['email_address'],
                    'recipient_type' => 'subscription',
                    'recipient_reference_id' => $record['subscription_ref_id'],
                    'send_status' => 'sent', // Assume old records were sent
                    'triggered_by_rule' => $record['triggered_by'],
                    'sent_at' => $record['date_sent'],
                    'send_result' => $record['result'],
                    'created_at' => $record['date_sent']
                ];
                
                $this->campaign_manager->record_send_history($history_data);
                $migrated++;
                
            } catch (Exception $e) {
                // Skip duplicates or errors
                continue;
            }
        }
        
        echo "Migrated $migrated email history records\n";
        
        // Update campaign statistics
        $this->updateCampaignStats($campaign['id']);
    }
    
    /**
     * Update campaign statistics based on migrated history
     */
    private function updateCampaignStats($campaign_id) {
        $stats = $this->db->table('email_campaign_history')
            ->select([
                'COUNT(*) as total_sent',
                'SUM(CASE WHEN send_status = "delivered" THEN 1 ELSE 0 END) as total_delivered',
                'SUM(CASE WHEN send_status = "failed" THEN 1 ELSE 0 END) as total_failed'
            ])
            ->where('campaign_id', $campaign_id)
            ->first();
            
        $this->campaign_manager->update_campaign($campaign_id, [
            'total_sent' => $stats['total_sent'] ?? 0,
            'total_delivered' => $stats['total_delivered'] ?? 0,
            'total_failed' => $stats['total_failed'] ?? 0
        ]);
        
        echo "Updated campaign statistics\n";
    }
    
    /**
     * Add navigation entry
     */
    private function addNavigationEntry() {
        echo "Adding navigation entry...\n";
        
        $nav_sql = file_get_contents(__DIR__ . '/system/sql/email_campaigns_navigation.sql');
        $this->db->getPdo()->exec($nav_sql);
        
        echo "Navigation entry added\n";
    }
}

// Run the migration if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    try {
        $migration = new EmailCampaignMigration();
        $migration->run();
        echo "\n✅ Migration completed successfully!\n";
        echo "You can now access Email Campaigns from the navigation menu.\n";
        echo "The existing send_email.php script will continue to work but should be replaced with the new campaign system.\n";
    } catch (Exception $e) {
        echo "\n❌ Migration failed: " . $e->getMessage() . "\n";
        exit(1);
    }
}
