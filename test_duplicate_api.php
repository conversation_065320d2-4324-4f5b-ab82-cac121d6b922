<?php
/**
 * Direct API test for duplicate functionality
 */

// Include the necessary files
require_once 'system/classes/startup_sequence.class.php';
require_once 'system/functions/functions.php';

// Initialize the system
$path['fs_app_root'] = __DIR__;
$schema = require_once $path['fs_app_root'] . '/system/config/path_schema.php';
$path['fs_system'] = $path['fs_app_root'] . '/' . $schema['system']['root'];
startup_sequence::start($path, $schema);

use system\data_source_manager;

// Include the API functions
require_once 'system/api/data_sources.api.php';

echo "<h1>Direct API Test for Duplicate Functionality</h1>";

try {
    // Get all data sources
    $data_sources = data_source_manager::get_data_sources();
    
    if (empty($data_sources)) {
        echo "<p style='color: orange;'>No data sources found. Please create one first.</p>";
        exit;
    }
    
    $test_source = $data_sources[0];
    echo "<h2>Testing with Data Source: {$test_source['name']} (ID: {$test_source['id']})</h2>";
    
    // Test the duplicate_view API function directly
    echo "<h3>1. Testing duplicate_view API function:</h3>";
    $params = ['id' => $test_source['id']];
    $result = \api\data_sources\duplicate_view($params);
    
    if (is_string($result)) {
        echo "<p style='color: green;'>✓ duplicate_view returned HTML content (length: " . strlen($result) . " characters)</p>";
        
        // Check if the result contains expected elements
        $checks = [
            'Copy of' => strpos($result, 'Copy of') !== false,
            'data-source-builder' => strpos($result, 'data-source-builder') !== false,
            'Duplicate Data Source' => strpos($result, 'Duplicate Data Source') !== false,
            'form' => strpos($result, '<form') !== false,
            'name field' => strpos($result, 'name="name"') !== false
        ];
        
        echo "<h4>Content Checks:</h4>";
        echo "<ul>";
        foreach ($checks as $check => $passed) {
            $status = $passed ? '✓' : '✗';
            $color = $passed ? 'green' : 'red';
            echo "<li style='color: $color;'>$status $check</li>";
        }
        echo "</ul>";
        
        // Show a snippet of the result
        echo "<h4>HTML Preview (first 500 characters):</h4>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; overflow-x: auto; max-height: 200px;'>";
        echo htmlspecialchars(substr($result, 0, 500)) . (strlen($result) > 500 ? '...' : '');
        echo "</pre>";
        
    } else {
        echo "<p style='color: red;'>✗ duplicate_view returned unexpected result type: " . gettype($result) . "</p>";
        echo "<pre>" . print_r($result, true) . "</pre>";
    }
    
    // Test the duplicate API function
    echo "<h3>2. Testing duplicate API function:</h3>";
    $duplicate_result = \api\data_sources\duplicate($params);
    
    if (is_array($duplicate_result)) {
        if ($duplicate_result['success'] ?? false) {
            echo "<p style='color: green;'>✓ duplicate API function succeeded. New ID: " . ($duplicate_result['new_id'] ?? 'unknown') . "</p>";
            
            // Clean up the test duplicate
            if (!empty($duplicate_result['new_id'])) {
                $db = \system\database::table('autobooks_data_sources');
                $db->where('id', $duplicate_result['new_id'])->delete();
                echo "<p style='color: blue;'>✓ Test duplicate cleaned up</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ duplicate API function failed: " . ($duplicate_result['error'] ?? 'unknown error') . "</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ duplicate API function returned unexpected result type: " . gettype($duplicate_result) . "</p>";
    }
    
    // Test data source retrieval
    echo "<h3>3. Testing data source retrieval:</h3>";
    $retrieved_source = data_source_manager::get_data_source($test_source['id']);
    if ($retrieved_source) {
        echo "<p style='color: green;'>✓ Successfully retrieved data source</p>";
        echo "<h4>Data Source Structure:</h4>";
        echo "<ul>";
        $important_fields = ['name', 'table_name', 'tables', 'selected_columns', 'filters', 'joins'];
        foreach ($important_fields as $field) {
            $value = $retrieved_source[$field] ?? null;
            $type = gettype($value);
            $preview = is_array($value) ? '[' . count($value) . ' items]' : (is_string($value) ? '"' . substr($value, 0, 50) . '"' : $value);
            echo "<li><strong>$field:</strong> $type - $preview</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>✗ Failed to retrieve data source</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='data_sources'>← Back to Data Sources</a></p>";
?>
