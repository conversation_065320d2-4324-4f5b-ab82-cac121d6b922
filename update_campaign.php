<?php
/**
 * Simple script to update the test campaign
 */

// Set up basic database connection
$host = 'localhost';
$dbname = 'autobooks';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Update the campaign
    $stmt = $pdo->prepare("UPDATE email_campaigns SET subject_template = ? WHERE id = 3");
    $stmt->execute(['Test Email for {{name}} - {{email}}']);
    
    echo "Campaign updated successfully!\n";
    
    // Verify the update
    $stmt = $pdo->prepare("SELECT id, name, status, subject_template, data_source_id FROM email_campaigns WHERE id = 3");
    $stmt->execute();
    $campaign = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($campaign) {
        echo "Campaign details:\n";
        echo "  ID: " . $campaign['id'] . "\n";
        echo "  Name: " . $campaign['name'] . "\n";
        echo "  Status: " . $campaign['status'] . "\n";
        echo "  Subject: " . $campaign['subject_template'] . "\n";
        echo "  Data Source ID: " . $campaign['data_source_id'] . "\n";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>
