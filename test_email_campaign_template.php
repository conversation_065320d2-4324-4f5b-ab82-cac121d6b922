<?php
/**
 * Test script to verify email campaign form template compilation
 */

// Define necessary constants
if (!defined('FS_TEMP')) define('FS_TEMP', 'temp');
if (!defined('DS')) define('DS', DIRECTORY_SEPARATOR);
if (!defined('FS_APP_ROOT')) define('FS_APP_ROOT', __DIR__ . '/');
if (!defined('APP_ROOT')) define('APP_ROOT', '/autobooks/');

// Include necessary files
require_once 'system/classes/edge/edge.class.php';

// Test data for the template
$test_data = [
    'title' => 'Test Campaign Form',
    'campaign' => [
        'id' => 1,
        'name' => 'Test Campaign',
        'description' => 'Test Description',
        'type' => 'general',
        'status' => 'draft',
        'from_email' => '<EMAIL>',
        'from_name' => 'Test Company',
        'subject_template' => 'Test Subject {{customer_name}}',
        'email_template' => 'test_template.html'
    ],
    'is_edit' => true,
    'campaign_types' => [
        'general' => 'General Campaign',
        'subscription_renewal' => 'Subscription Renewal'
    ]
];

try {
    echo "Testing email campaign form template compilation...\n";
    
    // Try to render the template
    $output = edge\Edge::render('email-campaign-form', $test_data);
    
    if ($output) {
        echo "✓ Template compiled successfully!\n";
        echo "✓ No undefined constant errors detected.\n";
        
        // Check if the output contains the expected elements
        if (strpos($output, 'Campaign Form') !== false) {
            echo "✓ Template contains expected content.\n";
        } else {
            echo "⚠ Template may not contain expected content.\n";
        }
        
        // Save the output for inspection if needed
        file_put_contents('temp/test_email_campaign_output.html', $output);
        echo "✓ Template output saved to temp/test_email_campaign_output.html\n";
        
    } else {
        echo "✗ Template compilation failed - no output generated.\n";
    }
    
} catch (Exception $e) {
    echo "✗ Template compilation failed with error:\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

echo "\nTest complete.\n";
?>
